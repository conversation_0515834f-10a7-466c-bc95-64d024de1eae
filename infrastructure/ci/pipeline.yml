---

ci_artifact_bucket_credentials: &bucket_credentials
  region_name: {{aws-default-region}}
  access_key_id: {{aws-dev-builder-key-id}}
  secret_access_key: {{aws-dev-builder-secret-access-key}}

aws_params: &aws_params
  AWS_ACCESS_KEY_ID: {{aws-dev-builder-key-id}}
  AWS_SECRET_ACCESS_KEY: {{aws-dev-builder-secret-access-key}}
  AWS_DEFAULT_REGION: {{aws-default-region}}

ci_slack_on_failure: &slack_on_failure
  put: slack-alert
  params:
    channel: "#innit-iq-dev"
    username: Concourse
    attachments:
      - text: |
          $BUILD_PIPELINE_NAME $BUILD_JOB_NAME job #$BUILD_NAME FAILED.
          Check it out at <https://cip.inh0.net/teams/main/pipelines/$BUILD_PIPELINE_NAME/jobs/$BUILD_JOB_NAME/builds/$BUILD_NAME|Build!>
        color: "#DC143C"

ci_slack_on_success: &slack_on_success
  put: slack-alert
  params:
    channel: "#innit-iq-dev"
    username: Concourse
    attachments:
      - text: |
          $BUILD_PIPELINE_NAME $BUILD_JOB_NAME job #$BUILD_NAME SUCCEED.
        color: "#36a64f"

resource_types:
  - name: slack-notification
    type: docker-image
    source:
      repository: cfcommunity/slack-notification-resource

resources:
  - name: slack-alert
    type: slack-notification
    icon: slack
    source:
      url: *****************************************************************************

  - name: fims-lite-prod
    type: git
    icon: github
    source:
      uri: **************:innitinc/fims-lite.git
      branch: main
      private_key: {{private-repo-key}}
      params: {depth: 1}

  - name: fims-lite-develop
    type: git
    icon: github
    source:
      uri: **************:innitinc/fims-lite.git
      branch: develop
      private_key: {{private-repo-key}}
      params: {depth: 1}

  - name: docker-image
    type: registry-image
    icon: docker
    source:
      repository: innitdocker/fims-lite
      username: {{docker-hub-username}}
      password: {{docker-hub-password}}
      registry_mirror: { host: nexus.inh0.net:18079, username: {{docker-hub-username}}, password: {{docker-hub-password}} }
      platform: {os: linux, architecture: arm64/v8}

  - name: version-int
    type: semver
    source:
      bucket: {{s3-artifacts-bucket}}
      key: version/fims-lite-int.version
      initial_version: 2.0.0
      <<: *bucket_credentials
  
  - name: version-prod
    type: semver
    source:
      bucket: {{s3-artifacts-bucket}}
      key: version/fims-lite-prod.version
      initial_version: 3.0.0
      <<: *bucket_credentials

jobs:
  - name: build-int
    serial: true
    plan:
      - in_parallel:
        - get: fims-lite-develop
          trigger: true
        - get: version-int
      - put: version-int
        params: {bump: patch}
      - load_var: version
        file: version-int/number
      - task: build
        file: fims-lite-develop/infrastructure/ci/task/build/build.yml
        input_mapping: 
          version: version-int
          fims-lite: fims-lite-develop
        vars:
          docker-hub-username: {{docker-hub-username}}
          docker-hub-password: {{docker-hub-password}}  
        params:
          DOCKER_IMAGE_NAME: innitdocker/fims-lite
          AWS_DEFAULT_REGION: {{aws-default-region}}
          AWS_SECRET_ACCESS_KEY: {{aws-dev-builder-secret-access-key}}
          AWS_ACCESS_KEY_ID: {{aws-dev-builder-key-id}}
        privileged: true
        on_failure:
          <<: *slack_on_failure
      # build using `oci-build` task
      - task: build-docker
        privileged: true
        input_mapping: 
          version: version-int
          fims-lite: fims-lite-develop
        config:
          platform: linux
          image_resource:
            type: registry-image
            source:
              repository: concourse/oci-build-task
              username: {{docker-hub-username}}
              password: {{docker-hub-password}}
          params:
            IMAGE_PLATFORM: linux/arm64
          inputs:
          - name: fims-lite
            path: .
          outputs:
          - name: image
          run:
            path: build
          caches:
          - path: cache
        on_failure:
          <<: *slack_on_failure
      - in_parallel:
        # push using `registry-image` resource
        - put: docker-image
          params: {image: image/image.tar, version: ((.:version))}
  - name: int
    serial: true
    plan:
      - in_parallel:
          - get: fims-lite-develop
            passed: [build-int]
            trigger: true
          - get: docker-image
            passed: [build-int]
          - get: version-int
            passed: [build-int]
      - task: fims-lite-task
        file: fims-lite-develop/infrastructure/ci/task/deploy/eks.yml
        input_mapping:
          version: version-int
          fims-lite: fims-lite-develop
        vars:
          docker-hub-username: {{docker-hub-username}}
          docker-hub-password: {{docker-hub-password}}
        params:
          AWS_SECRET_ACCESS_KEY: {{aws-dev-builder-secret-access-key}}
          AWS_ACCESS_KEY_ID: {{aws-dev-builder-key-id}}
          AWS_DEFAULT_REGION: {{aws-default-region}}
          ENVIRONMENT: int
          AWS_EKS_CLUSTER_NAME: innit-dev
          ASSETS_PUBLIC_PATH: {{default-assets-public-path}}
          FSS_HOST: {{int-fss-host}}
          FMP_HOST: {{int-fmp-host}}
          ICL_HOST: {{int-icl-host}}
          FLITE_HOST: {{int-flite-host}}
          MODULE_HOST: {{int-module-host}}
          FCS_HOST: {{int-fcs-host}}
          FAIS_HOST: {{int-fais-host}}
          WEB_BACKEND_HOST: {{dev-web-backend-host}}
          WEB_FRONTEND_HOST: {{dev-web-frontend-host}}
          OAUTH_DOMAIN: {{int-oauth-domain}}
          OAUTH_CLIENT_ID: {{int-oauth-client-id}}
          ELASTIC_APM_SECRET_TOKEN: {{elastic-apm-secret-token}}
        on_failure:
          <<: *slack_on_failure
        on_success:
          <<: *slack_on_success

  - name: build-prod
    serial: false
    plan:
      - in_parallel:
        - get: fims-lite-prod
          trigger: true
        - get: version-prod
      - put: version-prod
        params: {bump: patch}
      - load_var: version
        file: version-prod/number
      - task: build
        file: fims-lite-prod/infrastructure/ci/task/build/build.yml
        input_mapping: 
          version: version-prod
          fims-lite: fims-lite-prod
        vars:
          docker-hub-username: {{docker-hub-username}}
          docker-hub-password: {{docker-hub-password}}  
        params:
          DOCKER_IMAGE_NAME: innitdocker/fims-lite
          AWS_DEFAULT_REGION: {{aws-default-region}}
          AWS_SECRET_ACCESS_KEY: {{aws-dev-builder-secret-access-key}}
          AWS_ACCESS_KEY_ID: {{aws-dev-builder-key-id}}
        privileged: true
        on_failure:
          <<: *slack_on_failure
      # build using `oci-build` task
      - task: build-docker
        privileged: true
        input_mapping: 
          version: version-prod
          fims-lite: fims-lite-prod
        config:
          platform: linux
          image_resource:
            type: registry-image
            source:
              repository: concourse/oci-build-task
              username: {{docker-hub-username}}
              password: {{docker-hub-password}}
          params:
            IMAGE_PLATFORM: linux/arm64
          inputs:
          - name: fims-lite
            path: .
          outputs:
          - name: image
          run:
            path: build
          caches:
          - path: cache
        on_failure:
          <<: *slack_on_failure
      - in_parallel:
        # push using `registry-image` resource
        - put: docker-image
          params: {image: image/image.tar, version: ((.:version))}
  - name: prod
    serial: true
    plan:
      - in_parallel:
        - get: fims-lite-prod
          trigger: false
          passed: [build-prod]
        - get: docker-image
          passed: [build-prod]
        - get: version-prod
          passed: [build-prod]
      - task: fims-lite-task
        file: fims-lite-prod/infrastructure/ci/task/deploy/eks.yml
        input_mapping:
          version: version-prod
          fims-lite: fims-lite-prod
        vars:
          docker-hub-username: {{docker-hub-username}}
          docker-hub-password: {{docker-hub-password}}
        params:
          AWS_SECRET_ACCESS_KEY: {{aws-prod-builder-secret-access-key}}
          AWS_ACCESS_KEY_ID:  {{aws-prod-builder-key-id}}
          AWS_DEFAULT_REGION: {{aws-default-region}}
          ENVIRONMENT: prod
          AWS_EKS_CLUSTER_NAME: innit-prod
          ASSETS_PUBLIC_PATH: {{default-assets-public-path}}
          FSS_HOST: {{prod-fss-host}}
          FMP_HOST: {{prod-fmp-host}}
          ICL_HOST: {{prod-icl-host}}
          FLITE_HOST: {{prod-flite-host}}
          MODULE_HOST: {{prod-module-host}}
          FCS_HOST: {{prod-fcs-host}}
          FAIS_HOST: {{prod-fais-host}}
          WEB_BACKEND_HOST: {{prod-web-backend-host}}
          WEB_FRONTEND_HOST: {{prod-web-frontend-host}}
          OAUTH_DOMAIN: {{prod-oauth-domain}}
          OAUTH_CLIENT_ID: {{prod-oauth-client-id}}
          ELASTIC_APM_SECRET_TOKEN: {{elastic-apm-secret-token}}
        on_failure:
          <<: *slack_on_failure
        on_success:
          <<: *slack_on_success
...
