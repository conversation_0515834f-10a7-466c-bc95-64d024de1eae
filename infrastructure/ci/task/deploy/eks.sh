#!/bin/bash

set -e
set -u
set -x

export VERSION=$(cat version/number)

DEPLOY_DIR="fims-lite/"
DEPLOYMENT_NAME="fims-lite"

# deployment
cd ${DEPLOY_DIR}
eksctl utils write-kubeconfig --cluster=${AWS_EKS_CLUSTER_NAME} --region=${AWS_DEFAULT_REGION}
mkdir deploy
kustomize build infrastructure/kustomize/env/${ENVIRONMENT} | envsubst > deploy/deploy.yaml
kubectl apply --filename deploy/deploy.yaml

# Wait for deployment to complete
if ! kubectl rollout status deployment/${DEPLOYMENT_NAME} -n ${DEPLOYMENT_NAME} --timeout=3000s; then
    echo "Deployment timed out."
    exit 1
fi

# Check deployment status
deployment_status=$(kubectl get deployment ${DEPLOYMENT_NAME} -n ${DEPLOYMENT_NAME} -o jsonpath='{.status.conditions[?(@.type=="Available")].status}')

# Check if deployment is successful
if [ "$deployment_status" != "True" ]; then
    echo "Deployment failed. Rolling back..."
    # Rollback the deployment
    kubectl rollout undo deployment/${DEPLOYMENT_NAME} -n ${DEPLOYMENT_NAME}
    exit 1
fi

echo "Deployment successful."