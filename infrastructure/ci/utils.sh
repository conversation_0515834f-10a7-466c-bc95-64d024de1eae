#!/usr/bin/env bash

set -e

CONCOURSE_URL='https://cip.inh0.net'
CONCOURSE_TARGET='fims-lite'
KEY_ID='alias/platform-kms'

DEFAULT_CI_PRIVATE_CONFIG_PATH='conf/private.json'
DEFAULT_CI_PUBLIC_CONFIG_PATH='conf/public.yml'

DEFAULT_CI_DECRYPTED_CONFIG_OUT_PATH='conf/private-decrypted.yml'

encrypt-ci() {
    if [[ ${1} == "" ]]; then
        echo 'ERROR: AWS_PROFILE is required!'
        exit
    fi
    encrypt ${KEY_ID} ${DEFAULT_CI_DECRYPTED_CONFIG_OUT_PATH} ${DEFAULT_CI_PRIVATE_CONFIG_PATH} $1
    echo 'Concourse private configuration is updated!'
}

decrypt-ci() {
    if [[ ${1} == "" ]]; then
        echo 'ERROR: AWS_PROFILE is required!'
        exit
    fi
    decrypt ${DEFAULT_CI_PRIVATE_CONFIG_PATH} ${DEFAULT_CI_DECRYPTED_CONFIG_OUT_PATH} $1
    echo 'Concourse private configuration is decrypted to file:' ${DEFAULT_CI_DECRYPTED_CONFIG_OUT_PATH}!
}

pipeline() {
    if [ -z "$1" ]; then
        echo 'AWS_PROFILE is required!'
        echo "usage : ./utils.sh pipeline AWS_PROFILE"
        exit
    fi

    decrypt-ci $1

    fly -t ${CONCOURSE_TARGET} login -c ${CONCOURSE_URL}
    fly -t ${CONCOURSE_TARGET} set-pipeline -p ${CONCOURSE_TARGET} -c pipeline.yml --load-vars-from ${DEFAULT_CI_PUBLIC_CONFIG_PATH} --load-vars-from ${DEFAULT_CI_DECRYPTED_CONFIG_OUT_PATH}
    rm -f ${DEFAULT_CI_DECRYPTED_CONFIG_OUT_PATH}
}

decrypt() {
    cat $1 | python -c "import sys, json; print(json.load(sys.stdin)['CiphertextBlob'])" | base64 --decode > base_64_decoded_temp
    aws kms decrypt --ciphertext-blob fileb://base_64_decoded_temp --output text --query Plaintext --profile $3 | base64 --decode > $2
    rm -rf base_64_decoded_temp
}

encrypt() {
    aws kms encrypt --key-id $1 --plaintext fileb://$2 --output json > $3 --profile $4
}


usage() {
    echo "usage: ./utils.sh encrypt | decrypt | pipeline --profile {AWS_PROFILE}"
}

profile="default"
while [ "$1" != "" ]; do
    case $1 in
        encrypt )               action="encrypt"
                                ;;

        decrypt )               action="decrypt"
                                ;;

        pipeline )              action="pipeline"
                                ;;

        -p | --profile )        shift
                                profile="$1"
                                ;;

        * )
    esac
    shift
done


case "$action" in
    "encrypt")
        encrypt-ci ${profile}
        ;;
    "decrypt")
        decrypt-ci ${profile}
        ;;
    "pipeline")
        pipeline ${profile}
        ;;
    *) usage
       exit 1
esac
