{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Create a S3 bucket in src account with sharing to dest account", "Parameters": {"Environment": {"Description": "Name of Environment", "Type": "String"}, "BucketName": {"Description": "The name of the s3 bucket", "Type": "String"}, "SharingAccountId": {"Description": "The account id to share the bucket with", "Type": "String"}, "SharingRoleName": {"Description": "The role name in dest account to give access", "Type": "String"}, "TeamName": {"Description": "Used by team IAM policy to allow individuals to access.", "Type": "String"}}, "Resources": {"S3Bucket": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"BucketName": {"Ref": "BucketName"}, "Tags": [{"Key": "Name", "Value": {"Ref": "BucketName"}}, {"Key": "Team", "Value": {"Ref": "TeamName"}}]}}, "SharingBucketPolicy": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "S3Bucket"}, "PolicyDocument": {"Statement": [{"Sid": "Sharing permission", "Effect": "Allow", "Principal": {"AWS": [{"Ref": "SharingAccountId"}, {"Fn::Join": ["", ["arn:aws:iam::", {"Ref": "SharingAccountId"}, ":root"]]}]}, "Action": ["s3:*"], "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "BucketName"}]]}, {"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "BucketName"}, "/*"]]}]}]}}}}, "Outputs": {"S3Bucket": {"Value": {"Ref": "S3Bucket"}, "Description": "The newly created S3 bucket"}}}