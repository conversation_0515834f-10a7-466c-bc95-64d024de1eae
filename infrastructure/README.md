This is the package for deployment, continuous integration and 
continuous development scripts. 

## Cloud Formation Templates

The bulk of this repository are a set of cloud formation templates and 
their related config files. These are all under the `aws` folder.

### Setup

> Install `cfmanager` package first 
[`https://github.com/innitinc/infrastructure/tree/master/cfmanager`]

```shell
$ pip install git+https://github.com/innitinc/infrastructure.git@cf-v2.2.1#subdirectory=cfmanager
```

### Usage

> A sample usage for deploying high priority queue for 
integration environment

```shell
$ cf deploy --debug -e int-high -p innit-dev sqs.yml
```

### Notes

Secrets in the config files are encrypted using `alias/platform-kms`, 
to decrypt/encrypt the secrets, AWS KMS access is needed

> Encrypt a text 

```shell
$ aws kms encrypt --key-id alias/platform-kms --plaintext ${SECRET_TEXT} --query CiphertextBlob --output text ```

> Decrypt a secret

```shell
$ aws kms decrypt --key-id alias/platform-kms --plaintext ${SECRET_TEXT} --query CiphertextBlob --output text \
  		| base64 --decode > secret_blob
```

## Continuous Integration

`Concourse` is preferred tool for the continuous integration. 
Projects concourse templates and job scripts are all under the `ci` folder.

### Utils

You can use `utils.sh` to control the project pipeline

> Decrypt concourse private configuration

```shell
$ ./utils.sh decrypt --profile AWS_PROFILE
```

> Encrypt concourse private decrypted configuration (private-config-decrypted.yml)

```shell
$ ./utils.sh encrypt --profile AWS_PROFILE
```

> Create/update the project concourse pipeline

```shell
$ ./utils.sh pipeline --profile AWS_PROFILE
```