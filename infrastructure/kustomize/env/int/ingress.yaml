apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/group.name: dev-external #dev-internal or dev-external
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:485978741528:certificate/3ffaeb9d-afc1-4906-8e31-5c620fdd662f
    external-dns.alpha.kubernetes.io/hostname: dev-foodlm.innit.com
  name: fims-lite
  namespace: fims-lite
spec:
  ingressClassName: alb
  rules:
  - host: dev-foodlm.innit.com
    http:
      paths:
      - path: /
        backend:
          service:
            name: fims-lite
            port:
              number: 8080
        pathType: Prefix
  tls:
  - hosts:
    - dev-foodlm.innit.com
