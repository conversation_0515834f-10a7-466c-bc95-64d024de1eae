apiVersion: apps/v1
kind: Deployment
metadata:
  name: fims-lite
  namespace: fims-lite
spec:
  template:
    spec:
      containers:
      - name: fims-lite
        env:
        - name: ENVIRONMENT
          value: prod
        - name: NUXT_PUBLIC_ENVIRONMENT
          value: prod
        - name: ASSETS_PUBLIC_PATH
          value: ${ASSETS_PUBLIC_PATH}
        - name: FSS_HOST
          value: ${FSS_HOST}
        - name: FMP_HOST
          value: ${FMP_HOST}
        - name: ICL_HOST
          value: ${ICL_HOST}
        - name: FLITE_HOST
          value: ${FLITE_HOST}
        - name: MODULE_HOST
          value: ${MODULE_HOST}
        - name: FCS_HOST
          value: ${FCS_HOST}
        - name: FAIS_HOST
          value: ${FAIS_HOST}
        - name: NUXT_PUBLIC_WEB_BACKEND_HOST
          value: ${WEB_BACKEND_HOST}
        - name: NUXT_PUBLIC_WEB_FRONTEND_HOST
          value: ${WEB_FRONTEND_HOST}
        - name: NUXT_PUBLIC_OAUTH_DOMAIN
          value: ${OAUTH_DOMAIN}
        - name: NUXT_PUBLIC_OAUTH_CLIENT_ID
          value: ${OAUTH_CLIENT_ID}
        - name: ELASTIC_APM_SECRET_TOKEN
          value: ${ELASTIC_APM_SECRET_TOKEN}
        - name: ELASTIC_APM_SERVICE_NAME
          value: fims-lite
        - name: ELASTIC_APM_SERVER_URL
          value: https://fce369a0f330460783b82ec3c121af06.apm.us-west-2.aws.cloud.es.io:443
        - name: ELASTIC_APM_ENVIRONMENT
          value: prod
        - name: ELASTIC_APM_BREAKDOWN_METRICS
          value: 'true'
