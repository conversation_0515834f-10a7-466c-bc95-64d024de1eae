apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/group.name: prod-external #prod-internal or prod-external
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:710170291963:certificate/5e6dbba2-3cf0-4b7d-a704-67cfd773435a
    external-dns.alpha.kubernetes.io/hostname: foodlm.innit.com
  name: fims-lite
  namespace: fims-lite
spec:
  ingressClassName: alb
  rules:
  - host: foodlm.innit.com
    http:
      paths:
      - path: /
        backend:
          service:
            name: fims-lite
            port:
              number: 8080
        pathType: Prefix
  tls:
  - hosts:
    - foodlm.innit.com
