# application name
application: fims-lite

# common settings for all environments
common:
  # https://github.com/ThoughtWorksStudios/eb_deployer/wiki/Default-Configuration
  # Solution stack for elastic beanstalk, default is 64bit tomcat 7 for JAVA app
  solution_stack_name: <%= ENV['SOLUTION_STACK_NAME'] %>

  # Tier name for environments. Current supported values are WebServer and Worker
  # tier: WebServer

  # AWS region to deploy. Default to us-east-1
  region: us-west-2

  # There are three deployment strategies: 'blue-green', 'blue-only', or 'inplace-update'.
  # Blue green deployments keep two elastic beanstalk environments and always deploy to
  # inactive one, to achieve zero downtime.
  # Blue only deployments do everything that the blue green deployments do except for the final
  # inactive to active CNAME swap leaving the newly deployed application on the inactive
  # "blue" instance.
  # Inplace-update strategy will only keep one environment, and update the version inplace on
  # deploy. Inplace-update will save resources but will suffer from downtime.
  # (All old environments need be destroyed when you switching between strategies.)
  # Default strategy is 'blue-green'.
  strategy: blue-green
  blue_green_terminate_inactive: true

  # Name of s3 bucket where uploaded application packages will be stored.
  # Note that the string ".packages" will be added as a suffix to your bucket.
  # So, if "thoughtworks.simple" is passed as the bucket name, the actual s3 bucket
  # name will be thoughtworks.simple.packages. Default to application name.
  package_bucket: fims-lite-artifacts

  # If phoenix mode is turned on, it will terminate the old elastic
  # beanstalk environment and recreate on deploy. For blue-green
  # deployment it terminates the inactive environment first then
  # recreate it. This is useful to avoid configuration drift and
  # accumulating state on the ec2 instances. Default is off but we recommend
  # it to be turned on for production environment.
  phoenix_mode: on

  # The tags you would like to be associated with your resources.
  # These tags will only be used when you first launch an environment.  If you are using
  # phoenix_mode set as true each time you deploy you will get a new environment and therefore
  # any changes to your tags.  If phoenix_mode is false then it will only use your tags on the
  # initial deploy.
  tags:
    app_name: fims-lite

  # Specifies the maximum number of versions to keep. Older versions are removed
  # and deleted from the S3 source bucket as well. If specified as zero or not
  # specified, all versions will be kept.  If a version_prefix is given, only removes
  # version starting with the prefix.
  keep_latest: 10

  # Specifies a prefix to prepend to the version label.
  # This can be useful if you want to use different binaries for different
  # environments.
  # version_prefix:

  # Generating version label for package to be deployed. A readable version label will
  # provide better traceablity of your deployment process.
  # By default setting is:
  # version_label: <%= package_digest %>
  # which means using MD5 digest of the package file. If you deploy using build
  # pipeline tool such as GO, switching to pipline counter is highly suggested to
  # increase the readability. Following example will read pipeline counter from environment
  # variable with a fall back to digest for local deployment:
  version_label: <%= ENV['VERSION'] %>


  # Smoke test value should be a piece of ruby code with access to single variable
  # "host_name" -- environment DNS name. Smoke test snippet will be evaluated at
  # the end of the deployment for inplace-update deployment. For blue-green
  # deployment it will run after inactive environment update is completed and before
  # switching over.
  # Defining a smoke test is highly recommended for serious usage. By default we use
  # The simplest one that just be checking server landing page using curl, e.g.
  #  smoke_test: |
  #      require 'net/http'
  #      require 'json'
  #
  #      # check the Elastic Beanstalk URL,
  #      # because in blue-green deployment it hasn't been swapped to a *.inh0.net doamin at this point
  #      host_healthcheck = "https://#{host_name}/admin/healthcheck"
  #
  #      puts "Running healthcheck against environment: #{host_healthcheck}"
  #
  #      # check and wait for the HTTP 200 OK response
  #      curl_http_code = "curl -s -o /dev/null -w \"%{http_code}\" --insecure #{host_healthcheck}"
  #      Timeout.timeout(240) do
  #        until ['200', '301', '302'].include?(`#{curl_http_code}`.strip)
  #          sleep 5
  #        end
  #      end
  #
  #      uri = URI(host_healthcheck)
  #      request = Net::HTTP::Get.new(uri.path)
  #
  #      # Check the new version matches
  #      # ignore SSL cert because:
  #      # 1. we turn off HTTP 80 so we need to use SSL
  #      # 2. It serves the *.inh0.net certificate but is from an ElasticBeanstalk.com domain
  #
  #      response = Net::HTTP.start(
  #                    uri.host, uri.port,
  #                    :use_ssl => uri.scheme == 'https',
  #                    :verify_mode => OpenSSL::SSL::VERIFY_NONE) do |https|
  #                      https.request(request)
  #                    end
  #      json_response = JSON.parse(response.body)
  #
  #      json_response["version"]["message"] == ENV['VERSION']

  # List of Elastic Beanstalk health states that will be considered healthy when deploying.
  # You may want to override default 'Green' with that list, if for example you are using
  # Elastic Beanstalk worker tier and you don't want eb_deployer to fail if you have messages
  # in your SQS queue.
  # By default eb_deployer only considers 'Green' as the healthy state.
  # For a list of all Elastic Beanstalk health states refer to:
  # http://docs.aws.amazon.com/elasticbeanstalk/latest/dg/using-features.healthstatus.html#using-features.healthstatus.colors
  # accepted_healthy_states:
  #  - Green
  #  - Yellow

  # Elastic Beanstalk settings that will apply to the environments you are
  # deploying.
  # For all available options take a look at
  # http://docs.aws.amazon.com/elasticbeanstalk/latest/dg/command-options.html
  option_settings:
    # Following is an example of set EC2 ssh key name. This allow you ssh into the ec2
    # instance. See http://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-key-pairs.html
    - namespace: aws:autoscaling:launchconfiguration
      option_name: EC2KeyName
      value: dev-platform

    # Following is an example which changes EC2 instance type
    # from t1.micro (default) to m1.small. Instances with t1.micro type sometime
    # are not very responsible, so m1.small is suggested for saving time.
    # But if you care about the marginal cost difference you can comment this out to
    # go back to t1.micro.
    - namespace: aws:autoscaling:launchconfiguration
      option_name: InstanceType
      value: t4g.large
    # this instance profile will give the EC2 instance that the EB application is hosted on permission to access S3 and more
    # among other things, S3 is needed for the Docker Hub credentials to pull the Docker image to start the instance
    - namespace: aws:autoscaling:launchconfiguration
      option_name: IamInstanceProfile
      value: <%= ENV['INSTANCE_PROFILE'] %>
      # default of one instance. Ensure it's reset if inactive_settings (used with blue-green deployments) sets it to 0
    - namespace: aws:autoscaling:asg
      option_name: MinSize
      value: "1"
    - namespace: aws:autoscaling:asg
      option_name: MaxSize
      value: "1"
      # make sure cooldown is reset back to default when environment becomes active again
    - namespace: aws:autoscaling:asg
      option_name: Cooldown
      value: "600"
    - namespace: aws:elasticbeanstalk:application
      option_name: "Application Healthcheck URL"
      value: "/ping"
    - namespace: aws:ec2:vpc
      option_name: "VPCId"
      value: <%= ENV['VPC_ID'] %>
    - namespace: aws:ec2:vpc
      option_name: Subnets
      value: <%= ENV['PRIVATE_SUBNETS'] %>
    - namespace: aws:ec2:vpc
      option_name: ELBSubnets
      value: <%= ENV['ELB_SUBNETS'] %>
    - namespace: aws:ec2:vpc
      option_name: ELBScheme
      value: public
    - namespace: aws:ec2:vpc
      option_name: AssociatePublicIpAddress
      value: 'false'
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: ENVIRONMENT
      value: <%= ENV['ENVIRONMENT'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: FSS_HOST
      value: <%= ENV['FSS_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: FMP_HOST
      value: <%= ENV['FMP_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: ICL_HOST
      value: <%= ENV['ICL_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: FLITE_HOST
      value: <%= ENV['FLITE_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: MODULE_HOST
      value: <%= ENV['MODULE_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: FCS_HOST
      value: <%= ENV['FCS_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: FAIS_HOST
      value: <%= ENV['FAIS_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: WEB_BACKEND_HOST
      value: <%= ENV['WEB_BACKEND_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: WEB_FRONTEND_HOST
      value: <%= ENV['WEB_FRONTEND_HOST'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: OAUTH_DOMAIN
      value: <%= ENV['OAUTH_DOMAIN'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: OAUTH_CLIENT_ID
      value: <%= ENV['OAUTH_CLIENT_ID'] %>
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: ASSETS_PUBLIC_PATH
      value: <%= ENV['ASSETS_PUBLIC_PATH'] %>
    - namespace: aws:elb:loadbalancer
      option_name: LoadBalancerHTTPSPort
      value: "443"
    - namespace: aws:elb:loadbalancer
      option_name: LoadBalancerSSLPortProtocol
      value: "SSL"
    - namespace: aws:elb:loadbalancer
      option_name: SSLCertificateId
      value: "arn:aws:acm:us-west-2:485978741528:certificate/3ffaeb9d-afc1-4906-8e31-5c620fdd662f"
    - namespace: aws:elasticbeanstalk:healthreporting:system
      option_name: SystemType
      value: basic
    - namespace: aws:autoscaling:launchconfiguration
      option_name: SSHSourceRestriction
      value: tcp, 22, 22, 172.30.19.0/24  
    - namespace: aws:autoscaling:launchconfiguration
      option_name: DisableIMDSv1
      value: "true"
    #gp3 volume
    - namespace: aws:autoscaling:launchconfiguration
      option_name: RootVolumeType
      value: "gp3"

  # If resources specified, eb_deployer will use the CloudFormation
  # template you provide to create a default CloudFormation stack with
  # name <application_name>-<env-name> for the environment current
  # deploying. And Outputs of the CloudFormation can be mapped to Elastic Beanstalk
  # options settings.
  # keys:
  #    template => CloudFormation template file with JSON format
  #    policy => CloudFormation policy file with JSON format
  #    override_policy => (false) If override_policy is true and a policy file is provided then the policy will temporarily override any existing policy on the resource stack during this update,
  #                       otherwise the provided policy will replace any existing policy on the resource stack
  #    inputs => A Hash, input values for the CloudFormation template
  #    outputs => A Hash with key map to your CloudFormation template outputs and value as elastic beanstalk settings namespace and option_name.
  #    capabilities => An array. You need set it to ['CAPABILITY_IAM'] if the
  # template include IAM Instance Profile.
#  resources:
#
#    # For example creating a RDS instance for blue green deployment(check jruby-rails4
#    # sample project which has a working example):
#    template: elasticbeanstalk_cloudformation.json
#    inputs:
#      Environment: <%= environment %>

# You can define environment here. Each environment can overriden any common settings

environments:
  int:
    option_settings:
      - namespace: aws:autoscaling:launchconfiguration
        option_name: SecurityGroups
        value: sg-02bf766a7b9c54ac9

  prod:
    option_settings:
      - namespace: aws:autoscaling:launchconfiguration
        option_name: EC2KeyName
        value: prod-platform

      - namespace: aws:autoscaling:launchconfiguration
        option_name: SecurityGroups
        value: sg-083159714e5c9fdb5

      - namespace: aws:autoscaling:asg
        option_name: MaxSize
        value: "3"

      - namespace: aws:elb:loadbalancer
        option_name: SSLCertificateId
        value: "arn:aws:acm:us-west-2:710170291963:certificate/5e6dbba2-3cf0-4b7d-a704-67cfd773435a"
