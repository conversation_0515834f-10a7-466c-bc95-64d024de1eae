module.exports = {
  apps : [
    {
      name: 'fims-light',
      exec_mode: 'cluster',
      instances: 'max',
      cwd: '/app',   
      script: '.output/server/index.mjs',
      env: {
        PORT: 8080,
      },
      node_args: '--require=elastic-apm-node/start.js --experimental-loader=elastic-apm-node/loader.mjs',
      log_type: 'json',
      merge_logs: true,
      log_file: '/app/fims-lite.log'
    },
    {
      name: 'filebeat',
      script: '/usr/share/filebeat/bin/filebeat', 
      args: '-e -c /etc/filebeat/filebeat.yml',  
      exec_mode: 'fork', 
      max_memory_restart: '500M',  
      log_file: '/var/log/pm2-filebeat.log'
    }
  ]
}; 