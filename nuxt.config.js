require("dotenv").config();

const apm = require('elastic-apm-node').start({
  // Set required service name (allowed characters: a-z, A-Z, 0-9, -, _, and space)
  serviceName: 'fims-light',
  environment: process.env.ENVIRONMENT || 'dev',
  secretToken: 'pTVeV0oOrXeuC1t93c',
  serverUrl: 'https://fce369a0f330460783b82ec3c121af06.apm.us-west-2.aws.cloud.es.io:443'
});

const AUTH_DOMAIN = process.env.OAUTH_DOMAIN || 'dev-login.innit.com';

export default defineNuxtConfig({
  srcDir: 'src/',
  ssr: false,
  store: true,

  alias: {
    "@сonstants": "/сonstants",
    "~plugins": "~/src/plugins"
  },

  nitro: {
    output: {
      publicDir: './.output/public',
    },
  },

  // Headers of the page
  app: {
    buildAssetsDir: '/_nuxt/', // Base directory for built assets
    head: {
      title: 'Innit - Your Food. Simplified & Solved',
      htmlAttrs: {
        lang: 'en-us',
      },
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1' },
        { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge' }
      ],
      link: [
        {
          rel: 'icon',
          href: 'https://www.innit.com/assets/images/sitewide/favicon-1.png',
        }
      ],
      css: [
        'swiper/dist/css/swiper.css'
      ],
      __dangerouslyDisableSanitizers: ['script']
    }
  },

  runtimeConfig: {
    public: {
      OAUTH_DOMAIN: process.env.NUXT_PUBLIC_OAUTH_DOMAIN || 'dev-login.innit.com',
      OAUTH_CLIENT_ID: process.env.NUXT_PUBLIC_OAUTH_CLIENT_ID || 'hH9dfjtuJzVSWRnl3BwcHHfKAupQODTR',
      WEB_BACKEND_HOST: process.env.NUXT_PUBLIC_WEB_BACKEND_HOST || 'http://localhost:9008',
      FRONT_END_HOST: process.env.NUXT_PUBLIC_WEB_FRONTEND_HOST || 'http://localhost:9008',
      ENVIRONMENT: process.env.NUXT_PUBLIC_ENVIRONMENT || 'localhost'
    }
  },

  // Plugins
  plugins: [
    { src: '~/plugins/03.auth0.js', ssr: false },
    { src: '~/plugins/zendesk.js', mode: 'client'},
    { src: '~/plugins/setConsole.js', ssr: false },
    { src: '~/plugins/i18n.js' },
    { src: '~/plugins/filters.js' },
    { src: '~/plugins/pagination.js', ssr: false },
    { src: '~/plugins/draggable.js', ssr: false },
    { src: '~/plugins/calendar.js', ssr: false },
    { src: '~/plugins/vuex.js'},
    { src: '~/plugins/apm.js', mode: 'client' },
    { src: '~/plugins/projectAndLang.js' },
    { src: '~/plugins/axios-web-backend.js' },
    { src: '~/plugins/tracker/tracker.js', mode: 'client' },
    { src: '~/plugins/keys.js' },
    { src: '~/plugins/eventBus.js' , ssr: false },
    { src: '~/plugins/axios.js' }
  ],
  buildModules: [
    // '@nuxtjs/device',
    '@nuxtjs/vuex-module',
    ['@pinia/nuxt', { disableVuex: false }]
  ],
  // device: {
  //   refreshOnResize: true
  // },

  // Global CSS
  css: [
    "video.js/dist/video-js.min.css",
    "~/assets/scss/style.scss",
  ],

  // Build configuration
  build: {
    postcss: {
      postcssOptions: {
        plugins: {
          'postcss-nested': {},
          'autoprefixer': {}
        }
      }
    },
    extend(config, { isDev, isClient }) {
      config.output.publicPath = process.env.ASSETS_PUBLIC_PATH || '/_web/public/';
      if (isDev && isClient) {
        config.performance.hints = false;
      }
    },
    publicPath: process.env.ASSETS_PUBLIC_PATH || '/_web/public/',
    transpile: ['@vuepic/vue-datepicker']
  },

  loaders: {
    scss: {
      implementation: require('sass'),
    }
  },

  // Router configuration
  router: {
    base: process.env.BASE_URL,
    middleware: ['auth'],
    scrollBehavior(to, from, savedPosition) {
      return savedPosition || { top: 0, left: 0 };
    },
  },

  // Auth configuration
  // auth: {
  //   redirect: {
  //     login: '/login',
  //     logout: '/login',
  //     callback: '/'
  //   },
  //   strategies: {
  //     auth0: {
  //       audience: 'https://api.innit.com/',
  //       domain: AUTH_DOMAIN,
  //       clientId: process.env.OAUTH_CLIENT_ID || 'hH9dfjtuJzVSWRnl3BwcHHfKAupQODTR',
  //       // scope: ["openid", "profile", "email", "innit_admin"],
  //       responseType: 'code',
  //       grantType: 'authorization_code',
  //       codeChallengeMethod: 'S256'
  //     }
  //   }
  // },

  // Environment variables

  // Server middleware
  serverMiddleware: [
    '~/api/config.js',
    '~/api/redirect-to-www.js',
    {
      path: '/ping',
      handler(req, res) {
        res.end("pong");
      }
    },
    {
      path: '/robots.txt',
      handler(req, res) {
        res.end(
          "User-agent: *\n" +
          (process.env.ENVIRONMENT === "prod" ? "Allow: /\n" : "Disallow: /\n") +
          "Sitemap: https://www.innit.com/sitemap.xml"
        );
      }
    }
  ],

  // Modules
  modules: [
    [
      '@pinia/nuxt',
      {
        autoImports: [
          // automatically imports `usePinia()`
          'defineStore',
          // automatically imports `usePinia()` as `usePiniaStore()`
          ['defineStore', 'definePiniaStore'],
        ],
      }
    ],
    '@vueuse/nuxt',
    'nuxt-svgo'
  ],

  pinia: {
    storesDirs: ["./stores/**"],
  },

  // Device settings
  device: {
    refreshOnResize: true
  },

  server: {
    port: 9008, // default: 3000
  },

  vite: {
    logLevel: false, // Suppresses warnings and only shows errors
    server: {
      watch: {
        usePolling: false, // This can help with file watching in some cases
      },
    },

    // Enable auto import of custom directives
    vue: {
      template: {
        compilerOptions: {
          directiveTransforms: {},
        },
      },
    },
    // More Vite configurations...
  },

  svgo: {
    global: false,
    autoImportPath: './assets/images/icons/'
  },

  experimental: {
    appManifest: false,
    emitRouteChunkError: 'automatic-immediate'
  },

  compatibilityDate: '2024-10-05',
});
