import { getItemFromLocalStorage } from "../services/local-storage-service.js";
import { STORAGE_KEY } from "../сonstants/storage-key.js";
import { ref } from 'vue';

// TMP solution!!!
export const CONFIG = {
  IS_PROD: false,
}

export const useConfig = () => {
  const runtimeConfig = useRuntimeConfig();

  const config = ref({
    BASE_PATH_WEB_BACKEND: '/api/',

    INNER_API_ENDPOINT_CONFIG_INIT: 'apiConfig',
    INNER_API_ENDPOINT_CONFIG_MENUS: 'menuConfig',

    DEFAULT_PROJECT_ID: 'default_project',

    HEADERS: {
      X_INNIT_PROJECT_ID: 'X-Innit-ProjectId',
    },

    GENERATOR: {
      TOKEN: runtimeConfig.public.ENVIRONMENT === "prod" ? "Token 583c4036-aa1c-4fbe-aafb-15a99309ec71" : "Token cba3d9a4-e443-4360-b884-c1d6b3632b53",
      PROJECT_ID: runtimeConfig.public.ENVIRONMENT === "prod" ? "c72f1c329609458bbc9db7624fe4008c" : "2ed7830b586b432f9bf515f19614178c",
    },
    /**
     * PROD_TOKEN: "e269f73972d73e9a4c06e89421c77b62"
     * DEV_TOKEN: "fe91e817197ac7c2b8eef7491801c64e"
     */
    MIXPANEL: {
      TOKEN: runtimeConfig.public.ENVIRONMENT === "prod" ? "e269f73972d73e9a4c06e89421c77b62" : "fe91e817197ac7c2b8eef7491801c64e",
      ENABLE: true,
    },

    /**
     * PROD_TOKEN: "waw6ab/innit-iq-prod"
     * DEV_TOKEN: "waw6ab/innit-iq"
     */
    LOGROCKET: {
      TOKEN: runtimeConfig.public.ENVIRONMENT === "prod" ? "waw6ab/innit-iq-prod" : "waw6ab/innit-iq",
      ENABLE: runtimeConfig.public.ENVIRONMENT === "prod",
    },

    IS_PROD: runtimeConfig.public.ENVIRONMENT === "prod" || !!getItemFromLocalStorage(STORAGE_KEY.DEBUG_IS_PROD),

    ENVIRONMENT: runtimeConfig.public.ENVIRONMENT,
    WEB_BACKEND_HOST: runtimeConfig.public.WEB_BACKEND_HOST,
    FRONT_END_HOST: runtimeConfig.public.FRONT_END_HOST,
    OAUTH_DOMAIN: runtimeConfig.public.OAUTH_DOMAIN,
    OAUTH_CLIENT_ID: runtimeConfig.public.OAUTH_CLIENT_ID,

    IS_SERVER: import.meta.server,
    IS_CLIENT: import.meta.client,
  });

  // TMP solution!!!
  CONFIG.IS_PROD = config.value.IS_PROD;

  return {
    config,
  };
}
