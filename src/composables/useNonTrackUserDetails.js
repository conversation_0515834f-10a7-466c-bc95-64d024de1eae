import { ref } from "vue";

export function useNonTrackUserDetails() {
  const tracker = ref(null);

  async function setUnknownTrackerIdentityAsync() {
    if (tracker.value) {
      tracker.value.identify(
        {
          id: "non trace",
          name: "GUEST",
        },
        {
          mixpanel: { enable: true },
          logrocket: { enable: true },
        }
      );
    }
  }

  function setTracker(trackerInstance) {
    tracker.value = trackerInstance;
  }

  return {
    setUnknownTrackerIdentityAsync,
    setTracker,
  };
}
