import { useNuxtApp } from "#app";
import { useConfigStore } from "../stores/config.js";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { prepareV2LogoutUrl } from "../utils/prepare-v2-logout-url.js";
import { useConfig } from "./useConfig.js";
import { getItemFromLocalStorage } from "../services/local-storage-service.js";
import { STORAGE_KEY } from "../сonstants/storage-key.js";

async function onRequest({ request, options }) {
  const { config } = useConfig();

  if (request?.includes(config.value.OAUTH_DOMAIN)) {
    return;
  }

  const { $store } = useNuxtApp();
  const { authorizationToken } = useInnitAuthStore();

  if (authorizationToken.value) {
    options.headers.set('Authorization', authorizationToken.value);
  }

  if (!options.headers.has(config.value.HEADERS.X_INNIT_PROJECT_ID)) {
    const cachedProject = getItemFromLocalStorage(STORAGE_KEY.USER_DATA_PROJECT);
    options.headers.set(
      config.value.HEADERS.X_INNIT_PROJECT_ID,
      $store.state?.userData?.project?.id || cachedProject?.id || config.value.DEFAULT_PROJECT_ID
    );
  }
};

// Handle the response errors
function onResponseError({ request, response, options }) {
  const code = parseInt(response?.status);
  if (code === 401) {
    useNuxtApp()?.$auth?.logout({
      openUrl(url) {
        const urlWithRedirect = prepareV2LogoutUrl(url);
        window.location.replace(urlWithRedirect);
      },
    })?.catch();
  }
};

function prepareUrl(
  url,
  clientKey = undefined,
  endpointKey = undefined,
  urlParam = "",
  urlParamReplaceKey = "",
) {
  const { getConfigEndpointHost } = useConfigStore();

  let fetchBaseURL = "";
  let fetchUrl = url;
  let param = "";

  if (clientKey) {
    const { endpoint, baseURL } = getConfigEndpointHost(clientKey, endpointKey);
    fetchBaseURL = baseURL || fetchBaseURL;
    fetchUrl = endpoint || fetchUrl;
  }

  if (urlParam) {
    if (urlParamReplaceKey) {
      fetchUrl = fetchUrl.replace(urlParamReplaceKey, urlParam);
    } else {
      param = `/${urlParam}`;
    }
  }

  return {
    fetchUrl: fetchUrl + param,
    fetchBaseURL,
  };
};

export const useSimpleCustomFetch = (
  url,
  options,
  clientKey = undefined,
  endpointKey = undefined,
  urlParam = "",
  urlParamReplaceKey = "",
) => {
  const { fetchUrl, fetchBaseURL } = prepareUrl(url, clientKey, endpointKey, urlParam, urlParamReplaceKey);
  return $fetch(fetchUrl, {
    baseURL: fetchBaseURL,
    ...options,
    onRequest,
    onResponseError,
  });
};

export const useCustomFetch = (
  url,
  options,
  clientKey = undefined,
  endpointKey = undefined,
  urlParam = "",
  urlParamReplaceKey = "",
) => {
  const { fetchUrl, fetchBaseURL } = prepareUrl(url, clientKey, endpointKey, urlParam, urlParamReplaceKey);
  return useFetch(fetchUrl, {
    baseURL: fetchBaseURL,
    ...options,
    $fetch: useNuxtApp().$customFetch,
    onRequest,
    onResponseError,
  })
};

export const useCustomLazyFetch = (
  url,
  options,
  clientKey = undefined,
  endpointKey = undefined,
  urlParam = "",
  urlParamReplaceKey = "",
) => {
  const { fetchUrl, fetchBaseURL } = prepareUrl(url, clientKey, endpointKey, urlParam, urlParamReplaceKey);
  return useLazyFetch(fetchUrl, {
    baseURL: fetchBaseURL,
    ...options,
    $fetch: useNuxtApp().$customFetch,
    onRequest,
    onResponseError,
  })
};
