import { ref, isRef, computed } from "vue";
import { useRouter } from "vue-router";
import { generateUUID } from "@/utils/generateUUID";
import { useRefUtils } from "@/composables/useRefUtils";
import { useNuxtApp } from "#app";
import { useTimeUtils } from "@/composables/useTimeUtils";
import { isValidImageUrl } from "@/utils/is-valid-image-url";

export const useCommonUtils = (selectedArticle, selectedCategoryData, selectedRecipe, contentCTAText) => {
  const disabledDates = ref([]);
  const disableList = ref([]);
  const { getRef } = useRefUtils();
  const router = useRouter();
  const { $eventBus } = useNuxtApp();
  const instance = getCurrentInstance();
  const { convertIso8601ToTimestamp } = useTimeUtils();

  const routeToPage = (page, query = null) => {
    router.push({
      path: `/${page}`,
      query: query ? { routeFrom: query } : {},
    });
  };

  const extractISIN = (url) => url.split('=')?.[1] ?? null;

  const updateShowAsterisk = computed(() => {
    return selectedArticle.value.length || selectedCategoryData.value.length || selectedRecipe.value.length;
  });

  const checkForTextPresence = computed(() => {
    return updateShowAsterisk.value && !contentCTAText.value.trim();
  });

  const updateSelectedItems = (targetArray, response) => {
    if (!Array.isArray(targetArray) && !isRef(targetArray)) {
      throw new Error("targetArray must be a reactive array or a plain array.");
    }

    const array = isRef(targetArray) ? targetArray.value : targetArray;

    if (response) {
      Array.isArray(response) ? array.push(...response) : array.push(response);
    }
  };

  const setOpacity = (refId, value) => {
    const element = getRef(refId);
    if (element) {
      element.style.opacity = value;
    }
  };

  const numericOnly = (number) => number.replace(/\D/g, "");

  if (!instance) {
    console.warn("useCommonUtils must be called inside a setup function of a component.");
    return {};
  }

  const triggerLoading = (message, value) => {
    $eventBus.emit(message, value);
    // Use the proxy to access $root
    instance.proxy.$root.$emit(message, value);
  };

  const isEmptyOrWhitespace = (str) => (str?.trim() ?? "") === "";

  const splitAndTrimUrls = (urlString) => {
    return urlString.includes(",")
      ? urlString
          .split(",")
          .map((url) => url.trim())
          .filter((url) => !!url)
      : [urlString.trim()];
  };

  const validateURL = (url) => {
    const urlPattern = /^(https?:\/\/)?([^\s$.?#].[^\s]*)$/;
    return !urlPattern.test(url);
  };

  const validateRecipeURL = (url) => {
    const pattern = /^(http:\/\/|https:\/\/).*\w+/;
    return pattern.test(url);
  };

  const formatSlug = (slug) => {
    if (!slug) return "";
    return slug.replace(/\d/g, "").toLowerCase().replace(/\s+/g, "-");
  };

  const formatAndTrimSlug = (slug, slugLength) => {
    if (!slug) return "";
    let trimmedString = slug.substring(0, slugLength).trim();
    return trimmedString.replace(/\s+/g, "-");
  };

  const generateIncrementedSlug = (baseSlug, count) => {
    const incrementedCount = count >= 2 ? count + 1 : -2;
    return `${baseSlug}${incrementedCount}`;
  };

  const extractNumericCount = (slug) => {
    const match = slug.match(/\d+/);
    return match ? parseInt(match[0]) : 0;
  };

  const parseInputString = (inputString) => {
    if (!inputString) return;
    return inputString.includes(",")
      ? inputString
          .split(",")
          .map((splitString) => splitString.trim())
          .filter(Boolean)
      : [inputString.trim()];
  };

  const checkDuplicate = () => {
    const sourceUrl = window.location.href;
    return sourceUrl.includes("create-duplicate");
  };

  const processScheduledElement = (element) => {
    const timestampInSeconds = getCurrentTimestampInSeconds();
    const timestamp = element.publishDate;
    if (timestampInSeconds < timestamp) {
      const date = new Date(timestamp * 1000);
      disabledDates.value.push(date);
    }
    disableList.value.push({
      date: element.publishDate || "",
      description: element.title || "",
      template: element.template || "",
      state: element.state || "",
    });
  };
  const formatToMidnightTimestamp = (date) => {
    let tempDate = new Date(date || new Date());
    tempDate.setHours(0, 0, 0, 0);
    return Math.floor(tempDate.getTime() / 1000);
  };

  const getCurrentTimestampInSeconds = () => {
    return formatToMidnightTimestamp();
  };

  const isScheduledWithPublishDate = (element) => {
    return element.state === "scheduled" && element.publishDate;
  };

  const extractTimeParts = (time) => {
    const iso8601DurationRegex = convertIso8601ToTimestamp();
    const matches = time.match(iso8601DurationRegex) || [];
    return {
      hour: matches[1] || "",
      minute: matches[2] || "",
      second: matches[3] || "",
    };
  };

  const splitLangAndCountry = (lang) => {
    const [language, country] = lang.split("-");
    return { language, country };
  };

  const sortAscendingAlphabetically = (array, property) => {
    return array.slice().sort((a, b) => a[property].localeCompare(b[property]));
  };

  const useCalendarMarkers = (disableList, mode = "default") => {
    const markers = computed(() => {
      return disableList.value.map((item) => {
        switch (mode) {
          case "rawTitle":
            return {
              date: item.date,
              type: "",
              tooltip: [{ text: item.title ?? "No title" }],
            };
          case "default":
          default:
            return {
              date: new Date(item.date * 1000),
              type: "",
              tooltip: [{ text: `${item.template ?? "Unknown"} hero is scheduled` }],
            };
        }
      });
    });

    return { markers };
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  };

  return {
    useCalendarMarkers,
    routeToPage,
    updateShowAsterisk,
    checkForTextPresence,
    scrollToTop,
    setOpacity,
    numericOnly,
    triggerLoading,
    isEmptyOrWhitespace,
    generateUUID,
    getDisabledDates: () => disabledDates,
    getDisableList: () => disableList,
    splitAndTrimUrls,
    validateURL,
    validateRecipeURL,
    formatSlug,
    formatAndTrimSlug,
    generateIncrementedSlug,
    extractNumericCount,
    parseInputString,
    checkDuplicate,
    getCurrentTimestampInSeconds,
    processScheduledElement,
    isScheduledWithPublishDate,
    isValidImageUrl: isValidImageUrl,
    extractTimeParts,
    splitLangAndCountry,
    sortAscendingAlphabetically,
    updateSelectedItems,
    extractISIN
  };
};
