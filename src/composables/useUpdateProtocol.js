import { onMounted, onBeforeUnmount } from 'vue';

export function useUpdateProtocol() {
  const updateProtocol = () => {
    if (window.location.protocol === 'http:' && !window.location.href.includes('localhost')) {
      window.location.protocol = 'https:';
    }
  };

  onMounted(() => {
    updateProtocol();
    window.addEventListener('popstate', updateProtocol);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('popstate', updateProtocol);
  });
}
