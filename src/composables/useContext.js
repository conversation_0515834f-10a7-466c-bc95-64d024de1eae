// composables/useContext.js

import { ref } from 'vue';
import { jwtCheckExpiration } from "@/utils/jwt-check-expiration";
import { PAGE_PERMISSIONS } from "@/сonstants/page-permissions";
import { PROJECT_PERMISSIONS } from "@/сonstants/project-permissions";
import { STORAGE_KEY } from "@/сonstants/storage-key";
import { useProjectLang } from './useProjectLang';
import { useNuxtApp } from '#app';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useAuth0 } from "@auth0/auth0-vue";
import { ACCESS_TOKEN_SILENTLY_OPTIONS } from "../сonstants/auth0-options.js";
import { useInnitAuth } from "./useInnitAuth.js";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useConfigStore } from "../stores/config.js";
import { prepareV2LogoutUrl } from "../utils/prepare-v2-logout-url.js";
import { setItemToLocalStorage, getItemFromLocalStorage, removeItemFromLocalStorage } from "../services/local-storage-service.js";

export function useContext() {
  const isInnitRequests = ref(false);
  const isTrackersIdentified = ref(false);

  const router = useRouter();
  const store = useStore();
  const configStore = useConfigStore();
  const {
    findProjectWithPermissionsAsync,
    projectPermissions,
  } = useProjectLang();
  const {
    $tracker,
  } = useNuxtApp();
  const auth0 = useAuth0();
  const { isInnitAdmin } = useInnitAuthStore();

  const trackerIdentify = (isForce = false) => {
    if (!auth0.isAuthenticated.value) {
      return;
    }

    if (isTrackersIdentified.value && !isForce) {
      return;
    }

    const user = auth0.user.value;
    $tracker.identify(
      {
        id: String(user.sub),
        name: user.nickname,
        email: user.email,
      },
      {
        mixpanel: { enable: true },
        logrocket: { enable: true },
      }
    );

    isTrackersIdentified.value = true
  };

  const getDataAsync = async () => {
    if (!auth0.isAuthenticated.value) {
      return;
    }

    try {
      await store.dispatch('userData/fetchProjectsAsync', { isAdmin: isInnitAdmin.value, isHotRefresh: false });
      getUserDataAndFeaturesAsync();
      trackerIdentify();
    } catch (error) {
      console.error('Error fetching projects or user data:', error);
    }
  };

  const getUserDataAndFeaturesAsync = () => {
    Promise.all([
      store.dispatch('userData/fetchLangsAsync', { isHotRefresh: true }),
      store.dispatch('userData/fetchUserPermissionsAsync', { isHotRefresh: true }),
      configStore.fetchFeaturesAsync({ isHotRefresh: true }),
    ]).then(() => {
      checkPagePermissionsAsync().catch();
    }).catch((error) => {
      findProjectWithPermissionsAsync(error).catch();
    });
    isInnitRequests.value = true;
  };

  const fetchUserPermissionsAsync = async () => {
    await store.dispatch('userData/fetchUserPermissionsAsync', { isHotRefresh: true });
  };

  const checkTokenExpirationAsync = async () => {
    if (document.hidden || !auth0.isAuthenticated.value) {
      return;
    }

    // const token = $auth.strategy?.token?.get();
    // if (!token || jwtCheckExpiration(token)) {
    //   // await $auth.logout();
    // }
  };

  const checkPagePermissionsAsync = async (path = "") => {
    const routePath = path || router.currentRoute.value.path;
    const pagePermissions = PAGE_PERMISSIONS[routePath];

    if (
      isInnitAdmin.value
      || routePath === "/access-denied"
      || routePath === "/"
      || !pagePermissions
      || projectPermissions.value === null
    ) {
      return;
    }

    if (projectPermissions.value.length === 1 && projectPermissions.value[0] === PROJECT_PERMISSIONS.DEFAULT_PERMISSION) {
      return navigateTo('/create-project');
    }

    const hasPermission = pagePermissions.every(permission => projectPermissions.value.includes(permission));
    if (!hasPermission) {
      setItemToLocalStorage(STORAGE_KEY.PREVIOUS_PAGE_BEFORE_ACCESS_DENIED, routePath);
      return navigateTo('/access-denied');
    }
  };

  const appLogout = async () => {
    $tracker.reset();
    await auth0.logout({
      openUrl(url) {
        const urlWithRedirect = prepareV2LogoutUrl(url);
        window.location.replace(urlWithRedirect);
      },
    });
  };

  const getPreviousPageBeforeAccessDenied = () => {
    return getItemFromLocalStorage(STORAGE_KEY.PREVIOUS_PAGE_BEFORE_ACCESS_DENIED);
  };

  const clearPreviousPageBeforeAccessDenied = () => {
    removeItemFromLocalStorage(STORAGE_KEY.PREVIOUS_PAGE_BEFORE_ACCESS_DENIED);
  };

  return {
    trackerIdentify,
    isInnitRequests,
    getDataAsync,
    getUserDataAndFeaturesAsync,
    checkTokenExpirationAsync,
    checkPagePermissionsAsync,
    fetchUserPermissionsAsync,
    appLogout,
    getPreviousPageBeforeAccessDenied,
    clearPreviousPageBeforeAccessDenied,
  };
}
