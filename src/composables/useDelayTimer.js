export function useDelayTimer() {
  const delay = (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
  };

  const batchDelay = (ms) => {
    let timeoutId;
    const promise = new Promise((resolve) => {
      timeoutId = setTimeout(() => {
        resolve();
      }, ms);
    });
    return {
      promise,
      timeoutId,
    };
  };

  return {
    delay,
    batchDelay,
  };
}
