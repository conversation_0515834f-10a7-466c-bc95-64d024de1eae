import { useNuxtApp } from "#app";
import { useCategoryStore } from "@/stores/category.js";
import { useUserDataStore } from "@/stores/userData.js";

export function useCategorySaveOperations(state, methods) {
  const { $keys } = useNuxtApp();
  const categoryStore = useCategoryStore();
  const userDataStore = useUserDataStore();

  const CATEGORY_STATUS = {
    ACTIVE: "active",
    HIDDEN: "hidden",
    PUBLISHED: "published",
  };

  const createCategoryWithVariants = async () => {
    const currentLang = userDataStore.getDefaultLang;
    let payload = {
      isin: null,
      name: state.categoriesName.value.trim(),
      slug: state.categoriesSlug.value,
      image: state.imageResponseUrl.value ? state.imageResponseUrl.value.replace(/\?.*/, "") : state.image.value,
      localized: state.recipeVariantList.value.reduce((acc, variant) => {
        if (variant?.name && variant?.lang !== currentLang) {
          acc[variant.lang] = { name: variant.name };
        }
        return acc;
      }, {})
    };

    if (payload.image?.[currentLang]) {
      payload.image = await setLanguageVariant(payload.image);
    }

    if (payload.slug?.[currentLang]) {
      payload.slug = await setLanguageVariant(payload.slug);
    }

    if (!payload.image) delete payload.image;
    if (!payload.slug) delete payload.slug;
    const variant = !!(state.finalAvailableLangs.value && state.finalAvailableLangs.value.length > 1);
    const response = await categoryStore.postCategoryAsync(payload, currentLang, variant);

    if (response?.isin && state.selectedCategoryRecipe.value.length) {
      state.categoryISIN.value = response.isin;
      await postCategoryRecipeAsync(state.selectedCategoryRecipe.value);

      if (state.categoryPromotedRecipes.value.length) {
        await savePromotedRecipeAsync();
      }
    } else if (response?.isin) {
      state.categoryISIN.value = response.isin;
    }

    if (state.isPublish.value && response?.isin) {
      await patchPublishCategoryStateAsync(response.isin);
      await patchPublishCategoryAsync(response.isin);
    }
  };

  const updateCategoryWithVariants = async () => {
    const currentLang = userDataStore.getDefaultLang;
    let payload = {
      name: state.categoriesName.value.trim(),
      slug: state.categoriesSlug.value?.trim() || "",
      image: state.imageResponseUrl.value ? state.imageResponseUrl.value.replace(/\?.*/, "") : state.image.value,
      localized: state.recipeVariantList.value.reduce((acc, variant) => {
        if (variant?.name && variant?.lang !== currentLang) {
          acc[variant.lang] = { name: variant.name };
        }
        return acc;
      }, {})
    };
    if (Object.keys(payload.localized).length === 0) {
      delete payload.localized;
    }
    
    if (!payload.slug) delete payload.slug;

    if (state.saveRemovedCategoryVariants.value?.length) {
      await deleteVariantAsync();
    }

    if (state.selectedCategoryRecipe.value?.length) {
      await postCategoryRecipeAsync(state.selectedCategoryRecipe.value);

      if (state.isAbortedCheckingOperationStatus.value) {
        throw new Error("Operation was aborted");
      }
    }

    await savePromotedRecipeAsync();

    if (state.recipeMatchesIsinsRemove.value.length) {
      await removeCategoryRecipeAsync();

      if (state.isAbortedCheckingOperationStatus.value) {
        throw new Error("Operation was aborted");
      }
    }
    await patchPublishCategoryAsync(payload, state.categoryISIN.value);
    await patchPublishCategoryStateAsync(state.categoryISIN.value);
  };

  const setLanguageVariant = (variantList) => {
    const copyObjectData = [];
    if (
      state.finalSelectedLanguage.value.length &&
      variantList?.[state.lang.value]
    ) {
      state.finalSelectedLanguage.value.forEach((item) => {
        copyObjectData.push({ [item]: variantList[state.lang.value] });
      });
    }

    return Object.assign({}, ...copyObjectData);
  };

  const postCategoryRecipeAsync = async (recipeIsins) => {
    try {
      const payload = {
        sourceId: $keys.KEY_NAMES.SOURCE_ID,
        data: {
          action: "add",
          isin: state.categoryISIN.value,
          targets: recipeIsins,
        },
      };

      const response = await categoryStore.postCategoryRecipeAsync(payload);

      if (response?.opId) {
        await methods.checkOperationStatusAsync(response.opId);
      }

      return response;
    } catch (error) {
      console.error(
        "[IQ][CategoryForm] Error in postCategoryRecipeAsync:",
        error
      );
      throw error;
    }
  };

  const savePromotedRecipeAsync = async () => {
    const promotedData = state.categoryPromotedRecipes.value
      ? state.categoryPromotedRecipes.value.map((recipe) => recipe.isin)
      : [];

    const payload = {
      isin: state.categoryISIN.value,
      targetIsin: state.categoryISIN.value,
      campaignType: "categoryRecipeSuggestion",
      promotedRecipeIsins: [...new Set(promotedData)],
      filteredRecipeIsins: state.filteredRecipeIsins.value || [],
      preview: false,
    };

    try {
      const currentLang = userDataStore.getDefaultLang;
      await categoryStore.saveRecipeCampaignDataAsync(payload, currentLang);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error saving promoted recipes:", error);
      throw error;
    }
  };

  const removeCategoryRecipeAsync = async () => {
    const payload = {
      sourceId: $keys.KEY_NAMES.SOURCE_ID,
      data: {
        action: "remove",
        isin: state.categoryISIN.value,
        targets: state.recipeMatchesIsinsRemove.value,
      },
    };

    try {
      const response = await categoryStore.postCategoryRecipeAsync(payload);
      const operationId = response.opId;
      await methods.checkOperationStatusAsync(operationId);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error removing category recipes:", error);
      throw error;
    }
  };

  const patchPublishCategoryAsync = async (payload,isin) => {
    if (isin) {
      const currentLang = userDataStore.getDefaultLang;
      const variant = !!(state.finalAvailableLangs.value && state.finalAvailableLangs.value.length > 1);
      try {
        await categoryStore.patchCategoryAsync(payload, isin, currentLang, variant);
      } catch (error) {
        console.error(
          "[IQ][CategoryForm] Error updating category status:",
          error
        );
        throw error;
      }
    }
  };
  const patchPublishCategoryStateAsync = async (isin) => {
    if (isin && state.categoriesState.value) {
      const payload = {
        status: state.categoriesState.value === CATEGORY_STATUS.PUBLISHED ? CATEGORY_STATUS.ACTIVE : CATEGORY_STATUS.HIDDEN,
      };
      try {
        await categoryStore.patchCategoryStateAsync(payload, isin);
      } catch (error) {
        console.error(
          "[IQ][CategoryForm] Error updating category status:",
          error
        );
        throw error;
      }
    }
  };

  const deleteVariantAsync = async () => {
    try {
      await categoryStore.deleteLanguageVariantAsync(state.categoryISIN.value, state.saveRemovedCategoryVariants.value);
    } catch (e) {
      console.error("[IQ][CategoryForm] Error deleting variants:", e);
    }
  };

  const removeAllCategoryAssociationsAsync = async (isin) => {
    try {
      const recipeAssociationPayload = {
        sourceId: $keys.KEY_NAMES.SOURCE_ID,
        data: {
          action: 'removeAll',
          entityType: 'recipe',
          isin: isin,
        },
      };

      const recipeResponse = await categoryStore.postCategoryRecipeAsync(recipeAssociationPayload);
      if (recipeResponse?.opId) {
        await waitForOperationCompletion(recipeResponse.opId);
      }
      const categoryGroupPayload = {
        sourceId: $keys.KEY_NAMES.SOURCE_ID,
        data: {
          action: 'removeAll',
          entityType: 'recipeCategoryGroup',
          isin: isin,
        },
      };

      const categoryGroupResponse = await categoryStore.postCategoryRecipeAsync(categoryGroupPayload);
      if (categoryGroupResponse?.opId) {
        await waitForOperationCompletion(categoryGroupResponse.opId);
      }
      const promises = [];

      state.recipeVariantList.value.forEach((variant) => {
        if (variant.lang !== state.lang.value) {
          promises.push(
            categoryStore.deleteLanguageVariantAsync(isin, [variant.lang])
          );
        }
      });

      await Promise.all(promises);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error removing category associations:", error);
      throw error;
    }
  };

  const waitForOperationCompletion = async (operationId) => {
    const maxAttempts = 30;
    let attempts = 0;
    const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];

    while (attempts < maxAttempts) {
      try {
        await categoryStore.getOperationStatusAsync(operationId);
        const status = categoryStore.getOperationStatus;

        if (states.includes(status?.state)) {
          if (status.state === $keys.KEY_NAMES.FAILED) {
            throw new Error(`Operation failed: ${status.message || 'Unknown error'}`);
          }
          return;
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
        attempts++;
      } catch (error) {
        console.error("[IQ][CategoryForm] Error checking operation status:", error);
        throw error;
      }
    }

    throw new Error("Operation timeout: Recipe association removal took too long");
  };

  return {
    createCategoryWithVariants,
    updateCategoryWithVariants,
    setLanguageVariant,
    postCategoryRecipeAsync,
    savePromotedRecipeAsync,
    removeCategoryRecipeAsync,
    patchPublishCategoryAsync,
    deleteVariantAsync,
    removeAllCategoryAssociationsAsync,
    waitForOperationCompletion,
  };
}
