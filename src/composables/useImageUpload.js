import { ref } from 'vue';
import axios from 'axios';

export const useImageUpload = (options = {}) => {
  const {
    entity,
    onImageUploaded,
    onUploadProgress,
    getPreSignedUrl,
    maxSize = 15 * 1024 * 1024, // 15MB
    warningSize = 1 * 1024 * 1024, // 1MB
  } = options;

  const uploadImagePercentage = ref(0);
  const loadedImageSize = ref(0);
  const imageResponseUrl = ref('');

  const uploadFile = (file) => {
    if (!file) {
      return;
    }

    const size = parseInt(file.size.toFixed(0));
    if (size > warningSize && size < maxSize) {
      return {
        shouldShowWarning: true,
        upload: () => uploadImageAsync(file)
      };
    } else if (size >= maxSize) {
      return {
        shouldShowError: true
      };
    }

    return {
      shouldUpload: true,
      upload: () => uploadImageAsync(file)
    };
  };

  const uploadImageAsync = async (file) => {
    const reader = new FileReader();
    reader.addEventListener("load", async () => {
      if (!reader.result) {
        return;
      }
      uploadImagePercentage.value = 1;
      loadedImageSize.value = 0;
      
      const params = {
        entity,
        content: "image",
        extension: file?.type.split("/")[1],
        public: true,
      };

      const response = await getPreSignedUrl(params);
      const imageUrl = response?.url || "";
      imageResponseUrl.value = imageUrl;
      
      await uploadImageFileAsync(imageUrl, file);
      if (onImageUploaded) {
        onImageUploaded(reader.result);
      }
    }, false);
    reader.readAsDataURL(file);
  };

  const uploadImageFileAsync = async (url, file) => {
    try {
      const uploadedImageFunction = (data) => {
        if (data === 100) {
          uploadImagePercentage.value = 99;
          setTimeout(() => uploadImagePercentage.value = 100, 2000);
        }
      };

      const cancelToken = axios?.CancelToken ? axios.CancelToken.source() : {};
      await axios.put(url, file, {
        headers: {
          "Content-Type": file.type,
          "x-amz-acl": "public-read",
        },
        cancelToken: cancelToken?.token,
        onUploadProgress: (progressEvent) => {
          const percentage = parseInt(Math.round((progressEvent.loaded / progressEvent.total) * 100));
          uploadImagePercentage.value = percentage;
          uploadedImageFunction(percentage);
          loadedImageSize.value = progressEvent.loaded;
          if (onUploadProgress) {
            onUploadProgress(percentage, progressEvent.loaded);
          }
        },
      });
    } catch (e) {
      console.error(`[IQ][${entity}] Cannot upload image.`, e);
    }
  };

  return {
    uploadFile,
    uploadImagePercentage,
    loadedImageSize,
    imageResponseUrl
  };
}; 