// composables/useEventUtils.js

export function useEventUtils() {
  const preventDefault = (event) => {
    event.preventDefault();
  };

  const isKeyInArray = (event, keys) => {
    return keys.includes(event.keyCode);
  };

  const isAlphaNumeric = (event) => {
    const keyCode = event.which || event.code;
    return (
      (keyCode >= 97 && keyCode <= 122) || // lowercase letters
      (keyCode >= 65 && keyCode <= 90) || // uppercase letters
      (keyCode >= 48 && keyCode <= 57) || // numbers
      keyCode === 32 // space
    );
  };

  const preventTabKeyPress = (event) => {
    if (isKeyInArray(event, [9])) {
      preventDefault(event);
    }
  };

  const preventInvalidNumberInput = (event) => {
    if (isKeyInArray(event, [45, 43])) {
      // '-' and '+'
      preventDefault(event);
    }
  };

  const preventEnterAndSpaceKeyPress = (event) => {
    if (isKeyInArray(event, [13, 32])) {
      // Enter and Space keys
      preventDefault(event);
    }
  };

  const preventSpecialCharacters = (event) => {
    if (isKeyInArray(event, [124, 126, 47])) {
      // '|', '~', '/'
      preventDefault(event);
    }
  };

  const restrictNumericInput = (event) => {
    if (
      !isKeyInArray(event, [46, 17]) &&
      !isKeyInArray(event, [48, 49, 50, 51, 52, 53, 54, 55, 56, 57])
    ) {
      preventDefault(event);
    }
  };

  const restrictToAlphabets = (event) => {
    if (!isAlphaNumeric(event)) {
      preventDefault(event);
    }
  };

  const restrictToAlphanumeric = (event) => {
    if (
      !isAlphaNumeric(event) &&
      !isKeyInArray(event, [45, 18, 43]) // '-', Ctrl, '+'
    ) {
      preventDefault(event);
    }
  };

  const preventNonNumericInput = (event) => {
    if (
      !isKeyInArray(event, [8, 44]) &&
      !isKeyInArray(event, [48, 49, 50, 51, 52, 53, 54, 55, 56, 57])
    ) {
      preventDefault(event);
    }
  };

  const restrictSpecialCharacters = (event) => {
    const keyCode = event.keyCode;
    const isNumberKey = keyCode >= 48 && keyCode <= 57; // '0' to '9'
    const isRestrictedKey = keyCode === 189 || keyCode === 43; // '-' or '+'
    if (isRestrictedKey || !isNumberKey) {
      preventDefault(event);
    }
  };

  const onEscapeKeyPress = (callback) => {
    const handleEscape = (event) => {
      if (event.key === "Escape") {
        callback();
      }
    };

    onMounted(() => {
      document.addEventListener("keyup", handleEscape);
    });

    onBeforeUnmount(() => {
      document.removeEventListener("keyup", handleEscape);
    });
  };

  return {
    preventDefault,
    isKeyInArray,
    isAlphaNumeric,
    preventTabKeyPress,
    preventInvalidNumberInput,
    preventEnterAndSpaceKeyPress,
    preventSpecialCharacters,
    restrictNumericInput,
    restrictToAlphabets,
    restrictToAlphanumeric,
    preventNonNumericInput,
    restrictSpecialCharacters,
    onEscapeKeyPress,
  };
}
