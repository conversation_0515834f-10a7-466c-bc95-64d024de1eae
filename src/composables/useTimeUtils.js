const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

export const useTimeUtils = () => {
  const formatDateToLocaleString = (date, options) => {
    return date.toLocaleString("en-US", options).replace(",", "");
  };

  const formatJsonTimestamp = (jsonTimestamp) => {
    const date = new Date(jsonTimestamp);
    const options = { month: "short", day: "numeric", year: "numeric" };
    return formatDateToLocaleString(date, options);
  };

  const convertToTimestamp = (dateString) => {
    const currentDateGMT = new Date(dateString);
    currentDateGMT.setUTCHours(8, 0, 0, 0);
    return Math.floor(currentDateGMT.getTime() / 1000);
  };

  const formatDateToReadableString = (dateString) => {
    const dateObject = new Date(dateString);
    const day = dateObject.getDate();
    const monthIndex = dateObject.getMonth();
    const year = dateObject.getFullYear();
    return `${monthNames[monthIndex]} ${day}, ${year}`;
  };

  const formatScheduleDateRange = (start, end) => {
    const formatTimestamp = (timestamp) => {
      const date = new Date(timestamp * 1000);
      if (isNaN(date)) return null;
      const month = monthNames[date.getMonth()];
      return `${month} ${date.getDate()}, ${date.getFullYear()}`;
    };
    const formattedStartDate = formatTimestamp(start);
    const formattedEndDate = formatTimestamp(end);
    if (!formattedStartDate || !formattedEndDate) {
      return "Invalid Date";
    }
    if (new Date(end * 1000) < new Date(start * 1000)) {
      return formattedStartDate;
    }
    return `${formattedStartDate} to ${formattedEndDate}`;
  };

  const convertTimeStamp = (jsonTimestamp) => {
    const timestamp = jsonTimestamp * 1000;
    const date = new Date(timestamp);
    const options = { month: "long", day: "numeric", year: "numeric" };
    return date.toLocaleDateString("en-US", options);
  };

  const formatDate = (inputDate) => {
    const dateParts = inputDate?.split("-");
    const year = dateParts[0];
    const month = monthNames[parseInt(dateParts[1]) - 1];
    const day = dateParts[2];
    return `${month} ${day}, ${year}`;
  };

  const convertTimestampToCustomFormat = (timestamp) => {
    const milliseconds = timestamp * 1000;
    const dateObject = new Date(milliseconds);
    const months = [
      "January", "February", "March", "April", "May", "June", "July",
      "August", "September", "October", "November", "December"
    ];
    const monthName = months[dateObject.getMonth()];
    const day = dateObject.getDate();
    const year = dateObject.getFullYear();
    const hours = dateObject.getHours().toString().padStart(2, "0");
    const minutes = dateObject.getMinutes().toString().padStart(2, "0");
    const seconds = dateObject.getSeconds().toString().padStart(2, "0");
    const utcOffsetHours = -Math.floor(dateObject.getTimezoneOffset() / 60);
    const utcOffsetMinutes = Math.abs(dateObject.getTimezoneOffset() % 60);
    const utcOffset =
      (utcOffsetHours >= 0 ? "+" : "-") +
      utcOffsetHours.toString().padStart(2, "0") +
      ":" +
      utcOffsetMinutes.toString().padStart(2, "0");
    return `${monthName} ${day}, ${year}, ${hours}:${minutes}:${seconds} (UTC${utcOffset})`;
  };

  const parseDurationString = (durationString) => {
    const parts = extractDurationParts(durationString);
    let totalSeconds = 0;
    parts.forEach((part) => {
      const value = parseInt(part, 10);
      if (part.includes("H")) {
        totalSeconds += value * 3600;
      } else if (part.includes("M")) {
        totalSeconds += value * 60;
      } else if (part.includes("S")) {
        totalSeconds += value;
      }
    });
    const hours = calculateTimeUnit(totalSeconds, 3600);
    const minutes = calculateTimeUnit(totalSeconds % 3600, 60);
    const seconds = totalSeconds % 60;
    let formattedTime = [];
    if (hours > 0) formattedTime.push(`${hours} hr`);
    if (minutes > 0) formattedTime.push(`${minutes} min`);
    if (seconds > 0) formattedTime.push(`${seconds} sec`);
    return formattedTime.join(" ").trim();
  };

  const extractDurationParts = (durationString) => {
    const regex = /\d+[HMS]/g;
    return durationString.match(regex) || [];
  };

  const calculateTimeUnit = (value, time) => {
    return Math.floor(value / time);
  };

  const formatTimestampToDate = (jsonTimestamp, locale = 'en-US') => {
    const milliseconds = jsonTimestamp * 1000;
    const date = new Date(milliseconds);
    const options = { month: "short", day: "numeric", year: "numeric" };
    const formattedDate = date.toLocaleString(locale, options);
    const [month, day, year] = formattedDate.split(" ");
    return `${month.charAt(0).toUpperCase()}${month.slice(1).toLowerCase()} ${day} ${year}`;
  };

  const convertIso8601ToTimestamp = () => {
    return /^PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?$/;
  };

  const getFormattedTimeFromTimestamp = (timestamp, $t) => {
    const currentDate = new Date();
    const inputDate = new Date(timestamp * 1000);
    const timeDifferenceInMs = currentDate - inputDate;
    const daysDifference = Math.floor(timeDifferenceInMs / (1000 * 60 * 60 * 24));

    if (daysDifference === 0) {
      return $t("COMMON.TODAY");
    } else if (daysDifference === 1) {
      return $t("COMMON.1_DAY_AGO");
    } else if (daysDifference < 30) {
      return `${daysDifference} ${$t("COMMON.DAYS_AGO")}`;
    } else if (daysDifference < 365) {
      const monthsDifference = Math.floor(daysDifference / 30);
      return `${monthsDifference} month${monthsDifference > 1 ? "s" : ""} ${$t("COMMON.AGO")}`;
    } else {
      const yearsDifference = Math.floor(daysDifference / 365);
      return `${yearsDifference} year${yearsDifference > 1 ? "s" : ""} ${$t("COMMON.AGO")}`;
    }
  };

  const formatDateObject = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const isDateAfterOrEqual = (date1, date2) => {
    const normalizedDate1 = new Date(date1);
    const normalizedDate2 = new Date(date2);
    normalizedDate1.setHours(0, 0, 0, 0);
    normalizedDate2.setHours(0, 0, 0, 0);
    return normalizedDate1 >= normalizedDate2;
  };

  const isDateDisabled = (date, disabledDates) => {
    const formattedDisabledDates = disabledDates.map(disable => formatDateObject(new Date(disable)));
    return formattedDisabledDates.includes(date.id);
  };

  const checkDate = (date) => isDateAfterOrEqual(date.date, new Date());

  const formatDateRangeOrSingle = (date) => {
    if (!date) return "";
    const formatSingleDate = (d) => {
      if (!(d instanceof Date)) return "";
      return `${monthNames[d.getMonth()]} ${d.getDate()}, ${d.getFullYear()}`;
    };
    if (Array.isArray(date) && date.length === 2) {
      const [start, end] = date;
      return `${formatSingleDate(start)} - ${formatSingleDate(end)}`;
    }
    if (date instanceof Date) {
      return formatSingleDate(date);
    }
    return "";
  };

  const getTimeStamp = (dateString) => {
    const dateObject = new Date(dateString);
    return Math.floor(dateObject.getTime() / 1000);
  };

  return {
    isDateDisabled,
    checkDate,
    formatDateToLocaleString,
    formatJsonTimestamp,
    convertToTimestamp,
    getTimeStamp,
    formatDateToReadableString,
    formatScheduleDateRange,
    convertTimeStamp,
    formatDate,
    convertTimestampToCustomFormat,
    parseDurationString,
    extractDurationParts,
    calculateTimeUnit,
    formatTimestampToDate,
    convertIso8601ToTimestamp,
    getFormattedTimeFromTimestamp,
    formatDateRangeOrSingle,
  };
};
