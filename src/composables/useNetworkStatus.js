import { ref, onMounted, onBeforeUnmount } from 'vue'

export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine);

  const updateNetworkStatus = () => {
    isOnline.value = navigator.onLine;
  };

  onMounted(() => {
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('online', updateNetworkStatus);
    window.removeEventListener('offline', updateNetworkStatus);
  });

  return { isOnline };
}
