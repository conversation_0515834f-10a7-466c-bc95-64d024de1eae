import { ACCESS_TOKEN_SILENTLY_OPTIONS } from "../сonstants/auth0-options.js";
import { useAuth0 } from "@auth0/auth0-vue";
import { useInnitAuthStore } from "../stores/innit-auth.js";

export const useInnitAuth = () => {
  const auth0 = useAuth0();
  const {
    setAuthorizationToken,
    setTokenData,
    setInnitAdmin,
  } = useInnitAuthStore();

  const makeAuthorizationTokenString = (token) => {
    return token ? `Bearer ${token}` : null
  };

  const getTokenDataAsync = async () => {
    try {
      const data = await auth0.getAccessTokenSilently(ACCESS_TOKEN_SILENTLY_OPTIONS);
      return data;
    } catch (error) {
      console.error("Cannot get token data!", error);
      return null;
    }
  };

  const getAuthorizationTokenAsync = async () => {
    try {
      const data = await getTokenDataAsync();
      return makeAuthorizationTokenString(data?.access_token);
    } catch (error) {
      console.error("Cannot parse Authorization token!", error);
      return null;
    }
  };

  const checkTokenSilentlyAsync = async () => {
    if (!auth0.isAuthenticated.value) {
      return;
    }

    try {
      const data = await getTokenDataAsync();

      if (data) {
        setTokenData(data);
        setInnitAdmin(data?.scope?.includes('innit_admin'));
        setAuthorizationToken(makeAuthorizationTokenString(data?.access_token));
      }
    } catch (error) {
      console.error("Cannot check Token data silently!", error);
    }
  };

  const checkIsInnitAdminAsync = async () => {
    const data = await getTokenDataAsync();
    return data?.scope?.includes('innit_admin');
  };

  return {
    checkTokenSilentlyAsync,
    getTokenDataAsync,
    getAuthorizationTokenAsync,
    checkIsInnitAdminAsync,
  };
}
