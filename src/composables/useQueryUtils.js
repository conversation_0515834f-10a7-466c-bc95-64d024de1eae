import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";

export const useQueryUtils = () => {
  const route = useRoute();

  const getPageQuery = (isOriginValue = false) => {
    const pageQuery = route.query[QUERY_PARAM_KEY.PAGE];

    if (isOriginValue) {
      return pageQuery;
    }

    return pageQuery > 1 ? pageQuery : undefined;
  };

  const getSearchQuery = () => {
    return route.query[QUERY_PARAM_KEY.SEARCH] || undefined;
  };

  return {
    getPageQuery,
    getSearchQuery,
  };
}
