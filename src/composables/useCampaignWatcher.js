import { watch } from "vue";
import { useCommonUtils } from "../composables/useCommonUtils";

export function useWatcherUtils() {
  const { triggerLoading } = useCommonUtils();
  const watchReactiveValue = (reactiveValue, eventName) => {
    if (!reactiveValue || !eventName) {
      console.warn("Invalid arguments passed to watchReactiveValue.");
      return;
    }

    watch(
      () => reactiveValue.value,
      (newValue) => {
        triggerLoading(eventName, newValue);
      }
    );
  };

  return {
    watchReactiveValue,
  };
}
