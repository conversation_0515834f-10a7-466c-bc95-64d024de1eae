import { getCurrentInstance } from 'vue';

export function useRefUtils() {
  const getRef = (refName) => {
    const instance = getCurrentInstance();

    if (instance?.proxy?.$refs?.[refName]) {
      return instance.proxy.$refs[refName];
    } else if (instance?.proxy?.$el?.querySelector(`#${refName}`)) {
      return instance.proxy.$el.querySelector(`#${refName}`);
    } else {
      return document?.querySelector(`#${refName}`);
    }
  };
  return {
    getRef,
  };
}
