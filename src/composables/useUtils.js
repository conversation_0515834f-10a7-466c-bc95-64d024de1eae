import { useConfig } from "./useConfig.js";

const readOnlyProviders = [];

export function useUtils() {
  const { config } = useConfig();

  const isReadOnlyProvider = (provider) => {
    return readOnlyProviders.includes(provider);
  };

  const printConsole = (item, ...theArgs) => {
    // Only log if not in production and on the client-side
    if (!config.value.IS_PROD && import.meta.env.SSR === false) {
      console.log(item, ...theArgs);
    }
  };

  return {
    isReadOnlyProvider,
    printConsole,
  };
}
