import GlobalModalWrapper from "../components/global-modal/GlobalModalWrapper.vue";

/**
 * Global state for modals
 * @type {ShallowReactive<Record<string, ModalConfig>>}
 */
const modalsState = shallowReactive({});

/**
 * Updates the visibility state of a modal
 * @param {string} name - Modal name
 * @param {boolean} isOpen - Modal visibility state
 */
const updateModalsVisibleState = (name, isOpen) => {
  if (modalsState[name]) {
    modalsState[name] = {
      ...modalsState[name],
      isOpen,
    };
  }
};

/**
 * @typedef {Object} ModalConfig
 * @property {object|Function} component - Vue component
 * @property {string} name - Unique modal name
 * @property {boolean} isOpen - Open state
 * @property {object} [props] - Props passed to the component
 * @property {string} [width]
 * @property {string} [maxWidth]
 * @property {string} [height]
 * @property {string} [maxHeight]
 * @property {string} [modalWrapperClass]
 * @property {boolean} [hideCloseBtn]
 * @property {boolean} [skipClickOutside]
 * @property {boolean} [skipEscapeClick]
 * @property {Function} [onCallback] - Callback when modal sends data
 * @property {Function} [onClose] - Callback when modal closes
 */

/**
 * Composable for managing modals
 * @param {Record<string, any>} options
 * @param {Boolean} cleanupOnUnmount
 * @returns {{
 *   modalsState: typeof modalsState,
 *   openModal: Function,
 *   closeModal: Function
 *   updateModalProps: Function
 *   cleanModalsState: Function
 * }}
 *
 * @example
 * const { openModal, closeModal } = useBaseModal({
 *   deleteModal: DeleteModalComponent
 * });
 *
 * @example
 * const { openModal, closeModal } = useBaseModal({
 *   deleteModal: {
 *     component: DeleteModalComponent,
 *     name: 'deleteModal',
 *     props: { id: 123 },
 *     onCallback: (data) => console.log(data),
 *   }
 * });
 */
export const useBaseModal = (options = {}, cleanupOnUnmount = false) => {
  const hasOptions = () => Object.keys(options).length > 0;
  const cleanModalsState = () => {
    for (const key in modalsState) {
      delete modalsState[key];
    }
  };

  const prepareModalInstances = () => {
    if (import.meta.server) {
      return;
    }

    if (!hasOptions()) {
      return;
    }

    for (const key in options) {
      const item = options[key];
      const component = item?.component || item;
      const isRawComponent = Boolean(component?.render || component?.setup || typeof component === "function");

      if (!isRawComponent) {
        console.warn(`[useBaseModal] Modal '${key}' is missing a component`);
        continue;
      }

      modalsState[key] = {
        component: markRaw(component),
        name: item?.name || key,
        isOpen: item?.isOpen || false,
        props: item?.props || {},
        width: item?.width,
        maxWidth: item?.maxWidth,
        height: item?.height,
        maxHeight: item?.maxHeight,
        modalWrapperClass: item?.modalWrapperClass,
        hideCloseBtn: item?.hideCloseBtn,
        skipClickOutside: item?.skipClickOutside,
        skipEscapeClick: item?.skipEscapeClick,
        onCallback: item?.onCallback,
        onClose: item?.onClose,
      };
    }
  };

  /**
   * Opens a modal
   * @param {Partial<ModalConfig> & { name: string }} config
   */
  const openModal = ({ name = "", isOpen = true, ...rest }) => {
    if (modalsState[name]) {
      modalsState[name] = {
        ...modalsState[name],
        isOpen,
        ...rest,
      };
    } else {
      console.warn(`[useBaseModal] Modal '${name}' is not registered`);
    }
  };

  /**
   * Updates the props of an existing modal in the modals state.
   *
   * @param {Partial<ModalConfig> & { name: string, props: object }} config
   *
   * @example
   * updateModalProps({
   *   name: "confirmModal",
   *   props: { title: "Are you sure?" }
   * });
   */
  const updateModalProps = ({ name = "", props = {}}) => {
    if (modalsState[name]) {
      modalsState[name] = {
        ...modalsState[name],
        props: {
          ...modalsState[name].props,
          ...props,
        }
      };
    } else {
      console.warn(`[useBaseModal] Cannot update props for unknown modal: '${name}'`);
    }
  };

  /**
   * Closes a modal by name
   * @param {string} name
   */
  const closeModal = (name) => {
    updateModalsVisibleState(name, false);
  };

  prepareModalInstances();

  onBeforeUnmount(() => {
    if (cleanupOnUnmount && hasOptions()) {
      cleanModalsState();
    }
  });

  return {
    modalsState,
    openModal,
    closeModal,
    updateModalProps,
    cleanModalsState,
  };
};

/**
 * Renders all active modals
 * @param {typeof modalsState} modals
 * @returns {VNode[]}
 */
export function renderModals(modals) {
  return Object.entries(modals).map(([key, modal]) => {
    if (!modal.isOpen) {
      return null;
    }

    const callback = (cb, data) => typeof cb === "function" && cb(data);

    const close = (name, cb, data) => {
      callback(cb, data);
      updateModalsVisibleState(name, false);
    };

    return h(
      GlobalModalWrapper,
      {
        isOpen: modal.isOpen,
        name: modal.name,
        width: modal.width,
        maxWidth: modal.maxWidth,
        height: modal.height,
        maxHeight: modal.maxHeight,
        modalWrapperClass: modal.modalWrapperClass,
        hideCloseBtn: modal.hideCloseBtn,
        skipClickOutside: modal.skipClickOutside,
        skipEscapeClick: modal.skipEscapeClick,
        onClose: (data) => close(modal.name, modal.onClose, data),
      },
      {
        default: () => {
          return h(
            modal.component,
            {
              ...modal.props,
              onCallback: (data) => callback(modal.onCallback, data),
              onClose: (data) => close(modal.name, modal.onClose, data),
            },
          );
        },
      }
    );
  });
}
