<template>
  <div class="nuxt-error-page">
    <div class="not-found-popup-container">
      <div class="not-found-container">
        <img alt="info" class="not-found-popup-image" src="@/assets/images/red-info.svg?skipsvgo=true" />
        <div class="not-found-main-container">
          <span class="not-found-popup-text">{{ message }}</span>
          <div class="not-found-text">
            <NuxtLink to="/overview">{{ $t("COMMON.NAVIGATE_HOME_MESSAGE") }}</NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NuxtError",
  components: {},
  props: {
    error: {
      type: Object,
      default: null,
    },
  },
  head() {
    return {
      title: this.message,
      meta: [
        {
          name: "viewport",
          content:
            "width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no",
        },
      ],
    };
  },
  computed: {
    statusCode() {
      return (this.error && this.error.statusCode) || 500;
    },
    message() {
      return this.error.message || `Error`;
    },
  },
};
</script>
