<template>
  <NuxtLoadingIndicator
    color="#4db935"
    errorColor="#ff0000"
    :height="5"
    :throttle="0"
  />

  <div>
    <div class="wrapper">
      <page-header />
      <sidebar />

      <slot />

    </div>
    <div class="footer">
      <page-footer />
    </div>

    <noscript class="disable-script-error-popup">
      JavaScript is disabled. Please enable JavaScript to use this website.
    </noscript>
  </div>

  <GlobalModal />
</template>

<script setup>
import PageHeader from "@/components/layout/PageHeader.vue";
import PageFooter from "@/components/layout/PageFooter.vue";
import Sidebar from "@/components/layout/sidebar";
import { useContext } from "../composables/useContext";
import { onBeforeUnmount, onMounted, watch } from "vue";
import { useProjectLang } from "../composables/useProjectLang.js";
import { useAuth0 } from "@auth0/auth0-vue";
import { useInnitAuth } from "../composables/useInnitAuth.js";
import { useRoute } from "vue-router";
import GlobalModal from "../components/global-modal/GlobalModal.vue";

const auth0 = useAuth0();
const auth = useInnitAuth();
const context = useContext();
const projectLang = useProjectLang();
const route = useRoute();

const fetchAllDataAsync = async () => {
  if (auth0.isLoading.value) {
    return;
  }

  if (!auth0.isAuthenticated.value) {
    await navigateTo('/login');
    return;
  }

  await auth.checkTokenSilentlyAsync();

  context.trackerIdentify(true);

  projectLang.readyProject(async ({ isProjectReady }) => {
    if (route?.path === "/") {
      const path = !isProjectReady ? "/create-project" : "/overview";
      await navigateTo(path);
    }
  });

  await context.getDataAsync();
};

watch(() => auth0.isLoading.value, async () => await fetchAllDataAsync());

onMounted(async () => {
  await fetchAllDataAsync();
  document.addEventListener("visibilitychange", context.checkTokenExpirationAsync);
});

onBeforeUnmount(() => {
  document.removeEventListener("visibilitychange", context.checkTokenExpirationAsync);
});
</script>
