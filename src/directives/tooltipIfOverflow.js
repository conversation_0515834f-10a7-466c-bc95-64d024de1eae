export default {
  mounted(el, binding) {
    const checkOverflow = () => {
      const textEl = el.querySelector('.simple-data-tooltip-text') || el
      const isOverflowing =
        textEl.scrollWidth > textEl.clientWidth ||
        textEl.scrollHeight > textEl.clientHeight

      if (isOverflowing) {
        el.setAttribute('data-tooltip-text', binding.value)
        el.classList.add('simple-data-tooltip')
      } else {
        el.removeAttribute('data-tooltip-text')
        el.classList.remove('simple-data-tooltip')
      }
    }

    // Delay to ensure DOM is painted
    requestAnimationFrame(checkOverflow)

    // Optional: recheck on resize
    const observer = new ResizeObserver(checkOverflow)
    observer.observe(el)

    el._overflowObserver = observer // store it for unmounting
  },
  unmounted(el) {
    if (el._overflowObserver) {
      el._overflowObserver.disconnect()
    }
  },
}
