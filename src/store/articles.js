import { KEYS } from "@/сonstants/keys.js";

const state = () => ({
  editArticleData: [],
  articleUUID: [],
  articlesData: [],
  articleCategoriesData: [],
  postArticles: [],
  postArticleCategories: [],
  patchArticleCategories: [],
  deleteArticleCategories: [],
  patchArticlesData: [],
  deleteArticleMaster: [],
  patchCategoryOrders: [],
  patchArticleOrders: [],
  uploadZipFiles: [],
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_EDIT_ARTICLE_DATA: createMutation("editArticleData"),
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getArticleData: createGetter("articleData"),
  getEditArticleData: createGetter("editArticleData"),
};

const handleError = (method, error) => {
  console.error(`${KEYS.KEY_NAMES.ERROR_IN} ${method}:`, error);
};

const logMissingParameters = () => {
  console.error(`${KEYS.KEY_NAMES.MISSING_REQUIRED_PARAMETERS}`);
};

const actions = {
  async getEditArticlesDataAsync({ commit, rootGetters }, { lang, uuid }) {
    if (!lang || !uuid) {
      logMissingParameters();
      return;
    }

    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "articleData"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang };
      endpoint = `${endpoint}/${uuid}`;
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      commit("SET_EDIT_ARTICLE_DATA", response.data);
    } catch (error) {
      handleError("getEditArticlesDataAsync", error);
    }
  },
  async getArticleDataAsync({ rootGetters }, { lang }) {
    if (!lang) {
      logMissingParameters();
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "articleData"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang };
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      return response.data;
    } catch (error) {
      handleError("getArticleDataAsync", error);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
