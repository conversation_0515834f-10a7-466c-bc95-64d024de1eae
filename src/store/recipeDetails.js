/**
 * @module store/recipeDetails
 */
import { KEYS } from '@/сonstants/keys';

import { getRecipeImageObject } from "@/models/recipe-generator.model";
import { useNuxtApp } from 'nuxt/app';
const state = () => ({
  recipeGeneratedImageList: [],
  selectedImageCount: 0,
  recipeSimplifyData: {},
  recipeGeneratedInnitImageList: {},
  recipeAllergensList: [],
  nutritionalDataList: [],
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_RECIPE_GENERATED_IMAGE_LIST: createMutation("recipeGeneratedImageList"),
  SET_SELECTED_IMAGE_COUNT: createMutation("selectedImageCount"),
  SET_RECIPE_SIMPLE_DATA: createMutation("recipeSimplifyData"),
  SET_UPLOADED_IMAGE_LIST: createMutation("recipeGeneratedInnitImageList"),
  SET_RECIPE_ALLERGENS_LIST: createMutation("recipeAllergensList"),
  SET_NUTRITIONAL_DATA: createMutation("nutritionalDataList"),

  TOGGLE_SELECTED_RECIPE_IMAGE(state, key) {
    const image = state.recipeGeneratedImageList.find((img) => img.key === key);
    if (image) {
      image.isSelected = !image.isSelected;

      if (image.isSelected) {
        state.selectedImageCount += 1;
      } else {
        state.selectedImageCount -= 1;
      }
    }
  },
};

const actions = {
  setImageList({ commit, state }, { imageList }) {
    if (!imageList?.length) {
      return;
    }

    const newImageList = imageList.map(getRecipeImageObject);
    const oldImageList = state.recipeGeneratedImageList || [];

    const combinedImageList = newImageList.concat(
      oldImageList.filter(
        (oldImage) =>
          !newImageList.some((newImage) => newImage.key === oldImage.key)
      )
    );

    commit("SET_RECIPE_GENERATED_IMAGE_LIST", combinedImageList);
  },
  selectGeneratedRecipeImage({ commit }, { key }) {
    commit("TOGGLE_SELECTED_RECIPE_IMAGE", key);
  },
  resetImageList({ commit }) {
    commit("SET_RECIPE_GENERATED_IMAGE_LIST", []);
    commit("SET_SELECTED_IMAGE_COUNT", 0);
  },
  async getRecipeSimpleDataAsync({ commit, rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }

    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "simplifyRecipe"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL
      });
      if (response) {
        commit("SET_RECIPE_SIMPLE_DATA", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getRecipeSimpleDataAsync", error);
    }
  },
  async batchUploadImagesAsync({ commit, rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "uploadImagesFromUrls"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL
      });
      if (response) {
        commit("SET_UPLOADED_IMAGE_LIST", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "batchUploadImagesAsync", error);
    }
  },
  async deleteLanguageVariantAsync({ rootGetters }, { lang, isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters['config/getClientEndpoint']("flite", "deleteRecipe");
      endpoint = endpoint.replace("{isin}", isin);
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.delete(endpoint, {
        baseURL,
        params: { langs: lang }
      });
      return response.data;
    } catch (error) {
      console.error(this.$keys.KEY_NAMES.ERROR_IN + "deleteLanguageVariantAsync", error);
    }
  },
  async getRecipeAllergensAsync({ rootGetters, commit }, { query, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getRecipeAllergens"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { q: query, lang }
      });
      if (response) {
        commit("SET_RECIPE_ALLERGENS_LIST", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getRecipeAllergensAsync", error);
    }
  },
  async getNutritionalDataAsync({ commit, rootGetters }) {
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getNutritionalData"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL
      });
      if (response) {
        commit("SET_NUTRITIONAL_DATA", response?.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getNutritionalDataAsync", error);
    }
  },
  async checkSlugExistAsync({ rootGetters }, { slug }) {
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getRecipe"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { slug }
      });
      return response?.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "checkSlugExistAsync", error);
    }
  },

  async saveRecipeAsync({ rootGetters }, { payload, txnId }) {
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "saveRecipe"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { txnId }
      });
      return response?.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "saveRecipeAsync", error);
    }
  },
  async patchScheduleRecipes({ rootGetters }, { payload, lang }) {
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getRecipeSchedule"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.patch(endpoint, payload, {
        baseURL,
        params: { lang }
      });
      return response?.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "patchScheduleSelectedRecipesAsync", error);
    }
  },
  async postScheduleRecipes({ rootGetters }, { payload, lang }) {
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getRecipeSchedule"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { lang }
      });
      return response?.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "postScheduleSelectedRecipesAsync", error);
    }
  },
  async calculateNutritionalDVPAsync({ rootGetters }, { payload, lang }) {
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "calculateNutritionalDVP"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { lang }
      });
      return response?.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "calculateNutritionalDVPAsync", error);
    }
  }

};

const createGetter = (property) => (state) => state[property];

const getters = {
  getGeneratedRecipeImage: createGetter("recipeGeneratedImageList"),
  getSelectedRecipeImage: (state) =>
    state.recipeGeneratedImageList.filter((img) => img.isSelected),
  getSelectedImageCount: createGetter("selectedImageCount"),
  getRecipeSimplifyData: createGetter("recipeSimplifyData"),
  getBatchUploadImagesList: createGetter("recipeGeneratedInnitImageList"),
  getRecipeAllergens: createGetter("recipeAllergensList"),
  getNutritionalData: createGetter("nutritionalDataList"),
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
