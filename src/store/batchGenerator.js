import { KEYS } from '@/сonstants/keys';

const state = () => ({
  batchPromptsList: [],
  batchRecipesList: {},
  isBatchGenerationInProgress: false,
  batchPromptKey: "",
  hiddenBatchList:{},
});

const getters = {
  getBatchPromptsList: (state) => state.batchPromptsList,
  getBatchRecipeList: (state) => state.batchRecipesList,
  getBatchGenerationInProgress: (state) => state.isBatchGenerationInProgress,
  getBatchPromptKey: (state) => state.batchPromptKey,
  getHiddenBatchList: (state) => state.hiddenBatchList
};

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_BATCH_PROMPTS_LIST: createMutation("batchPromptsList"),
  SET_BATCH_RECIPES_LIST: createMutation("batchRecipesList"),
  SET_BATCH_GENERATION_IN_PROGRESS: createMutation(
    "isBatchGenerationInProgress"
  ),
  SET_BATCH_PROMPT_KEY: createMutation("batchPromptKey"),
  SET_HIDDEN_BATCH_LIST: createMutation("hiddenBatchList")
};

const actions = {
  async setBatchGenerationInProgressAsync({ commit }, value) {
    commit("SET_BATCH_GENERATION_IN_PROGRESS", value);
  },
  async getBatchPromptsAsync({ rootGetters, commit }, payload) {
    if (!payload) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_MISSING_BATCH_PROMPT_PARAMETERS
      );
      return;
    }
    const endpoint = rootGetters["config/getClientEndpoint"](
      "flite",
      "getBatchPromptList"
    );
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    try {
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params: payload,
      });
      if (response?.data?.results) {
        commit("SET_BATCH_PROMPTS_LIST", response.data.results);
        return response.data.results;
      } else {
        console.error(
          KEYS.BATCH_GENERATOR_KEYS.ERROR_UNEXPECTED_RESPONSE_FORMAT
        );
        return [];
      }
    } catch (error) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_GET_BATCH_PROMPTS,
        error
      );
    }
  },
  async getBatchRecipesAsync({ rootGetters, commit }, { lang, key }) {
    const missingParams = [];
    if (!key) {
      missingParams.push("key");
    }
    if (!lang) {
      missingParams.push("lang");
    }
    if (missingParams.length) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_MISSING_PARAMETERS.replace(
          "{missingParams}",
          missingParams.join(", ")
        )
      );
      return;
    }
    const endpoint =
      rootGetters["config/getClientEndpoint"]("flite", "getBatchPromptList") +
      `/${key}`;
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    try {
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { langs: lang, checkRecipesExist: true },
      });
      if (response) {
        commit("SET_BATCH_RECIPES_LIST", response?.data);
      }
    } catch (error) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_IN_GET_BATCH_RECIPES_ASYNC,
        error
      );
    }
  },
  async postBatchPromptsAsync({ rootGetters, commit }, { params, payload }) {
    if (!payload?.prompts?.length) {
      console.error(KEYS.BATCH_GENERATOR_KEYS.MISSING_OR_EMPTY_PROMPTS);
      return;
    }
    if (!params?.lang) {
      console.error(KEYS.BATCH_GENERATOR_KEYS.MISSING_LANG_PARAMS);
      return;
    }
    const endpoint = rootGetters["config/getClientEndpoint"](
      "flite",
      "getBatchPromptList"
    );
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    try {
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: params,
      });
      commit("SET_BATCH_PROMPT_KEY", response?.data?.key);
    } catch (error) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_IN_POST_BATCH_ASYNC,
        error
      );
    }
  },
  async postHiddenItemsAsync({ rootGetters }, { payload }) {
    if (!payload?.type || !payload?.itemId) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_MISSING_BATCH_PROMPT_PARAMETERS
      );
      return;
    }
    const endpoint = rootGetters["config/getClientEndpoint"](
      "flite",
      "hiddenItems"
    );
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    const { $axios } = useNuxtApp();
    try {
      await $axios.post(endpoint, payload, {
        baseURL,
      });
    } catch (error) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_IN_POST_HIDDEN_BATCH_ASYNC,
        error
      );
    }
  },
  async getHiddenItemsAsync({ rootGetters, commit }, { payload }) {
    if (!payload?.type || !payload?.itemId) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_MISSING_BATCH_PROMPT_PARAMETERS
      );
      return;
    }
    const endpoint = rootGetters["config/getClientEndpoint"](
      "flite",
      "hiddenItems"
    );
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    const { $axios } = useNuxtApp();
    try {
      const response = await $axios.get(endpoint, {
        baseURL,
        params: payload,
      });
      if (response) {
        commit("SET_HIDDEN_BATCH_LIST", response);
      }
    } catch (error) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_GET_HIDDEN_BATCH,
        error
      );
    }
  },
  async deleteHiddenItemsAsync({ rootGetters }, { uuid }) {
    if (!uuid) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_MISSING_BATCH_PROMPT_PARAMETERS
      );
      return;
    }
    const endpoint = `${rootGetters["config/getClientEndpoint"]("flite","hiddenItems")}/${uuid}`;
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    const { $axios } = useNuxtApp();
    try {
       await $axios.delete(endpoint, { baseURL } );
    } catch (error) {
      console.error(
        KEYS.BATCH_GENERATOR_KEYS.ERROR_DELETE_BATCH_HIDDEN_ITEMS,
        error
      );
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
