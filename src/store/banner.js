import { KEYS } from "@/сonstants/keys.js";
const state = () => ({
  bannerData: [],
  editBannerData: {},
});

const createMutation = (property) => (state, value) => {
  state[property] = value;
};

const mutations = {
  SET_BANNER_DATA: createMutation("bannerData"),
  SET_EDIT_BANNER_DATA: createMutation("editBannerData"),
  CLEAR_BANNER_DATA: (state) => {
    state.bannerData = [];
  },
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getBannerListData: createGetter("bannerData"),
  getEditBannerData: createGetter("editBannerData"),
};

const createEndpoint = (rootGetters, endpointKey, uuid = '') => {
  let endpoint = rootGetters["config/getClientEndpoint"]("flite", endpointKey);
  if (uuid) {
    endpoint = `${endpoint}/${uuid}`;
  }
  return endpoint;
};

const logMissingParameters = () => {
  console.error(`${KEYS.KEY_NAMES.MISSING_REQUIRED_PARAMETERS}`);
};

const handleError = (method, error) => {
  console.error(`${KEYS.KEY_NAMES.ERROR_IN} ${method}:`, error);
};

const actions = {
  /**
   * Retrieves banner data from the server based on the language parameter.
   *
   * Constructs the endpoint URL and headers for the GET request.
   * Makes a GET request to fetch banner data.
   *
   * @param {Object} context - Vuex context object.
   * @param {Object} params - Contains the `lang` parameter.
   * @returns {Promise<void>} Resolves when the data is committed to the store.
   */
  async getBannerListDataAsync({ commit, rootGetters }, { lang }) {
    if (!lang) {
      logMissingParameters();
    }
    try {
      const { $axios } = useNuxtApp();
      const endpoint = createEndpoint(rootGetters, "getBannerList");
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, { baseURL, params: { lang } });
      commit("SET_BANNER_DATA", response.data);
    } catch (error) {
      handleError("getBannerListDataAsync", error);
    }
  },
  /**
   * Updates an existing banner data on the server.
   *
   * Constructs the endpoint URL for the PATCH request using the UUID.
   * Makes a PATCH request to update the banner data.
   *
   * @param {Object} context - Vuex context object.
   * @param {Object} params - Contains `payload` and `uuid`.
   * @returns {Promise<void>} Resolves when the data is committed to the store.
   */

  async patchBannerDataAsync({ commit, rootGetters }, { payload, uuid }) {
    if (!payload || !uuid) {
      logMissingParameters();
    } 
    try {
      const { $axios } = useNuxtApp();
      const endpoint = createEndpoint(rootGetters, "getBannerList", uuid);
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.patch(endpoint, payload, { baseURL });
      commit("SET_BANNER_DATA", response.data);
    } catch (error) {
      handleError("patchBannerDataAsync", error);
    }
  },
   /**
   * Retrieves specific banner data for editing from the server.
   *
   * Constructs the endpoint URL and headers for the GET request using the UUID.
   * Makes a GET request to fetch banner data.
   *
   * @param {Object} context - Vuex context object.
   * @param {Object} params - Contains `lang`, and `uuid`.
   * @returns {Promise<void>} Resolves when the data is committed to the store.
   */

  async getEditBannerDataAsync({ commit, rootGetters }, { lang, uuid }) {
    if (!lang || !uuid) {
      logMissingParameters();
    } 
    try {
      const { $axios } = useNuxtApp();
      const endpoint = createEndpoint(rootGetters, "getBannerList", uuid);
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, { baseURL, params: { lang } });
      commit("SET_EDIT_BANNER_DATA", response.data);
    } catch (error) {
      handleError("getEditBannerDataAsync", error);
    }
  },
    /**
   * Deletes a specific banner from the server based on its UUID.
   *
   * Constructs the endpoint URL for the DELETE request using the UUID.
   * Makes a DELETE request to remove the banner.
   *
   * @param {Object} context - Vuex context object.
   * @param {Object} params - Contains `uuid`.
   * @returns {Promise<void>} Resolves when the banner data is cleared from the store.
   */

  async deleteBannerDataAsync({ commit, rootGetters }, { uuid }) {
    if (!uuid) {
      logMissingParameters();
    } 
    try {
      const { $axios } = useNuxtApp();
      const endpoint = createEndpoint(rootGetters, "getBannerList", uuid);
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      await $axios.delete(endpoint, { baseURL });
      commit("CLEAR_BANNER_DATA");
    } catch (error) {
      handleError("deleteBannerDataAsync", error);
    }
  },

  /**
   * Posts new banner data to the server.
   *
   * Constructs the endpoint URL and headers for the POST request.
   * Makes a POST request to submit the banner data.
   *
   * @param {Object} context - Vuex context object.
   * @param {Object} params - Contains `payload`.
   * @returns {Promise<void>} Resolves when the data is committed to the store.
   */
  async postBannerDataAsync({ commit, rootGetters }, { payload }) {
    if (!payload) {
      logMissingParameters();
    }
    try {
      const { $axios } = useNuxtApp();
      const endpoint = createEndpoint(rootGetters, "getBannerList");
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, payload, { baseURL });
      commit("SET_BANNER_DATA", response.data);
    } catch (error) {
      handleError("postBannerDataAsync", error);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
