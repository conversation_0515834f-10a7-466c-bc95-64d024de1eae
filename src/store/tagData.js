import { getEndpointFromStore } from "@/utils/get-endpoint-from-store";

const state = () => ({
  tags: [],

  tagListForTable: [],
  pagination: {
    from: 0,
    size: 15,
    total: 0,
  },
});

const mutations = {
  SET_TAGS(state, payload) {
    state.tags = payload;
  },
  SET_PAGINATION_TOTAL(state, payload) {
    state.pagination.total = payload.total;
  },
  SET_TAG_LIST_FOR_TABLE(state, payload) {
    state.tagListForTable = payload;
  },
};

const actions = {
  async getTagMasterDataAsync(
    { commit, rootGetters },
    { categoryAlert, recipeTotal, query, from, size, lang, state }
  ) {
    try {
      const { $axios } = useNuxtApp();
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "getTagMasterData");
      const params = {
        lang: lang,
        includeAlerts: categoryAlert,
        includeTotals: recipeTotal,
        q: query,
        from: from,
        size: size,
        sort: "lastMod",
        state: state,
      };
      const response = await $axios.get(endpoint, {
        baseURL,
        params: params,
      });

      if (response) {
        commit("SET_TAGS", response);
      }

      return response;
    } catch (error) {
      console.error(error);
    }
  },

  /**
   * @deprecated
   *
   * @param commit
   * @param rootGetters
   * @param params
   * @returns {Promise<void>}
   */
  async getTagListAsync({ commit, rootGetters }, { params }) {
    if (!params) {
      return;
    }

    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "getTagMasterData");
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      const { results, total } = response?.data ?? {};
      commit("SET_TAG_LIST_FOR_TABLE", results);
      commit("SET_PAGINATION_TOTAL", { total });
    } catch (error) {
      console.error(error);
    }
  },
  resetTagList({ commit }) {
    commit("SET_TAG_LIST_FOR_TABLE", []);
    commit("SET_PAGINATION_TOTAL", { total: 0 });
  },
};

const getters = {
  getTags: (state) => state.tags,
  getPagination: (state) => state.pagination,
  getTagListForTable: (state) => state.tagListForTable,
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
};
