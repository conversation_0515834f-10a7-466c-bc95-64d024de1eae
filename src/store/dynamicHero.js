import { KEYS } from "@/сonstants/keys.js";

const state = () => ({
  dynamicHeroData: [],
  editDynamicHeroData: [],
  uuidData: [],
  postDynamicHeroData: [],
  deleteDynamicHeroData: [],
  patchDynamicHeroData: [],
  postDynamicForm: []
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_DYNAMIC_HERO_DATA: createMutation("dynamicHeroData"),
  SET_EDIT_DYNAMIC_HERO_DATA: createMutation("editDynamicHeroData"),  
  SET_UUID_DATA: createMutation("uuidData"),
  SET_POST_DYNAMIC_HERO_DATA: createMutation("postDynamicHeroData"),
  SET_DELETE_DYNAMIC_HERO_DATA: createMutation("deleteDynamicHeroData"),
  SET_PATCH_DYNAMIC_HERO_DATA: createMutation("patchDynamicHeroData"),
  SET_POST_DYNAMIC_FORM: createMutation("postDynamicForm")   
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getDynamicHeroData: createGetter("dynamicHeroData"),
  getEditDynamicHeroData: createGetter("editDynamicHeroData"),
  getUUIDData: createGetter("uuidData"),
  getArticleData: createGetter("articleData"),
  getPostDynamicHeroData: createGetter("postDynamicHeroData"),
  getDeleteDynamicHeroData: createGetter("deleteDynamicHeroData"),
  getPatchDynamicHeroData: createGetter("patchDynamicHeroData"),
  getPostDynamicForm: createGetter("postDynamicForm")
};

const handleError = (method, error) => {
  console.error(`${KEYS.KEY_NAMES.ERROR_IN} ${method}:`, error);
};

const logMissingParameters = () => {
  console.error(`${KEYS.KEY_NAMES.MISSING_REQUIRED_PARAMETERS}`);
};

const actions = {
  async getDynamicHeroDataAsync({ commit, rootGetters }, { lang }) {
    if (!lang) {
      logMissingParameters();
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getDynamicHeroList"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang };
      const response = await this.$axios.get(endpoint, {
        baseURL,
        params,
      });
      commit("SET_DYNAMIC_HERO_DATA", response.data);
    } catch (error) {
      handleError("getDynamicHeroDataAsync", error);
    }
  },
  async postDynamicHeroDataAsync({ commit, rootGetters }, { payload }) {
    if (!payload) {
      logMissingParameters();
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getDynamicHeroList"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;

      const response = await this.$axios.post(endpoint, payload, {
        baseURL,
      });
      commit("SET_POST_DYNAMIC_HERO_DATA", response.data);
    } catch (error) {
      handleError("postDynamicHeroDataAsync", error);
    }
  },
  async getEditDynamicHeroDataAsync({ commit, rootGetters }, { lang, uuid }) {
    if (!lang || !uuid) {
      logMissingParameters();
      return;
    }

    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getDynamicHeroList"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang };
      endpoint = `${endpoint}/${uuid}`;

      const response = await this.$axios.get(endpoint, {
        baseURL,
        params,
      });
      commit("SET_EDIT_DYNAMIC_HERO_DATA", response.data);
    } catch (error) {
      handleError("getEditDynamicHeroDataAsync", error);
    }
  },
  async deleteDynamicHeroDataAsync({ rootGetters }, { uuid }) {
    if (!uuid) {
      logMissingParameters();
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getDynamicHeroList"
      );
      endpoint = `${endpoint}/${uuid}`;
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      await this.$axios.delete(endpoint, {
        baseURL,
      });
      commit("SET_DELETE_DYNAMIC_HERO_DATA");
    } catch (error) {
      handleError("deleteDynamicHeroDataAsync", error);
    }
  },
  async patchDynamicHeroDataAsync({ commit, rootGetters }, { payload, uuid }) {
    if (!payload || !uuid) {
      logMissingParameters();
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getDynamicHeroList"
      );
      endpoint = `${endpoint}/${uuid}`;
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;

      const response = await this.$axios.patch(endpoint, payload, {
        baseURL,
      });
      commit("SET_PATCH_DYNAMIC_HERO_DATA", response.data);
    } catch (error) {
      handleError("patchDynamicHeroDataAsync", error);
    }
  },
  async postDynamicFormAsync({ commit, rootGetters }, { payload, lang }) {
    if (!payload || !lang) {
      logMissingParameters();
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getDynamicHeroList"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang };

      const response = await this.$axios.post(endpoint, payload, {
        baseURL,
        params
      });
      commit("SET_POST_DYNAMIC_FORM", response.data);
    } catch (error) {
      handleError("postDynamicFormAsync", error);
    }
  },
  async getUUIDAsync({ commit, rootGetters }, { lang }) {
    if (!lang) {
      logMissingParameters();
      return;
    }

    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "articleData"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang };
      endpoint = endpoint + `/uuid`;

      const response = await this.$axios.get(endpoint, {
        baseURL,
        params,
      });
      commit("SET_UUID_DATA", response.data);
    } catch (error) {
      handleError("getUUIDAsync", error);
    }
  },
};

export default {
  state,
  mutations,
  actions,
  getters,
};
