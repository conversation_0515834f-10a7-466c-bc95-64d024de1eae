const state = () => ({
  isin: [],
});

const mutations = {
  SET_ISIN(state, value) {
    state.isin = value;
  },
};

const getters = {
  getISIN(state) {
    return state.isin;
  },
};

const actions = {
  async getNewISINsAsync({ commit, rootGetters }, { lang, payload }) {
    if (!lang || !Object.keys(payload).length) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getNewIsins"
      );
      const { $axios } = useNuxtApp();
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, null, {
        baseURL,
        params: { langs: lang, ...payload },
      });
      if (response.data) {
        commit("SET_ISIN", response.data);
      }
    } catch (error) {
      console.error(error);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
