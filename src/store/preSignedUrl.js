import { KEYS } from '@/сonstants/keys';

const state = () => ({
  preSignedUrl: {},
});

const mutations = {
  SET_PRESIGNED_URL(state, value) {
    state.preSignedUrl = value;
  },
};

const getters = {
  getPreSignedUrl(state) {
    return state.preSignedUrl;
  },
};

const actions = {
  async getPreSignedImageUrlAsync({ commit, rootGetters }, { isin, params }) {
    if (!isin || !Object.keys(params).length) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getPreSignedUrl"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.get(
        endpoint.replace("{isin}", isin),
        {
          baseURL,
          params: { ...params },
        }
      );
      if (response) {
        commit("SET_PRESIGNED_URL", response);
      }
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} getPreSignedImageUrlAsync:`, error);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
