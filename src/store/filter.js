/**
 * @module store/filter
*/

/**
 * Vuex state object initialized with empty filters, no error, and empty active category.
 * @function 
 */

const state = () => ({
    filters: {},
    hasError: false,
    activeCategory: {}
});
/**
 * Mutations for updating state in Vuex store.
 * @type {Object}
 * @property {Function} SET_FILTERS - Mutation to set filters in the state.
 *   @property {Object} SET_FILTERS.state - The Vuex state object.
 *   @property {any} SET_FILTERS.filters - The new filters to set.
 * @property {Function} SET_HAS_ERROR - Mutation to set error state for filters.
 *   @property {Object} SET_HAS_ERROR.state - The Vuex state object.
 *   @property {boolean} SET_HAS_ERROR.hasError - Whether there is an error with filters.
 * @property {Function} SET_ACTIVE_CATEGORY - Mutation to set the active category.
 *   @property {Object} SET_ACTIVE_CATEGORY.state - The Vuex state object.
 *   @property {any} SET_ACTIVE_CATEGORY.category - The new active category to set.
 * @property {Function} SET_SELECT_STATE - Mutation to set selection state of a filter.
 *   @property {Object} SET_SELECT_STATE.state - The Vuex state object.
 *   @property {Object} SET_SELECT_STATE.payload - Payload containing information about the filter and selection state.
 *   @property {Object} SET_SELECT_STATE.payload.filter - The filter object to update.
 *   @property {boolean} SET_SELECT_STATE.payload.isSelected - Whether the filter is selected.
 * @property {Function} SET_DROPDOWN_IS_OPEN - Mutation to set dropdown open state.
 *   @property {Object} SET_DROPDOWN_IS_OPEN.state - The Vuex state object.
 *   @property {Object} SET_DROPDOWN_IS_OPEN.payload - Payload containing information about the dropdown.
 *   @property {Object} SET_DROPDOWN_IS_OPEN.payload.dropdown - The dropdown object to update.
 *   @property {boolean} SET_DROPDOWN_IS_OPEN.payload.isOpen - Whether the dropdown is open.
 * @property {Function} SET_GROUP_IS_OPEN - Mutation to set group open state.
 *   @property {Object} SET_GROUP_IS_OPEN.state - The Vuex state object.
 *   @property {Object} SET_GROUP_IS_OPEN.payload - Payload containing information about the group.
 *   @property {Object} SET_GROUP_IS_OPEN.payload.group - The group object to update.
 *   @property {boolean} SET_GROUP_IS_OPEN.payload.isOpen - Whether the group is open.
 * @property {Function} RESET_FILTER - Mutation to reset a filter.
 *   @property {Object} RESET_FILTER.state - The Vuex state object.
 *   @property {Object} RESET_FILTER.filter - The filter object to reset.
 */

const mutations = {
    SET_FILTERS(state, filters) {
        state.filters = filters
    },
    SET_HAS_ERROR(state, hasError) {
        state.hasErrorFilters = hasError
    },
    SET_ACTIVE_CATEGORY(state, category) {
        if (category) {
            state.activeCategory = category
        }
    },
    SET_SELECT_STATE(state, payload) {
        if (payload.filter.selectedCount && payload.isSelected) {
            payload.filter.selectedCount++
        } else if (payload.isSelected) {
            payload.filter.selectedCount = 1
        } else if (!payload.selected) {
            if (payload.filter.selectedCount > 0) {
                payload.filter.selectedCount--
            } else {
                payload.filter.selectedCount = 0
            }
        }
        payload.filter.selected = payload.filter.selectedCount > 0
    },
    SET_DROPDOWN_IS_OPEN: function (state, payload) {
        payload.dropdown.dropdownIsOpen = payload.isOpen
    },
    SET_GROUP_IS_OPEN(state, payload) {
        payload.group.groupIsOpen = payload.isOpen
    },
    RESET_FILTER(state, filter) {
        filter.selectedCount = 0
        filter.selected = false
    }
};
const actions = {
    setActiveCategory({ commit, state }, payload) {
        if (payload) {
            const category = state.filters.find(c => payload === c.key);
            commit('SET_ACTIVE_CATEGORY', category)
        } else {
            commit('SET_ACTIVE_CATEGORY', {})
        }
    },
    changeSelectStateOfFilter({ commit, state }, payload) {
        const category = state.activeCategory;
        for (let subCategory of category.subCategories) {
            if (subCategory.filters) {
                for (let filter of subCategory.filters) {
                    if (
                      (filter?.key &&
                        payload.dependentFilters.includes(filter.key)) ||
                      filter?.keys?.some((key) =>
                        payload.dependentFilters.includes(key)
                      )
                    ) {
                      commit("SET_SELECT_STATE", {
                        filter: filter,
                        isSelected: payload.selected,
                      });
                    }
                }
            }
        }
    },
    resetFilters({ commit, state, dispatch }) {
        commit('SET_ACTIVE_CATEGORY', {});
        for (let category of state.filters) {
            for (let subCategory of category.subCategories) {
                dispatch('resetSubCategory', subCategory)
            }
        }
    },
    resetSubCategory({ commit, state }, subCategory) {
        if (subCategory.groups) {
            subCategory.groups.forEach(group => {
                group.filters.forEach(filter => commit('RESET_FILTER', filter))

            })
        }
        if (subCategory.filters) {
            subCategory.filters.forEach(filter => commit('RESET_FILTER', filter))
        }
    },
    selectCategoryFilters({ commit, state }, filterKeys) {
        if (filterKeys && state.activeCategory?.subCategories) {
            let categoryFilter = state.activeCategory.subCategories.find(subCategory => subCategory.name === "category")
            let categories = categoryFilter.groups ? categoryFilter.groups : [categoryFilter]

            categories.forEach(group => {
                group.filters.forEach(filter => {
                    if (filterKeys.includes(filter.key)) {
                        if (!filter.selected) {
                            commit('SET_SELECT_STATE', { filter: filter, isSelected: true })
                        }
                    } else if (filter.selected) {
                        commit('SET_SELECT_STATE', { filter: filter, isSelected: false })
                    }
                })
            })
        }
    },
    setDropdownIsOpen({ commit }, payload) {
        commit('SET_DROPDOWN_IS_OPEN', payload)
    },
    setGroupIsOpen({ commit }, payload) {
        commit('SET_GROUP_IS_OPEN', payload)
    },
    changeSelectStateOfFilterForPersonalization({ commit, state }, filter) {
        if (filter) {
            commit('SET_SELECT_STATE', { filter: filter, isSelected: true })
        }
    }
};

const getters = {
    hasSelectedFiltersInSubCategory: (state) => (subCategory) => {
        const category = state.activeCategory;
        const sc = category.subCategories.find(sc => sc.name === subCategory);
        if (sc.groups) {
            return sc.groups.some(group => group.filters.some(filter => filter.selected))
        } else {
            return sc.filters.some(filter => filter.selected);
        }
    },
    getKeysOfSelectedAllergens: (state, getters) => {
        return getters['getItemsOfSelectedSubCategory']('allergens').map(f => f.key);
    },
    getKeysOfSelectedWants: (state, getters) => {
        return getters['getItemsOfSelectedSubCategory']('wants').flatMap(f => f.keys);
    },
    getKeysOfSelectedDislikes: (state, getters) => {
        return getters['getItemsOfSelectedSubCategory']('dislikes').flatMap(f => f.keys);
    },
    getKeysOfSelectedDiets: (state, getters) => {
        return getters['getItemsOfSelectedSubCategory']('diet').flatMap(f => f.key).filter(Boolean);
    },
    getKeysOfSelectedCategories: (state, getters) => {
        return getters['getItemsOfSelectedSubCategory']('category').flatMap(f => f.key);
    },
    getItemsOfSelectedSubCategory: (state) => (subCategoryName) => {
        const category = state.activeCategory;
        if (Object.keys(category).length) {
            const subCategory = category.subCategories.find(subCategory => subCategory.name === subCategoryName);
            if (subCategory) {
                if ("groups" in subCategory) {
                    return subCategory.groups.flatMap(group => group.filters.filter(f => f.selected))
                } else {
                    return subCategory.filters.filter(f => f.selected)
                }
            }
        }

        return []
    },
    getSubCategoriesOfFilters: (state) => (key) => {
        const filters = state.filters.filter((item) => item.key === key);
        if (filters?.length) {
            return filters[0];
        }
        return [];
    }
};

export default {
    state,
    mutations,
    actions,
    getters
}