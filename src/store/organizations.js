import { getEndpointFromStore } from "@/utils/get-endpoint-from-store";

const state = () => ({
  organizations: [],
  isin: '',
  preSignedUrl: {},

  /**
   * @type {OrganizationsObject[]}
   */
  organizationsList: [],
  pagination: {
    from: 0,
    size: 15,
    total: 0,
  },
});

const mutations = {
  SET_ORGANIZATIONS(state, value) {
    state.organizations = value;
  },
  SET_ISIN(state, value) {
    state.isin = value;
  },
  SET_PRESIGNED_URL(state, value) {
    state.preSignedUrl = value;
  },
  SET_ORGANIZATIONS_LIST(state, value) {
    state.organizationsList = value;
  },
  SET_PAGINATION_TOTAL(state, payload) {
    state.pagination.total = payload.total;
  },
};

const getters = {
  getOrganizationsData(state) {
    return state.organizations;
  },
  getISIN(state) {
    return state.isin;
  },
  getPreSignedUrl(state) {
    return state.preSignedUrl;
  },
  getOrganizationsList(state) {
    return state.organizationsList;
  },
  getPagination(state) {
    return state.pagination;
  },
};

const actions = {

  /**
   * @deprecated
   *
   * @param commit
   * @param rootGetters
   * @param params
   * @returns {Promise<void>}
   */
  async getOrganizationsListForTableAsync({ commit, rootGetters }, { params }) {
    if (!params) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "getSearchOrganizations");
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      const data = response?.data;
      const result = data?.results || [];
      const pagination = {
        total: data?.total
      };
      commit("SET_ORGANIZATIONS_LIST", result);
      commit("SET_PAGINATION_TOTAL", pagination);
    } catch (error) {
      console.error("Error in getSearchOrganizationsAsync:", error);
    }
  },
  async getSearchOrganizationsAsync({ commit, rootGetters }, { lang, payload }) {
    if (!lang || !Object.keys(payload).length) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "getSearchOrganizations");
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { lang: lang, ...payload },
      });
      if (response.data) {
        commit("SET_ORGANIZATIONS", response);
      }
    } catch (error) {
      console.error("Error in getSearchOrganizationsAsync:", error);
    }
  },

  async deleteOrganizationsListAsync({ rootGetters }, { lang, payload }) {
    if (!lang || !Object.keys(payload).length) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "deleteOrganization");
      const { $axios } = useNuxtApp();
      const response = await $axios.delete(`${endpoint}/${payload.isin}`, {
        baseURL,
        params: { user: payload.user, langs: lang },
      });
      return response;
    } catch (error) {
      console.error("Error in deleteOrganizationsListAsync:", error);
    }
  },

  async getNewISINsAsync({ commit, rootGetters }, { lang, payload }) {
    if (!lang || !Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "getNewIsins");
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, null, {
        baseURL,
        params: { langs: lang, ...payload },
      });
      if (response) {
        commit("SET_ISIN", response?.data);
      }
    } catch (error) {
      console.error("Error in getNewISINsAsync:", error);
    }
  },

  async getPreSignedImageUrlAsync({ commit, rootGetters }, { isin, payload }) {
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "getPreSignedUrlImage");
      const { $axios } = useNuxtApp();
      const response = await $axios.get(
        endpoint.replace("{isin}", isin),
        {
          baseURL,
          params: { ...payload },
        }
      );
      if (response) {
        commit("SET_PRESIGNED_URL", response?.data);
      }
    } catch (error) {
      console.error("Error in getPreSignedImageUrlAsync:", error);
    }
  },

  async postOrganizationsDataAsync({ rootGetters }, { payload, params }) {
    if (!Object.keys(payload).length || !Object.keys(params).length) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "postOrganizations");
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { ...params },
      });
      return response;
    } catch (error) {
      console.error("Error in postOrganizationsDataAsync:", error);
    }
  },

  async getOrganizationsDataAsync({ rootGetters }, { params }) {
    if (!Object.keys(params).length) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "getOrganizationsData");
      const { $axios } = useNuxtApp();
      const response = await $axios.get(`${endpoint}/${params.isIn}`, {
        baseURL,
        params: { lang: params.lang },
      });
      return response?.data;
    } catch (error) {
      console.error("Error in getOrganizationsDataAsync:", error);
    }
  },

  resetOrganisationList({ commit }) {
    commit("SET_ORGANIZATIONS_LIST", []);
    commit("SET_PAGINATION_TOTAL", { total: 0 });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
