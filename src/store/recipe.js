/**
 * @module store/recipe
 */
import { processCsvResponse, processCsvData } from "@/utils/csvProcessor";
import { KEYS } from '@/сonstants/keys';
import JSONbig from "json-bigint";
import { getEndpointFromStore } from "@/utils/get-endpoint-from-store";
import { useNuxtApp } from "nuxt/app";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import { storeToRefs } from "pinia";

const JSONbigParser = JSONbig({ storeAsString: true });

const state = () => ({
  recipeData: {},
  recipeScheduleData: {},
  recipe: {},
  overviewVideos: [],
  recipeDiets: [],
  recipeCsvData: [],
  exportPromptHistory: [],
  getSearchGeneratedData: {},
  recentActivityResponse: {},
  exportedCount: 0,
  recipeScheduleList: []
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_RECIPE_DATA: createMutation("recipeData"),
  SET_RECIPE_SCHEDULE_DATA: createMutation("recipeScheduleData"),
  SET_RECIPE: createMutation("recipe"),
  SET_OVERVIEW_VIDEOS: createMutation("overviewVideos"),
  SET_RECIPE_DIETS: createMutation("recipeDiets"),
  SET_SEARCH_GENERATED_DATA: createMutation("getSearchGeneratedData"),
  RESET_SEARCH_GENERATED_DATA: createMutation("getSearchGeneratedData"),
  SET_RECIPE_CSV_DATA: createMutation("recipeCsvData"),
  SET_EXPORT_PROMPT_HISTORY: createMutation("exportPromptHistory"),
  SET_RECENT_ACTIVITY_RESPONSE: createMutation("recentActivityResponse"),
  SET_EXPORTED_COUNT: createMutation("exportedCount"),
  SET_RECIPE_SCHEDULE_LIST: createMutation("recipeScheduleList")
};

const handleError = (method, error) => {
  console.error(`${KEYS.KEY_NAMES.ERROR_IN} ${method}:`, error);
};
/**
 * Actions for managing recipe-related operations in Vuex store.
 * @type {Object}
 * @property {Function} setRecipe - Action to set a recipe in the Vuex state.
 *   @property {Function} setRecipe.commit - The Vuex commit function to commit mutations.
 *   @property {Object} setRecipe.state - The Vuex state object.
 *   @property {Object} payload - The recipe object to set.
 *   @property {Array} payload.steps - The steps array of the recipe.
 * @property {Function} postImportRecipeAsync - Action to import a recipe asynchronously.
 *   @property {Function} postImportRecipeAsync.rootGetters - The Vuex rootGetters to access root getters.
 *   @property {string} lang - The language code for the request.
 *   @property {Object} payload - The payload containing the recipe data to import.
 *   @returns {Promise<Object|undefined>} The response object from the server, or undefined if parameters are missing.
 * @property {Function} getOverviewRecipeStatisticsAsync - Action to fetch overview statistics for recipes asynchronously.
 *   @property {Function} getOverviewRecipeStatisticsAsync.rootGetters - The Vuex rootGetters to access root getters.
 *   @property {string} lang - The language code for the request.
 *   @returns {Promise<Object|undefined>} The response object containing recipe statistics, or undefined if parameters are missing.
 */
const actions = {
  async setRecipe({ commit, state }, payload) {
    if (payload?.steps?.length) {
      commit("SET_RECIPE", payload);
    }
  },

  async getRecipeAsync({ commit, rootGetters }, { params, isin }) {
    const { $axios, $t } = useNuxtApp();

    if (!isin || !Object.keys(params).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }

    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "icl", "getRecipe");
      const modifiedEndpoint = `${endpoint}/${isin}`;
      const response = await $axios.get(modifiedEndpoint, {
        baseURL,
        params,
        transformResponse: data => JSONbigParser.parse(data),
      });

      commit("SET_RECIPE_DATA", response?.data);
    } catch (error) {
      handleError("getRecipeAsync", error);
    }
  },

  async getRecipeScheduleDataAsync({ rootGetters, commit }, { params }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(params).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getRecipeSchedule"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: params,
      });
      commit("SET_RECIPE_SCHEDULE_DATA", response.data[0])
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getRecipeScheduleDataAsync:",
        error
      );
    }
  },

  async postImportRecipeAsync({ rootGetters }, { lang, payload, onError }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !payload) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }

    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "postImportRecipe"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { langs: lang },
      });

      return response;
    } catch (error) {
      onError(error);
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "postImportRecipeAsync:",
        error
      );
    }
  },

  async getOverviewRecipeStatisticsAsync({ rootGetters }, { lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }

    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getRecipesCount"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { langs: lang },
      });

      return response;
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getOverviewRecipeStatisticsAsync:",
        error
      );
    }
  },
  async getOverviewVideosAsync({ commit, rootGetters }) {
    try {
      const projectId = rootGetters["userData/getProject"]?.id;
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "selfServeVideo"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const { $axios } = useNuxtApp();

      const response = await $axios.get(endpoint, {
        baseURL,
        params: { projectId: projectId },
      });
      commit('SET_OVERVIEW_VIDEOS', response?.data);
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getOverviewVideosAsync:",
        error
      );
    }
  },
  async resetGeneratedPrompt({ commit }) {
    commit("RESET_SEARCH_GENERATED_DATA", {});
  },
  async getSearchGeneratorDataAsync({ commit, rootGetters }, { lang, isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    let endpoint = rootGetters["config/getClientEndpoint"](
      "flite",
      "generatorData"
    );
    endpoint = `${endpoint}/search`;
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    try {
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { lang, isin },
      });
      commit("SET_SEARCH_GENERATED_DATA", response.data);
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getSearchGeneratorDataAsync:",
        error
      );
    }
  },
  async getRecipeMasterListAsync({ rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getRecipeList"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { ...payload },
      });
      const dynamicHeroStore = useDynamicHeroStore();
      const { disabledArticleDataList } = storeToRefs(dynamicHeroStore);
  
      const result = response.data?.results || [];
  
      result.forEach((item) => {
        if (item?.isin && disabledArticleDataList.value?.includes(item.isin)) {
          item.usedInHero = true;
        }
      });
  
      return response.data;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} getRecipeMasterListAsync:`, error);
    }
  },
  

  async getRecipeDietsAsync(
    { rootGetters, commit },
    { query, recipeCount, lang }
  ) {
    const { $axios, $t } = useNuxtApp();
    if (!lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getRecipeDiets"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang: lang, q: query, includeTotals: recipeCount };
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      commit("SET_RECIPE_DIETS", response?.data);
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getRecipeDietsAsync:",
        error
      );
    }
  },
  async getExportPromptHistoryAsync({ rootGetters, commit }, { lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang) {
      console.error($t("COMMON.MISSING_REQUIRED_PARAMETERS"));
      return;
    }
    const endpoint = rootGetters["config/getClientEndpoint"](
      "flite",
      "getExportPromptHistory"
    );
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    try {
      const response = await $axios.get(endpoint, { baseURL });
      if (typeof response.data === "string") {
        const { processedCsv, rowCount } = processCsvResponse(response.data);
        commit("SET_EXPORT_PROMPT_HISTORY", processedCsv);
        commit("SET_EXPORTED_COUNT", rowCount)
      }
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + " getExportPromptHistoryAsync:",
        error
      );
    }
  },

  async getRecipeCSVAsync({ rootGetters, commit }, { status, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!status || !lang) {
      console.error($t("COMMON.MISSING_REQUIRED_PARAMETERS"));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getRecipeCSV"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const params = { country: lang.split("-")[1] };
      if (status === KEYS.KEY_NAMES.ALL) {
        params.statuses = "active,hidden";
      } else if (status === KEYS.KEY_NAMES.PUBLISHED) {
        params.statuses = "active";
      } else if (status === KEYS.KEY_NAMES.UNPUBLISHED) {
        params.statuses = "hidden";
      }
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      const { rowCount } = processCsvData(response.data);
      commit("SET_RECIPE_CSV_DATA", response.data);
      commit("SET_EXPORTED_COUNT", rowCount)
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getRecipeCSVAsync:",
        error
      );
    }
  },
  async postRecentActivityAsync({ rootGetters, commit }, { lang, payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !payload) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"]("flite", "recentActivity");
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { langs: lang },
      });
      commit('SET_RECENT_ACTIVITY_RESPONSE', response?.data);
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} postRecentActivityAsync:`, error);
    }
  },
  async getAllRecipeScheduleAsync({ rootGetters, commit }, {  payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!payload) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"]("flite", "getRecipeSchedule");
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        payload,
      });
      commit('SET_RECIPE_SCHEDULE_LIST', response.data);
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} getAllRecipeSchedule:`, error);
    }
  },
  async publishRecipeAsync({ rootGetters }, { isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "publishRecipe"
      );
      endpoint = endpoint.replace("{isin}", isin);
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.post(endpoint, {}, {
        baseURL
      });
      return response;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} publishRecipeAsync:`, error);
    }
  },
  async deleteRecipeAsync({ rootGetters }, { isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"]("flite", "deleteRecipe");
      endpoint = endpoint.replace("{isin}", isin);
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.delete(endpoint, {
        baseURL,
      });
      return response;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} deleteRecipeAsync:`, error);
    }
  },
  async deleteScheduleRecipeAsync({ rootGetters }, { isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"]("flite", "getRecipeSchedule");
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.delete(`${endpoint}/${isin}`, {
        baseURL,
      });
      return response;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} deleteScheduleRecipeAsync:`, error);
    }
  },
  async scheduleRecipesAsync({ rootGetters }, { lang, payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !payload) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"]("flite", "getRecipeSchedule");
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params: { langs: lang },
      });
      return response;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} scheduleRecipesAsync:`, error);
    }
  }
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getRecipe: createGetter("recipe"),
  getRecipeSteps: createGetter("recipe.steps"),
  selfServeVideo: createGetter("overviewVideos"),
  getRecipeDiets: createGetter("recipeDiets"),
  getSearchGeneratedData: createGetter("getSearchGeneratedData"),
  getRecipeCsvData: createGetter("recipeCsvData"),
  getExportPromptHistory: createGetter("exportPromptHistory"),
  getExportedRecipesCount: createGetter("exportedCount"),
  getAllRecipeSchedule: createGetter("recipeScheduleList"),
  getRecipeData: createGetter("recipeData"),
  getRecipeScheduleData: createGetter("recipeScheduleData"),
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
