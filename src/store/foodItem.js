
/**
 * @module store/foodItem
*/

const state = () => ({
    foodItemName: null
});

/**
 * Mutations for updating state in Vuex store related to food item name.
 * @type {Object}
 * @property {Function} SET_Food_Item_Name - Mutation to set the food item name in the state.
 *   @property {Object} SET_Food_Item_Name.state - The Vuex state object.
 *   @property {string} SET_Food_Item_Name.name - The new name of the food item to set.
 */
const mutations = {
    SET_Food_Item_Name(state, name) {
        state.foodItemName = name
    },
};

/**
 * Actions for updating food item name in Vuex store.
 * @type {Object}
 * @property {Function} setFoodItemName - Action to update the food item name in the state.
 *   @property {Function} setFoodItemName.context.commit - The Vuex commit function to commit mutations.
 *   @property {Object} setFoodItemName.context.state - The Vuex state object.
 *   @property {string} setFoodItemName.payload - The new name of the food item to set.
 */
const actions = {
    setFoodItemName({ commit, state }, payload) {
        if (payload) {
            commit('SET_Food_Item_Name', payload);
        }
    },
};

/**
 * Getters for accessing food item related state in Vuex store.
 * @type {Object}
 * @property {Function} getFoodItemName - Getter to retrieve the current food item name from the state.
 *   @property {Object} getFoodItemName.state - The Vuex state object.
 *   @returns {string} The current food item name.
 */
const getters = {
    getFoodItemName: (state) => {
        return state.foodItemName;
    }
};

export default {
    state,
    mutations,
    actions,
    getters
}
