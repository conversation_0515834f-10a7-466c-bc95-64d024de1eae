import {
  CONTENT_GENERATION_TYPE,
  GET_CONTENT_GENERATION_STEPS,
  GET_IMAGE_GENERATION_MODELS,
  GET_RECIPE_GENERATION_FLAGS,
  GET_TEXT_GENERATION_MODELS,
  getRecipeImageObject,
  IMAGE_GENERATION_MODEL_NAME,
} from "@/models/recipe-generator.model";
import { compareByDecreasing } from "@/utils/compare-by-decreasing";
import { compareByAssessment } from "@/utils/compare-by-assessment";
import {
  getGenerationDataGetter,
  setFlagUtil,
  setGenerationProgressStepsUtil,
  setImageSelectedUtil,
  setImagesReviewDetailsUtil,
  updateImageGenerationModelsUtil,
} from "@/utils/recipe-generator-store";
import { compareByIncreasing } from "@/utils/compare-by-increasing";
import { generateUUID } from "@/utils/generateUUID";
import { useNuxtApp } from "nuxt/app";

function initialState() {
  return {
    recipeUUID: generateUUID(),
    resetAt: Date.now(),

    flag: GET_RECIPE_GENERATION_FLAGS(),

    promptValue: "",
    promptValueArchive: [],
    modifyPromptValue: "",

    generationStepsState: GET_CONTENT_GENERATION_STEPS(),

    servesCount: 4,
    title: "",
    ingredients: "",
    instructions: "",

    editedServesCount: null,
    editedTitle: "",
    editedIngredients: "",
    editedInstructions: "",
    promptHistory: [],

    recipeReview: {
      consistency_rating_star: 0,
      texture_rating_star: 0,
      taste_rating_star: 0,
    },

    /**
     * If we need to update the image according to some condition.
     *
     * @example
     * forceImageRefresh = Date.now()
     *
     * @type {(number|null)}
     *
     * @default null
     */
    forceImageRefresh: null,

    /**
     * Image list from recipe image steps.
     *
     * @type {Array.<RecipeImageObject>}
     * @example
     * [{
     *   "key": "https://storage.googleapis.com/trial-images/int-cms/1718298691945/sample_0.png",
     *   "averageValue": 3.222222222222222,
     *   "assessment": null,
     *   "isSelected": false,
     *   "selectedOrder": null,
     *   "isMainImage": false,
     *   "source": "aiGenerated",
     *   "image": {
     *     "link": "https://storage.googleapis.com/trial-images/int-cms/1718298691945/sample_0.png",
     *     "model": "model",
     *     "prompt": "Some prompt text",
     *   },
     *   "review": {
     *     "color_rating_star": 4.5
     *     "accuracy_rating_star": 2,
     *     "realism_rating_star": 4.5,
     *   }
     * }]
     * @default []
     */
    imageList: [],
    isImageSelectedLimit: false,

    selectedImage: undefined,

    /**
     * @property {string} assessment - assessment of the recipe
     */
    recipeRating: null,

    /**
     * @property {string} assessmentReason
     */
    assessmentReason: null,

    /**
     * Advance output data.
     *
     * @member {string}
     */
    streamText: "",

    /**
     * Generation progress steps.
     * @module store/recipeGeneration
     * @property {CONTENT_GENERATION_TYPE} type
     * @property {CONTENT_GENERATION_STEP} step
     * @property {string} result - get from stream output
     * @property {boolean} isStreamOutput - mark that output from stream
     * @property {boolean} isDone - mark that step gets result output from stream or from custom event and step is showed
     * @property {boolean} isHidden - display step or not
     *
     * @example
     * [{
     *   type: "progress | result | failure",
     *   step: "moderate_prompt",
     *   result: "# Moderating the recipe",
     *   isStreamOutput: true,
     *   isDone: false,
     *   isHidden: false,
     * }]
     *
     * @default []
     */
    generationProgressSteps: [],

    traceId: undefined,

    openOutputPanel: undefined,

    /**
     * List of text models
     *
     * @type {Array.<TextGenerationModelObject>}
     * @example
     * [{
     *   "id": "gpt-4o",
     *   "name": "GPT",
     *   "selected": true,
     *   "disabled": true,
     *   "enabled": true
     * }]
     * @default []
     */
    textGenerationModels:  GET_TEXT_GENERATION_MODELS(),

    /**
     * List of image models
     *
     * @type {Array.<ImageGenerationModelObject>}
     * @example
     * [{
     *   "id": "imagen-1",
     *   "name": "Imagen 1",
     *   "selected": true,
     *   "disabled": false,
     *   "enabled": true
     * }]
     * @default []
     */
    imageGenerationModels: GET_IMAGE_GENERATION_MODELS(),
    isFoodLMGenerator: false,
  };
}

const state = () => initialState();

// Helper function to create mutations
const createMutation = (property) => (state, value) => state[property] = value;

// Helpers function to create actions that commit mutations
const createAction = (mutation) => ({ commit }, { value }) => commit(mutation, value);
const createActionWithCondition = (mutation) => ({ commit }, { value }) => {
  if (!value) {
    return;
  }

  commit(mutation, value)
};

const mutations = {
  SET_PROMPT_VALUE: createMutation("promptValue"),
  SET_PROMPT_VALUE_ARCHIVE: createMutation("promptValueArchive"),
  SET_MODIFY_PROMPT_VALUE: createMutation("modifyPromptValue"),
  SET_RECIPE_TO_EDITED_FIELD_TITLE: createMutation("editedTitle"),
  SET_RECIPE_TO_EDITED_FIELD_INGREDIENTS: createMutation("editedIngredients"),
  SET_RECIPE_TO_EDITED_FIELD_INSTRUCTIONS: createMutation("editedInstructions"),
  SET_RECIPE_TO_EDITED_FIELD_SERVES_COUNT: createMutation("editedServesCount"),
  SET_IMAGE_LIST: createMutation("imageList"),
  SET_SELECTED_IMAGE: createMutation("selectedImage"),
  SET_STREAM_TEXT: createMutation("streamText"),
  SET_GENERATION_PROGRESS_STEPS: createMutation("generationProgressSteps"),
  SET_TRACE_ID: createMutation("traceId"),
  SET_FORCE_IMAGE_REFRESH: createMutation("forceImageRefresh"),
  SET_RECIPE_RATING: createMutation("recipeRating"),
  SET_OPEN_OUTPUT_PANEL: createMutation("openOutputPanel"),
  SET_IMAGE_GENERATION_MODEL: createMutation("imageGenerationModels"),
  SET_PROMPT_HISTORY: createMutation("promptHistory"),
  SET_ASSESSMENT_REASON: createMutation("assessmentReason"),
  SET_RECIPE_REVIEW: createMutation("recipeReview"),
  SET_IS_IMAGE_SELECTED_LIMIT: createMutation("isImageSelectedLimit"),
  SET_FLAG(state, { flag, value }) {
    state.flag[flag] = value;
  },
  UPDATE_STEPS_STATE(state, { step, stepValue }) {
    state.generationStepsState[step] = stepValue;
  },
  SET_RECIPE(state, { title, ingredients, instructions, servesCount }) {
    state.title = title;
    state.ingredients = ingredients;
    state.instructions = instructions;
    state.servesCount = servesCount;
  },
  SET_RECIPE_TO_EDITED_FIELDS(state, { title, ingredients, instructions, servesCount }) {
    state.editedTitle = title;
    state.editedIngredients = ingredients;
    state.editedInstructions = instructions;
    state.editedServesCount = servesCount;
  },
  SET_FOODLM_GENERATOR(state, value) {
    state.isFoodLMGenerator = value;
  }
};

const actions = {
  setPromptValue: createAction("SET_PROMPT_VALUE"),
  setModifyPromptValue: createAction("SET_MODIFY_PROMPT_VALUE"),
  setEditedRecipeTitle: createAction("SET_RECIPE_TO_EDITED_FIELD_TITLE"),
  setEditedRecipeIngredients: createAction("SET_RECIPE_TO_EDITED_FIELD_INGREDIENTS"),
  setEditedRecipeInstructions: createAction("SET_RECIPE_TO_EDITED_FIELD_INSTRUCTIONS"),
  setEditedRecipeServesCount: createAction("SET_RECIPE_TO_EDITED_FIELD_SERVES_COUNT"),
  setRecipeRating: createAction("SET_RECIPE_RATING"),
  setAssessmentReason: createAction("SET_ASSESSMENT_REASON"),
  setOpenOutputPanel: createAction("SET_OPEN_OUTPUT_PANEL"),
  setTraceId: createActionWithCondition("SET_TRACE_ID"),
  setSelectedImage: createActionWithCondition("SET_SELECTED_IMAGE"),
  setFlag: setFlagUtil(),
  setImagesReviewDetails: setImagesReviewDetailsUtil(),
  setGenerationProgressSteps: setGenerationProgressStepsUtil(),
  updateImageGenerationModels: updateImageGenerationModelsUtil(),
  setImageSelected: setImageSelectedUtil(),
  setPromptValueToArchive({ commit, state }, { value }) {
    commit("SET_PROMPT_VALUE_ARCHIVE", [value, ...state.promptValueArchive]);
  },
  updateStep({ commit }, { step, stepValue }) {
    if (!step || !stepValue) {
      return;
    }

    commit('UPDATE_STEPS_STATE', { step, stepValue });
  },
  setRecipe({ commit }, { title, ingredients, instructions, servesCount }) {
    if (!title || !ingredients || !instructions) {
      return;
    }

    const payload = {
      title: title || "",
      ingredients: ingredients?.join("\n") || "",
      instructions: instructions?.join("\n") || "",
      servesCount: servesCount,
    }

    commit('SET_RECIPE', payload);
    commit('SET_RECIPE_TO_EDITED_FIELDS', payload);
  },
  setStreamText({ commit, state }, { value }) {
    commit('SET_STREAM_TEXT', state.streamText + value);
  },
  setRecipeReview({ commit }, { data }) {
    const model = data?.model;
    const payload = {
      consistency_rating_star: model?.consistency_rating_star,
      texture_rating_star: model?.texture_rating_star,
      taste_rating_star: model?.taste_rating_star,
    };
    commit("SET_RECIPE_REVIEW", payload);
  },
  cancelEditRecipe({ commit, state }) {
    const payload = {
      title: state.title,
      ingredients: state.ingredients,
      instructions: state.instructions,
      servesCount: state.servesCount,
    };
    commit('SET_RECIPE_TO_EDITED_FIELDS', payload);
  },
  confirmEditRecipe({ commit, state}) {
    const payload = {
      title: state.editedTitle,
      ingredients: state.editedIngredients,
      instructions: state.editedInstructions,
      servesCount: state.editedServesCount,
    };
    commit('SET_RECIPE', payload);
  },
  setImageList({ commit, state }, { imageList }) {
    if (!imageList?.length) {
      return;
    }

    const newImageList = imageList.map(getRecipeImageObject) || [];
    const oldImageList = state?.imageList || [];
    const result = newImageList.concat(oldImageList);

    commit('SET_IMAGE_LIST', result);
  },
  setMainImage({ commit, state }, { key, value }) {
    const imageList = state.imageList?.map(item => ({
      ...item,
      isMainImage: item.key === key ? value : false
    }));

    commit('SET_IMAGE_LIST', imageList);
  },
  updateImageAssessment({ commit, state }, { key, assessment }) {
    const imageList = [...state.imageList];
    const imageIndex = imageList.findIndex((item) => item.key === key);

    if (imageIndex === -1) {
      return;
    }

    imageList[imageIndex].assessment = assessment;

    commit('SET_IMAGE_LIST', imageList);
  },
  async getPromptHistoryAsync({ rootGetters, commit }, { params }) {
    const { $axios, $t } = useNuxtApp();
    if(!Object.keys(params).length){
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    const endpoint = rootGetters["config/getClientEndpoint"]("flite", "getPromptHistory");
    const baseUrl = rootGetters["config/getClientConfig"]("flite").host;
    try {
      const response = await $axios.get(endpoint, {
        baseURL: baseUrl,
        params,
      });
      if (response) {
        commit("SET_PROMPT_HISTORY", response?.data);
      }
    } catch (error) {
      console.error($t('COMMON.ERROR_FETCHING_PROMPT_HISTORY'), error);
    }
  },
  resetGenerationProgressSteps({ commit }) {
    commit('SET_GENERATION_PROGRESS_STEPS', []);
  },
  setForceImageRefresh({ commit }) {
    commit("SET_FORCE_IMAGE_REFRESH", Date.now());
  },
  reset({ state }, {
    keepProgress = false,
    keepTextModels = false,
    keepImageModels = false,
  }) {
    const s = initialState();
    const keysToKeep = {
      generationProgressSteps: keepProgress,
      textGenerationModels: keepTextModels,
      imageGenerationModels: keepImageModels,
      promptValueArchive: true,
    };
    Object.entries(s).forEach(([key, value]) => {
      state[key] = keysToKeep[key] ? state[key] : value;
    });
  },
  setFoodLMGenerator({ commit }, value) {
    commit('SET_FOODLM_GENERATOR', value);
  },
};

const getters = {
  getRecipeUUID: (state) => state.recipeUUID,
  getTraceId: (state) => state.traceId,
  getFlag: (state) => (flag) => state.flag[flag],
  getPromptHistory: (state) => state.promptHistory,
  getPromptValue: (state) => state.promptValue,
  getModifyPromptValue: (state) => state.modifyPromptValue,
  getImageList: (state) => state.imageList,
  getSelectedImage: (state) => state.selectedImage,
  getStreamText: (state) => state.streamText,
  getGenerationProgressSteps: (state) => state.generationProgressSteps,
  getForceImageRefresh: (state) => state.forceImageRefresh,
  getResetAt: (state) => state.resetAt,
  getRecipeRating: (state) => state.recipeRating,
  getAssessmentReason: (state) => state.assessmentReason,
  getOpenOutputPanel: (state) => state.openOutputPanel,
  getImageGenerationModels: (state) => state.imageGenerationModels,
  getImageGenerationModelsForStreamPayload: (state) => state.imageGenerationModels.filter(({ selected }) => selected).map(({id}) => ({ id })),
  getTextGenerationModels: (state) => state.textGenerationModels,
  getTextGenerationModelsForStreamPayload: (state) => state.textGenerationModels.filter(({ selected }) => selected).map(({id}) => ({ id })),
  getRecipeReview: (state) => state.recipeReview,
  getEditedRecipeTitle: (state) => state.editedTitle,
  getEditedRecipeIngredients: (state) => state.editedIngredients,
  getEditedRecipeInstructions: (state) => state.editedInstructions,
  getEditedRecipeServesCount: (state) => state.editedServesCount,
  getIsImageSelectedLimit: (state) => state.isImageSelectedLimit,
  getRecipeData: (state) => {
    return {
      title: state.title,
      ingredients: state.ingredients,
      instructions: state.instructions,
      servesCount: state.servesCount,
    };
  },
  getSortedImageListByAverageStarValue: (state) => {
    if (!state?.imageList?.length) {
      return undefined;
    }

    const sortedImageList = state.imageList
      .filter(item => item?.key && item?.averageValue)
      .sort((a, b) => {
        // Sort first by assessment order, then by averageValue in decreasing order
        const assessmentComparison = compareByAssessment(a, b);
        return assessmentComparison !== 0 ? assessmentComparison : compareByDecreasing(a, b);
      });

    return sortedImageList?.length ? sortedImageList : undefined;
  },
  getImageListForPrimaryImages: (state) => {
    if (!state?.imageList?.length) {
      return undefined;
    }

    return state.imageList
      .filter((item) => item?.isSelected)
      .sort((a, b) => compareByIncreasing(a, b, "selectedOrder"))
      .map((item) => {
        return {
          id: item.key,
          url: item.key,
          isMainImage: item.isMainImage,
          source: item.source,
          tooltip: `Model: ${IMAGE_GENERATION_MODEL_NAME[item?.image?.model]}`
        };
      });
  },
  getMainImage: (state) => {
    return state.imageList?.find((item) => item.isMainImage);
  },
  getImagesForSaving: (state) => {
    if (!state?.imageList?.length) {
      return [];
    }

    return state.imageList.filter((item) => item.isSelected);
  },
  isCorrectGenerated: (state) => {
    const {
      title,
      ingredients,
      instructions,
      servesCount
    } = state;
    return !!(title && ingredients && instructions && servesCount);
  },
  getRecipeForImagesRefreshing: (state) => {
    return {
      recipe: {
        title: state.title,
        servings: state.servesCount,
        ingredients: state.ingredients.split("\n"),
        instructions: state.instructions.split("\n"),
      }
    }
  },
  getRecipeForSaving: (state) => {
    return {
      title: state.title,
      servings: state.servesCount,
      ingredients: state.ingredients.split("\n"),
      instructions: state.instructions.split("\n"),
    }
  },
  getRecipeForModification: (state) => {
    return {
      input: state.promptValue,
      customizations: state.modifyPromptValue,
      recipe: {
        title: state.title,
        servings: state.servesCount,
        ingredients: state.ingredients.split("\n"),
        instructions: state.instructions.split("\n"),
      }
    }
  },
  getFoodLMGenerator: (state) => state.isFoodLMGenerator,
  getFirstGenerationData: (state) => getGenerationDataGetter(state, true),
  getGenerationData: (state) => getGenerationDataGetter(state, false),
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
