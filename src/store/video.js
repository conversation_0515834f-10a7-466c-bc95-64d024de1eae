import { KEYS } from '@/сonstants/keys';
import { useNuxtApp } from "nuxt/app";

const state = () => ({
  videoDataList: [],
  videoUploaded: {},
  videoDeleted: {},
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_VIDEO_DATA_LIST: createMutation("videoDataList"),
  SET_VIDEO_UPLOADED: createMutation("videoUploaded"),
  SET_VIDEO_DELETED: createMutation("videoDeleted"),
};

const actions = {
  async getVideoDataAsync({ rootGetters, commit }) {
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getVideos"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
      });
      commit("SET_VIDEO_DATA_LIST", response.data);
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getVideoDataAsync:",
        error
      );
    }
  },

  async postVideoDataAsync({ commit, rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!payload) {
      console.error($t("COMMON.MISSING_REQUIRED_PARAMETERS"));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getVideos"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL,
      });

      commit("SET_VIDEO_UPLOADED", response);
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "postVideoDataAsync:",
        error
      );
    }
  },
  async deleteVideoAsync({ commit, rootGetters }, { name }) {
    const { $axios, $t } = useNuxtApp();
    if (!name) {
      console.error($t("COMMON.MISSING_REQUIRED_PARAMETERS"));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getVideos"
      );
      endpoint = `${endpoint}/${name}`;
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.delete(endpoint, {
        baseURL,
      });
      commit("SET_VIDEO_DELETED", response);
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "deleteVideoAsync:", error);
    }
  },
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getVideoDataList: createGetter("videoDataList"),
  getVideoUploaded: createGetter("videoUploaded"),
  getVideoDeleted: createGetter("videoDeleted"),
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
