  import qs from "query-string";
import { KEYS } from "@/сonstants/keys.js";
import { getEndpointFromStore } from "@/utils/get-endpoint-from-store";
import { IngredientObject } from "@/models/ingredients.model";

const state = () => ({
  ingredientProductCount: [],
  featureConfig: [],
  ingredientKeywords: [],
  ingredientLocked: [],
  ingredientUnLocked: [],
  recipeUnit: [],
  recipeList: [],
  searchProducts: [],
  ingredientAlreadyAddedProduct: [],
  ingredientCampaign: [],
  ingredients: [],
  brandDistribution: [],
  ingredientsProduct: [],
  ingredientsCampaignData: [],
  updateIngredientsName: [],
  operationStatus: [],

  ingredientListForTable: [],
  pagination: {
    from: 0,
    size: 15,
    total: 0,
  },
});

const createMutation = (setter) => (state, payload) => {
  state[setter] = payload.data;
};

const mutations = {
  SET_INGREDIENT_PRODUCT_COUNT: createMutation("ingredientProductCount"),
  SET_FEATURE_CONFIG: createMutation("featureConfig"),
  SET_INGREDIENT_KEYWORDS: createMutation("ingredientKeywords"),
  SET_INGREDIENT_LOCKED: createMutation("ingredientLocked"),
  SET_INGREDIENT_UN_LOCKED: createMutation("ingredientUnLocked"),
  SET_RECIPE_UNIT: createMutation("recipeUnit"),
  SET_RECIPE_LIST: createMutation("recipeList"),
  SET_SEARCH_PRODUCTS: createMutation("searchProducts"),
  INGREDIENT_ALREADY_ADDED_PRODUCTS: createMutation("ingredientAlreadyAddedProduct"),
  RESET_INGREDIENT_CAMPAIGN(state) {
    state.ingredientCampaign = [];
  },
  SET_INGREDIENT_CAMPAIGN: createMutation("ingredientCampaign"),
  SET_INGREDIENTS: createMutation("ingredients"),
  SET_BRAND_DISTRIBUTION: createMutation("brandDistribution"),
  SET_INGREDIENT_PRODUCTS: createMutation("ingredientsProduct"),
  SET_INGREDIENT_CAMPAIGN_DATA: createMutation("ingredientsCampaignData"),
  SET_UPDATE_INGREDIENT_NAME: createMutation("updateIngredientsName"),
  SET_OPERATION_STATUS: createMutation("operationStatus"),
  SET_INGREDIENT_KEYWORD: createMutation("ingredientKeyword"),
  SET_PAGINATION_TOTAL(state, payload) {
    state.pagination.total = payload.total;
  },
  SET_INGREDIENT_LIST_FOR_TABLE: createMutation("ingredientListForTable"),
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getIngredientProductCount: createGetter("ingredientProductCount"),
  getFeatureConfig: createGetter("featureConfig"),
  getIngredientKeywords: createGetter("ingredientKeywords"),
  getLockedIngredient: createGetter("ingredientLocked"),
  getUnLockedIngredient: createGetter("ingredientUnLocked"),
  getRecipeUnit: createGetter("recipeUnit"),
  getRecipeList: createGetter("recipeList"),
  getSearchProducts: createGetter("searchProducts"),
  getIngredientAlreadyAddedProduct: createGetter(
    "ingredientAlreadyAddedProduct"
  ),
  getIngredientCampaign: createGetter("ingredientCampaign"),
  getIngredients: createGetter("ingredients"),
  getBrandDistribution: createGetter("brandDistribution"),
  getIngredientsProduct: createGetter("ingredientsProduct"),
  getIngredientsCampaignData: createGetter("ingredientsCampaignData"),
  getUpdateIngredientsName: createGetter("updateIngredientsName"),
  getOperationStatus: createGetter("operationStatus"),
  getIngredientKeyword: createGetter("ingredientKeyword"),
  getPagination: createGetter("pagination"),
  getIngredientListForTable: createGetter("ingredientListForTable"),
};

const validateInput = (input) => {
  if (!Object.keys(input).length) {
    const { $t } = useNuxtApp();
    console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
    return false;
  }
  return true;
};

const handleError = (method, error) => {
  console.error(`${KEYS.KEY_NAMES.ERROR_IN} ${method}:`, error);
};

const validateParamsAndPayload = (params, payload) => {
  if (!Object.keys(params).length || !Object.keys(payload).length) {
    const { $t } = useNuxtApp();
    console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
    return false;
  }
  return true;
};

const actions = {

  /**
   * @deprecated
   *
   * @param commit
   * @param rootGetters
   * @param params
   * @returns {Promise<void>}
   */
  async fetchIngredientsAsync({ commit, rootGetters }, { params }) {
    if (!validateInput(params)) {
      return;
    }

    try {
      const { endpoint, baseURL } = getEndpointFromStore(rootGetters, "flite", "searchIngredients");
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      const { results, total } = response?.data ?? {};
      commit("SET_INGREDIENT_LIST_FOR_TABLE", { data: results.map(IngredientObject) });
      commit("SET_PAGINATION_TOTAL", { total });
    } catch (error) {
      handleError("searchIngredients", error);
    }
  },

  async getIngredientProductCountsAsync(
    { commit, rootGetters },
    { params, payload }
  ) {
    if (!validateParamsAndPayload(params, payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "multiGetIngredientProducts"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });
      commit("SET_INGREDIENT_PRODUCT_COUNT", response);
    } catch (error) {
      handleError("getIngredientProductCountsAsync", error);
    }
  },

  async getFeatureConfigAsync({ commit, rootGetters }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getFeatureConfig"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      commit("SET_FEATURE_CONFIG", response);
    } catch (error) {
      handleError("getFeatureConfigAsync", error);
    }
  },

  async getIngredientKeywordsAsync({ commit, rootGetters }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getingredientKeywords"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
        paramsSerializer: (params) => {
          return qs.stringify(params, { arrayFormat: "repeat" });
        },
      });
      commit("SET_INGREDIENT_KEYWORDS", response);
    } catch (error) {
      handleError("getIngredientKeywordsAsync", error);
    }
  },

  async getIngredientLockedCampaignAsync({ commit, rootGetters }, { payload }) {
    if (!validateInput(payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getIngredientLocked"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, { baseURL });
      commit("SET_INGREDIENT_LOCKED", response);
    } catch (error) {
      handleError("getIngredientLockedCampaignAsync", error);
    }
  },

  async getIngredientUnlockedCampaignAsync(
    { commit, rootGetters },
    { payload }
  ) {
    if (!validateInput(payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getIngredientUnlocked"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, { baseURL });
      commit("SET_INGREDIENT_UN_LOCKED", response);
    } catch (error) {
      handleError("getIngredientUnlockedCampaignAsync", error);
    }
  },

  async getRecipeUnitConfigAsync({ commit, rootGetters }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getRecipeUnitConfig"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      commit("SET_RECIPE_UNIT", response);
    } catch (error) {
      handleError("getRecipeUnitConfigAsync", error);
    }
  },

  async getRecipeAsync({ commit, rootGetters }, { params, isin }) {
    const { $t, $axios } = useNuxtApp();
    if (!Object.keys(params).length || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "icl",
        "getRecipe"
      );
      const modifiedEndpoint = `${endpoint}/${isin}`;
      const response = await $axios.get(modifiedEndpoint, {
        baseURL,
        params,
      });
      commit("SET_RECIPE_LIST", response);
    } catch (error) {
      handleError("getRecipeAsync", error);
    }
  },

  async getSearchProductsAsync({ commit, rootGetters }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "fss",
        "getSearchProducts"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      commit("SET_SEARCH_PRODUCTS", response);
    } catch (error) {
      handleError("getSearchProductsAsync", error);
    }
  },

  async getIngredientAlreadyAddedProductsAsync(
    { commit, rootGetters },
    { payload, params }
  ) {
    if (!validateParamsAndPayload(params, payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getIngredientMatchedProducts"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });
      commit("INGREDIENT_ALREADY_ADDED_PRODUCTS", response);
    } catch (error) {
      handleError("getIngredientAlreadyAddedProductsAsync", error);
    }
  },

  async getIngredientsCampaignAsync({ commit, rootGetters }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "fmp",
        "getIngredientsCampaign"
      );
      commit("RESET_INGREDIENT_CAMPAIGN");
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      commit("SET_INGREDIENT_CAMPAIGN", response);
    } catch (error) {
      handleError("getIngredientsCampaignAsync", error);
    }
  },

  async getIngredientAsync({ commit, rootGetters }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "icl",
        "getIngredient"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      commit("SET_INGREDIENTS", response);
    } catch (error) {
      handleError("getIngredientAsync", error);
    }
  },

  async getBrandDistributionAsync(
    { commit, rootGetters },
    { params, payload }
  ) {
    if (!validateParamsAndPayload(params, payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getIngredientBrands"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });
      commit("SET_BRAND_DISTRIBUTION", response);
    } catch (error) {
      handleError("getBrandDistributionAsync", error);
    }
  },

  async getIngredientProductMatchesAsync(
    { commit, rootGetters },
    { params, payload }
  ) {
    if (!validateParamsAndPayload(params, payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "getIngredientProducts"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });
      commit("SET_INGREDIENT_PRODUCTS", response);
    } catch (error) {
      handleError("getIngredientProductMatchesAsync", error);
    }
  },

  async saveIngredientCampaignDataAsync(
    { commit, rootGetters },
    { params, payload }
  ) {
    if (!validateParamsAndPayload(params, payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "postIngredientCampaign"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });
      commit("SET_INGREDIENT_CAMPAIGN_DATA", response);
    } catch (error) {
      handleError("saveIngredientCampaignDataAsync", error);
    }
  },

  async postUpdateIngredientNameAsync({ commit, rootGetters }, { payload }) {
    if (!validateInput(payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "postUpdateOperation"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, { baseURL });
      commit("SET_UPDATE_INGREDIENT_NAME", response);
    } catch (error) {
      handleError("postUpdateIngredientNameAsync", error);
    }
  },

  async getOperationStatusAsync({ commit, rootGetters }, { operationId }) {
    const { $t, $axios } = useNuxtApp();
    if (!operationId) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "icl",
        "getOperationStatus"
      );
      const modifiedEndpoint = endpoint.replace("{opId}", operationId);
      const response = await $axios.get(modifiedEndpoint, { baseURL });
      commit("SET_OPERATION_STATUS", response);
    } catch (error) {
      handleError("getOperationStatusAsync", error);
    }
  },

  async postIngredientsKeywordAsync({ commit, rootGetters }, { payload }) {
    if (!validateInput(payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "flite",
        "postingredientKeywords"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, { baseURL });
      commit("SET_INGREDIENT_KEYWORD", response);
    } catch (error) {
      handleError("postIngredientsKeywordAsync", error);
    }
  },
  async getIngredientsCampaignDataAsync({ rootGetters }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointFromStore(
        rootGetters,
        "fmp",
        "getIngredientsCampaign"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, { baseURL, params });
      return response.data;
    } catch (error) {
      handleError("getIngredientsCampaignDataAsync", error);
    }
  },

  async getAllFoodItemIsinAsync({ rootGetters, commit }, { isins, lang }) {
    const { $t, $axios } = useNuxtApp();
    if (!lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"]("fss", "getAllFoodItemIsin");
      const baseURL = rootGetters["config/getClientConfig"]("fss").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { isins: isins.join(","), country: lang.split("-")[1] },
      });
      return response.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getAllFoodItemIsinAsync", error);
    }
  },

  async getProductsAsync({ rootGetters, commit }, { gtins, lang }) {
    const { $t, $axios } = useNuxtApp();
    if (!lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"]("fss", "getProducts");
      const baseURL = rootGetters["config/getClientConfig"]("fss").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { gtins: gtins.join(","), country: lang.split("-")[1] },
      });
      return response.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getProductsAsync", error);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
