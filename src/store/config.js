import URLFormatter from "@/services/URLFormatter";
import utils from "../mixins/utils";
import { useNuxtApp } from "#app";
import axios from "axios";
import { useConfig } from "../composables/useConfig.js";

/**
 * @module store/config
 *
 */
const state = () => ({
  config: {},
  sidebarNavs: [],
  features: {},
});

/**
 * @type {Object}
 * @property {function} SET_CONFIG - Sets the configuration in the Vuex state.
 * @property {object} SET_CONFIG.state - The Vuex state object.
 * @property {any} SET_CONFIG.config - The new configuration to set.
 * @property {function} SET_SIDEBAR_NAVS - Sets the sidebar navigation items in the Vuex state,if the provided array of navigation items is not empty.
 * @property {object} SET_SIDEBAR_NAVS.state - The Vuex state object.
 * @property {Array} SET_SIDEBAR_NAVS.navs - The array of sidebar navigation items.
 * @property {function} SET_FEATURES - Sets the features in the Vuex state, if features are provided.
 * @property {object} SET_FEATURES.state - The Vuex state object.
 * @property {any} SET_FEATURES.features - The features object or value to set.
 */

const mutations = {
  SET_CONFIG(state, config) {
    state.config = config;
  },
  SET_SIDEBAR_NAVS(state, navs) {
    if (navs?.length) {
      state.sidebarNavs = navs;
    }
  },
  SET_FEATURES(state, features) {
    if (features) {
      state.features = features;
    }
  },
};

/**
 * Actions for managing asynchronous data fetching and mutations in Vuex store.
 * @type {Object}
 * @property {function} fetchConfigAsync - Fetches configuration asynchronously and commits it to Vuex state.
 *   @property {object} context - The Vuex action context.
 *   @property {Function} context.commit - The commit function to trigger mutations.
 * @property {function} fetchSidebarNavsAsync - Fetches sidebar navigation items asynchronously and commits them to Vuex state.
 *   @property {object} context - The Vuex action context.
 *   @property {Function} context.commit - The commit function to trigger mutations.
 * @property {function} fetchFeaturesAsync - Fetches features asynchronously and commits them to Vuex state if not already fetched or forced refresh is requested.
 *   @property {object} context - The Vuex action context.
 *   @property {Function} context.commit - The commit function to trigger mutations.
 *   @property {object} context.state - The current state of the Vuex module.
 *   @property {object} context.rootGetters - The root getters of the Vuex store.
 *   @property {object} payload - Payload containing additional parameters.
 *   @property {boolean} payload.isHotRefresh - Flag indicating whether to force refresh features.
 */
const actions = {
  async fetchConfigAsync({ commit,state }) {
    if (!Object.keys(state.config).length) {
      try {
        const { config } = useConfig();
        const endpoint = `${config.value.BASE_PATH_WEB_BACKEND}${config.value.INNER_API_ENDPOINT_CONFIG_INIT}`;
        const response = await axios.get(endpoint);
        commit("SET_CONFIG", response?.data);
      } catch {}
    }
  },

  async fetchSidebarNavsAsync({ commit,state }) {
    if (!Object.keys(state.sidebarNavs).length) {
      try {
        const { config } = useConfig();
        const endpoint = `${config.value.BASE_PATH_WEB_BACKEND}${config.value.INNER_API_ENDPOINT_CONFIG_MENUS}`;
        const response = await axios.get(endpoint);
        commit("SET_SIDEBAR_NAVS", response?.data);
      } catch {}
    }
  },

  async fetchFeaturesAsync({ commit, state, rootGetters }, { isHotRefresh }) {
    if (Object.keys(state.features).length && !isHotRefresh) {
      return;
    }

    if (!rootGetters["userData/getProject"]?.id) {
      return;
    }

    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getFeatureConfig"
      );
      const { $axios } = useNuxtApp();
      const baseUrl = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, {
        baseURL: baseUrl,
      });

      if (response) {
        commit("SET_FEATURES", response?.data);
      }
    } catch (error) {}
  },
};

/**
 * Getters for accessing state properties in Vuex store.
 * @type {Object}
 * @property {Function} getConfig - Getter function to retrieve the configuration object.
 *   @property {Object} getConfig.state - The Vuex state object.
 *   @returns {any} The configuration object stored in state.
 * @property {Function} getClientConfig - Getter function to retrieve client-specific configuration.
 *   @property {Object} getClientConfig.state - The Vuex state object.
 *   @property {string} getClientConfig.client - The client identifier.
 *   @returns {Object} The configuration object for the specified client.
 * @property {Function} getClientEndpoint - Getter function to retrieve client-specific endpoint URL.
 *   @property {Object} getClientEndpoint.state - The Vuex state object.
 *   @property {string} getClientEndpoint.state.client - The client identifier.
 *   @property {string} getClientEndpoint.state.endpoint - The endpoint key.
 *   @property {Object} [replaceMap={}] - Optional map for URL template replacement.
 *   @returns {string} The formatted endpoint URL.
 * @property {Function} getICAMeal - Getter function to retrieve ICAMeal endpoint URL.
 *   @property {Object} getICAMeal.state - The Vuex state object.
 *   @property {string} getICAMeal.state.client - The client identifier.
 *   @property {string} getICAMeal.state.endpoint - The endpoint key.
 *   @property {string} getICAMeal.state.gtin - The GTIN (Global Trade Item Number).
 *   @returns {string} The formatted ICAMeal endpoint URL.
 * @property {Function} getAppSocialLinks - Getter function to retrieve social media links for an app.
 *   @property {Object} getAppSocialLinks.state - The Vuex state object.
 *   @property {string} getAppSocialLinks.state.app - The app identifier.
 *   @property {string} getAppSocialLinks.state.socialMedia - The social media platform.
 *   @returns {string} The social media link for the specified app and platform.
 * @property {Function} getAppDownloadLinks - Getter function to retrieve download links for an app.
 *   @property {Object} getAppDownloadLinks.state - The Vuex state object.
 *   @property {string} getAppDownloadLinks.state.app - The app identifier.
 *   @property {string} getAppDownloadLinks.state.store - The store identifier.
 *   @returns {string} The download link for the specified app and store.
 * @property {Function} getWebsiteLinks - Getter function to retrieve website links by ID.
 *   @property {Object} getWebsiteLinks.state - The Vuex state object.
 *   @property {string} getWebsiteLinks.state.id - The website link identifier.
 *   @returns {string} The website link for the specified ID.
 * @property {Function} getFRWebsiteLinks - Getter function to retrieve French website links by ID.
 *   @property {Object} state - The Vuex state object.
 *   @property {string} id - The website link identifier.
 *   @returns {string} The French website link for the specified ID.
 * @property {Function} getSharableLinks - Getter function to retrieve sharable links for social media.
 *   @property {Object} getSharableLinks.state - The Vuex state object.
 *   @property {string} getSharableLinks.state.socialMedia - The social media platform.
 *   @property {Object} [replaceMap={}] - Optional map for URL template replacement.
 *   @returns {string} The formatted sharable link for the specified social media.
 * @property {Function} getSidebarNavs - Getter function to retrieve sidebar navigation items.
 *   @property {Object} getSidebarNavs.state - The Vuex state object.
 *   @returns {Array} The array of sidebar navigation items stored in state.
 */

const getters = {
  getConfig: (state) => {
    return state.config;
  },
  getClientConfig: (state) => (client) => {
    try {
      return state.config.clients[client];
    } catch (err) {
      utils.methods.printConsole("Missing client config for key: " + client);
      return {};
    }
  },
  getClientEndpoint:
    (state) =>
    (client, endpoint, replaceMap = {}) => {
      try {
        let template = state.config.clients[client].endpoints[endpoint];
        return URLFormatter.urlFormat(template, replaceMap);
      } catch (e) {
        utils.methods.printConsole(
          "Missing client's endpoint config for key: " + client
        );
        return "";
      }
    },
  getSidebarNavs: (state) => {
    if (!state.sidebarNavs || process.server) {
      return [];
    }

    return state.sidebarNavs
      .map((item) => ({
        ...item,
        isInSidebar: state.features[item.uniqueId] || false,
      }))
      .filter(({ isInSidebar }) => isInSidebar);
  },
  getFeatures: (state) => {
    return state.features;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
