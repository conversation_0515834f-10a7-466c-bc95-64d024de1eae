export const useSearchStore = defineStore("Search", () => {
  const searchContextsRef = ref({
    global: {
      str: "",
      emitQueryParam: true,
    },
    detailsPage: {
      str: "",
      emitQueryParam: false,
    }
  });

  const isSearchEnableRef = ref(false);
  const isSearchEnabledContexts = ref({
    global: false,
    detailsPage: false
  });

  const searchContexts = computed(() => searchContextsRef);
  const searchQuery = computed(() => searchContextsRef.value.global);
  const isSearchEnabled = computed(() => isSearchEnableRef);

  const getSearchQuery = (context = 'global') => {
    return computed(() => searchContextsRef.value[context] || reactive({ str: "", emitQueryParam: true }));
  };

  const getIsSearchEnabled = (context = 'global') => {
    return computed(() => isSearchEnabledContexts.value[context] || false);
  };

  const setSearchQuery = (value, { emitQueryParam = true, context = 'global' } = {}) => {
    const trimmed = value?.trim() ?? '';

    searchContextsRef.value = {
      ...searchContextsRef.value,
      ...{
        [context]: {
          str: value,
          emitQueryParam,
        }
      },
    };

    isSearchEnableRef.value = !!trimmed;
    isSearchEnabledContexts.value[context] = !!trimmed;
  };

  return {
    searchContextsRef,
    isSearchEnabledContexts,

    searchContexts,
    searchQuery,
    isSearchEnabled,
    setSearchQuery,
    getSearchQuery,
    getIsSearchEnabled,
  };
});
