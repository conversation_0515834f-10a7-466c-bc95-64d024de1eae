import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useSimpleCustomFetch } from '../composables/useCustomFetch.js';
import { STORAGE_KEY } from '../сonstants/storage-key.js';
import { 
  getItemFromLocalStorage, 
  setItemToLocalStorage, 
  removeItemFromLocalStorage 
} from '../services/local-storage-service.js';

export const useUserDataStore = defineStore('userData', () => {
  // State
  const project = ref(null);
  const projectList = ref([]);
  const langs = ref(null);
  const projectPermissions = ref(null);
  const projectUsers = ref([]);

  // Computed getters
  const getProject = computed(() => project.value);
  const getProjectList = computed(() => projectList.value);
  const getProjectById = computed(() => (id) => {
    return projectList.value?.find(project => project.id === id);
  });
  
  const getDefaultLang = computed(() => {
    return langs.value?.defaultLang ||
      getItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS)?.defaultLang ||
      (project.value?.id === "roche_main" ? "fr-FR" : "en-US");
  });
  
  const getAvailableLangs = computed(() => {
    return langs.value?.availableLangs ||
      getItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS)?.availableLangs ||
      null;
  });
  
  const getProjectPermissions = computed(() => {
    return projectPermissions.value?.permissions;
  });
  
  const getProjectUsers = computed(() => projectUsers.value);

  // Actions
  const setProject = (projectData) => {
    project.value = projectData;
    setItemToLocalStorage(STORAGE_KEY.USER_DATA_PROJECT, {
      id: projectData.id,
      displayName: projectData.displayName
    });
  };

  const setProjectList = (list) => {
    projectList.value = list;
  };

  const setLangs = (langsData) => {
    langs.value = langsData;
    setItemToLocalStorage(STORAGE_KEY.USER_DATA_LANGS, langsData);
  };

  const setDefaultLang = (lang) => {
    if (!langs.value) {
      langs.value = {};
    }
    langs.value.defaultLang = lang;
    localStorage.setItem(
      STORAGE_KEY.USER_DATA_LANGS,
      JSON.stringify({ defaultLang: lang })
    );
  };

  const setProjectUsers = (users) => {
    projectUsers.value = users;
  };

  const setPermissions = (permissions) => {
    projectPermissions.value = permissions;
  };

  const clear = () => {
    project.value = null;
    projectList.value = [];
    langs.value = null;
    projectPermissions.value = null;
    projectUsers.value = [];
    removeItemFromLocalStorage(STORAGE_KEY.USER_DATA_PROJECT);
    removeItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS);
  };

  const updateLanguageOnProjectChange = () => {
    const newLang = project.value?.id === "roche_main" ? "fr-FR" : "en-US";
    setDefaultLang(newLang);
  };

  const postProjectAsync = async (payload) => {
    try {
      await useSimpleCustomFetch('', {
        method: 'POST',
        body: payload
      }, 'flite', 'postProject');
    } catch (error) {
      console.error('[IQ][useUserDataStore] Error in postProjectAsync:', error);
      throw error;
    }
  };

  const fetchLangsAsync = async () => {
    try {
      const response = await useSimpleCustomFetch('', {}, 'flite', 'getLangs');
      if (response) {
        setLangs(response);
      }
    } catch (error) {
      console.error('[IQ][useUserDataStore] Error fetching languages:', error);
      throw error;
    }
  };

  return {
    // State
    project,
    projectList,
    langs,
    projectPermissions,
    projectUsers,

    // Computed
    getProject,
    getProjectList,
    getProjectById,
    getDefaultLang,
    getAvailableLangs,
    getProjectPermissions,
    getProjectUsers,

    // Actions
    setProject,
    setProjectList,
    setLangs,
    setDefaultLang,
    setProjectUsers,
    setPermissions,
    clear,
    updateLanguageOnProjectChange,
    postProjectAsync,
    fetchLangsAsync,
  };
});
