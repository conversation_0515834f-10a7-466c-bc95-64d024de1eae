import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";

export const useBannerStore = defineStore("Banner", () => {
  const bannerListRef = ref([]);
  const editBannerDataRef = ref({});
  const isLoadingRef = ref(true);

  const bannerList = computed(() => bannerListRef.value);
  const editBannerData = computed(() => editBannerDataRef.value);
  const isLoading = computed(() => isLoadingRef.value);

  /**
   * Retrieves banner data from the server based on the language parameter.
   *
   * @param {Object} params - Contains the `lang` parameter.
   * @returns {Promise<void>} Resolves when the data is fetched.
   */
  const getBannerListAsync = async ({ lang }) => {
    try {
      isLoadingRef.value = true;
      const response = await useSimpleCustomFetch(
        "",
        { params: { lang: lang } },
        "flite",
        "getBannerList"
      );
      bannerListRef.value = response || [];
    } catch (error) {
      console.error("[IQ][useBannerStore] Error in getBannerListAsync", error);
    } finally {
      isLoadingRef.value = false;
    }
  };

  /**
   * Retrieves specific banner data for editing from the server.
   *
   * @param {Object} params - Contains `lang` and `uuid`.
   * @returns {Promise<void>} Resolves when the data is fetched.
   */
  const getEditBannerAsync = async ({ lang, uuid }) => {
    if (!lang || !uuid) {
      console.error("[IQ][useBannerStore] Missing required parameters for getEditBannerAsync");
      return;
    }

    try {
      isLoadingRef.value = true;
      const langParam = lang ? { lang: lang.replace("fr", "fr-FR") } : {};
      const response = await useSimpleCustomFetch(
        "",
        { params: langParam },
        "flite",
        "getBannerList",
        uuid
      );
      editBannerDataRef.value = response || {};
      return response;
    } catch (error) {
      console.error("[IQ][useBannerStore] Error in getEditBannerAsync", error);
      throw error;
    } finally {
      isLoadingRef.value = false;
    }
  };

  /**
   * Updates an existing banner data on the server.
   *
   * @param {Object} params - Contains `payload`, `uuid`, and `lang`.
   * @returns {Promise<void>} Resolves when the data is updated.
   */
  const patchBannerAsync = async ({ payload, uuid, lang }) => {
    if (!payload || !uuid) {
      console.error("[IQ][useBannerStore] Missing required parameters for patchBannerAsync");
      return;
    }

    try {
      await useSimpleCustomFetch(
        "",
        {
          method: "PATCH",
          body: payload,
          params: { lang: lang },
        },
        "flite",
        "getBannerList",
        uuid
      );
      return true;
    } catch (error) {
      console.error("[IQ][useBannerStore] Error in patchBannerAsync", error);
      throw error;
    }
  };

  /**
   * Deletes a banner from the server.
   *
   * @param {Object} params - Contains `uuid` and `lang`.
   * @returns {Promise<void>} Resolves when the banner is deleted.
   */
  const deleteBannerAsync = async ({ uuid, lang }) => {
    if (!uuid) {
      console.error("[IQ][useBannerStore] Missing required parameters for deleteBannerAsync");
      return;
    }

    try {
      await useSimpleCustomFetch(
        "",
        {
          method: "DELETE",
          params: { lang: lang },
        },
        "flite",
        "getBannerList",
        uuid
      );
      return true;
    } catch (error) {
      console.error("[IQ][useBannerStore] Error in deleteBannerAsync", error);
      throw error;
    }
  };

  /**
   * Posts new banner data to the server.
   *
   * @param {Object} params - Contains `payload` and `lang`.
   * @returns {Promise<void>} Resolves when the banner is created.
   */
  const postBannerAsync = async ({ payload, lang }) => {
    if (!payload) {
      console.error("[IQ][useBannerStore] Missing required parameters for postBannerAsync");
      return;
    }

    try {
      await useSimpleCustomFetch(
        "",
        {
          method: "POST",
          body: payload,
          params: { lang: lang },
        },
        "flite",
        "getBannerList"
      );
      return true;
    } catch (error) {
      console.error("[IQ][useBannerStore] Error in postBannerAsync", error);
      throw error;
    }
  };

  return {
    // State
    bannerListRef,
    editBannerDataRef,
    isLoadingRef,

    // Getters
    bannerList,
    editBannerData,
    isLoading,

    // Actions
    getBannerListAsync,
    getEditBannerAsync,
    patchBannerAsync,
    deleteBannerAsync,
    postBannerAsync,
  };
});
