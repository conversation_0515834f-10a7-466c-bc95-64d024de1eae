import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useSimpleCustomFetch } from '../composables/useCustomFetch.js';

export const useEditSearchStore = defineStore('editSearch', () => {
  // State
  const editSearch = ref({});

  // Computed
  const getEditSearch = computed(() => editSearch.value);

  // Actions
  const getEditSearchAsync = async () => {
    try {
      const response = await useSimpleCustomFetch('', {}, 'flite', 'getEditSearch');

      if (response) {
        editSearch.value = response;
      }
      return response;
    } catch (error) {
      console.error('[IQ][useEditSearchStore] Error getting edit search:', error);
      throw error;
    }
  };

  const postEditSearchAsync = async (payload, user) => {
    if (!Object.keys(payload).length || !user) {
      console.error('[IQ][useEditSearchStore] Missing required parameters');
      return;
    }

    try {
      const params = { user };
      const response = await useSimpleCustomFetch('', {
        method: 'POST',
        body: payload,
        params
      }, 'flite', 'getEditSearch');

      return response;
    } catch (error) {
      console.error('[IQ][useEditSearchStore] Error posting edit search:', error);
      throw error;
    }
  };

  const resetStore = () => {
    editSearch.value = {};
  };

  return {
    // State
    editSearch,

    // Computed
    getEditSearch,

    // Actions
    getEditSearchAsync,
    postEditSearchAsync,
    resetStore,
  };
});
