import { ref } from "vue";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";

export const useArticleItemStore = defineStore("ArticleItem", () => {
  const isLoadingRef = ref(true);
  const initialPayloadRef = ref(null);
  const hasChangesRef = ref(false);
  const uuidRef = ref("");
  const titleRef = ref("");
  const orderRef = ref(null);
  const lastUpdateRef = ref(null);
  const createDateRef = ref(null);
  const imageRef = ref("");
  const imageResponseUrlRef = ref("");
  const sourceRef = ref("");
  const fliteSourceRef = ref("");
  const langRef = ref("");
  const pagesRef = ref([]);
  const categoryRef = ref("");
  const stateRef = ref(""); // published | unpublished
  const isNewRef = ref(false);
  const hasVideoRef = ref(false);

  const isLoading = computed(() => isLoadingRef);
  const uuid = computed(() => uuidRef);
  const title = computed(() => titleRef);
  const order = computed(() => orderRef);
  const lastUpdate = computed(() => lastUpdateRef);
  const createDate = computed(() => createDateRef);
  const image = computed(() => imageRef);
  const imageResponseUrl = computed(() => imageResponseUrlRef);
  const source = computed(() => sourceRef);
  const fliteSource = computed(() => fliteSourceRef);
  const lang = computed(() => langRef);
  const pages = computed(() => pagesRef);
  const category = computed(() => categoryRef);
  const state = computed(() => stateRef);
  const isNew = computed(() => isNewRef);
  const hasVideo = computed(() => hasVideoRef);
  const hasChanges = computed(() =>  hasChangesRef);

  const resetStore = () => {
    isLoadingRef.value  = true;
    initialPayloadRef.value = null;
    hasChangesRef.value = false;
    uuidRef.value = "";
    titleRef.value = "";
    orderRef.value = null;
    lastUpdateRef.value = null;
    createDateRef.value = null;
    imageRef.value = "";
    imageResponseUrlRef.value = "";
    sourceRef.value = "";
    fliteSourceRef.value = "";
    langRef.value = "";
    pagesRef.value = [];
    categoryRef.value = "";
    stateRef.value = "";
    isNewRef.value = false;
    hasVideoRef.value = false;
  };

  const getPayload = () => {
    return {
      uuid: uuidRef.value,
      title: titleRef.value,
      order: orderRef.value,
      image: imageResponseUrlRef.value ? imageResponseUrlRef.value.replace(/\?.*/, "") : imageRef.value,
      source: sourceRef.value,
      fliteSource: fliteSourceRef.value,
      pages: pagesRef.value,
      category: categoryRef.value,
      state: stateRef.value,
      isNew: isNewRef.value,
      hasVideo: hasVideoRef.value,
    };
  };

  const setInitialPayload = () => {
    initialPayloadRef.value = getPayload();
  };

  const setArticleDetailsForPreview = ({title, image, category, pages}) => {
    titleRef.value = title;
    imageRef.value = image;
    categoryRef.value = category;
    pagesRef.value = pages;
  };

  const getArticleUUIDAsync = async ({ lang }) => {
    const fetchUUID = async () => {
      const response = await useSimpleCustomFetch("", { params: { lang } }, "flite", "articleDataUUID");
      uuidRef.value = response?.uuid || "";
    };

    try {
      await fetchUUID();
    } catch (e) {
      console.error("[IQ][useArticleItemStore] Cannot fetch article UUID", e);
      await fetchUUID();
    }
  };

  const uploadZipFileAsync = async ({ lang, formData }) => {
    try {
      const response = await useSimpleCustomFetch(
        "",
        {
          method: "POST",
          body: formData,
          params: { lang },
        },
        "flite",
        "articleData",
        `${uuidRef.value}/zip`,
      );

      pagesRef.value = response?.pages?.map((item) => `https://${item}`) || [];
      sourceRef.value =  response?.url ? `https://${response.url}` : "";
      fliteSourceRef.value = response?.fliteUrl ? `https://${response.fliteUrl}` : "";
    } catch (e) {
      console.error("[IQ][useArticleItemStore] Cannot upload zip file", e);
      throw new Error(e);
    }
  };

  const postArticleAsync = async ({ lang }) => {
    try {
      setInitialPayload();
      hasChangesRef.value = false;
      await useSimpleCustomFetch(
        "",
        {
          method: "POST",
          body: getPayload(),
          params: { lang },
        },
        "flite",
        "articleData"
      );
      return true;
    } catch (e) {
      console.error("[IQ][useArticleItemStore] Cannot post article", e);
      throw new Error(e);
    }
  };

  const patchArticleAsync = async ({ lang }) => {
    try {
      setInitialPayload();
      hasChangesRef.value = false;
      await useSimpleCustomFetch(
        "",
        {
          method: "PATCH",
          body: getPayload(),
          params: { lang },
        },
        "flite",
        "articleData",
        uuidRef.value,
      );
      return true;
    } catch (e) {
      console.error("[IQ][useArticleItemStore] Cannot post article", e);
      throw new Error(e);
    }
  };

  const getArticleAsync = async ({ lang, uuid }) => {
    try {
      isLoadingRef.value = true;
      const response = await useSimpleCustomFetch(
        "",
        {
          params: { lang },
        },
        "flite",
        "articleData",
        uuid,
      );

      if (response?.uuid) {
        uuidRef.value = response.uuid;
        titleRef.value = response.title || "";
        orderRef.value = response.order || null;
        imageRef.value = response.image || "";
        lastUpdateRef.value = response.lastUpdate || "";
        createDateRef.value = response.createDate || "";
        sourceRef.value = response.source || "";
        fliteSourceRef.value = response.fliteSource || "";
        langRef.value = response.lang || "";
        pagesRef.value = response.pages || [];
        categoryRef.value = response.category || "";
        stateRef.value = response.state || "";
        isNewRef.value = response.isNew || false;
        hasVideoRef.value = response.hasVideo || false;
      }

      setInitialPayload();
      isLoadingRef.value = false;
      return !!response?.uuid;
    } catch (e) {
      console.error("[IQ][useArticleItemStore] Cannot get article by uuid:", uuid, e);
      isLoadingRef.value = false;
      throw new Error(e);
    }
  };

  const deleteArticleAsync = async ({ lang }) => {
    try {
      await useSimpleCustomFetch(
        "",
        {
          method: "DELETE",
          params: { lang },
        },
        "flite",
        "articleData",
        uuidRef.value,
      );
      return true;
    } catch (e) {
      console.error("[IQ][useArticleItemStore] Cannot delete article by uuid:", uuid, e);
      throw new Error(e);
    }
  };

  watch(() => getPayload(), (val) => {
    const initial = initialPayloadRef.value;

    const findChanges = () => {
      if (!initial) return false;
      if (titleRef.value !== initial.title) return true;
      if (imageRef.value !== initial.image) return true;
      if (JSON.stringify(pagesRef.value) !== JSON.stringify(initial.pages)) return true;
      if (categoryRef.value !== initial.category) return true;
      if (stateRef.value !== initial.state) return true;
      if (isNewRef.value !== initial.isNew) return true;
      if (hasVideoRef.value !== initial.hasVideo) return true;
      return false;
    };

    hasChangesRef.value = findChanges();
  }, { immediate: true });

  return {
    isLoading,
    uuid,
    title,
    order,
    lastUpdate,
    createDate,
    image,
    imageResponseUrl,
    source,
    fliteSource,
    lang,
    pages,
    category,
    state,
    isNew,
    hasVideo,
    hasChanges,

    getPayload,
    setInitialPayload,
    getArticleAsync,
    getArticleUUIDAsync,
    uploadZipFileAsync,
    postArticleAsync,
    patchArticleAsync,
    deleteArticleAsync,
    setArticleDetailsForPreview,
    resetStore,
  };
});
