import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";

export const useIngredientStore = defineStore("ingredient", () => {
  // State
  const ingredientReferenceProduct = ref([]);
  const isLoading = ref(false);

  // Getters
  const getIngredientReferenceProduct = computed(
    () => ingredientReferenceProduct
  );
  const getIsLoading = computed(() => isLoading);

  // Actions
  const getIngredientReferenceProductDataAsync = async (id, lang) => {
    if (!id || !lang) {
      console.error(
        "[IQ][useIngredientStore] Missing required parameters for getIngredientReferenceProductDataAsync"
      );
      return;
    }

    try {
      isLoading.value = true;

      const response = await useSimpleCustomFetch(
        "",
        { params: { lang } },
        "flite",
        "getIngredientReferenceProduct",
        id
      );

      if (response) {
        ingredientReferenceProduct.value = response;
        return response;
      }
    } catch (error) {
      console.error(
        "[IQ][useIngredientStore] Error in getIngredientReferenceProductDataAsync:",
        error
      );
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  const resetStore = () => {
    ingredientReferenceProduct.value = [];
    isLoading.value = false;
  };

  return {
    // State
    ingredientReferenceProduct,
    isLoading,

    // Getters
    getIngredientReferenceProduct,
    getIsLoading,

    // Actions
    getIngredientReferenceProductDataAsync,
    resetStore,
  };
});
