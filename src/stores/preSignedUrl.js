import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useSimpleCustomFetch } from '../composables/useCustomFetch.js';

export const usePreSignedUrlStore = defineStore('preSignedUrl', () => {
  // State
  const preSignedUrl = ref({});

  // Computed
  const getPreSignedUrl = computed(() => preSignedUrl.value);

  // Actions
  const getPreSignedImageUrlAsync = async (isin, params) => {
    if (!isin || !Object.keys(params).length) {
      console.error('[IQ][usePreSignedUrlStore] Missing required parameters');
      return;
    }

    try {
      const response = await useSimpleCustomFetch('', {
        params: { ...params }
      }, 'icl', 'getPreSignedUrl', isin, '{isin}');

      if (response) {
        preSignedUrl.value = response;
      }
      return response;
    } catch (error) {
      console.error('[IQ][usePreSignedUrlStore] Error getting pre-signed URL:', error);
      throw error;
    }
  };

  const resetStore = () => {
    preSignedUrl.value = {};
  };

  return {
    // State
    preSignedUrl,

    // Computed
    getPreSignedUrl,

    // Actions
    getPreSignedImageUrlAsync,
    resetStore,
  };
});
