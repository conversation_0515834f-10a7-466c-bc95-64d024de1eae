import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";

export const useRecipesStore = defineStore("Recipes", () => {

  const getSchedulesRecipesAsync = async () => {
    try {
      const response = await useSimpleCustomFetch("", {}, "flite", "getRecipeSchedule");
      return response;
    } catch (e) {
      console.error("[IQ][useRecipesStore] Cannot fetch schedules recipes", e);
      return [];
    }
  };

  return {
    getSchedulesRecipesAsync,
  };
})
