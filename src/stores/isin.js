import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useSimpleCustomFetch } from '../composables/useCustomFetch.js';

export const useIsinStore = defineStore('isin', () => {
  // State
  const isin = ref([]);

  // Computed
  const getISIN = computed(() => isin.value);

  // Actions
  const getNewISINsAsync = async (lang, payload) => {
    if (!lang || !Object.keys(payload).length) {
      console.error('[IQ][useIsinStore] Missing required parameters');
      return;
    }

    try {
      const response = await useSimpleCustomFetch('', {
        method: 'POST',
        params: { langs: lang, ...payload }
      }, 'flite', 'getNewIsins');

      if (response) {
        isin.value = response;
      }
      return response;
    } catch (error) {
      console.error('[IQ][useIsinStore] Error getting new ISINs:', error);
      throw error;
    }
  };

  const resetStore = () => {
    isin.value = [];
  };

  return {
    // State
    isin,

    // Computed
    getISIN,

    // Actions
    getNewISINsAsync,
    resetStore,
  };
});
