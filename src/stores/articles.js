import { computed, ref } from "vue";
import { storeToRefs } from "pinia";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";

export const useArticlesStore = defineStore("Articles", () => {
  const listRef = ref([]);
  const isLoadingRef = ref(true);
  const articlesCategoriesRef = ref([]);

  const dynamicHeroStore = useDynamicHeroStore();
  const { disabledArticleDataList } = storeToRefs(dynamicHeroStore);

  const articlesList = computed(() => listRef);
  const isArticleStoreLoading = computed(() => isLoadingRef);
  const articlesCategories = computed(() => articlesCategoriesRef);

  const getArticlesAsync = async (params) => {
    try {
      isLoadingRef.value = true;
      const response = await useSimpleCustomFetch("", { params }, "flite", "articleData");

      if (Array.isArray(response)) {
        const disabledList = new Set(disabledArticleDataList.value || []);

        response.forEach(({ content }) => {
          content?.forEach((item) => {
            item.usedInHero = disabledList.has(item.uuid);
          });
        });
      }

      listRef.value = response;
    } catch (e) {
      console.error("[IQ][useArticlesStore] Articles Store, get articles error.", e);
    } finally {
      isLoadingRef.value = false;
    }
  };

  const getArticlesCategoriesAsync = async (params) => {
    try {
      const response = await useSimpleCustomFetch("", { params }, "flite", "articleCategoriesData");
      articlesCategoriesRef.value = response || [];
    } catch (e) {
      console.error("[IQ][useArticlesStore] Cannot fetch articles categories", e);
    }
  };

  /**
   *
   * @param body {{name: "", order: ""}}
   * @returns {Promise<boolean>}
   */
  const postArticleCategoryAsync = async (body) => {
    try {
      await useSimpleCustomFetch("", {
        method: "POST",
        body,
      }, "flite", "articleCategoriesData");
      await getArticlesCategoriesAsync();
      return true;
    } catch (e) {
      console.error("[IQ][useArticlesStore] Cannot post new article category", e);
      return false;
    }
  };

  const changeCategoryNameAsync = async ({ name, order, uuid }) => {
    try {
      await useSimpleCustomFetch("", {
        method: "PATCH",
        body: {
          name,
          order,
        },
      }, "flite", "articleCategoriesData", uuid);
      await getArticlesAsync();
      return true;
    } catch (e) {
      console.error("[IQ][useArticlesStore] Cannot change article category name", e);
      return false;
    }
  };

  return {
    listRef,
    isLoadingRef,
    articlesList,
    articlesCategories,
    isArticleStoreLoading,

    getArticlesAsync,
    getArticlesCategoriesAsync,
    postArticleCategoryAsync,
    changeCategoryNameAsync,
  };
});
