import { computed, ref } from "vue";

export const useInnitAuthStore = defineStore("InnitAuth", () => {
  const authorizationTokenRef = ref(null);
  const tokenDataRef = ref(null);
  const isInnitAdminRef = ref(null);

  const authorizationToken = computed(() => authorizationTokenRef);
  const tokenData = computed(() => tokenDataRef);
  const isInnitAdmin = computed(() => isInnitAdminRef);
  const isReady = computed(() => !!tokenDataRef?.value?.access_token);

  const setAuthorizationToken = (token) => authorizationTokenRef.value = token;
  const setTokenData = (data) => tokenDataRef.value = data;
  const setInnitAdmin = (val) => isInnitAdminRef.value = val;

  return {
    authorizationTokenRef,
    tokenDataRef,
    isInnitAdminRef,

    authorizationToken,
    tokenData,
    isInnitAdmin,
    isReady,

    setAuthorizationToken,
    setTokenData,
    setInnitAdmin,
  };
});
