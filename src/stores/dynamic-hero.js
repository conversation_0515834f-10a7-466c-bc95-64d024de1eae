import { computed, ref } from "vue";
import { KEYS } from "@/сonstants/keys.js";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";

export const useDynamicHeroStore = defineStore("DynamicHero", () => {
  const dynamicHeroData = ref([]);
  const isLoading = ref(false);
  const editDynamicHeroData = ref([]);
  const dynamicHeroID = ref("");
  const postDynamicHeroData = ref([]);
  const disabledArticleDataList = ref([]);

  const { extractISIN } = useCommonUtils();

  const dynamicHeroDataList = computed(() => dynamicHeroData);
  const editDynamicHeroDataList = computed(() => editDynamicHeroData);
  const isLoadingData = computed(() => isLoading);
  const dynamicHeroUUIDData = computed(() => dynamicHeroID);
  const postDynamicHeroDataList = computed(() => postDynamicHeroData);
  const disabledArticleDataListGetter = computed(() => disabledArticleDataList);

  const handleError = (method, error) => {
    console.error(`${KEYS.KEY_NAMES.ERROR_IN} ${method}:`, error);
  };

  const logMissingParameters = () => {
    console.error(`${KEYS.KEY_NAMES.MISSING_REQUIRED_PARAMETERS}`);
  };

  const getDynamicHeroListDataAsync = async ({ lang }) => {
    if (!lang) {
      logMissingParameters();
      return;
    }
    try {
      const response = await useSimpleCustomFetch("", {}, "flite", "getDynamicHeroList");
      if (!response) return;
      dynamicHeroData.value = response;
      disabledArticleDataList.value = response
        .filter(({ state, publishDate, data }) =>
          [KEYS.KEY_NAMES.SCHEDULED, KEYS.KEY_NAMES.LIVE].includes(state) &&
          publishDate &&
          data?.ctaLink
        )
        .map(({ data }) => extractISIN(data.ctaLink))
        .filter(Boolean);
    } catch (error) {
      handleError("getDynamicHeroListDataAsync", error);
    }
  };
  const getEditPageDynamicHeroDataAsync = async ({ lang, uuid }) => {
    if (!lang || !uuid) {
      logMissingParameters();
      return;
    }

    try {
      const response = await useSimpleCustomFetch(
        "",
        { params: { lang } },
        "flite",
        "getDynamicHeroList",
        uuid
      );
      editDynamicHeroData.value = response;
    } catch (error) {
      handleError("getEditPageDynamicHeroDataAsync", error);
    }
  };
  const deleteDynamicHeroDataAsync = async ({ uuid }) => {
    if (!uuid) {
      logMissingParameters();
      return;
    }

    try {
      isLoading.value = true;
      await useSimpleCustomFetch(
        "",
        { method: "DELETE" },
        "flite",
        "getDynamicHeroList",
        uuid
      );
    } catch (error) {
      handleError("deleteDynamicHeroDataAsync", error);
    } finally {
      isLoading.value = false;
    }
  };
  const postDynamicHeroDataAsync = async ({ payload }) => {
    if (!payload) {
      logMissingParameters();
      return;
    }
    try {
      const response = await useSimpleCustomFetch(
        "",
        { method: "POST", body: payload },
        "flite",
        "getDynamicHeroList"
      );
      postDynamicHeroData.value = response;
    } catch (error) {
      handleError("postDynamicHeroDataAsync", error);
    }
  };
  const patchDynamicHeroDataAsync = async ({ payload, uuid }) => {
    if (!payload || !uuid) {
      logMissingParameters();
      return;
    }

    try {
      isLoading.value = true;
      await useSimpleCustomFetch(
        "",
        { method: "PATCH", body: payload },
        "flite",
        "getDynamicHeroList",
        uuid
      );
    } catch (error) {
      handleError("patchDynamicHeroDataAsync", error);
    } finally {
      isLoading.value = false;
    }
  };
  const getHeroUUIDAsync = async ({ lang }) => {
    if (!lang) {
      logMissingParameters();
      return;
    }

    try {
      const response = await useSimpleCustomFetch(
        "",
        { params: { lang } },
        "flite",
        "articleData",
        "uuid"
      );
      dynamicHeroID.value = response;
    } catch (error) {
      handleError("getHeroUUIDAsync", error);
    }
  };

  return {
    dynamicHeroDataList,
    isLoadingData,
    editDynamicHeroDataList,
    dynamicHeroUUIDData,
    postDynamicHeroDataList,
    disabledArticleDataList,
    disabledArticleDataListGetter,
    getDynamicHeroListDataAsync,
    deleteDynamicHeroDataAsync,
    patchDynamicHeroDataAsync,
    getEditPageDynamicHeroDataAsync,
    postDynamicHeroDataAsync,
    getHeroUUIDAsync
  };
});
