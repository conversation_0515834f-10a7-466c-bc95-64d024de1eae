import { TABLE_MANAGER_STATE } from "../models/table-manager.model.js";

export const useTableManagerStore = (
  {
    storeId,
    defaultPagination,
  }
) => defineStore(`Table-Manager-${storeId}`, () => {
  const stateRef = ref(TABLE_MANAGER_STATE.INITIAL);
  const rowsRef = ref([]);
  const isLoadingRef = ref(true);
  const paginationRef = ref(defaultPagination);
  const isFirstLoadCompletedRef = ref(false);

  const state = computed(() => stateRef);
  const rows = computed(() => rowsRef);
  const isLoading = computed(() => isLoadingRef);
  const pagination = computed(() => paginationRef);
  const isFirstLoadCompleted = computed(() => isFirstLoadCompletedRef);

  const setState = (value) => {
    stateRef.value = value;
  };

  const setRows = (value) => {
    rowsRef.value = value || [];
  };

  const setLoading = (value) => {
    isLoadingRef.value = value;
  };

  const setFirstLoad = (value) => {
    isFirstLoadCompletedRef.value = value;
  };

  const setPaginationFrom = (value) => {
    if (value !== undefined && value !== null) {
      paginationRef.value.from = value;
    }
  };

  const setPaginationTotal = (value) => {
    if (value !== undefined && value !== null) {
      paginationRef.value.total = value;
    }
  };

  const reset = () => {
    setState(TABLE_MANAGER_STATE.LOADING);
    setRows([]);
    setLoading(true);
    setPaginationTotal(0);
    setFirstLoad(false);
  };

  return {
    tms_state: state,
    tms_rows: rows,
    tms_isLoading: isLoading,
    tms_pagination: pagination,
    tms_isFirstLoadCompleted: isFirstLoadCompleted,

    tms_setState: setState,
    tms_setRows: setRows,
    tms_setLoading: setLoading,
    tms_setPaginationTotal: setPaginationTotal,
    tms_setPaginationFrom: setPaginationFrom,
    tms_setFirstLoad: setFirstLoad,
    tms_reset: reset,
  };
})();
