<template>
  <Modal id="addRecipeMatches" @close="onClose">
    <template #addRecipeMatches>
      <div class="add-recipe-matches-modal-hero">
        <div class="header-section">
          <div class="title-section-hero">
            <div class="title-hero">
              {{ $t("CONTENT_FORM.ADD_RECIPE_TO_FORM") }}
            </div>
            <div class="search-box-pop-hero">
              <input
                type="text"
                class="search-input-box-hero"
                placeholder="Find a recipe hero"
                autocomplete="off"
                @keypress.enter="onSearch"
                @input="onQueryInput"
                v-model.trim="localQuery"
                :class="{ 'align-search-input-box-hero': localQuery }"
              />
              <img
                alt=""
                :class="
                  localQuery
                    ? 'search-icon-green-image-hero'
                    : 'search-icon-green-image-hero disable-icon'
                "
                @click="onSearch"
                src="@/assets/images/search-grey.png"
              />
              <img
                alt=""
                class="exit-search-icon-hero"
                v-if="isSearchPopupExitEnable"
                @click="onResetQuery"
                src="@/assets/images/exit-search.svg?skipsvgo=true"
              />
            </div>
          </div>
          <div class="close-icon-section">
            <div class="close-icon">
              <img
                src="@/assets/images/exit-gray.png"
                alt=""
                @click="onCloseConfirm"
              />
            </div>
          </div>
        </div>
        <div class="add-table-content-hero" ref="addTable">
          <div v-if="isTableLoading" class="table-image-loader-hero">
            <div class="loader-hero"></div>
          </div>
          <table class="add-table-hero" v-if="!isTableLoading">
            <caption></caption>
            <tbody>
              <div v-if="!recipeList.length" class="no-recipe-result-hero">
                {{ $t("COMMON.NO_RESULTS") }}
              </div>
              <th></th>
              <tr
                v-for="(recipe, index) in recipeList"
                :key="index"
                :class="
                  recipe.isAdded
                    ? 'add-recipe-body-hero background-color'
                    : 'add-recipe-body-hero'
                "
              >
                <td class="table-image-recipe-hero">
                  <div class="image-recipe-hero">
                    <img
                      alt="recipe"
                      class="image"
                      v-if="
                        recipe.media?.[lang]?.image &&
                        !recipe.media?.[lang]?.externalImageUrl
                      "
                      :src="recipe.media[lang].image || defaultImage"
                      @error="$event.target.src = defaultImage"
                    />
                    <img
                      alt="external"
                      v-if="recipe.media?.[lang]?.externalImageUrl"
                      class="table-image"
                      :src="
                        recipe.media[lang].externalImageUrl || defaultImage
                      "
                      @error="$event.target.src = `${defaultImage}`"
                    />
                    <img alt="default" class="image" v-else :src="defaultImage" />
                  </div>
                </td>
                <td class="table-recipe-code-hero">
                  <div class="recipe-code-hero">{{ recipe.isin || "" }}</div>
                </td>
                <td>
                  <div class="recipe-name-tooltip-hero">
                    <span>{{ recipe.title?.[lang] || "" }}</span>
                  </div>
                  <div
                    v-if="recipe.subtitle?.[lang]"
                    class="recipe-subtitle-hero"
                  >
                    <span>{{ recipe.subtitle[lang] }}</span>
                  </div>
                </td>
                <td>
                  <div class="recipe-details-hero">
                    <div v-if="recipe.time?.total" class="details-hero">
                      <img
                        src="@/assets/images/Time-meal.png"
                        alt=""
                        class="image"
                      />
                      <span>{{ parseDurationString(recipe.time.total) }}</span>
                    </div>
                    <div
                      v-if="recipe.ingredients?.[lang]?.length"
                      class="details-hero"
                    >
                      <img
                        src="@/assets/images/shopping_cart_black_24dp.png"
                        alt=""
                        class="image"
                      />
                      <span>
                        {{ recipe.ingredients[lang].length }}
                        {{
                          recipe.ingredients[lang].length === 1
                            ? "ingredient"
                            : "ingredients"
                        }}
                      </span>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="add-recipe-btn-hero">
                    <button
                      v-if="!recipe.isAdded"
                      @click="onSelectRecipe(recipe, index)"
                      @keydown="preventEnterAndSpaceKeyPress($event)"
                      :class="selectedItem ? 'disabled' : 'add-btn-hero'"
                    >
                      {{ $t("COMMON.SELECT") }}
                    </button>
                    <button
                      v-if="recipe.isAdded"
                      @click="onConfirmSelection(recipe, index)"
                      class="added-btn-hero"
                    >
                      {{ $t("COMMON.SELECTED") }}
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-if="canLoadMore && !isTableLoading" class="load-button-hero">
            <button class="load-more-hero" @click="onLoadMore">
              {{ $t("COMMON.LOAD_MORE") }}
            </button>
          </div>
        </div>
        <div class="done-section-hero">
          <button
            type="button"
            class="done-button-hero btn-green"
            @click="onDone"
          >
            {{ $t("BUTTONS.DONE_BUTTON") }}
          </button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script setup>
const props = defineProps({
  recipeList: { type: Array, default: () => [] },
  lang: { type: String, required: true },
  query: { type: String, default: "" },
  isTableLoading: { type: Boolean, default: false },
  defaultImage: { type: String, required: true },
  canLoadMore: { type: Boolean, default: false },
  isSearchPopupExitEnable: { type: Boolean, default: false },
  selectedItem: { type: Boolean, default: false },
});
const { parseDurationString } = useTimeUtils();
const { preventEnterAndSpaceKeyPress } = useEventUtils();
const emit = defineEmits([
  "close",
  "search",
  "resetQuery",
  "selectRecipe",
  "confirmSelection",
  "loadMore",
  "done",
]);

const localQuery = ref(props.query);
const onClose = () => emit("close");
const onSearch = () => emit("search", props.query);
const onSelectRecipe = (recipe, index) => emit("selectRecipe", recipe, index);
const onConfirmSelection = (recipe, index) =>
  emit("confirmSelection", recipe, index);
const onLoadMore = () => emit("loadMore");
const onDone = () => emit("done");
const onCloseConfirm = () => emit("close");
const onQueryInput = (event) => {
  emit("update:query", event.target.value);
};
const onResetQuery = () => {
  emit("resetQuery");
  localQuery.value = '';
}
</script>
