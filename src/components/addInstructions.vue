<template>
  <div class="main-instruction-section">
    <div class="add-instruction-container">
      <div class="title-data-section">
        <div class="recipe-title-section">
          <div class="text-section"></div>
          <div class="button-section">
            <button
              type="button"
              @click="backNavigate"
              @keydown="preventEnterAndSpaceKeyPress($event)"
              class="btn-green-outline"
            >
              Cancel
            </button>
            <button
              type="button"
              @click="saveInstructionsData()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
              :class="isSaveButtonEnabled ? 'btn-green' : 'disabled-button btn-green'"
            >
              Save
            </button>
          </div>
        </div>
      </div>
      <div class="recipe-steps-container">
        <div class="recipe-title-container" v-if="recipeSelectedLanguage === defaultLang">
          <div class="recipe-flag-box">
            <img alt="" v-if="recipeVariantSelectedLanguage == 'en-US'" class="recipe-flag"
              src="~/assets/images/us-flag.png" />
            <img alt="" v-if="recipeVariantSelectedLanguage == 'fr-FR'" class="recipe-flag"
              src="~/assets/images/france-flag.png" />
            <img alt="" v-if="recipeVariantSelectedLanguage == 'es-US'" class="recipe-flag"
              src="~/assets/images/spain-flag.png" />
          </div>
          <div class="recipe-title">Recipe Step Form</div>
        </div>
        <div class="recipe-title-container" v-else>
          <div class="recipe-variant-flag-section">
            <img alt="" v-if="recipeVariantSelectedLanguage == 'fr-FR'" class="recipe-flag"
              src="~/assets/images/france-flag.png" />
            <img alt="" v-if="recipeVariantSelectedLanguage == 'es-US'" class="recipe-flag"
              src="~/assets/images/spain-flag.png" />
          </div>
          <div class="recipe-title">Recipe Variant Step Form</div>
        </div>
        <div class="recipe-steps-main-content">
          <div :class="recipeSelectedLanguage == defaultLang
              ? 'video-container'
              : 'video-container disable-media'
            ">
            <div id="delete" v-if="(uploadedMedia >= 1 && uploadedMedia <= 99) ||
              (uploadImagePercentage >= 1 && uploadImagePercentage <= 99) ||
              video ||
              (image && recipeSelectedLanguage == defaultLang)
              ">
              <div :class="recipeSelectedLanguage == defaultLang
                  ? 'delete-video-icon'
                  : 'delete-video-icon disable-delete-button'
                ">
                <img alt="" class="delete-video-icon-image" src="@/assets/images/deleteVideoBtn.png"
                  @click="showDeleteVideoPopup()" />
              </div>
            </div>
            <div v-if="(uploadedMedia >= 1 && uploadedMedia <= 99) ||
              (uploadImagePercentage >= 1 && uploadImagePercentage <= 99) ||
              video ||
              image
              " class="video-main-div">
              <div class="image-inner-container">
                <div class="progress-image" v-show="uploadImagePercentage >= 1 && uploadImagePercentage <= 99
                  ">
                  <div class="progress-image-content">
                    <div v-show="uploadImagePercentage >= 1 && uploadImagePercentage <= 5
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 6 &&
                      uploadImagePercentage <= 11
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 12 &&
                      uploadImagePercentage <= 17
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 18 &&
                      uploadImagePercentage <= 24
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 25 &&
                      uploadImagePercentage <= 30
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 31 &&
                      uploadImagePercentage <= 36
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 37 &&
                      uploadImagePercentage <= 41
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 42 &&
                      uploadImagePercentage <= 49
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 50 &&
                      uploadImagePercentage <= 55
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 56 &&
                      uploadImagePercentage <= 61
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 62 &&
                      uploadImagePercentage <= 67
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 68 &&
                      uploadImagePercentage <= 74
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 75 &&
                      uploadImagePercentage <= 80
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 81 &&
                      uploadImagePercentage <= 86
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 87 &&
                      uploadImagePercentage <= 92
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage >= 93 &&
                      uploadImagePercentage <= 98
                      ">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true" />
                    </div>
                    <div v-show="uploadImagePercentage == 99">
                      <img alt="" class="progress-icon" src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true" />
                    </div>
                    <div class="upload-text">
                      <div class="upload-heading" v-if="uploadImagePercentage >= 1 &&
                        uploadImagePercentage <= 98
                        ">
                        Upload is in progress
                      </div>
                      <div class="upload-heading text-light-h4" v-else>Uploaded</div>
                      <span class="upload-media text-light-h6">{{ (loadedImageSize / 1024000).toFixed(1) }} of
                        {{ (uploadImageSize / 1024000).toFixed(1) }}
                        MB</span>
                    </div>
                  </div>
                </div>
              </div>
              <img v-if="image &&
                !video &&
                (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                " class="image-and-video" :src="`${image}`" alt="" />
              <div class="progress-video" v-show="uploadedMedia >= 1 && uploadedMedia <= 99">
                <div class="progress-video-content" style="display: none">
                  <div v-show="uploadedMedia >= 1 && uploadedMedia <= 5">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 6 && uploadedMedia <= 11">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 12 && uploadedMedia <= 17">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 18 && uploadedMedia <= 24">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 25 && uploadedMedia <= 30">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 31 && uploadedMedia <= 36">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 37 && uploadedMedia <= 41">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 42 && uploadedMedia <= 49">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 50 && uploadedMedia <= 55">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 56 && uploadedMedia <= 61">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 62 && uploadedMedia <= 67">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 68 && uploadedMedia <= 74">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 75 && uploadedMedia <= 80">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 81 && uploadedMedia <= 86">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 87 && uploadedMedia <= 92">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia >= 93 && uploadedMedia <= 98">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true" />
                  </div>
                  <div v-show="uploadedMedia == 99">
                    <img alt="" class="progress-icon" src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true" />
                  </div>
                  <div class="upload-text">
                    <div class="upload-heading" v-if="uploadedMedia >= 1 && uploadedMedia <= 98">
                      Upload is in progress
                    </div>
                    <div class="upload-heading text-light-h4" v-else>Uploaded</div>
                    <span class="upload-media text-light-h6">{{ (loadedVideoSize / 1024000).toFixed(1) }} of
                      {{ (uploadVideoSize / 1024000).toFixed(1) }} MB</span>
                  </div>
                </div>
              </div>
              <img alt="Placeholder" v-show="!isVideoLoaded && video" class="video-loader" :src="placeholderImage" />
              <video
                v-show="video && (uploadedMedia === 0 || uploadedMedia === 100) && isVideoLoaded"
                class="image-and-video"
                :src="`${video}`"
                id="step-video"
                @canplaythrough="checkVideoLoaded"
                aria-label="Instruction Video"
                title="Video description"
              >
                <track v-if="video?.subtitles" :src="video.subtitles" kind="subtitles" srclang="en" label="English">
                <track v-if="video?.description" :src="video.description" kind="description" srclang="en" label="English">
              </video>
              <div v-if="uploadedMedia === 0 || uploadedMedia === 100">
                <button class="btn-reset" type="button" @click="openVideoPopup()" >
                <img alt="video" v-if="video" class="play-video-icon-image" src="@/assets/images/videoPlayBtn.png" />
              </button>
              </div>
              <div class="replace-video-tag" v-if="(uploadedMedia === 0 || uploadedMedia === 100) &&
                (uploadImagePercentage === 0 || uploadImagePercentage === 100)
                ">
                <div class="hover-image">
                  <div style="display: none" class="replace-logo">
                    <img alt="" src="@/assets/images/replace-video-button.svg?skipsvgo=true" class="replace-video-icon" />
                    <label for="product-video" class="replace-button-title">
                      REPLACE <br />
                      PHOTO OR VIDEO</label>
                  </div>
                  <input type="file" class="upload-input" title="Update Video"
                    @click="uploadSameImageVideo($event)" @change="checkUploadedFiles" accept=".jpg,.mp4,.png,.jpeg"
                    ref="productVideo" />
                </div>
              </div>
            </div>
            <div class="image-section" v-else id="mealImage">
              <div class="hover-image">
                <div style="display: none" class="replace-logo">
                  <img alt="" src="@/assets/images/replace-video-button.svg?skipsvgo=true" class="replace-video-icon" />
                  <label for="product-video" class="replace-button-title">
                    UPLOAD <br />
                    PHOTO OR VIDEO</label>
                </div>
                <input type="file" class="upload-input" title="Update Picture" @click="uploadSameImageVideo($event)"
                  @change="checkUploadedFiles" accept=".jpg,.mp4,.png,.jpeg" id="product-image" />
              </div>
            </div>
            <span class="optional-video">jpg,png format (recom. 1MB, max 15MB), mp4 format (max 1GB)</span>
          </div>
          <div class="recipe-steps-title">
            <div class="steps-count">STEP {{ instructionIdx }}</div>
            <div class="step-title">
              <input
                  id="instruction"
                  v-if="instructionsData?.[recipeSelectedLanguage]"
                  type="text"
                  class="input-title-area"
                  autocomplete="off"
                  tabindex="0"
                  placeholder="Add a title"
                  autocapitalize="off"
                  v-model.trim="instructionsData[recipeSelectedLanguage].title"
              />
              <span v-if="!instructionsData?.[recipeSelectedLanguage]?.title?.trim()"
               class="asterisk-input">*
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="instruction-data add-instruction-form-container notes-section">
        <div class="form-title">
          <div class="recipe-variant-instruction-text">
            <div class="recipe-variant-flag-section">
              <img alt="" v-if="recipeVariantSelectedLanguage == 'en-US'" class="recipe-flag"
                src="~/assets/images/us-flag.png" />
              <img alt="" v-if="recipeVariantSelectedLanguage == 'fr-FR'" class="recipe-flag"
                src="~/assets/images/france-flag.png" />
              <img alt="" v-if="recipeVariantSelectedLanguage == 'es-US'" class="recipe-flag"
                src="~/assets/images/spain-flag.png" />
            </div>
            <p class="form-title-header instruction-text">
              Instructions
              <span class="compulsory-field">*</span>
            </p>
          </div>
        </div>
        <div class="description-section" v-if="instructionsData &&
          instructionsData[recipeSelectedLanguage] &&
          instructionsData[recipeSelectedLanguage].instructions
          ">
          <div v-for="(instruction, index) in instructionsData[
            recipeSelectedLanguage
          ].instructions" :key="index">
            <textarea class="description" id="instruction" v-model.trim="instruction.text"
              placeholder="Enter Instruction"></textarea>
            <span v-if="instruction.text == ''" class="asterisk-input">
              *
            </span>
            <div class="buttons-section">
              <div class="delete" :class="recipeSelectedLanguage == defaultLang
                  ? 'delete'
                  : 'hide-button'
                ">
                <img alt="" src="@/assets/images/delete-icon.png" @click="deleteInstruction(instruction, index)"
                  width="18" height="20" />
              </div>
            </div>
          </div>
          <div class="add-instruction-button-main-container" :class="recipeSelectedLanguage == defaultLang
              ? 'add-instruction-button-main-container'
              : 'disable-add-button'
            ">
            <div class="add-instruction-button-container">
              <button type="button" class="btn-green" @click="addInstructionRow()" @keydown="preventEnterAndSpaceKeyPress($event)">
                {{ $t('COMMON.ADD_INSTRUCTION') }}
              </button>
            </div>
            <span class="add-instruction-text" v-if="availableStepLang.length > 1">{{ $t('ADDING_CHANGES_VARIANT')
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <deleteModal v-if="isDeleteModalVisible" :closeModal="closeModal"
      :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_INGREDIENT')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_INGREDIENT_POPUP')"
      :productDescriptionTwo="$t('DESCRIPTION_POPUP.STEP')" :deleteItem="removeIngredient"
      :availableLanguage="availableLang.length" :deleteIngredientIndex="deleteIngredientIndex" />
    <deleteModal v-if="isDeleteInstructionModalVisible" :closeModal="closeModal"
      :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_INSTRUCTION')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_INSTRUCTION_POPUP')"
      :productDescriptionTwo="$t('DESCRIPTION_POPUP.STEP')" :deleteItem="removeIngredient"
      :availableLanguage="availableLang.length" :deleteInstructionIndex="deleteInstructionIndex" />
    <deleteModal v-if="isDeleteVideoModalVisible" :closeModal="closeModal"
      :productInfoTitle="$t('DESCRIPTION_POPUP.DELETE_MEDIA')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
      :productDescriptionTwo="$t('DESCRIPTION_POPUP.MEDIA')" :deleteItem="deleteVideo"
      :availableLanguage="availableLang.length" />
    <cancelModal v-if="isConfirmModalVisible" :availableLang="[]" :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToRecipe" :closeModal="closeModal" />
    <Modal v-show="isAddIngredientsFromRecipe" id="addIngredientsFromRecipe">
      <template #addIngredientsFromRecipe>
        <div class="add-ingredients-from-recipe">
          <button class="close-image" @click="closeModal" type="button">
            <img src="@/assets/images/close.svg?skipsvgo=true" alt="cross-icon"/>
          </button>
          <div class="add-ingredients-title text-h2">
            {{ $t('COMMON.ADD_INGREDIENTS_FROM_RECIPE') }}
          </div>
          <div class="add-table" ref="ingredientsAddTable">
            <table class="table-data" aria-label="table-data">
              <tbody>
                <tr class="add-ingredients-col-name text-light-h4">
                  <th></th>
                  <th class="quantity">{{ $t('QUANTITY') }}</th>
                  <th class="uom">{{ $t('UOM') }}</th>
                  <th class="ingredien-match">{{ $t('INGREDIENT.INGREDIENT_NAME') }}</th>
                </tr>
                <tr
                  v-for="(ingredientData, index) in ingredientPopUpData"
                  :key="index"
                  :id="`checkBoxId${index}`"
                  class = "add-ingredients-data text-light-h3"
                  :class="ingredientData.isChecked ? 'add-ingredients-background' : ''"
                  >
                  <td>
                    <div class="ingredient-checkbox">
                      <div class="ingredients-container">
                        <div class="round">
                          <button type="button" class="btn-reset" @click="selectIngredientData(ingredientData)">
                            <input v-if="ingredientData.isChecked" type="checkbox" />
                            <label for="selectIngredient" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="quantity" @click="selectIngredientData(ingredientData)">
                    {{ ingredientData.quantity }}
                  </td>
                  <td class="uom" @click="selectIngredientData(ingredientData)">
                    {{ ingredientData.UOM }}
                  </td>
                  <td class="ingredien-match" @click="selectIngredientData(ingredientData)">
                    {{ ingredientData.name }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="add-ingredients-save-button">
            <button type="button"
              :class="isIngredientSelected ? 'btn-green' : 'disabled-button btn-green'"
              @click="addIngredientsFromPopup()">
              Done
            </button>
          </div>
        </div>
      </template>
    </Modal>
    <recipeVideo v-if="isOpenVideo" :videoLink="video" :closeModal="closeModal" />
    <Modal v-show="isErrorOccuredModalVisible" @close="closeModal">
      <template #recipeStepErrorModal>
        <div class="edit-product-publish-modal">
          <div class="publish-content">
            <div class="publish-head">
              <div v-if="isUnableToSaveStepInstruction ||
                isUnableToSaveStepEmptyInstruction ||
                isUnableToSaveStepIngredientsName ||
                isUnableToSaveStepIngredientsRawText
                ">
                <span class="unable-to-save"> Unable to Save Recipe Step </span>
                <br />
                <div class="unableToSaveError">
                  <span v-if="isUnableToSaveStepEmptyInstruction" class="unable-to-save-title">
                    The following field(s) is required.
                  </span>
                  <ul class="error-list">
                    <li v-if="isUnableToSaveStepEmptyInstruction" class="unable-to-save-list no-instruction">
                      Instructions
                    </li>
                  </ul>
                  <span v-if="isUnableToSaveStepInstruction ||
                    isUnableToSaveStepIngredientsName ||
                    isUnableToSaveStepIngredientsRawText
                    " class="unable-to-save-title">
                    The following field(s) cannot be empty:
                  </span>
                  <br v-if="isUnableToSaveStepInstruction ||
                    isUnableToSaveStepIngredientsName ||
                    isUnableToSaveStepIngredientsRawText
                    " />
                  <ul class="error-list" v-if="isUnableToSaveStepInstruction ||
                    isUnableToSaveStepIngredientsName ||
                    isUnableToSaveStepIngredientsRawText
                    ">
                    <li v-if="isUnableToSaveStepInstruction" class="unable-to-save-list">
                      Instructions
                    </li>
                    <li v-if="isUnableToSaveStepIngredientsName" class="unable-to-save-list">
                      Ingredient Name
                    </li>
                    <li v-if="isUnableToSaveStepIngredientsRawText" class="unable-to-save-list">
                      Ingredient's Name
                    </li>
                  </ul>
                </div>
              </div>
              <span v-if="!isUnableToSaveStepInstruction &&
                !isUnableToSaveStepEmptyInstruction &&
                !isUnableToSaveStepIngredientsName &&
                !isUnableToSaveStepIngredientsRawText
                ">Unable to Save the Recipe, an unexpected error occurred</span>
            </div>
            <div class="button-container">
              <button type="button" class="btn-green" @click="closeModal">Okay</button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-show="isInvalidImageModalVisible" @close="closeModal">
      <template #editProductMatches>
        <div class="invalid-file-type-modal">
          <div class="invalid-file-type-modal-content">
            <div class="invalid-file-header">
              Invalid Image/Video file format
            </div>
            <div class="invalid-file-text">Accepted file formats: jpg,png,mp4</div>
            <div class="invalid-file-type-button-container">
              <button type="button" class="btn-green" @click="closeModal()">
                Okay
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <sizeLimit
      v-if="isUploadingImagePopupVisible || isMaxImagePopupVisible || isMaxVideoPopupVisible"
      :continueImage="isUploadingImagePopupVisible ? continueImage : null"
      :maxFileSize="isUploadingImagePopupVisible ? $t('DESCRIPTION_POPUP.LARGER_FILE') : ''"
      :optimalImageSize="isUploadingImagePopupVisible ? $t('DESCRIPTION_POPUP.OPTIMAL_IMAGE') : ''"
      :imageSizeAlert="isMaxImagePopupVisible ? $t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE') : isMaxVideoPopupVisible ? $t('DESCRIPTION_POPUP.MAX_VIDEO_SIZE') : ''"
      :fileSizeAlert="isMaxImagePopupVisible ? $t('DESCRIPTION_POPUP.MAX_IMAGE') : isMaxVideoPopupVisible ? $t('DESCRIPTION_POPUP.MAX_VIDEO') : ''"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopupVisible"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
      :isMaxVideoPopupVisible="isMaxVideoPopupVisible"
    />
    <div class="add-instruction-form-container ingredients-section" id="scrollUomList">
      <div class="recipe-variant-ingredient-text">
        <div class="form-title">
          <div class="recipe-variant-flag-section">
            <img alt="" v-if="recipeVariantSelectedLanguage == 'en-US'" class="recipe-flag"
              src="~/assets/images/us-flag.png" />
            <img alt="" v-if="recipeVariantSelectedLanguage == 'fr-FR'" class="recipe-flag"
              src="~/assets/images/france-flag.png" />
            <img alt="" v-if="recipeVariantSelectedLanguage == 'es-US'" class="recipe-flag"
              src="~/assets/images/spain-flag.png" />
          </div>
          <p class="form-title-header">Ingredients</p>
        </div>
      </div>
      <div class="description-section">
        <table class="ingredients-table" aria-label="ingredients-table">
          <thead v-if="instructionsData &&
            instructionsData[recipeSelectedLanguage] &&
            instructionsData[recipeSelectedLanguage].ingredients &&
            instructionsData[recipeSelectedLanguage].ingredients.length
            ">
            <tr>
              <th style="width: 4%"></th>
              <th style="width: 30%">{{ $t('QUANTITY') }}</th>
              <th style="width: 20%">UOM</th>
              <th style="width: 50%">
                {{ $t('INGREDIENT.INGREDIENT_NAME') }}
                <span class="compulsory-field">*</span>
              </th>
              <th style="min-width: 40px"></th>
            </tr>
          </thead>
          <div v-if="instructionsData &&
            instructionsData[recipeSelectedLanguage] &&
            instructionsData[recipeSelectedLanguage].ingredients
            ">
            <draggable group="ingredients" :list="instructionsData[recipeSelectedLanguage].ingredients"
              class="table-row-data" :scroll-sensitivity="200" :force-fallback="true" ghost-class="hidden-list"
              @start="drag = true" @end="drag = false" handle=".draggable-icon">
              <tr v-for="(ingredient, index) in instructionsData[
                recipeSelectedLanguage
              ].ingredients" :key="index">
                <td style="width: 4%" class="draggable-icon" :class="recipeSelectedLanguage == defaultLang
                    ? 'draggable-icon'
                    : 'hide-button'
                  ">
                  <img alt="" src="@/assets/images/drag-text.svg?skipsvgo=true" />
                </td>
                <td class="ingredient-name-box">
                  <input
                    type="number"
                    @keypress="restrictNumericInput($event)"
                    class="filter-select-input-popup text-light-h3 quantity-number no-scroll"
                    autocomplete="off"
                    tabindex="0"
                    :id="`quantity${index}`"
                    @blur="getQuantity(ingredient)"
                    @paste="handlePaste($event, 'stepQuantity', index)"
                    v-model="ingredient.quantityMirror"
                    min="0"
                    max="1000"
                    @focus="toggleDropdownOff(); disableScroll()"
                    autocapitalize="off"
                    placeholder="0"
                  />
                </td>
                <td class="filter-icon">
                    <input
                      type="text"
                      class="filter-select-input-popup"
                      @keypress="restrictToAlphabets($event)"
                      autocomplete="off"
                      tabindex="0"
                      :id="`ingredientUom${index}`"
                      v-model="ingredient.UOMMirror"
                      autocapitalize="off"
                      @input="
                        toggleDropdownUOM('ingredientUom', index, ingredient);
                        searchUOMList(ingredient);
                        saveButtonEnable();
                      "
                      @keyup.down="ingredientUomAutocompleteArrowDown(ingredient)"
                      @keyup.up="ingredientUomAutocompleteArrowUp(ingredient)"
                      @keyup.enter="ingredientUomAutocompleteEnter(ingredient)"
                      @focus="
                        toggleDropdownUOM('ingredientUom', index, ingredient);
                        searchUOMList(ingredient);
                      "
                      @blur="toggle('ingredientUom', index, ingredient)"
                    />
                    <button class="drop-icon-box btn-reset"  @click="toggleDropdownButton('ingredientUom', index, ingredient)" type="button">
                      <img
                        alt="drop-icon"
                        src="@/assets/images/arrow-right.png"
                        class="dropdown-icon"
                        :class="{ rotate: ingredient.uomAutocomplete && ingredientsUomList }"
                      />
                    </button>
                  <div class="autocomplete-results-nouom" v-show="ingredient.uomAutocomplete && searchedUomText">
                    {{ $t('COMMON.NO_RESULT') }}
                  </div>
                  <ul v-if="ingredient.uomAutocomplete" class="autocomplete-results" id="ingredientsUomList">
                    <div v-if="searchedUomText == ''">
                      <li v-for="(result, i) in ingredientsUomList" :key="i" :class="{
                        'autocomplete-result': true,
                        'is-active':
                          i === ingredientUomAutocompleteArrowCounter,
                      }" id="ingredientCounter" @click.prevent="
  setIngredientUomResult(result, ingredient)
  ">
                        {{ result.display }}
                      </li>
                    </div>
                    <div v-if="searchedUomText !== ''">
                      <li v-for="(result, i) in searchedUomList" :key="i" :class="{
                        'autocomplete-result': true,
                        'is-active':
                          i === ingredientUomAutocompleteArrowCounter,
                      }" @click.prevent="
  setIngredientUomResult(result, ingredient)
  ">
                        {{ result.display }}
                      </li>
                    </div>
                  </ul>
                </td>
                <td class="filter-icon-ing">
                  <span
                    class="ing-name-tool"
                    @mouseover="checkIngName(index)"
                    @mouseleave="hideIngTip(index)"
                    :class="{
                      'simple-data-tooltip': isIngNameTooltipVisible,
                    }"
                    :data-tooltip-text="isIngNameTooltipVisible && ingredient.nameMirror"
                  >
                    <input
                      type="text"
                      class="filter-select-input-popup"
                      autocomplete="off"
                      tabindex="0"
                      @focus="toggleDropdownOff()"
                      :id="`foodItemName${index}`"
                      autocapitalize="off"
                      v-model="ingredient.nameMirror"
                      :placeholder="placeholder"
                      @change="checkIngTitle()"
                      @keypress="preventSpecialCharacters($event)"
                    />
                  </span>
                </td>

                <td class="menu">
                  <div class="menu-box" :id="index" :class="recipeSelectedLanguage == defaultLang
                      ? 'menu-box'
                      : 'hide-button'
                    ">
                    <img alt="" @click="
                      deleteModalVisible('ingredientModal', index, ingredient)
                      " src="@/assets/images/delete-icon.png" width="18" height="20" />
                  </div>
                </td>
              </tr>
            </draggable>
          </div>
          <tbody>
            <tr>
              <td>
                <div class="add-ingredient-main-container" :class="recipeSelectedLanguage == defaultLang
                    ? 'add-ingredient-main-container'
                    : 'hide-button'
                  ">
                  <div class="add-ingredient-container">
                    <button type="button" :class="isAddIngredientDisabled
                        ? 'disabled-button btn-green-outline'
                        : 'btn-green-outline'
                      " @click="addIngredientRow()" @keydown="preventEnterAndSpaceKeyPress($event)">
                      {{ $t('COMMON.ADD_INGREDIENT') }}
                    </button>
                  </div>
                  <span class="add-ingredient-text text-light-h4" v-if="availableStepLang.length > 1">{{ $t('COMMON.ADDING_SORTING_MESSAGE') }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import recipeVideo from "@/components/recipeVideo";
import cancelModal from "@/components/cancel-modal";
import { useRefUtils } from '@/composables/useRefUtils';
import { useDelayTimer } from "@/composables/useDelayTimer";
import { useEventUtils } from "@/composables/useEventUtils";
import { useCommonUtils } from "@/composables/useCommonUtils";
import sizeLimit from "@/components/size-limit.vue";
import OrganizationsService from "@/services/OrganizationsService";
import Modal from "@/components/Modal";
import deleteModal from "@/components/delete-modal";
import axios from "axios";
import { useNuxtApp } from '#app'
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import { useRoute } from "vue-router";

// images
import placeholderImage from "@/assets/images/Eclipse-1s-200px.gif";


const isVideoLoaded = ref(false);
const drag = ref(false);
const isUploadingImagePopupVisible = ref(false);
const isMaxImagePopupVisible = ref(false);
const isMaxVideoPopupVisible = ref(false);
const isInvalidImageModalVisible = ref(false);
const isVideoMuted = ref(false);
const ingredientPopUpData = ref([]);
const instructionsData = ref([]);
const loadedImageSize = ref(0);
const isDeleteModalVisible = ref(false);
const instructionStepLength = ref('');
const deletedInstructions = ref([]);
const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const deleteInstructionIndex = ref('');
const deleteIngredientIndex = ref('');
const originalData = ref([]);
const isIngredientSelected = ref(false);
const selectedUom = ref([]);
const ingredientUomAutocompleteArrowCounter = ref(0);
const video = ref('');
const image = ref('');
const isOpenVideo = ref(false);
const isAddIngredientsFromRecipe = ref(false);
const isStepMediaModified = ref(false);
const isIngredientDeleted = ref(false);
const isInstructionDeleted = ref(false);
const deletedIngredientFromStep = ref('');
const file = ref([]);
const filesName = ref('');
const videoFilesName = ref('');
const imageResponseUrl = ref('');
const videoResponseUrl = ref('');
const cancelImage = ref({});
const selectedIngredientData = ref([]);
const isAddIngredientDisabled = ref(false);
const cancelVideo = ref({});
const isApiCanceled = ref(false);
const deleteInstructionId = ref('');
const isDeleteVideoModalVisible = ref(false);
const isDeleteInstructionModalVisible = ref(false);
const isUnableToSaveStepEmptyInstruction = ref(false);
const isUnableToSaveStepInstruction = ref(false);
const isUnableToSaveStepIngredientsName = ref(false);
const isUnableToSaveStepIngredientsRawText = ref(false);
const isErrorOccuredModalVisible = ref(false);
const recipeISIN = ref(false);
const availableStepLang = ref([]);
const deletedIngredientArray = ref([]);
const addedIngredientArray = ref([]);
const uploadedMedia = ref(0);
const uploadVideoSize = ref(0);
const loadedVideoSize = ref(0);
const deleteIngredientsData = ref('');
const lang = ref('');
const recipeSelectedLanguage = ref('');
const uploadImageConfirm = ref('');
const deletedInstructionsData = ref([]);
const uploadImagePercentage = ref(0);
const uploadImageSize = ref(0);
const searchedUomText = ref("");
const ingredientUomAutocompleteOpen = ref(false);
const ingredientsData = ref();
const searchedUomList = ref();
const isIngNameTooltipVisible = ref(false);

// utility
const route = useRoute();
const { $eventBus, $auth } = useNuxtApp();
const { t } = useI18n();
const store = useStore();
const { getRef } = useRefUtils();
const { delay } = useDelayTimer();
const { preventDefault, preventEnterAndSpaceKeyPress, preventSpecialCharacters, restrictNumericInput, restrictToAlphabets } = useEventUtils();
const { triggerLoading, isEmptyOrWhitespace } = useCommonUtils();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const props = defineProps({
  instructionIndex: {
    type: Number,
  },
  closeInstructionPageAsync: {
    type: Function,
  },
  instructionIdx: {
    type: Number,
  },
  editInstructionClicked: {
    type: Object,
  },
  stepsIngredientPopUp: {
    type: Array,
  },
  recipeIsin: {
    type: String,
  },
  ingredientsUomList: {
    type: Array,
  },
  recipeName: {
    type: String,
  },
  recipeVariantSelectedLanguage: {
    type: String,
  },
  project: {
    type: Object,
  },
  defaultLang: {
    type: String,
  },
  availableLang: {
    type: Array,
  },
});
onMounted(() => {
  lang.value = props.defaultLang;
  recipeISIN.value = props.recipeIsin;
  recipeSelectedLanguage.value = props.recipeVariantSelectedLanguage;
  availableStepLang.value = props.availableLang;

  image.value =
    props.editInstructionClicked?.[recipeSelectedLanguage.value]?.media?.image || '';

  video.value =
    props.editInstructionClicked?.[recipeSelectedLanguage.value]?.media?.video?.[0]?.url || '';

  instructionsData.value = props.editInstructionClicked;
  ingredientPopUpData.value = props.stepsIngredientPopUp;

  const clone = {
    title: props.editInstructionClicked?.[recipeSelectedLanguage.value]?.title || '',
    instructions: props.editInstructionClicked?.[recipeSelectedLanguage.value]?.instructions?.map(data => ({ text: data.text })) || [],
    ingredients: props.editInstructionClicked?.[recipeSelectedLanguage.value]?.ingredients?.map(ingredient => ({
      name: ingredient?.name || '',
      nameMirror: ingredient?.nameMirror || '',
      id: ingredient?.id || '',
      quantity: ingredient?.quantity || 0,
      quantityMirror: ingredient?.quantityMirror || 0,
      UOM: ingredient?.UOM || '',
      UOMMirror: ingredient?.UOMMirror || '',
      rawText: ingredient?.rawText || '',
      uomAutocomplete: false,
      note: ingredient?.note || '',
      modifier: ingredient?.modifier || '',
      keywords: ingredient?.keywords || [],
      isChecked: ingredient?.isChecked || false,
      isDropDown: ingredient?.isDropDown || false,
      group: ingredient?.group || '',
      shoppable: ingredient?.shoppable || false,
    })) || [],
    media: {
      image: props.editInstructionClicked?.[recipeSelectedLanguage.value]?.media?.image || '',
      video: [
        {
          url: props.editInstructionClicked?.[recipeSelectedLanguage.value]?.media?.video?.[0]?.url || '',
        },
      ],
    },
  };

  originalData.value = clone;
  instructionStepLength.value = props.editInstructionClicked?.[recipeSelectedLanguage.value]?.instructions.length || 0;

  document.addEventListener('input', handleTypeInput);
  document.addEventListener('keyup', handleESCClickOutside);

  filterIngredientPopupData();
  updateInstructionsData();
});

watch(drag, (newVal) => {
  if (newVal) {
    toggleDropdownOff();
    isCampaignModified.value = newVal;
    $eventBus.emit("campaignModified", isCampaignModified.value);
  } else {
    $eventBus.emit("campaignModified", isCampaignModified.value);
  }
});

const checkVideoLoaded = () => {
  isVideoLoaded.value = true;
};

const checkIngTitle = () => {
  const selectedLanguageData = instructionsData.value?.[recipeSelectedLanguage.value];
  return selectedLanguageData?.ingredients?.every(
    ({ nameMirror }) => !isEmptyOrWhitespace(nameMirror)
  );
};
const updateInstructionsData = () => {
  try {
    instructionsData.value = JSON.parse(JSON.stringify(props.editInstructionClicked || {}));
  } catch (e) {
    console.error(e);
  }
};
const checkingDescription = () => {
  const instructions = instructionsData.value?.[recipeSelectedLanguage.value]?.instructions;
  return instructions?.length && instructions.every(item => item?.text?.trim());
};
const backToRecipe = () => {
  backNavigateConfirm();
  closeModal();
};
const handlePaste = (event, value, index) => {
  preventDefault();
  const clipboardData = event.clipboardData || window?.clipboardData;
  const pastedText = clipboardData.getData("text");
  const isNegative = pastedText.includes("-");

  if (!isNegative && !isNaN(pastedText)) {
    if (value === "stepQuantity") {
      instructionsData.value[recipeSelectedLanguage.value]?.ingredients?.forEach(
        (item, insideIndex) => {
          if (insideIndex === index) {
            item.quantityMirror = pastedText;
          }
        }
      );
    }
  }
};
const getQuantity = (ingredient) => {
  ingredient.quantityMirror = parseFloat(ingredient.quantityMirror);
};
const handleESCClickOutside = (event) => {
  if (event && event.key === "Escape") {
    closeModal();
  }
};
const saveButtonEnable = () => {
  isCampaignModified.value = true;
};
const disableScroll = () => {
  document.addEventListener("wheel", function () {
    const activeElement = document.activeElement;
    if (activeElement?.type === "number" && activeElement?.classList?.contains("no-scroll")) {
      activeElement.blur();
    }
  });
};
const checkIngName = (index) => {
  const aboutIng = getRef("foodItemName" + index);
  if (aboutIng.scrollWidth > aboutIng.clientWidth) {
    isIngNameTooltipVisible.value = true;
  }
};
const hideIngTip = (index) => {
  isIngNameTooltipVisible.value = false;
};

const getIsins = () => {
  return OrganizationsService.getNewIsins(
    props.project,
    'recipe',
    props.defaultLang,
    $auth.user.value?.email || "",
    store,
    $auth
  )
    .then((response) => {
      recipeISIN.value = response?.isin || '';
    })
    .catch(console.error);
};

const onMute = () => {
  const video = getRef("video-container-popup");
  if (video && !video.muted) {
    isVideoMuted.value = true;
    video.muted = true;
  } else {
    isVideoMuted.value = false;
    video.muted = false;
  }
};
const toggle = (section, index, ingredient) => {
  if (ingredientUomAutocompleteOpen.value && !isEmptyOrWhitespace(searchedUomText.value)) {
    const hasUomList = props.ingredientsUomList?.length;
    const hasMatchingUom = hasUomList && props.ingredientsUomList.some(e => e.display === ingredient.UOMMirror);

    if (!hasUomList || !hasMatchingUom) {
      ingredient.UOMMirror = '';
    }

    ingredient.uomAutocomplete = false;
    ingredientUomAutocompleteOpen.value = false;
  }
};
const toggleDropdownButton = (section, index, ingredient) => {
  if (section !== "ingredientUom") return;

  updateArrowCounter(ingredient);
  resetUOMMirrorIfNotFound(ingredient);

  ingredient.uomAutocomplete ? closeAllDropdowns() : openDropdown(index, ingredient);
};

const openDropdown = (index, ingredient) => {
  searchedUomText.value = "";
  ingredient.uomAutocomplete = true;
  ingredientUomAutocompleteOpen.value = true;

  getRef("scrollUomList")?.scrollIntoView();
  closeOtherDropdowns(index);

  attachOutsideClickListener();
};

const closeAllDropdowns = () => {
  instructionsData.value[recipeSelectedLanguage.value].ingredients.forEach(item => {
    item.uomAutocomplete = false;
  });
  ingredientUomAutocompleteOpen.value = false;

  removeOutsideClickListener();
};

const attachOutsideClickListener = () => {
  removeOutsideClickListener();
  setTimeout(() => document.addEventListener("click", handleOutsideClick));
};

const removeOutsideClickListener = () => {
  document.removeEventListener("click", handleOutsideClick);
};

const handleOutsideClick = (event) => {
  if (!event.target.closest(".filter-select-input-popup, .drop-icon-box")) {
    closeAllDropdowns();
  }
};

const updateArrowCounter = (ingredient) => {
  ingredientUomAutocompleteArrowCounter.value = props.ingredientsUomList
    .findIndex(item => item.display === ingredient.UOMMirror) || 0;
};

const resetUOMMirrorIfNotFound = (ingredient) => {
  if (searchedUomText.value && !props.ingredientsUomList.some(e => e.display === ingredient.UOMMirror)) {
    ingredient.UOMMirror = "";
  }
};
const closeOtherDropdowns = (currentIndex) => {
  instructionsData.value[recipeSelectedLanguage.value].ingredients.forEach((item, idx) => {
    if (idx !== currentIndex) item.uomAutocomplete = false;
  });
};
const toggleDropdownUOM = (section, index, ingredient) => {
  if (section === 'ingredientUom') {
    searchedUomText.value = '';
    if (props.ingredientsUomList.length > 0) {
      ingredient.uomAutocomplete = true;
      ingredientUomAutocompleteOpen.value = true;

      instructionsData.value[recipeSelectedLanguage.value].ingredients.forEach((data, idx) => {
        if (idx !== index) {
          data.uomAutocomplete = false;
        }
      });

      const data = ingredientsData.value;
      ingredientsData.value = {};
      ingredientsData.value = data;

      getRef(`ingredientUom${index}`)?.focus();
    }
  }
};
const ingredientUomAutocompleteArrowDown = (ingredient) => {
  if (searchedUomText.value === '') {
    if (ingredientUomAutocompleteArrowCounter.value + 1 < props.ingredientsUomList.length) {
      ingredientUomAutocompleteArrowCounter.value += 1;
    }

    const ele = getRef('ingredientsUomList');
    if (ele) {
      ele.scrollTop += 38;
    }

    ingredient.UOMMirror = props.ingredientsUomList[ingredientUomAutocompleteArrowCounter.value].display;
  }

  if (searchedUomText.value !== '' && searchedUomList.value?.length > 0) {
    if (ingredientUomAutocompleteArrowCounter.value + 1 < searchedUomList.value.length) {
      ingredientUomAutocompleteArrowCounter.value += 1;
    }

    ingredient.UOMMirror = searchedUomList.value[ingredientUomAutocompleteArrowCounter.value].display;
  }
};
const ingredientUomAutocompleteArrowUp = (ingredient) => {
  if (searchedUomText.value === "") {
    if (ingredientUomAutocompleteArrowCounter.value > 0) {
      ingredientUomAutocompleteArrowCounter.value -= 1;
    }

    if (props.ingredientsUomList.length > 0) {
      let scroll = getRef("ingredientsUomList");
      if (scroll) {
        scroll.scrollTop -= 38;
      }
    }

    ingredient.UOMMirror = props.ingredientsUomList[ingredientUomAutocompleteArrowCounter.value].display;
  }

  if (searchedUomText.value !== "" && searchedUomList.value.length > 0) {
    if (ingredientUomAutocompleteArrowCounter.value > 0) {
      ingredientUomAutocompleteArrowCounter.value -= 1;
    }

    ingredient.UOMMirror = searchedUomList.value[ingredientUomAutocompleteArrowCounter.value].display;
  }
};
const ingredientUomAutocompleteEnter = (ingredient) => {
  if (ingredientUomAutocompleteArrowCounter.value > 0) {
    if (searchedUomText.value === "") {
      const result = props.ingredientsUomList[ingredientUomAutocompleteArrowCounter.value];
      ingredient.UOMMirror = result.display;
      selectedUom.value.push(result);
    }

    if (searchedUomText.value !== "") {
      const result = searchedUomList.value[ingredientUomAutocompleteArrowCounter.value];
      ingredient.UOMMirror = result.display;
      selectedUom.value.push(result);
    }
  }

  ingredientUomAutocompleteArrowCounter.value = -1;
  ingredient.uomAutocomplete = false;
};
const setIngredientUomResult = (result, ingredient) => {
  if (result.display === ingredient.UOMMirror) {
    return;
  }
  ingredient.UOMMirror = result.display;
  isCampaignModified.value = true;
  selectedUom.value.push(result);
  searchedUomText.value = "";
  ingredient.uomAutocomplete = false;
  checkInputPresence(ingredient);
};
const searchUOMList = (ingredient) => {
  searchedUomText.value = ingredient.UOMMirror;
  searchedUomList.value = [];

  if (searchedUomText.value !== "") {
    const input = searchedUomText.value;
    const filter = input.toUpperCase();

    for (const item of props.ingredientsUomList) {
      const textValue = item?.display?.toUpperCase() || "";

      if (textValue.startsWith(filter)) {
        searchedUomList.value.push(item);
      }
    }
  }
};
const checkInputPresence = (ingredient) => {
  if (!ingredient.UOMMirror) {
    ingredientUomAutocompleteOpen.value = false;
    ingredientUomAutocompleteArrowCounter.value = -1;
  }
};
const toggleDropdownOff = () => {
  const ingredients = instructionsData[recipeSelectedLanguage.value]?.ingredients;

  if (ingredients) {
    ingredients.forEach((item) => {
      item.uomAutocomplete = false;
    });
  }
};
const addIngredientRow = () => {
  isAddIngredientsFromRecipe.value = !isAddIngredientsFromRecipe.value;
  toggleDropdownOff();
};
const filterIngredientPopupData = () => {
  const ingredients = instructionsData.value[recipeSelectedLanguage.value]?.ingredients;

  if (!ingredients) {
    return;
  }

  const formatIngredient = (ingredient) => {
    const { quantity = "", name = "", UOM = "" } = ingredient;
    return `${quantity}-${name}-${UOM.toLowerCase()}`;
  };

  const ingredientSet = new Set(ingredients.map(formatIngredient));

  ingredientPopUpData.value = ingredientPopUpData.value.filter(ingredientData => {
    return !ingredientSet.has(formatIngredient(ingredientData));
  });

  checkPopupIngredient();
};
const showDeleteVideoPopup = () => {
  isDeleteVideoModalVisible.value = true;
};
const deleteVideo = () => {
  isCampaignModified.value = true;
  isDeleteVideoModalVisible.value = false;
  isApiCanceled.value = false;
  image.value = "";
  video.value = "";

  props.editInstructionClicked[recipeSelectedLanguage.value].media.video[0].url = "";
  instructionsData.value[props.defaultLang].media.video[0].url = "";

  props.editInstructionClicked[recipeSelectedLanguage.value].media.image = "";
  instructionsData.value[props.defaultLang].media.image = "";

  toggleDropdownOff();

  if (uploadedMedia.value !== 0 && uploadedMedia.value !== 100) {
    resetUploadState();
  }
};
const resetUploadState = () => {
  uploadedMedia.value = 0;
  cancelVideo.value.cancel();
  image.value = "";
  video.value = "";
  const videoMedia = props.editInstructionClicked[recipeSelectedLanguage.value].media.video[0];
  videoMedia.url = "";
  props.editInstructionClicked[recipeSelectedLanguage.value].media.image = "";

  if (uploadImagePercentage.value !== 0 && uploadImagePercentage.value !== 100) {
    uploadImagePercentage.value = 0;
    loadedImageSize.value = 0;
    cancelImage.value.cancel();
    props.editInstructionClicked[recipeSelectedLanguage.value].media.image = "";
  }
};
const openVideoPopup = () => {
  isOpenVideo.value = true;
  toggleDropdownOff();
};
const checkUploadedFiles = (event) => {
  isStepMediaModified.value = true;
  isCampaignModified.value = true;
  file.value = event?.target?.files || event?.srcElement?.files;

  const fileType = file.value?.[0]?.type?.split("/")[0];
  if (fileType === $keys.KEY_NAMES.IMAGE) {
    videoResponseUrl.value = "";
    uploadFiles();
  } else if (fileType === $keys.KEY_NAMES.VIDEO) {
    uploadVideoFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};
const continueImage = () => {
  file.value = uploadImageConfirm.value;
  const reader = new FileReader();

  reader.addEventListener("load", async () => {
    image.value = reader.result;
    if (image.value) {
      loadedImageSize.value = 0;
      uploadImagePercentage.value = 1;
      await uploadImageAsync();
      video.value = "";
    }
  }, false);

  if (file.value[0]) {
    reader.readAsDataURL(file.value[0]);
  }
};
const uploadFiles = () => {
  isCampaignModified.value = true;

  if (file.value.length > 0) {
    filesName.value = file.value[0].name.toLowerCase();
    const reg = /\.(jpg|png|jpeg)$/;

    if (!reg.test(filesName.value)) {
      isInvalidImageModalVisible.value = true;
      file.value = [];
      videoFilesName.value = "";
      return;
    }

    const fileSize = file.value[0].size;
    uploadImageSize.value = fileSize;

    const sizeInMB = fileSize / (1024 * 1024);
    if (sizeInMB >= 15) {
      file.value = [];
      videoFilesName.value = "";
      isMaxImagePopupVisible.value = true;
      return;
    } else if (sizeInMB >= 1) {
      isUploadingImagePopupVisible.value = true;
      uploadImageConfirm.value = file.value;
      file.value = [];
      videoFilesName.value = "";
    }

    const reader = new FileReader();

    reader.onload = async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
        video.value = "";
      }
    };
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  }
};
const uploadImageAsync = async () => {
  if (!route?.query?.isin) {
    await getIsins();
  }

  if (file.value.length > 0 && image.value) {
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      async () => {
        const fileToUpload = file.value[0];
        const extension = fileToUpload.type.split("/")[1];

        const params = {
          entity: "recipe",
          content: "image",
          lang: recipeSelectedLanguage.value,
          extension: extension,
          public: true,
        };

        if (recipeISIN.value) {
          await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
            isin: recipeISIN.value,
            params,
          });
          const response = store.getters['preSignedUrl/getPreSignedUrl'];
          uploadImageFile(response.data.url, fileToUpload);
          imageResponseUrl.value = response.data.url.replace(/\?.*/, "");
          props.editInstructionClicked[recipeSelectedLanguage.value].media.image = imageResponseUrl.value;
          instructionsData.value[props.defaultLang].media.image = imageResponseUrl.value;
          instructionsData.value[props.defaultLang].media.video[0].url = "";
          props.editInstructionClicked[
            recipeSelectedLanguage.value
          ].media.video[0].url = "";
          const isin = recipeISIN.value;
          $eventBus.emit("newInstructionsData", isin, "data_not_changed");
          isCampaignModified.value = true;
        }
      },
      false
    );
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  }
};

const updateMediaUrls = () => {
  props.editInstructionClicked[recipeSelectedLanguage.value].media.image = imageResponseUrl.value;
  instructionsData.value[props.defaultLang].media.image = imageResponseUrl.value;
  instructionsData.value[props.defaultLang].media.video[0].url = "";
  props.editInstructionClicked[recipeSelectedLanguage.value].media.video[0].url = "";
};

const uploadImageFile = (url, file) => {
  cancelImage.value = axios.CancelToken.source();

  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        ) || 0;
        uploadedImageFunctionAsync(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .then(() => {
      // Handle successful upload if needed
    })
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};

const uploadedImageFunctionAsync = async (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    await delay(2000);
    uploadImagePercentage.value = 100;
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = "";
};
const uploadVideoFiles = () => {
  if (file.value.length > 0) {
    const videoFile = file.value[0];
    videoFilesName.value = videoFile.name.toLowerCase();
    const reg = /(.*?)\.(mp4)$/;

    if (!videoFilesName.value.match(reg)) {
      isInvalidImageModalVisible.value = true;
      file.value = [];
      videoFilesName.value = "";
      return;
    }

    const fileSize = videoFile.size;
    uploadVideoSize.value = fileSize;

    if (fileSize > 1024 * 1024 * 1024) {
      file.value = [];
      videoFilesName.value = "";
      isMaxVideoPopupVisible.value = true;
      return;
    } else {
      image.value = "";
      uploadedMedia.value = 1;
      uploadDocumentAsync();
    }
  }
};
const uploadDocumentAsync = async () => {
  if (!route?.query?.isin) {
    await getIsins();
  }

  if (file.value.length > 0) {
    const extension = file.value[0].type.split("/")[1];
    const params = {
      entity: "recipe",
      content: "video",
      lang: recipeSelectedLanguage.value,
      extension: extension,
      public: true,
    };

    if (recipeISIN.value) {
      await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
        isin: recipeISIN.value,
        params,
      });
      const response = store.getters['preSignedUrl/getPreSignedUrl'];

      await uploadAsync(response.data.url, file.value[0]);
      videoResponseUrl.value = response.data.url.replace(/\?.*/, "") || "";

      props.editInstructionClicked[recipeSelectedLanguage.value].media.video[0].url = videoResponseUrl.value;
      instructionsData.value[props.defaultLang].media.video[0].url = videoResponseUrl.value;
      instructionsData.value[props.defaultLang].media.image = "";
      props.editInstructionClicked[recipeSelectedLanguage.value].media.image = "";
    }
    isStepMediaModified.value = false;
  }
};
const uploadAsync = async (url, file) => {
  cancelVideo.value = axios.CancelToken.source();

  try {
    axios.put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelVideo.value.token,
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded / progressEvent.total) * 100) || 0;
        uploadedMedia.value = percentCompleted === 0 ? 1 : percentCompleted;
        loadedVideoSize.value = progressEvent.loaded;
        uploadedVideoFunctionAsync(uploadedMedia.value);
      },
    });

    isApiCanceled.value = false;
  } catch (e) {
    if (axios.isCancel(e)) {
      console.error("Video request canceled.");
    } else {
      console.error(e);
    }

    isApiCanceled.value = true;
    uploadedMedia.value = 0;
    isDeleteVideoModalVisible.value = false;
    image.value = "";
    video.value = "";
    props.editInstructionClicked[recipeSelectedLanguage.value].media.video[0].url = "";
    props.editInstructionClicked[recipeSelectedLanguage.value].media.image = "";
  }
};
const uploadedVideoFunctionAsync = async (load) => {
  if (load === 100) {
    uploadedMedia.value = 99;
    await delay(2000);
    uploadedMedia.value = 100;
    isVideoLoaded.value = false;
    video.value = videoResponseUrl.value;
  }
};
const backNavigate = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backNavigateConfirm();
  }
  toggleDropdownOff();
};
const backNavigateConfirm = () => {
  if (deletedInstructions.value.length) {
    instructionsData.value[recipeSelectedLanguage.value]?.instructions?.push(
      deletedInstructions.value[0]
    );
  }
  instructionsData.value[recipeSelectedLanguage.value]?.instructions?.splice(
    instructionStepLength.value
  );

  deleteInstructionIndex.value = "";
  isCampaignModified.value = false;

  const dataObject = {
    data: originalData.value,
    index: props.instructionIndex,
    modified: "data_not_changed"
  };
  $eventBus.emit("originalInstructionsData", dataObject);

  $eventBus.emit("newInstructionsData", {
    isin: null,
    modified: "data_not_changed"
  });

  toggleDropdownOff();
  props.closeInstructionPageAsync(
    instructionsData.value[props.defaultLang],
    props.instructionIndex,
    deletedInstructionsData,
    addedIngredientArray,
    deletedIngredientArray,
    "data_not_changed"
  );
};
const addInstructionRow = () => {
  isCampaignModified.value = true;
  instructionsData.value[recipeSelectedLanguage.value].instructions.push({
    text: "",
    times: [],
    instructions_id: "new_instructions_id",
  });
};
const openDeleteModal = () => {
  isDeleteModalVisible.value = true;
};

const deleteModalVisible = (modalType, index, data) => {
  toggleDropdownOff();
  deleteIngredientIndex.value = null;
  if (modalType === "ingredientModal") {
    isIngredientDeleted.value = true;
    isDeleteModalVisible.value = true;
    deleteIngredientIndex.value = index;
    deleteIngredientsData.value = data?.id || "";
  } else {
    isDeleteModalVisible.value = true;
  }
};
const closeModal = () => {
  const scroll = getRef("ingredientsAddTable");
  if (scroll) {
    scroll.scrollTo(0, 0);
  }
  isOpenVideo.value = false;
  isAddIngredientsFromRecipe.value = false;
  isDeleteModalVisible.value = false;
  isConfirmModalVisible.value = false;
  isDeleteVideoModalVisible.value = false;
  isDeleteInstructionModalVisible.value = false;
  isErrorOccuredModalVisible.value = false;
  isInvalidImageModalVisible.value = false;
  isMaxImagePopupVisible.value = false;
  isMaxVideoPopupVisible.value = false;
  toggleDropdownOff();
  ingredientPopUpData.value.forEach((ingredientData) => {
    ingredientData.isChecked = false;
  });
  isIngredientSelected.value = false;
  isUploadingImagePopupVisible.value = false;
};
const addIngredientsFromPopup = () => {
  if (isIngredientSelected.value) {
    if (selectedIngredientData.value.length) {
      selectedIngredientData.value.forEach((ingredientData) => {
        ingredientData.nameMirror = ingredientData.name;
        ingredientData.UOMMirror = ingredientData.UOM;
        ingredientData.quantityMirror = ingredientData.quantity;
        instructionsData.value[recipeSelectedLanguage.value].ingredients.push(ingredientData);
        ingredientData.isChecked = false;
      });
    }

    const ingredientIndex = ingredientPopUpData.value.filter(
      (value) => !selectedIngredientData.value.includes(value)
    );
    ingredientPopUpData.value = ingredientIndex;
    selectedIngredientData.value = [];

    checkPopupIngredient();
    closeModal();
    isCampaignModified.value = true;
    isIngredientSelected.value = false;
    $eventBus.emit("ingredientsAdded");
  }
};
const checkPopupIngredient = () => {
  isAddIngredientDisabled.value = !(Array.isArray(ingredientPopUpData.value) && ingredientPopUpData.value.length);
};
const deleteRecipe = () => {
  isDeleteModalVisible.value = false;
  toggleDropdownOff();
};
const deleteInstruction = (id_store, index) => {
  isInstructionDeleted.value = true;
  deleteInstructionIndex.value = index;
  deleteInstructionId.value = id_store.instructions_id || "";
  isDeleteInstructionModalVisible.value = true;
  toggleDropdownOff();
};
const removeIngredient = () => {
  deleteInstructionConfirm(deleteInstructionIndex.value, deleteIngredientIndex.value);
  triggerLoading("newDeletedSuccess");
};
const deleteInstructionConfirm = (instructionDeleteIndex, ingredientDeleteIndex) => {
  if (isInstructionDeleted.value) {
    isCampaignModified.value = true;
    isDeleteModalVisible.value = false;
    if (deleteInstructionId.value && deleteInstructionId.value !== "new_instructions_id") {
      deletedInstructionsData.value.push(deleteInstructionId.value);
    }
    instructionsData.value[recipeSelectedLanguage.value].instructions.splice(instructionDeleteIndex, 1);

    deleteInstructionIndex.value = "";
    toggleDropdownOff();
    isInstructionDeleted.value = false;
    isDeleteInstructionModalVisible.value = false;

  } else if (isIngredientDeleted.value) {
    isCampaignModified.value = true;
    isDeleteModalVisible.value = false;

    deletedIngredientFromStep.value = instructionsData.value[recipeSelectedLanguage.value].ingredients.splice(ingredientDeleteIndex, 1);

    deletedIngredientArray.value = [deleteIngredientsData.value];
    ingredientPopUpData.value.unshift(deletedIngredientFromStep.value[0]);

    deleteIngredientIndex.value = "";
    toggleDropdownOff();
    isIngredientDeleted.value = false;
    deleteIngredientsData.value = "";

    checkPopupIngredient();
  }
};
const saveInstructionsData = () => {
  let errorMsg = false;

  isUnableToSaveStepInstruction.value = false;
  isUnableToSaveStepEmptyInstruction.value = false;
  isUnableToSaveStepIngredientsName.value = false;
  isUnableToSaveStepIngredientsRawText.value = false;

  const instructions = instructionsData.value[recipeSelectedLanguage.value].instructions;
  instructions.forEach((data) => {
    if (data.text.trim() === "") {
      errorMsg = true;
      isUnableToSaveStepInstruction.value = true;
    }
  });

  if (instructions.length <= 0) {
    errorMsg = true;
    isUnableToSaveStepEmptyInstruction.value = true;
  }

  const ingredients = instructionsData.value[recipeSelectedLanguage.value].ingredients;
  ingredients.forEach((data) => {
    if (data.name.trim() === "") {
      errorMsg = true;
      isUnableToSaveStepIngredientsName.value = true;
    }
  });

  if (errorMsg) {
    isErrorOccuredModalVisible.value = true;
  } else {
    if (isApiCanceled.value) {
      props.editInstructionClicked[recipeSelectedLanguage.value].media.video[0].url = "";
    }

    props.editInstructionClicked[recipeSelectedLanguage.value] = JSON.parse(
      JSON.stringify(instructionsData.value[recipeSelectedLanguage.value])
    );

    triggerLoading("newInstructionsData", null, "");
    isCampaignModified.value = false;
    triggerLoading("savedSuccess");

    props.closeInstructionPageAsync(
      instructionsData.value[props.defaultLang],
      props.instructionIndex,
      deletedInstructionsData.value,
      addedIngredientArray.value,
      deletedIngredientArray.value,
      ""
    );
  }

  toggleDropdownOff();
};
const handleTypeInput = (event) => {
  const id = event.target.id;

  if (id.includes("instruction")) {
    isCampaignModified.value = true;
  }

  const isIngredientField = id.includes("ingredientName") || id.includes("foodItemName") || id.includes("quantity");

  if (isIngredientField && getRef(id).contains(event.target)) {
    isCampaignModified.value = true;
  }
};
const selectIngredientData = (ingredientName) => {
  ingredientName.isChecked = !ingredientName.isChecked;

  selectedIngredientData.value = ingredientPopUpData.value.filter(ingredient => ingredient.isChecked);

  isIngredientSelected.value = selectedIngredientData.value.length > 0;
};

const cutRatingStringProduct = computed(() => (string) => string.replace("Match", ""));

const placeholder = computed(() => t('ENTER_FOOD_ITEM'));

const isSaveButtonEnabled = computed(() => {
  return (
    !!instructionsData.value?.[recipeSelectedLanguage.value]?.title &&
    !isEmptyOrWhitespace(instructionsData.value[recipeSelectedLanguage.value].title) &&
    checkingDescription() &&
    checkIngTitle() &&
    isCampaignModified.value &&
    (uploadedMedia.value === 0 || uploadedMedia.value === 100) &&
    (uploadImagePercentage.value === 0 || uploadImagePercentage.value === 100)
  );
});

onBeforeUnmount(() => {
  document.removeEventListener("input", handleTypeInput);
  document.removeEventListener("keyup", handleESCClickOutside);
});
</script>
