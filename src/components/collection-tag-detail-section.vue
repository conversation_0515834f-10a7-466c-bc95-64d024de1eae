<template>
  <div ref="popup" class="tag-content-section">
    <div class="content-inner-section">
      <div class="content">
        <div class="tags-name text-h3 font-weight-semi-bold">{{ $t('COMMON.SELECT_TAGS') }}</div>
        <div class="compulsory-select-tag-field">*</div>
        <div ref="showPanel" class="input-section-and-suggested-tag" id="show-panel">
          <button @click="focusTagInput()" type="button" ref="input-box" class="input-box">
            <div v-for="(tag, index) in selectedTags" :key="index" class="selected-tags-section">
              <div class="tag">
                <div class="tag-text">{{ tag?.name ?? '' }}</div>
                <img class="cross-icon" alt="cross-icon" @click="removeTag(tag)" src="@/assets/images/exit-search.svg?skipsvgo=true" />
              </div>
            </div>
            <input
              :class="selectedTags.length == 0 ? 'empty-input' : 'tag-input'"
              ref="tagInput"
              @keyup.enter="handleEnter"
              :placeholder="inputPlaceholder"
              type="text"
              v-model.trim="localSelectTag"
              autocomplete="off"
              @input="enterTagName()"
              @focus="focusTagPanel()"
            />
          </button>
          <div v-show="isTagPopupVisible" class="suggested-tags-section">
            <div class="intro text-h3 font-weight-semi-bold">{{ isTagPopupVisibleText }}:</div>
            <div v-if="tags && tags.length && !isTagsLoading" class="tag-section">
              <button
                v-for="(tag, index) in tags"
                :key="index"
                class="tags-box btn-reset"
                @click="chooseTag(tag)"
                type="button"
                :class="checkSelected(tag) ? 'selected-tags' : ''"
              >
              <div class="tag-name text-h3">{{ tag?.name ?? '' }}</div>
              <div class="tag-count text-light-h4">{{ tag?.totalRecipes ?? 0 }} {{ $t('COMMON.RECIPES_TEXT') }}</div>
              </button>
            </div>
            <div v-if="isTagsLoading" class="loading-container">
              <div class="loading-text text-light-h3">{{ $t('LOADER.LOADING') }}</div>
            </div>
            <div v-if="tags.length == 0 && !isTagsLoading" class="no-tag-result-container">
              <div class="no-result-text text-title-2 font-normal">{{ $t('COMMON.NO_RESULTS') }}</div>
            </div>
            <div class="button-section" :class="{ 'disabled-button': isTagsLoading }">
            <button
              class="pagination-button text-h3 font-weight-extra-bold"
              @click="prevPageAsync()"
              :class="fromTags === 0 ? 'no-button' : ''"
              id="prev-Button"
              type="button"
            >
              <div class="prev-image">
                <img src="@/assets/images/next-button.svg?skipsvgo=true" alt="next-icon"/>
              </div>
              <div class="text">{{ $t('COMMON.PREV') }}</div>
            </button>
            <button
              id="next-button"
              class="pagination-button text-h3 font-weight-extra-bold"
              :class="{ 'disabled-button': fromTags >= allAvailableTags || (allAvailableTags - fromTags) <= sizeTags }"
              @click="nextPageAsync()"
              type="button"
            >
              <div class="next-image">
                <img src="@/assets/images/next-button.svg?skipsvgo=true" alt="next-icon"/>
              </div>
              <div class="text">{{ $t('COMMON.NEXT') }}</div>
            </button>
          </div>
          </div>
        </div>
        <div class="collection-table-container">
          <div class="feature-recipe-text">
            <div class="text"><span v-if="selectedTags.length">{{ recipesCount }}</span> Featured Recipes</div>
            <div
              class="info-container simple-data-tooltip"
              :data-tooltip-text="tagDetailTooltip"
            >
              <img alt="" class="info-icon" src="@/assets/images/informationSymbol.png" />
            </div>
          </div>
          <div v-if="
            recipeList && recipeList.length == 0 && selectedTags.length !== 0 && isRecipeLoading
          " class="edit-collection-table-content-loader">
            <loader />
          </div>
          <table v-if="recipeList?.length" class="edit-collection-table" id="edit-collection-table">
            <caption></caption>
            <thead class="edit-collection-table-head">
              <tr class="title">
                <th></th>
                <th class="edit-collection-isin">
                  <span>Recipe ISIN </span>
                </th>
                <th class="edit-collection-title">
                  <span>Recipe Title</span>
                </th>
                <th class="edit-collection-tags"><span>Tags</span></th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in recipeList" :key="index" class="collection-body">
                <td class="collection-table-image-recipe">
                  <div class="collection-image-recipe">
                    <img alt="recipe" class="image" :src="getImageSrc(index)" />
                  </div>
                </td>
                <td class="collection-table-recipe-code">
                  <div class="collection-recipe-code">
                    <span>{{ item && item.isin ? item.isin :'' }} </span>
                  </div>
                </td>
                <td class="collection-table-recipe-name">
                  <div
                    :class="{
                      'simple-data-tooltip': isCollectionNameToolTip,
                    }"
                    :data-tooltip-text="isCollectionNameToolTip && item?.title?.[lang]"
                  >
                    <div
                      :id="`collection-recipe-name${index}${item.isin}`"
                      class="collection-recipe-name text-h3"
                      @mouseover="checkCollectionNameLength(index, item.isin)"
                      @mouseleave="hideCollectionNameToolTip()"
                    >
                      <span>{{ item?.title?.[lang] ?? '' }}</span>
                    </div>
                  </div>
                  <div class="collection-recipe-subtitle text-light-h3">
                    <span>
                      {{ item?.subtitle?.[lang] ?? "" }}</span>
                  </div>
                </td>
                <td>
                  <div class="collection-recipe-tag text-light-h3">
                    <span>{{ returnTagName(item?.tags?.[lang]) }}</span>
                  </div>
                </td>
                <td class="collection-tag">
                  <div class="collection-menu">
                    <div :class="item && item.dropDown
                      ? 'collection-menu-container menu-selected'
                      : 'collection-menu-container'
                      " :ref="item && item.dropDown ? 'menuSelected' : null" @click="toggleDropdown(item)">
                      <img alt="" v-if="item && item.dropDown" class="collection-edit-btn"
                        src="@/assets/images/green-edit-btn.svg?skipsvgo=true" />
                      <img alt="" v-if="item && !item.dropDown" class="collection-edit-btn"
                        src="@/assets/images/edit-btn.svg?skipsvgo=true" />
                    </div>
                    <button class="menu-box" @click="previewRecipe(item.isin)" type="button" v-if="item?.dropDown">
                      <ul class="menu-list text-title-2">
                        <li>{{ $t('BUTTONS.PREVIEW_BUTTON') }}</li>
                      </ul>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-show="!selectedTags.length && !recipeList.length" class="no-results-found text-title-2 font-normal">{{ $t('NO_RECIPES_SELECT_TAGS') }}
          </div>
        </div>
        <pagination
        v-show="recipesCount > 10"
        :currentPage="currentPage"
        :list="recipeList"
        :listTotal="recipesCount"
        :sizePerPage="sizeTags"
        :pageRange="6"
        @pageChange="pageChange"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import loader from "@/components/loader.vue";
import pagination from "@/components/pagination.vue";
import { useRefUtils } from "~/composables/useRefUtils";

// images
import defaultImage from "@/assets/images/default_recipe_image.png";

const props = defineProps({
  selectedTags: {
    type: Array,
    default: () => [],
  },
  tags: {
    type: Array,
    default: () => [],
  },
  tagsTotal: {
    type: Number,
    default: 0,
  },
  focusTagPanel: {
    type: Function,
  },
  recipesCount: {
    type: Number,
    default: 0,
  },
  recipeList: {
    type: Array,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  checkSelected: {
    type: Function,
  },
  inputPlaceholder: {
    type: String,
    default: "",
  },
  previewRecipe: {
    type: Function,
  },
  selectTag: {
    type: String,
    default: () => [],
  },
  sizeTags: {
    type: Number,
    default: 10,
  },
  toggleDropdown: {
    type: Function,
  },
  isTagsLoading: {
    type: Boolean,
    default: false,
  },
  isTagPopupVisibleText: {
    type: String,
    default: "",
  },
  chooseTag: {
    type: Function,
  },
  removeTag: {
    type: Function,
  },
  debounceTagEnter: {
    type: Function,
  },
  allAvailableTags: {
    type: Number,
    default: 0,
  },
  nextPageAsync: {
    type: Function,
  },
  prevPageAsync: {
    type: Function,
  },
  isTagPopupVisible: {
    type: Boolean,
    default: false,
  },
  lang: {
    type: String,
    default: "",
  },
  pageChange: {
    type: Function,
  },
  fromTags: {
    type: Number,
    default: 0,
  },
  isRecipeLoading: {
    type: Boolean,
    default: false,
  },
  recipeTagData: {
    type: Array,
  },
  setCampaignModified: {
    type: Function,
  },
  handleEnter: {
    type: Function,
  },
});

const emit = defineEmits(["update:selectTag"])

const localSelectTag = ref(props.selectTag);
const { $eventBus } = useNuxtApp();
const { getRef } = useRefUtils();
const tagInputRef = ref(null);
const tagDetailTooltip = "The Collection should have at least 5 Recipes.";
const isCollectionNameToolTip = ref(false);

onMounted(() => {
  $eventBus.on("pageChange", (page) => {
    pageChange(page);
  });
});

const focusTagInput = () => {
  // Ensure the reference exists before calling focus
  if (tagInputRef.value) {
    tagInputRef.value.focus();
  }
};

const getImageSrc = (index) => {
  const media = props.recipeList[index]?.media?.[props.lang];
  return media?.image || media?.externalImageUrl || defaultImage;
};

const enterTagName = (event) => {
  props.debounceTagEnter?.(event);
};

const checkCollectionNameLength = (index, isin) => {
  const collectionRecipeName = getRef("collection-recipe-name" + index + isin);
  isCollectionNameToolTip.value = collectionRecipeName.scrollWidth > collectionRecipeName.clientWidth;
};

const hideCollectionNameToolTip = () => {
  isCollectionNameToolTip.value = false;
};

const returnTagName = (recipeIsins) => {
  if (!recipeIsins?.length || !props.recipeTagData?.length) {
    return "";
  }

  const tagMap = new Map(props.recipeTagData.map((tag) => [tag.isin, tag.name]));
  return recipeIsins
    .map((isin) => tagMap.get(isin))
    .filter(Boolean)
    .join(", ");
};


watch(() => props.selectTag, (newValue) => {
  localSelectTag.value = newValue;
});

watch(localSelectTag, (newValue) => {
  emit("update:selectTag", newValue);
});

onBeforeUnmount(() => {
  $eventBus.off("pageChange");
});
</script>
