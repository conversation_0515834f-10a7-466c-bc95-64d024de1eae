<template>
  <div
    ref="sticky"
    class="simple-sticky-wrapper"
    :class="{
      'simple-sticky-wrapper-stuck': stuck,
    }"
  >
    <div
      class="simple-sticky-wrapper-inner"
      :style="stuck ? stickyStyles : {}"
    >
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';

const props = defineProps({
  top: { type: [String, Number], default: 0 },
  left: { type: [String, Number], default: 'initial' },
  right: { type: [String, Number], default: 'initial' },
  zIndex: { type: [String, Number], default: 50 },
  width: { type: String, default: '' },
  distance: { type: Number, default: 0 },
});

const sticky = ref(null);
const stuck = ref(false);
const elementOffsetTop = ref(0);
const elementWidth = ref(0);

const stickyStyles = computed(() => {
  return {
    position: 'fixed',
    top: typeof props.top === 'number' ? `${props.top}px` : props.top,
    left: typeof props.left === 'number' ? `${props.left}px` : props.left,
    right: typeof props.right === 'number' ? `${props.right}px` : props.right,
    zIndex: props.zIndex,
    width: props.width || `${elementWidth.value}px`,
  };
});

const recalculatePosition = () => {
  if (!sticky.value) return;
  const rect = sticky.value.getBoundingClientRect();
  elementOffsetTop.value = rect.top + window.scrollY;
  elementWidth.value = sticky.value.offsetWidth;
  onScroll();
};

const onScroll = () => {
  if (!sticky.value) return;
  stuck.value = window.scrollY > elementOffsetTop.value + props.distance;
};

let resizeObserver;

onMounted(() => {
  recalculatePosition();
  window.addEventListener('scroll', onScroll, { passive: true });
  window.addEventListener('resize', recalculatePosition, { passive: true });

  resizeObserver = new ResizeObserver(recalculatePosition);
  if (sticky.value) resizeObserver.observe(sticky.value);
});

onBeforeUnmount(() => {
  window.removeEventListener('scroll', onScroll);
  window.removeEventListener('resize', recalculatePosition);
  resizeObserver?.disconnect();
});
</script>
