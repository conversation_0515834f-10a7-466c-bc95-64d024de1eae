<template>
  <div class="recipe-preview-variant-main-container">
    <div class="recipe-preview-variant-title text-title-2">
      {{ $t("COMMON.RECIPE_VARIANTS") }}
    </div>
    <div
      id="recipe-preview-variant-dropdown"
      class="recipe-preview-variant-dropdown"
    >
      <button
        v-show="
          defaultvariant.language === props.recipePreviewVariantSelectedLanguage
        "
        v-for="(defaultvariant, index) in props.recipePreviewVariantLanguageList"
        :key="index"
        @click="openVariantDropdown()"
        type="button"
        class="recipe-preview-variant-selected text-title-2 font-normal btn-reset"
      >
        <span class="recipe-preview-variant-flag-image">
          <img
            alt="recipe-preview-variant-flag-icon"
            :src="defaultvariant?.languageFlag"
          />
        </span>
        <span v-if="defaultvariant?.language_name">
          {{ defaultvariant.language_name }}
          <span class="default-mark" v-if="defaultvariant.default_check"
            >({{ $t("RECIPE_PREVIEW.DEFAULT") }})</span
          >
        </span>
        <span class="dropdown-image">
          <img
            alt="Dropdown"
            :class="{
              'recipe-variant-dropdown-icon-open': props.isVariantDropdownVisible,
            }"
            class="recipe-variant-dropdown-icon"
            src="@/assets/images/arrow-right.png"
          />
        </span>
      </button>
      <div v-if="props.isVariantDropdownVisible" class="dropdown-list">
        <button
          @click="chooseRecipeVariant(variant.language)"
          type="button"
          v-for="(variant, index) in props.recipePreviewVariantLanguageList"
          class="dropdown-list-item text-title-5 btn-reset"
          :key="index"
        >
          <span class="recipe-preview-variant-flag-image">
            <img
              alt="recipe-preview-variant-flag-icon"
              :src="variant?.languageFlag || ''"
            />
          </span>
          <span
            >{{ variant.language_name }}
            <span class="default-mark" v-if="variant?.default_check"
              >({{ $t("RECIPE_PREVIEW.DEFAULT") }})</span
            >
          </span>
        </button>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  recipePreviewVariantLanguageList: {
    type: Array,
    default: () => [],
  },
  recipePreviewVariantSelectedLanguage: {
    type: String,
    default: "",
  },
  isVariantDropdownVisible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits();

function openVariantDropdown() {
  emit('openVariantDropdown');
}

function chooseRecipeVariant(language) {
  emit('chooseRecipeVariant', language);
}
</script>
