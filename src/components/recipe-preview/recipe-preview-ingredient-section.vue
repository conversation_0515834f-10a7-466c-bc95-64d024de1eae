<template>
  <div class="recipe-preview-ingredients-main-section">
    <div class="ingredient-top-section">
      <div class="ingredients-heading">
        {{ $t("RECIPE_PREVIEW.INGREDIENTS") }}
      </div>
      <div class="ingredient-serving-container">
        <span class="ingredients-per-serving" v-if="!servings">
          <img src="@/assets/images/spoon.png" alt="spoon" />
          <span class="no-serving-available">
            {{ $t("RECIPE_PREVIEW.NONE") }}
          </span>
          {{ $t("RECIPE_PREVIEW.SERVINGS") }}
        </span>
        <span
          class="ingredients-per-serving"
          v-else-if="availableServings.length <= 1"
        >
          <img src="@/assets/images/spoon.png" alt="spoon" />
          <span v-if="!servings" class="no-serving-available">
            {{ $t("RECIPE_PREVIEW.NONE") }}
          </span>
          <span v-if="servings" class="serving-available">{{ servings }}</span>
          {{ $t("RECIPE_PREVIEW.SERVINGS") }}
        </span>
        <button
          type="button"
          :class="
            isIngredientsDataLoading && isStepIngredientsDataLoading
              ? 'servings-drop-down disable-serving-dropdown btn-reset'
              : 'servings-drop-down btn-reset'
          "
          id="servings-drop-down"
          @click="$emit('showServingsData')"
          v-else
        >
          <span class="servings-container">
            <img
              class="serving-spoon-image"
              src="@/assets/images/spoon.png"
              alt="spoon"
            />
            <span class="serving-count" v-if="!selectedServingsName">
              {{ servings }} {{ $t("RECIPE_PREVIEW.SERVINGS") }}
            </span>
            <span class="serving-count" v-else>
              {{ selectedServingsName }} {{ $t("RECIPE_PREVIEW.SERVINGS") }}
            </span>
            <img
              alt="arrow"
              v-if="availableServings.length > 1"
              class="servings-dropdown-icon"
              src="@/assets/images/arrow-right.png"
              :class="{
                rotate: isServingsDropdownResultVisible,
              }"
            />
          </span>
        </button>
        <div class="servings-dropdown-container">
          <ul
            v-if="isServingsDropdownResultVisible"
            class="autocomplete-results"
          >
            <div>
              <li
                v-for="(result, index) in availableServings"
                :key="index"
                :class="
                  selectedServingsName
                    ? result == selectedServingsName
                      ? 'selected-servings'
                      : 'autocomplete-result'
                    : result == servings
                    ? 'selected-servings'
                    : 'autocomplete-result'
                "
              >
                <button
                  type="button"
                  @click="$emit('selectedServingsProductAsync', result)"
                  class="btn-reset serving-button"
                >
                  <p>{{ result }} {{ $t("RECIPE_PREVIEW.SERVINGS") }}</p>
                </button>
              </li>
            </div>
          </ul>
        </div>
      </div>
    </div>
    <div
      v-if="isIngredientsDataLoading"
      class="recipe-preview-ingredients-loading-main"
    >
      <div class="content">
        <div class="input-loading">
          <div class="loader-image"></div>
        </div>
      </div>
    </div>
    <div v-else class="ingredients-available">
      <div
        v-for="(
          ingredientGroup, groupIndex
        ) in ingredientsDataPreview?.children"
        :key="groupIndex"
        :class="
          ingredientGroup?.name
            ? 'ingredients-available-group'
            : 'ingredients-available-group ingredients-available-nogroup'
        "
        v-show="ingredientsCountPreview || isMasterPreview"
      >
        <div v-if="ingredientGroup?.name" class="ingredients-group">
          <span class="name-span">{{ ingredientGroup?.name || "" }}:</span>
        </div>
        <div class="ingredients-space">
          <div
            v-for="(ingredient, ingredientIndex) in ingredientGroup?.children"
            :key="ingredientIndex"
            class="ingredients-and-value"
            v-show="ingredientGroup?.children?.length"
          >
            <span class="circle-pointer"></span>
            <div class="ingredients-main">
              <div class="ingredients">
                <div>
                  {{ ingredient?.duplicateName || "" }}
                </div>
                <div class="ingredients-note">
                  {{ ingredient?.note || "" }}
                </div>
              </div>
            </div>
            <span
              class="value"
              v-if="ingredient?.duplicateQuantity && ingredient?.duplicateUOM"
            >
              {{ Math.round(ingredient?.duplicateQuantity * 100) / 100 || "" }}
              {{ ingredient?.duplicateUOM || "" }}
            </span>
            <span class="value" v-else> - </span>
          </div>
        </div>
        <div
          v-show="!ingredientGroup?.children?.length && ingredientGroup?.name"
          class="no-ingredient"
        >
          {{ $t("RECIPE_PREVIEW.NO_INGREDIENTS") }}
        </div>
      </div>
      <div
        v-if="!ingredientsCountPreview && !isMasterPreview"
        class="no-ingredients-available"
      >
        {{ $t("RECIPE_PREVIEW.NO_INGREDIENTS") }} <br />
        {{ $t("RECIPE_PREVIEW.CLICK_ON") }} <br />
        {{ $t("RECIPE_FORM") }}
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  ingredientsDataPreview: {
    type: Object,
    default: () => ({}),
  },
  ingredientsCountPreview: {
    type: Number,
    default: 0,
  },
  isMasterPreview: {
    type: Boolean,
    default: false,
  },
  servings: {
    type: [Number, String],
  },
  availableServings: {
    type: Array,
    default: () => [],
  },
  isIngredientsDataLoading: {
    type: Boolean,
    default: false,
  },
  isStepIngredientsDataLoading: {
    type: Boolean,
    default: false,
  },
  selectedServingsName: {
    type: String,
  },
  isServingsDropdownResultVisible: {
    type: Boolean,
    default: false,
  },
});
</script>