<template>
  <div class="recipe-preview-filter-slug-main-section">
    <div class="diets-sections">
      <div class="diets-heading display-text">
        {{ $t("RECIPE_PREVIEW.DIETS") }}
      </div>
      <div v-show="!selectedDietsPreview?.length" class="no-diet-available">
        {{ $t("RECIPE_PREVIEW.NO_DIETS") }} <br />
        {{ $t("RECIPE_PREVIEW.CLICK_ON_DIET") }} <br />
        {{ $t("RECIPE_FORM") }}
      </div>
      <div class="diet-available" v-show="selectedDietsPreview?.length">
        <div v-for="(diet, index) in selectedDietsPreview" :key="index">
          <div class="image-and-diet-name">
            <div class="image">
              <img :src="diet?.image || ''" alt="diet-icon" />
            </div>
            <div class="diet-item-name">
              {{ diet?.name || "" }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="categories-sections">
      <div class="categories-heading display-text">
        {{ $t("RECIPE_PREVIEW.CATEGORIES_HEADING") }}
      </div>
      <div
        class="category-available"
        v-if="selectedCategoriesPreview?.length"
      >
        <template v-for="(category, index) in selectedCategoriesPreview" :key="index">
          <div
            v-if="
              category?.data?.[recipePreviewVariantDefaultLanguage]?.name ||
              category?.data?.[lang]?.name &&
              category?.data?.[recipePreviewVariantDefaultLanguage]?.image ||
              category?.data?.[lang]?.image
            "
          >
            <div class="image-and-category-name">
              <div
                v-if="category?.data?.[recipePreviewVariantDefaultLanguage]?.image || category?.data?.[lang]?.image"
                class="image"
              >
                <img
                  :src="category?.data?.[recipePreviewVariantDefaultLanguage]?.image || category?.data?.[lang]?.image"
                  alt="category-icon"
                />
              </div>
              <div class="categories-item-name">
                {{category?.data?.[recipePreviewVariantDefaultLanguage]?.name || category?.data?.[lang]?.name}}
              </div>
            </div>
          </div>
        </template>
      </div>
      <div
        v-if="!selectedCategoriesPreview?.length"
        class="no-category-available"
      >
        {{ $t("RECIPE_PREVIEW.NO_CATEGORIES") }} <br />
        {{ $t("RECIPE_PREVIEW.CLICK_ON_CATEGORY") }} <br />
        {{ $t("RECIPE_FORM") }}
      </div>
    </div>
    <div class="tags-sections">
      <div class="tags-heading display-text">{{ $t("TAG.TAG_TEXT") }}:</div>
      <div v-if="!selectedTagsPreview?.length" class="no-tag-available">
        {{ $t("RECIPE_PREVIEW.NO_TAGS") }} <br />
        {{ $t("RECIPE_PREVIEW.CLICK_ON_TAG") }} <br />
        {{ $t("RECIPE_FORM") }}
      </div>
      <div
        class="tag-available"
        v-if="selectedTagsPreview?.length"
      >
        <template v-for="(tag, index) in selectedTagsPreview" :key="index">
          <div
            v-if="tag?.data?.[recipePreviewVariantDefaultLanguage]?.name || tag?.data?.[lang]?.name"
            class="tag-container-for-preview"
          >
            <span class="tags-title">{{ tag?.data?.[recipePreviewVariantDefaultLanguage]?.name || tag?.data?.[lang]?.name || "" }}</span>
          </div>
        </template>
      </div>
    </div>
    <div v-if="isDisplayAllergens || allergensList.length" class="allergens-sections">
      <div class="allergens-heading display-text">
        {{ $t("RECIPE_PREVIEW.ALLERGENS") }}
      </div>
      <div v-show="!selectedAllergens?.length" class="no-allergens-available">
        {{ $t("RECIPE_PREVIEW.ALLERGENS") }} <br />
        {{ $t("RECIPE_PREVIEW.CLICK_ON_ALLERGEN") }} <br />
        {{ $t("RECIPE_FORM") }}
      </div>
      <div class="allergens-available" v-show="selectedAllergens?.length">
        <div v-for="(allergens, index) in selectedAllergens" :key="index">
          <div class="image-and-allergens-name">
            <div class="image">
              <img :src="allergens?.image || ''" alt="allergens-icon" />
            </div>
            <div class="allergens-item-name">
              {{ allergens?.name || "" }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  selectedDietsPreview: {
    type: Array,
    default: () => [],
  },
  recipePreviewVariantDefaultLanguage: {
    type: String,
    default: "",
  },
  selectedCategoriesPreview: {
    type: Array,
    default: () => [],
  },
  selectedTagsPreview: {
    type: Array,
    default: () => [],
  },
  selectedAllergens: {
    type: Array,
    default: () => [],
  },
  isDisplayAllergens: {
    type: Boolean,
    default: false,
  },
  lang: {
    type: String,
    default: "",
  },
  allergensList: {
    type: Array,
    default: () => [],
  },
});
</script>
