<template>
  <div class="recipe-preview-filter-slug-main-section">
    <div class="slug-sections">
      <div class="slug-heading display-text">
        {{ $t("RECIPE_PREVIEW.SLUG") }}
      </div>
      <div v-if="!slugEdit" class="no-slug-available">
        {{ $t("RECIPE_PREVIEW.NO_SLUG") }}
      </div>
      <div class="slug-available" v-if="slugEdit">
        <div class="slug-container-for-preview">
          <span class="slug-title">
            {{ slugEdit }}
          </span>
        </div>
      </div>
    </div>
    <div class="author-sections">
      <div class="author-heading display-text">
        {{ $t("RECIPE_PREVIEW.AUTHOR") }}
      </div>
      <div v-if="!recipeAttributionAuthor" class="no-author-available">
        {{ $t("RECIPE_PREVIEW.NO_AUTHOR") }}
      </div>
      <div class="author-available" v-if="recipeAttributionAuthor">
        <div class="author-container-for-preview">
          <span class="author-title">
            {{ recipeAttributionAuthor }}
          </span>
        </div>
      </div>
    </div>
    <div class="publisher-sections">
      <div v-show="isShowPublisher" class="publisher-heading display-text">
        {{ $t("RECIPE_PREVIEW.PUBLISHER") }}
      </div>
      <div v-if="hasNoPublisher" class="no-publisher-available">
        {{ $t("RECIPE_PREVIEW.NO_PUBLISHER") }}
      </div>
      <div v-if="isPublisherAvailable" class="publisher-available">
        <div class="publisher-container-for-preview">
          <span class="publisher-data">
            <img alt="publisher" :src="`${publisherImagePreview}`" />
            <p>{{ selectedPublisherName }}</p>
          </span>
        </div>
      </div>
    </div>
    <div class="author-sections">
      <div class="author-heading display-text">{{ $t("EXTERNAL_ID") }}:</div>
      <div v-if="!recipeAttributionExternalId" class="no-author-available">
        {{ $t("RECIPE_PREVIEW.NO_EXTERNAL") }}
      </div>
      <div class="author-available" v-if="recipeAttributionExternalId">
        <div class="author-container-for-preview">
          <span class="author-title">
            {{ recipeAttributionExternalId }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useNuxtApp } from "nuxt/app";
const { $t } = useNuxtApp();
const props = defineProps({
  slugEdit: {
    type: String,
    default: "",
  },
  recipeAttributionAuthor: {
    type: String,
    default: "",
  },
  publisherImagePreview: {
    type: String,
    default: "",
  },
  selectedPublisherName: {
    type: String,
    default: "",
  },
  isShowPublisher: {
    type: Boolean,
    default: false,
  },
  recipeAttributionExternalId: {
    type: String,
    default: "",
  },
});

const hasNoPublisher = computed(() => {
  return (!props.publisherImagePreview || props.selectedPublisherName === $t("COMMON.SELECT_ONE")) && props.isShowPublisher;
});

const isPublisherAvailable = computed(() => {
  return props.publisherImagePreview && props.selectedPublisherName !== $t("COMMON.SELECT_ONE") && props.isShowPublisher;
});
</script>