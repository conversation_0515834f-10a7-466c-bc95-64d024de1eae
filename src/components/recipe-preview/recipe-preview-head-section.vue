<template>
  <div class="recipe-preview-head-main-container">
    <div class="recipe-preview-head-inner-container">
      <div class="recipe-name">
        <div class="recipe-title" v-if="recipeName">
          {{ recipeName }}
        </div>
        <div class="recipe-subtitle" v-if="recipeSubtitle">
          {{ recipeSubtitle }}
        </div>
        <div v-if="!recipeName && !recipeSubtitle" class="recipe-title">
          {{ $t("RECIPE_PREVIEW.NO_NAME") }}
        </div>
      </div>
      <div class="time-container">
        <div class="clock">
          <img src="@/assets/images/yellowtime.png" alt="Total Time" />
          <div class="time-title text-head-title-1">
            {{ $t("RECIPE_PREVIEW.TOTAL_TIME") }}
          </div>
          <div v-if="totalTime" class="time-value text-title-5">
            {{ totalTime }}
          </div>
          <div v-else class="time-value-not-available text-title-5">
            {{ $t("RECIPE_PREVIEW.NONE") }}
          </div>
        </div>
        <div class="clock">
          <img src="@/assets/images/blluetime.png" alt="Prep Time" />
          <div class="time-title text-head-title-1">
            {{ $t("RECIPE_PREVIEW.PREP_TIME") }}
          </div>
          <div v-if="preprationTime" class="time-value text-title-5">
            {{ preprationTime }}
          </div>
          <div v-else class="time-value-not-available text-title-5">
            {{ $t("RECIPE_PREVIEW.NONE") }}
          </div>
        </div>
        <div class="clock">
          <img src="@/assets/images/redtime.png" alt="Cook Time" />
          <div class="time-title text-head-title-1">
            {{ $t("RECIPE_PREVIEW.COOK_TIME") }}
          </div>
          <div v-if="cookingTime" class="time-value text-title-5">
            {{ cookingTime }}
          </div>
          <div v-else class="time-value-not-available text-title-5">
            {{ $t("RECIPE_PREVIEW.NONE") }}
          </div>
        </div>
      </div>
    </div>
    <div class="recipe-description text-title-5">
      {{ description || $t("RECIPE_PREVIEW.NO_DESCRIPTION_TO_SHOW") }}
    </div>

    <div class="price-section">
      <div class="price-description text-title-3">
        {{ $t("RECIPE_PREVIEW.PRICE_PER_SERVING") }}:
        <span v-if="recipePrice > 0" class="price-data text-title-5">
          {{ recipePrice }} {{ recipeCurrency }}
        </span>
        <span v-else class="price-data text-title-5">
          {{ $t("RECIPE_PREVIEW.NO_PRICE_TO_SHOW") }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  recipeName: {
    type: String,
    default: "",
  },
  recipeSubtitle: {
    type: String,
    default: "",
  },
  totalTime: {
    type: String,
    default: "",
  },
  preprationTime: {
    type: String,
    default: "",
  },
  cookingTime: {
    type: String,
    default: "",
  },
  description: {
    type: String,
    default: "",
  },
  recipePrice: {
    type: Number,
    default: 0,
  },
  recipeCurrency: {
    type: String,
    default: "Dollar",
  },
});
</script>