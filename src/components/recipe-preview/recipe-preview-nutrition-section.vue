<template>
  <div v-if="hasNutrientData" class="recipe-preview-nutrition-main-section">
    <div class="nutrition-main-section">
      <div class="nutrition-heading-section">
        <div class="nutrition-heading-text">
          {{ $t("RECIPE_PREVIEW.NUTRITION") }}:
        </div>
        <div class="nutrition-serving-drop-menu">
          <div class="nutrition-toogle-serving-view-container">
            <div class="nutrition-selection-container">
              <button
                :class="
                  isNutritionDropDownIconVisible
                    ? 'nutrition-drop-down-container box-container btn-reset'
                    : 'nutrition-drop-down-container box-container disable-drop-down-box btn-reset'
                "
                @click="$emit('showNutritionDropDown')"
                id="preview-nutrition-drop-down"
                type="button"
              >
                <span
                  v-if="selectedNutritionType === 'perServing'"
                  class="selected-nutrition"
                  >{{ $t("RECIPE_PREVIEW.PER_SERVING") }}</span
                >
                <span
                  v-if="selectedNutritionType === 'per100g'"
                  class="selected-nutrition"
                >
                  {{ $t("RECIPE_PREVIEW.PER_100G") }}</span
                >
                <img
                  alt="dropdown-icon"
                  v-if="isNutritionDropDownIconVisible"
                  class="nutrition-dropdown-icon"
                  src="@/assets/images/arrow-right.png"
                  :class="{
                    rotate: isNutritionDropdownResultVisible,
                  }"
                />
              </button>
              <ul
                v-if="isNutritionDropdownResultVisible"
                class="nutrition-autocomplete-results"
              >
                <div
                  v-for="(result, index) in nutritionServingList"
                  :key="index"
                >
                  <li class="nutrition-autocomplete-result">
                    <button
                      @click="$emit('selectedNutrition', result.type)"
                      class="btn-reset"
                      type="button"
                    >
                      <p>{{ result.name }}</p>
                    </button>
                  </li>
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="nutrition-content-section">
        <div
          class="nutrition-serving-content-area"
          v-if="selectedNutritionType === 'perServing'"
        >
          <div class="nutrition-serving-text">
            <div class="serving-text">
              {{ $t("RECIPE_PREVIEW.SERVING_SIZE") }}
            </div>
            <div class="serving-unit">
              {{ nutritionServingSize ?? "--" }}
            </div>
          </div>
          <div class="nutrition-serving-text">
            <div class="serving-text">
              {{ $t("RECIPE_PREVIEW.SERVING_SIZE_PER_CONTAINER") }}
            </div>
            <div class="serving-unit">
              {{ nutritionServingSizePerContainer ?? "--" }}
            </div>
          </div>
          <div class="horizontal-line"></div>
        </div>
        <div
          class="nutrition-content-row"
          v-for="(item, index) in choosePerServingPer100g()"
          :key="index"
        >
          <div
            class="nutrition-name-heading"
            :class="
              item && !item.subNutrientName ? '' : 'nutrition-sub-name-heading'
            "
          >
            <p class="name">
              {{ item?.nutrientName ?? "" }}
            </p>
          </div>
          <div v-if="item && !item.isHeader" class="nutrition-unit-heading">
            {{
              item?.valueAmount
                ? String(item.valueAmount).length >= 9
                  ? String(item.valueAmount).slice(0, 9)
                  : Number(Number(item.valueAmount))
                : ""
            }}
            {{ item?.valueUnit && item?.valueAmount ? item.valueUnit : "" }}
          </div>
          <div
            v-if="item && !item.isHeader"
            class="nutrition-percentage-unit-heading"
          >
            <span class="nutrition-unit">
              {{
                item.dvpValue
                  ? Number(Number(item.dvpValue).toFixed(2)) + "%"
                  : ""
              }}</span
            >
          </div>
        </div>
      </div>
    </div>
    <div class="nutrition-notes-section">
      <div class="nutrition-text text-light-h4">
        {{ $t("RECIPE_PREVIEW.NUTRITION_INFO") }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  nutrientTableData: {
    type: Object,
    default: () => ({}),
  },
  nutritionServingSize: {
    type: String,
    default: "",
  },
  nutritionServingSizePerContainer: {
    type: String,
    default: "",
  },
  isNutritionDropDownIconVisible: {
    type: Boolean,
    default: false,
  },
  isNutritionDropdownResultVisible: {
    type: Boolean,
    default: false,
  },
  selectedNutritionType: {
    type: String,
    default: "perServing",
  },
  nutritionServingList: {
    type: Array,
    default: () => [],
  },
  choosePerServingPer100g: {
    type: Function,
  },
});

const hasNutrientData = computed(() => {
  return (
    props.nutrientTableData.per100g?.length ||
    props.nutrientTableData.perServing?.length
  );
});
</script>
