<template>
  <div class="recipe-preview-steps-main-container">
    <div class="recipe-preview-steps-inner-container">
      <div class="recipe-preview-steps-title text-h5" data-test-id="recipe-step-heading">
        {{ $t("RECIPE_PREVIEW.RECIPE_STEPS") }}:
      </div>
      <div
        v-if="isStepIngredientsDataLoading"
        class="recipe-preview-steps-loading-main"
      >
        <div class="content">
          <div class="input-loading">
            <div class="loader-image"></div>
          </div>
        </div>
      </div>
      <div
        v-if="hasSteps && !isStepIngredientsDataLoading"
        class="recipe-preview-steps-content"
      >
        <div
          v-for="(step, stepIndex) in tasksDataPreview"
          :key="stepIndex"
          class="recipe-preview-steps"
        >
          <div
            v-if="hasImage(step) || hasVideo(step)"
            :class="mediaClass(step)"
          >
            <div v-if="hasImage(step)">
              <img :src="getImageSrc(step)" alt="recipe step" class="image" />
            </div>
            <video
              v-if="hasVideo(step)"
              :src="getVideoSrc(step)"
              class="instruction-media"
            >
              <track
                v-if="subtitlesSrc"
                :src="subtitlesSrc"
                kind="subtitles"
                srclang="en"
                label="English"
              />
              <track
                v-if="descriptionSrc"
                :src="descriptionSrc"
                kind="description"
                srclang="en"
                label="English"
              />
            </video>
            <button
              v-if="hasVideo(step)"
              type="button"
              class="btn-reset"
              @click="openRecipeStepVideoPopup(getVideoSrc(step))"
            >
              <img
                src="@/assets/images/videoPlayBtn.png"
                alt="play"
                class="play-video-icon-image"
              />
            </button>
            <div class="video-image-counting">
              <div class="counting text-title-5">{{ stepIndex + 1 }}</div>
            </div>
          </div>
          <div v-else class="recipe-counting text-title-5">
            <span>{{ stepIndex + 1 }}</span>
          </div>
          <div class="recipe-details">
            <div v-if="hasTitle(step)" class="recipe-title text-title-3">
              {{ getTitle(step) }}
            </div>
            <div v-if="hasInstructions(step)">
              <div
                v-for="(instruction, index) in getInstructions(step)"
                :key="index"
                class="recipe-description text-title-5"
              >
                {{ instruction.text || "" }}
              </div>
            </div>
            <div v-if="hasIngredients(step)" class="recipe-ingredients">
              <ul>
                <li
                  v-for="(ingredient, index) in getIngredients(step)"
                  :key="index"
                  class="text-title-5"
                >
                  <span v-if="showQuantityAndUOM(ingredient)">
                    {{ ingredient.quantityMirror || ingredient.quantity || 0 }}
                    {{
                      ingredient.UOMMirror?.trim() ||
                      ingredient.UOM?.trim() ||
                      ""
                    }}
                  </span>
                  <b>{{ ingredient.nameMirror || ingredient.name || "" }}</b>
                  <span v-if="ingredient.modifier"
                    >, {{ ingredient.modifier }}</span
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="!hasSteps && !isStepIngredientsDataLoading"
        class="no-recipe-steps-available text-title-5"
      >
        {{ $t("RECIPE_PREVIEW.NO_RECIPE_STEPS") }} <br />
        {{ $t("RECIPE_PREVIEW.CLICK_ON_ADD_STEP") }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue';

const props = defineProps({
  tasksDataPreview: {
    type: Array,
    default: () => [],
  },
  isStepIngredientsDataLoading: {
    type: Boolean,
    default: false,
  },
  recipePreviewVariantDefaultLanguage: {
    type: String,
    default: 'en',
  },
});

const emit = defineEmits();

const { tasksDataPreview, recipePreviewVariantDefaultLanguage } = toRefs(props);

const hasSteps = computed(() => tasksDataPreview.value.length > 0);
const subtitlesSrc = computed(() => props.productVideo?.subtitles || '');
const descriptionSrc = computed(() => props.productVideo?.description || '');

const getImageSrc = (step) => hasImage(step) ? step[recipePreviewVariantDefaultLanguage.value]?.duplicateMedia?.image : '';
const hasImage = (step) => step?.[recipePreviewVariantDefaultLanguage.value]?.duplicateMedia?.image;

const hasVideo = (step) => step?.[recipePreviewVariantDefaultLanguage.value]?.duplicateMedia?.video?.[0]?.url;
const getVideoSrc = (step) => hasVideo(step) ? step[recipePreviewVariantDefaultLanguage.value].duplicateMedia.video[0].url : '';

const hasTitle = (step) => step?.[recipePreviewVariantDefaultLanguage.value]?.duplicateTitle;
const getTitle = (step) => hasTitle(step) ? step[recipePreviewVariantDefaultLanguage.value].duplicateTitle : '';

const hasInstructions = (step) => step?.[recipePreviewVariantDefaultLanguage.value]?.duplicateInstructions?.length > 0;
const getInstructions = (step) => hasInstructions(step) ? step[recipePreviewVariantDefaultLanguage.value].duplicateInstructions : [];

const hasIngredients = (step) => step?.[recipePreviewVariantDefaultLanguage.value]?.duplicateIngredients?.length > 0;
const getIngredients = (step) => hasIngredients(step) ? step[recipePreviewVariantDefaultLanguage.value].duplicateIngredients : [];

const mediaClass = (step) => hasImage(step) ? 'disable instruction-gallery' : 'instruction-gallery';

const openRecipeStepVideoPopup = (videoUrl) => {
  emit('openRecipeStepVideoPopup', videoUrl);
};

const showQuantityAndUOM = (ingredient) => {
  return (ingredient.quantityMirror || ingredient.quantity > 0) && (ingredient.UOMMirror?.trim() || ingredient.UOM?.trim());
};
</script>
