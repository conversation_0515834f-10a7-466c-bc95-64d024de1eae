<template>
  <div
    class="badge"
    :class="[badgeType, badgeClass, {
      'badge-only-icon': onlyIcon,
      'badge-small': isSmall,
      'badge-outline': isOutline,
    }]"
  >
    <img
      v-if="imgSrc"
      alt="badge icon"
      :src="imgSrc"
    />
    <span v-if="label">{{ label }}</span>
  </div>
</template>

<script setup>
import { BADGE_TYPE } from "@/components/badge/badge-type";

defineProps({
  label: {
    required: true,
  },
  imgSrc: {
    default: "",
  },
  badgeClass: {
    type: String,
    default: "",
  },
  badgeType: {
    type: [String], // const BADGE_TYPE
    default: BADGE_TYPE.LIGHT_GREEN,
  },
  isSmall: {
    type: Boolean,
    default: false,
  },
  isOutline: {
    type: Boolean,
    default: false,
  },
  onlyIcon: {
    type: Boolean,
    default: false,
  },
});
</script>
