<template>
    <div class="terms-policy-table-main">
        <div class="terms-heading">{{ data.heading }}</div>
        <div class="terms-details-container">
            <div v-for="item in data.definitions" :key="item.title" class="terms-details-main">
                <div class="terms-title">{{ item.title }}</div>
                <p v-html="item.description" class="terms-paragraph"></p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "terms-policy-table",
    props: {
        data: {
            type: Object,
            required: true
        }
    },
}
</script>