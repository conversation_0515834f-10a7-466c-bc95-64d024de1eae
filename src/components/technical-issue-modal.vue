<template>
  <Modal>
    <template #newCategoryPopUp>
      <div class="technical-issue-modal">
        <div class="technical-issue-modal-content">
          <button type="button" class="btn-reset" @click="closeTechnicalIssueModal()">
            <img
              alt="close-icon"
              src="@/assets/images/close-icon.png"
            />
          </button>
        </div>
        <div class="technical-issue-header">
          {{ $t('TECHNICAL_ISSUES.TECHNICAL_HEADER') }}
        </div>
        <div class="technical-issue-details">
          <p
            v-if="hasUnpublishedDateTimeout"
            class="technical-issue-description"
          >
            {{ $t('TECHNICAL_ISSUES.TECHNICAL_UNPUBLISH_TITLE') }}
          </p>
          <p v-if="hasPublishedDateTimeout" class="technical-issue-description">
            {{ $t('TECHNICAL_ISSUES.TECHNICAL_PUBLISH_TITLE') }}
          </p>
          <br />
          <ol v-if="hasUnpublishedDateTimeout" class="issue-resolve-list">
            <li>
              {{ $t('TECHNICAL_ISSUES.TECHNICAL_UNPUBLISH_DESC') }}
            </li>
            <li>
              {{ $t('TECHNICAL_ISSUES.CONTACT') }}
              <span class="technical-issue-email-text">{{ $t('TECHNICAL_ISSUES.CMS_SUPPORT') }}</span>
            </li>
          </ol>
          <ol v-if="hasPublishedDateTimeout" class="issue-resolve-list">
            <li>
              {{ $t('TECHNICAL_ISSUES.CONTACT') }}
              <span class="technical-issue-email-text">{{ $t('TECHNICAL_ISSUES.CMS_SUPPORT') }}</span>
            </li>
            <li>{{ $t('TECHNICAL_ISSUES.CLEAR_SCHEDULE') }}</li>
            <li>
              {{ $t('TECHNICAL_ISSUES.CLEAR_SCHEDULE_DESC') }}
            </li>
          </ol>
        </div>
        <div class="button-section">
          <button type="button"
            class="btn-green-outline"
            @click="closeTechnicalIssueModal()"
          >
            {{ $t('BUTTONS.CLOSE_BUTTON') }}
          </button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
/**
 * @deprecated We should use src/components/pages/recipes/recipe-technical-issue-modal.vue component!
 */

import Modal from "@/components/Modal";
export default {
  name: "technical-issue-modal",
  components: {
    Modal,
  },
  props: {
    closeTechnicalIssueModal: {
      type: Function,
      default: () => {},
    },
    hasUnpublishedDateTimeout: {
      type: Boolean,
      default: false,
    },
    hasPublishedDateTimeout: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
