<template>
  <div
      ref="iq-r-g-slider-skeleton-fer"
      class="iq-r-g-slider iq-r-g-slider-skeleton"
      :class="{
        '__with-one-slide': slidesArray?.length <= 1
      }"
  >
    <div v-for="item in slidesArray" class="iq-r-g-slider-slide iq-r-g-slider-skeleton-slide">
      <div class="iq-r-g-slider-slide-wrapper">
        <span class="iq-r-g-slider-slide-number">{{ item }}</span>
        <div v-if="isImageLoading" class="iq-r-g-slider-slide-loader"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { useStore } from "vuex";
import { refUtils } from "@/mixins/refUtils";
import { RECIPE_GENERATION_FLAG } from "@/models/recipe-generator.model";
import { getMarginSlides } from "@/utils/recipe-generator";

const store = useStore();

const slidesArray = ref([1, 2, 3]);
const slideWidth = 305;

const isImageLoading = computed(() => {
  const isGenerating = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATING);
  const isImageRefreshing = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING);
  return isGenerating || isImageRefreshing;
});

const prepareSkeleton = () => {
  const skeletonContainerEl = refUtils.getRef("iq-r-g-slider-skeleton-fer");
  if (skeletonContainerEl) {
    const containerWidth = skeletonContainerEl.clientWidth;
    const slidesPerPage = Math.round(containerWidth / slideWidth);
    const { slides } = getMarginSlides(slidesPerPage, containerWidth, slideWidth);
    slidesArray.value = (slides <= 3) ? [...Array(slides + 1).keys()].filter(Boolean) : [1, 2, 3];
  }
};

onMounted(() => {
  prepareSkeleton();
  window.addEventListener('resize', prepareSkeleton);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', prepareSkeleton);
});
</script>
