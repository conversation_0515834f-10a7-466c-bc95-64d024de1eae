<template>
  <div ref="sliderRef" class="iq-r-g-image-selector">
    <div class="iq-r-g-image-selector-container" :style="imageSelectorContainerStyle">

      <div v-if="isImageLoading" class="iq-r-g-image-selector-loading">
        <div class="iq-r-g-image-selector-loading-body">
          <span class="iq-r-g-image-selector-loading-icon"></span>
          <span data-test-id="processing-image-validation-text" class="text-title-2 font-weight-semi-bold color-graphite-gray">{{ $t("GENERATOR.IMAGE_SELECTOR_LOADING_TEXT") }}</span>
        </div>
      </div>

      <button
          v-show="!isImageLoading && currentIndex > 0"
          type="button"
          class="btn-reset iq-r-g-image-selector-slider-nav-btn iq-r-g-image-selector-slider-nav-btn-prev"
          @click.stop="prevSlide"
      ><span></span></button>

      <div class="iq-r-g-image-selector-slider-wrapper">
        <div
            class="iq-r-g-image-selector-slider"
            :style="sliderStyle"
            :data-c-index="currentIndex"
            :data-m-index="maxIndex"
            :data-slider-groups-count="imageList?.length"
        >
          <template v-for="(innerList, index) in imageList">
            <div
              class="iq-r-g-image-selector-slider-group"
              data-test-id="generator-model-image-selector-slider-group"
              :style="getSliderGroupStyles(innerList?.length, index)"
              :data-slider-group-index="index"
              :data-slider-group-count="innerList?.length"
              :data-slider-gpoup-active="currentIndex === index ? 'true' : 'false'"
            >
              <template v-for="item in innerList">
                  <recipe-image-selector-slide
                    v-show="!item?.isSelected"
                    :isLoading="isImageLoading"
                    :isHiddenActions="isFoodLMGenerator"
                    :slideData="item"
                    :isShowLimitMessage="isImageSelectedLimit"
                    @onZoomInImage="zoomInImage"
                    @onSelect="selectImage"
                    @onAssessment="setAssessment"
                  ></recipe-image-selector-slide>
              </template>
            </div>
          </template>
        </div>
      </div>

      <button
          v-show="!isImageLoading && currentIndex < maxIndex"
          type="button"
          class="btn-reset iq-r-g-image-selector-slider-nav-btn iq-r-g-image-selector-slider-nav-btn-next"
          @click.stop="nextSlide"
      ><span></span></button>

    </div>

    <image-zoom-popup
      v-if="zoomImageSrc"
      :zoomedImage="zoomImageSrc"
      :selectedImageIndex="currentImageIndex"
      :imageList="imageList"
      @prev-image="updateZoomedImage($keys.KEY_NAMES.PREV)"
      @next-image="updateZoomedImage($keys.KEY_NAMES.NEXT)"
      @close-image-zoom="zoomOutImage"
    ></image-zoom-popup>

  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, getCurrentInstance } from "vue";
import { getRecipeImageObject, RECIPE_GENERATION_FLAG } from "@/models/recipe-generator.model";
import ImageZoomPopup from "@/components/image-zoom-popup.vue";
import RecipeImageSelectorSlide from "@/components/recipe-generator/recipe-image-selector-slide.vue";
import { useStore } from "vuex";
import { useNuxtApp } from "#app";

const store = useStore();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $eventBus } = useNuxtApp();

// Data variables (reactive)
const currentIndex = ref(0);
const maxIndex = ref(0);

const defaultSlideRow = ref(0);
const defaultSlideGrid = ref(0);
const currentSlideRow = ref(0);

const sliderWrapperWidth = ref(0);
const sliderHeight = ref(351);
const slideWidth = ref(165);
const slideXGap = ref(23);
const sliderShift = ref(188); // 165 + 23

const zoomImageSrc = ref(undefined);
const currentImageIndex = ref(0); // Index of the currently zoomed image
const defaultSlideList = ref([]);
const sliderRef = ref(null);

const isDefaultSliderState = ref(true);
const debouncedPrepareSlider = ref(undefined);
const debouncedGetDefaultSlideList = ref(undefined);

const isFoodLMGenerator = computed(() => 
  store.getters['recipeGeneration/getFoodLMGenerator']
);

const isImageLoading = computed(() => 
  store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATING) ||
  store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING)
);

const isSelectedImages = computed(() => {
  const list = store.getters["recipeGeneration/getSortedImageListByAverageStarValue"];
  return list?.filter(item => item?.isSelected)?.length;
});

const imageList = computed(() => {
  const sortedImageList = store.getters["recipeGeneration/getSortedImageListByAverageStarValue"];
  if (sortedImageList?.length && !isImageLoading.value) {
    return sortedImageList.filter(item => !item?.isSelected).reduce((resultArray, item, index) => {
      const chunkIndex = Math.floor(index / defaultSlideGrid.value);
      if (!resultArray[chunkIndex]) {
        resultArray[chunkIndex] = []; // start a new chunk
      }
      resultArray[chunkIndex].push(item);
      return resultArray;
    }, []);
  }
  return defaultSlideList.value;
});

const imageListCount = computed(() => {
  const list = store.getters["recipeGeneration/getSortedImageListByAverageStarValue"];
  return list?.filter(item => !item?.isSelected).length || defaultSlideList.value?.length;
});

const imageSelectorContainerStyle = computed(() => ({
  width: `${sliderWrapperWidth.value}px`,
}));

const sliderStyle = computed(() => {
  const sliderGroups = imageList.value?.length;
  return {
    width: `${sliderWrapperWidth.value * sliderGroups + slideXGap.value * (sliderGroups - 1)}px`,
    transform: `translateX(-${sliderShift.value * currentSlideRow.value}px)`,
  };
});

const isImageSelectedLimit = computed(() => {
  return store.getters["recipeGeneration/getIsImageSelectedLimit"];
});

const resetAt = computed(() => {
  return store.getters["recipeGeneration/getResetAt"];
});

watch(isImageLoading, (val) => {
  prepareSlider();
  if (val) {
    currentIndex.value = 0;
    currentSlideRow.value = 0;
  }
});

watch(imageList, () => {
  prepareSlider(true);
});

watch(isSelectedImages, (val) => {
  if (val) {
    isDefaultSliderState.value = false;
  }
});

watch(resetAt, (val) => {
  if (!val) {
    return;
  }
  resetAll();
});

onMounted(() => {
  prepareSlider();
  setDefaultSlideList();

  debouncedPrepareSlider.value = debounce(prepareSlider, 200);
  debouncedGetDefaultSlideList.value = debounce(setDefaultSlideList, 200);
  window.addEventListener('resize', debouncedPrepareSlider.value);
  window.addEventListener('resize', debouncedGetDefaultSlideList.value);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', debouncedPrepareSlider.value);
  window.removeEventListener('resize', debouncedGetDefaultSlideList.value);
});
/**
 * Updates the zoomed image based on the navigation direction.
 *
 * This method updates the `currentImageIndex` to either the next or previous image in the `imageList` array,
 * and sets the `zoomImageSrc` to the new image's source. The `imageList` is flattened before processing,
 * and the index is constrained to the valid range of images.
 *
 * @param {string} direction - The navigation direction, either 'next' or 'prev'.
 * If 'next', the current image index is incremented, and if 'prev', it's decremented.
 */
const updateZoomedImage = (direction) => {
  const flatImageList = imageList.value?.flat() || [];
  const maxIndexValue = flatImageList.length - 1;
  currentImageIndex.value = direction === 'next'
    ? Math.min(currentImageIndex.value + 1, maxIndexValue)
    : Math.max(currentImageIndex.value - 1, 0);
  
  zoomImageSrc.value = flatImageList[currentImageIndex.value]?.key;
};

const prepareSlider = (keepPosition = false) => {
  if (!keepPosition) {
    currentIndex.value = 0;
    currentSlideRow.value = 0;
  }

  const count = imageListCount.value;
  if (!count) {
    return;
  }

  const { slideRow, slideGrid } = getPossibleSlideDetails();
  defaultSlideRow.value = slideRow;
  defaultSlideGrid.value = slideGrid;

  const possibleMaxIndex = count / slideGrid;
  const decimals = possibleMaxIndex - Math.floor(possibleMaxIndex);
  const maxIndexValue = decimals >= 0.5 || decimals === 0 ? Math.round(possibleMaxIndex) - 1 : Math.round(possibleMaxIndex);
  maxIndex.value = count > slideGrid ? maxIndexValue : 0;

  sliderWrapperWidth.value = slideWidth.value * slideRow + slideXGap.value * (slideRow - 1);

  if (keepPosition && isSelectedImages.value) {
    // return the prev slide position if the current slide is empty
    if (count && currentSlideRow.value && count <= (currentSlideRow.value * 2)) {
      prevSlide();
    }
  } else if (keepPosition && !isSelectedImages.value && isDefaultSliderState.value) {
    resetAll();
  }
}

const resetAll = () => {
  currentIndex.value = 0;
  currentSlideRow.value = 0;
  isDefaultSliderState.value = true;
};

const nextSlide = () => {
  if (currentIndex.value < maxIndex.value) {
    currentSlideRow.value += Math.round((imageList.value[currentIndex.value + 1]?.length || 0) / 2);
    currentIndex.value++;
  }
};

const prevSlide = () => {
  if (currentIndex.value > 0) {
    currentSlideRow.value = Math.max(0, currentSlideRow.value - defaultSlideRow.value);
    currentIndex.value--;
  }
};

/**
 * Calculates the possible number of slides that can fit in a row and grid layout within a slider container.
 *
 * @returns {Object} An object containing the number of slides in a row (`slideRow`) and the number in the grid (`slideGrid`).
 */
const getPossibleSlideDetails = () => {
  const sliderEl = sliderRef.value;
  if (!sliderEl) {
    return {
      slideRow: 6,
      slideGrid: 12,
    };
  }

  // calculate the number of slides that can fit in the row based on the slider width
  const slidesInRow = Math.floor(sliderEl.clientWidth / slideWidth.value);

  // calculate the effective width available for slides, excluding gaps
  const clearSliderWidth = sliderEl.clientWidth - (slideXGap.value * (slidesInRow - 1));

  // calculate the final number of slides that can fit in a row
  const slideRow = Math.max(Math.floor(clearSliderWidth / slideWidth.value), 1);

  return {
    slideRow,
    slideGrid: slideRow * 2,
  };
};

/**
 * Sets the default slide list used when there are no images available.
 */
const setDefaultSlideList = () => {
  const { slideGrid } = getPossibleSlideDetails();
  defaultSlideList.value = [Array.from({ length: slideGrid }, () => getRecipeImageObject({}))];
};
/**
 * Generates styles for each slider group based on the number of slides and their index.
 *
 * @param {number} innerListLength - The length of the inner list (number of slides in a group).
 * @param {number} index - The index of the slider group.
 * @returns {Object} An object containing the styles for the slider group.
 */
const getSliderGroupStyles = (innerListLength, index) => {
  const slidesInRow = Math.ceil(innerListLength / 2);
  return {
    gap: `21px ${slideXGap.value}px`,
    width: `${slideWidth.value * slidesInRow + (slideXGap.value * (slidesInRow - 1))}px`,
    "min-width": index === 0 ? `${sliderWrapperWidth.value}px` : null,
  };
};
/**
 * Zooms in on an image by its key.
 * Finds the image in a flattened image list, updates the current image index, and sets the zoomed image source.
 *
 * @param {string} key - The unique key identifying the image to zoom in on.
 */
const zoomInImage = (key) => {
  const flatImageList = imageList.value.flat();
  currentImageIndex.value = flatImageList.findIndex((item) => item.key === key);
  zoomImageSrc.value = key;
};

const zoomOutImage = () => {
  zoomImageSrc.value = undefined;
};
const selectImage = ({ key, value }) => {
  store.dispatch('recipeGeneration/setImageSelected', { key, value });
};

const setAssessment = ({ key, value }) => {
  store.dispatch('recipeGeneration/updateImageAssessment', { key, assessment: value });
  $eventBus.emit($keys.KEY_NAMES.GENERATION_DATA_UPDATED);
};

const debounce = (func, wait) => {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => { func.apply(this, args); }, wait);
  };
};
</script>
