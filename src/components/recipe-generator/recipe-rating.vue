<template>
  <div class="iq-r-g-container">

    <template v-if="isRecipeTasteGenerating && !isRecipeReviewTasteGenerationComplete">
      <p class="iq-r-g-rating-calculating text-title-2 font-weight-semi-bold color-graphite-gray">Calculating rating…</p>
    </template>
    <template v-if="!isRecipeTasteGenerating && isRecipeReviewTasteGenerationComplete && isRecipeReview">
      <div class="iq-r-g-rating-rate">
        <p class="text-title-2 font-weight-semi-bold color-graphite-gray">Our AI validator rated this recipe:</p>
        <p class="text-title-2 font-normal color-graphite-gray">
          Taste: <span class="font-weight-semi-bold">{{ recipeReview.taste_rating_star }}</span>
        </p>
        <p class="text-title-2 font-normal color-graphite-gray">
          Texture: <span class="font-weight-semi-bold">{{ recipeReview.texture_rating_star }}</span>
        </p>
        <p class="text-title-2 font-normal color-graphite-gray">
          Consistency: <span class="font-weight-semi-bold">{{ recipeReview.consistency_rating_star }}</span>
        </p>
      </div>
    </template>


    <template v-if="isGenerationComplete && isGeneratedCorrectly && !isRecipeModifying">
      <template v-if="!isFeedbackSubmit">
        <div class="iq-r-g-rating-question">
          <p class="text-title-2 font-weight-semi-bold color-graphite-gray">What do you think?</p>
          <recipe-rating-buttons @customClick="rateRecipe"></recipe-rating-buttons>
        </div>

        <div v-if="recipeRating === badRating" class="iq-r-g-rating-feedback">
          <div class="iq-r-g-rating-feedback-actions">
            <div class="iq-r-g-rating-feedback-actions-radio">
              <label v-for="reason in reasonList" class="control-radio control-radio-20">
                {{ reason.label }}
                <input
                    type="radio"
                    name="feedback-field-name"
                    :value="reason.value"
                    v-model="assessmentReasonModel"
                >
                <span class="checkmark"></span>
              </label>
            </div>
            <button
                type="button"
                class="btn-green"
                :disabled="!assessmentReasonModel"
                @click="submitFeedback()"
            >Submit</button>
          </div>
          <p class="text-light-h3 color-shadow-gray">Your feedback helps us to further train and improve our validators. Thank you!</p>
        </div>
      </template>
      <template v-else>
        <p class="text-light-h3 color-shadow-gray">Thank you for the feedback!</p>
      </template>

    </template>
  </div>
</template>

<script setup>
import { ref, computed, watchEffect, watch, onBeforeUnmount, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { useNuxtApp } from "#app";
import {
  GENERATED_DATA_RESULT,
  RECIPE_GENERATION_FLAG,
  RECIPE_GENERATION_FLOW,
} from "@/models/recipe-generator.model";
import RecipeRatingButtons from "@/components/recipe-generator/recipe-rating-buttons.vue";

const store = useStore();
const { isGenerationComplete, scrollToPageBottom, isGeneratedCorrectly, isRecipeModifying, flow, resetAt } = useRecipeGenerator();

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $eventBus } = useNuxtApp();

const goodRating = GENERATED_DATA_RESULT.GOOD_RATING;
const badRating = GENERATED_DATA_RESULT.BAD_RATING;

const reasonList = [
  { label: "Not what I wanted", value: "Not what I wanted" },
  { label: "Poor quality/taste", value: "Poor quality/taste" },
  { label: "Too complicated", value: "Too complicated" },
  { label: "Other", value: "Other" },
];

// Reactive state
const assessmentReasonModel = ref(undefined);
const isFeedbackSubmit = ref(false);
let timer = null;

const recipeReview = computed(() => store.getters["recipeGeneration/getRecipeReview"]);
const recipeRating = computed(() => store.getters["recipeGeneration/getRecipeRating"]);
const isRecipeReview = computed(() => Object.values(recipeReview.value).some(Boolean));
const isRecipeTasteGenerating = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATING));
const isRecipeReviewTasteGenerationComplete = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATION_COMPLETE));

watchEffect(() => {
  if (recipeRating.value === badRating.value) {
    setTimeout(() => scrollToPageBottom(), 10);
  }
});
watch(flow, (val) => {
  if ([RECIPE_GENERATION_FLOW.RECIPE_GENERATING, RECIPE_GENERATION_FLOW.RECIPE_REGENERATING, RECIPE_GENERATION_FLOW.RECIPE_MODIFYING].includes(val)) {
    store.dispatch("recipeGeneration/setAssessmentReason", { value: null });
    store.dispatch("recipeGeneration/setRecipeRating", { value: null });
    resetAll();
  }
});
watch(resetAt, (val) => {
  if (!val) {
    return;
  }

  resetAll();

});
onBeforeUnmount(() => {
  resetAll();
});

const rateRecipe = (assessment) => {
  store.dispatch("recipeGeneration/setRecipeRating", { value: assessment });

  if (assessment === goodRating) {
    emitEvent();
  }
};
const submitFeedback = () => {
  store.dispatch("recipeGeneration/setAssessmentReason", { value: assessmentReasonModel.value });
  emitEvent();
};
    
const emitEvent = () => {
  $eventBus.emit($keys.KEY_NAMES.GENERATION_DATA_UPDATED);
  setFeedbackFlag();
};
const setFeedbackFlag = () => {
  clearTimer();
  timer = setTimeout(() => {
    isFeedbackSubmit.value = true;
  }, 150);
};
const clearTimer = () => {
  if (timer) {
    clearTimeout(timer);
  }
};
const resetAll = () => {
  isFeedbackSubmit.value = false;
  assessmentReasonModel.value = undefined;
  clearTimer();
};
</script>
