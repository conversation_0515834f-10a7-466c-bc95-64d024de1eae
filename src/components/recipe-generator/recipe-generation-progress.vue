<template>
  <div data-test-id="generator-recipe-output-panel" ref="progress-panel-ref" class="iq-r-g-recipe-output-panel" data-panel="progress">
    <div data-test-id="generator-recipe-output-loader" v-if="getGenerationProgressSteps?.length" class="iq-r-g-recipe-output-loader">
      <img src="@/assets/images/generate-recipe.png" alt="" />
      <p class="color-gray-tundora">{{ loaderText }}{{ isLoading ? "..." : "" }}</p>
    </div>

    <template v-for="item in getGenerationProgressSteps">
      <div
          class="iq-r-g-recipe-output-loader-text color-gray-tundora"
          :class="item.type === CONTENT_GENERATION_TYPE.FAILURE ? 'color-red-d' : 'color-gray-tundora'"
      >
        <template v-if="!item.isDone"><b>{{ item.result }}...</b></template>
        <template v-else>
          <span :data-test-id="item.result">{{ item.result }}</span>
          <img v-if="item.type === CONTENT_GENERATION_TYPE.RESULT" src="@/assets/images/generator-validation-check.png" alt=""/>
          <img v-else-if="item.type === CONTENT_GENERATION_TYPE.FAILURE" src="@/assets/images/generator-validation-error.png" alt=""/>
        </template>
      </div>
    </template>
  </div>
</template>

<script setup>
import { useStore } from 'vuex';
import { CONTENT_GENERATION_TYPE, RECIPE_GENERATION_FLOW } from "@/models/recipe-generator.model";
import { watch, computed } from 'vue';
import { useRefUtils } from '@/composables/useRefUtils';


const store = useStore();
const { getRef } = useRefUtils();
const { flow, isLoading } = useRecipeGenerator();
const getGenerationProgressSteps = computed(() => store.getters['recipeGeneration/getGenerationProgressSteps']);

const loaderText = computed(() => {
  return {
    [RECIPE_GENERATION_FLOW.RECIPE_GENERATING]: "Generating Recipe",
    [RECIPE_GENERATION_FLOW.RECIPE_MODIFYING]: "Modifying Recipe",
    [RECIPE_GENERATION_FLOW.RECIPE_REFRESH_IMAGE]: "Refreshing Images",
    [RECIPE_GENERATION_FLOW.INTERMEDIATE]: "Generation progress"
  }[flow.value] || "Generation progress";
});

const scrollDown = () => {
  if (process.client) {
    setTimeout(() => {
      const elRef = getRef('progress-panel-ref');
      if (elRef) {
        elRef.scrollTop = elRef.scrollHeight;
      }
    });
  }
};

watch(getGenerationProgressSteps, (steps) => {
  if (steps?.length && isLoading.value) {
    scrollDown();
  }
});

</script>
