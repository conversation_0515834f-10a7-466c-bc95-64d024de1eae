<template>
  <div class="iq-r-g-recipe-models">
    <div class="iq-r-g-recipe-models-wrapper">
      <div class="iq-r-g-recipe-models-list">
        <h3 data-test-id="generator-model-heading"  class="text-light-h3 color-graphite-gray">Model:</h3>
        <div data-test-id="generator-model-container" class="iq-r-g-recipe-models-list-group-text-models">
          <template v-for="model in textModelList">
            <label
                v-if="model.enabled"
                class="control-radio control-radio-20"
                :class="{ 'control-radio-silver': model.selected && model.disabled }"
            >
              {{ model.name }}
              <input
                  type="radio"
                  name="recipe-text-model-radio"
                  :value="model.id"
                  :checked="model.selected"
                  :disabled="isLoading || model.disabled"
              >
              <span class="checkmark"></span>
            </label>
          </template>
        </div>
        <div class="iq-r-g-recipe-models-list-group-divider"></div>
        <div class="iq-r-g-recipe-models-list-group-image-models">
          <div class="iq-r-g-recipe-models-list-group-image-models-holder">
            <div class="iq-r-g-recipe-models-list-group-image-models-holder-dynamic-checkbox">
              <div ref="checkBox" class="iq-r-g-recipe-models-list-group-image-models-holder-dynamic-checkbox-row">
                <div data-test-id="generator-image-model-container" class="iq-r-g-recipe-models-list-group-image-models-holder-dynamic-checkbox-row-firstrow">
                  <template v-for="(model, index) in imageModelList">
                    <label
                      v-if="model.enabled && index <= maxIndex"
                      :key="model.id"
                      class="checkbox checkbox-20"
                      :class="{
                        'checkbox-silver': model.selected && model.disabled,
                      }"
                    >
                      {{ model.name }}
                      <input
                        type="checkbox"
                        :value="model.id"
                        :checked="model.selected"
                        :disabled="isLoading || model.disabled"
                        @change="updateImageModel"
                      />
                      <span class="checkmark"></span>
                    </label>
                  </template>
               </div>
              </div>
            </div>
            <button
              type="button"
              class="iq-r-g-recipe-models-list-group-image-models-holder-dropdown-btn btn-reset"
              @click="openDialogDropdown()"
              data-test-id="generator-model-dropdown-button"
            >
                <img
                  alt="drop-icon"
                  class="icon"
                  src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                  :class="{
                    'icon-down': isCheckBoxDropDownVisible,
                  }"
                />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useStore } from "vuex";

// Use the Vuex store
const store = useStore();

// Reactive state variables
const isCheckBoxDropDownVisible = ref(false);
const isEnableDropDownIcon = ref(true);
const maxIndex = ref(1);
const { isLoading } = useRecipeGenerator();

// Computed properties
const imageModelList = computed(() => store.getters["recipeGeneration/getImageGenerationModels"]);
const textModelList = computed(() => store.getters["recipeGeneration/getTextGenerationModels"]);

// Method to open the dropdown dialog
const openDialogDropdown = () => {
  isCheckBoxDropDownVisible.value = !isCheckBoxDropDownVisible.value;
  maxIndex.value = isCheckBoxDropDownVisible.value ? imageModelList.value.length : 1;
};

// Method to update the image model
const updateImageModel = (event) => {
  if (!event.target) {
    return;
  }

  const { value, checked } = event.target;

  store.dispatch("recipeGeneration/updateImageGenerationModels", {
    id: value,
    selected: checked,
  });
};
</script>
