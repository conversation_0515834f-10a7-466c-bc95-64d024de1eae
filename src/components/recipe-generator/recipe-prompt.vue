<template>
  <form class="iq-r-g-prompt" @submit.prevent="submit">
    <input
      type="text"
      class="iq-r-g-prompt-field"
      data-test-id="generator-prompt-input"
      autocomplete="off"
      placeholder="Enter prompt"
      v-model.trim="promptValue"
    >
    <button type="submit" data-test-id="generate-button" class="iq-r-g-prompt-btn" :disabled="isLoading">
      <img src="@/assets/images/generator-white-icon.png" alt="" />
      {{ isGenerationComplete ? "Regenerate" : "Generate" }}
    </button>
  </form>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex'; // Import Vuex store

// Use the Vuex store
const store = useStore();
const { isGenerationComplete, isLoading } = useRecipeGenerator();
// Define promptValue as a computed property
const promptValue = computed({
  get: () => store.getters["recipeGeneration/getPromptValue"],
  set: (value) => {
    store.dispatch("recipeGeneration/setPromptValue", { value });
  },
});

// Define the emit function
const emit = defineEmits(['onGenerate']);

// Submit function
const submit = () => {
  if (!promptValue.value || isLoading.value) {
    return;
  }
  emit('onGenerate', promptValue.value);
};
</script>
