<template>
  <div ref="recipe-output-ref" class="iq-r-g-container">
    <div id="recipe-output-header" class="iq-r-g-container-header">
      <template v-for="item in panelButtons">
        <button type="button"
                class="iq-r-g-container-header-btn btn-reset display-4 color-green"
                :data-test-id="`generator-output-${item.label}-button`"
                :class="{
                   'iq-r-g-container-header-btn-active': item.isActive,
                   'iq-r-g-container-header-btn-right': item.isRightSide,
                }"
                :disabled="(flow === RECIPE_GENERATION_FLOW.RECIPE_BLANK && selectedPanelId !== item.id)
                || (isGenerating && item.disabledUntilGenerated && !isRecipeDetailsGenerationComplete)
                || (isImageRefreshing && item.disabledUntilImageRefreshing)
                || (isRecipeModifying && item.disabledUntilModifying)
                || (!isGeneratedCorrectly && isGenerationComplete && item.disabledIfGeneratedIncorrect)"
                @click="changePanel(item.id)"
        >{{ item.label }}</button>
      </template>
    </div>
    <div class="iq-r-g-container-body">
      <template v-if="selectedPanelId === RECIPE_OUTPUT_PANEL.PROGRESS">
        <recipe-generation-progress></recipe-generation-progress>
      </template>

      <div class="iq-r-g-recipe-output-panel" v-if="selectedPanelId === RECIPE_OUTPUT_PANEL.RECIPE" :data-panel="RECIPE_OUTPUT_PANEL.RECIPE">
        <recipe-generated-recipe-data></recipe-generated-recipe-data>
      </div>

      <template v-if="selectedPanelId === RECIPE_OUTPUT_PANEL.MODIFY">
        <recipe-output-modify
            @cancelModify="cancelModify"
            @confirmModify="confirmModify"
            :finishedModifyCallback="finishModify"
        ></recipe-output-modify>
      </template>

      <template v-if="selectedPanelId === RECIPE_OUTPUT_PANEL.EDIT">
        <recipe-output-edit @onCancel="cancelEdit" @onConfirm="confirmEdit"></recipe-output-edit>
      </template>

      <div class="iq-r-g-recipe-output-panel" v-if="selectedPanelId === RECIPE_OUTPUT_PANEL.ADVANCED_OUTPUT" :data-panel="RECIPE_OUTPUT_PANEL.ADVANCED_OUTPUT">
        <div class="iq-r-g-recipe-output-advance color-gray-tundora">
          {{ streamText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watchEffect, watch } from 'vue'
import { useStore } from 'vuex';
import { RECIPE_GENERATION_FLAG, RECIPE_GENERATION_FLOW, RECIPE_OUTPUT_PANEL } from "@/models/recipe-generator.model";
import RecipeOutputModify from "@/components/recipe-generator/recipe-output-modify.vue";
import RecipeOutputEdit from "@/components/recipe-generator/recipe-output-edit.vue";
import RecipeGeneratedRecipeData from "@/components/recipe-generator/recipe-generated-recipe-data.vue";
import RecipeGenerationProgress from "@/components/recipe-generator/recipe-generation-progress.vue";

const { isGenerationComplete, openOutputPanel: panelOpenValue, resetAt, scrollToPageBottom } = useRecipeGenerator();
const store = useStore();
const { isGenerating, isGeneratedCorrectly, isImageRefreshing, flow } = useRecipeGenerator();
const modifyPromptValue = ref("");
const selectedPanelId = ref(RECIPE_OUTPUT_PANEL.PROGRESS);
const panelButtons = ref([
  {
    id: RECIPE_OUTPUT_PANEL.PROGRESS,
    label: "Progress",
    isActive: true,
    isRightSide: false,
    disabledUntilGenerated: false,
    disabledUntilImageRefreshing: false,
    disabledUntilModifying: false,
    disabledIfGeneratedIncorrect: false,
  },
  {
    id: RECIPE_OUTPUT_PANEL.RECIPE,
    label: "Recipe",
    isActive: false,
    isRightSide: false,
    disabledUntilGenerated: true,
    disabledUntilImageRefreshing: false,
    disabledUntilModifying: true,
    disabledIfGeneratedIncorrect: true,
  },
  {
    id: RECIPE_OUTPUT_PANEL.MODIFY,
    label: "Modify",
    isActive: false,
    isRightSide: false,
    disabledUntilGenerated: true,
    disabledUntilImageRefreshing: false,
    disabledUntilModifying: true,
    disabledIfGeneratedIncorrect: true,
  },
  {
    id: RECIPE_OUTPUT_PANEL.EDIT,
    label: "Edit",
    isActive: false,
    isRightSide: false,
    disabledUntilGenerated: true,
    disabledUntilImageRefreshing: false,
    disabledUntilModifying: true,
    disabledIfGeneratedIncorrect: true,
  },
  {
    id: RECIPE_OUTPUT_PANEL.ADVANCED_OUTPUT,
    label: "Advanced Output",
    isActive: false,
    isRightSide: true,
    disabledUntilGenerated: false,
    disabledUntilImageRefreshing: false,
    disabledUntilModifying: false,
    disabledIfGeneratedIncorrect: false,
  }
]);

const openOutputPanelValue = computed(() => panelOpenValue);
const isRecipeDetailsGenerating = computed(() => {
  return store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_DETAILS_GENERATING);
});

const isRecipeDetailsGenerationComplete = computed(() => {
  return store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_DETAILS_GENERATION_COMPLETE);
});

const isRecipeModifying = computed(() => {
  return store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING);
});

const isRecipeModifyComplete = computed(() => {
  return store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFY_COMPLETE);
});

const streamText = computed(() => {
  return store.getters['recipeGeneration/getStreamText'];
});

const openOutputPanel = computed(() => {
  return store.getters["recipeGeneration/getOpenOutputPanel"];
});

watch(flow, (val) => {
  if (val === RECIPE_GENERATION_FLOW.RECIPE_GENERATING || val === RECIPE_GENERATION_FLOW.RECIPE_REGENERATING) {
    changePanel(RECIPE_OUTPUT_PANEL.PROGRESS);
    scrollToPageBottom();
  } else if (val === RECIPE_GENERATION_FLOW.RECIPE_MODIFYING || val === RECIPE_GENERATION_FLOW.RECIPE_REFRESH_IMAGE) {
    changePanel(RECIPE_OUTPUT_PANEL.PROGRESS);
  }
});

watch(isGenerationComplete, (val) => {
  if (val && isGeneratedCorrectly.value) {
    changePanel(RECIPE_OUTPUT_PANEL.RECIPE);
  }
});

watchEffect(() => {
  if (panelOpenValue) {
    changePanel(panelOpenValue);
    store.dispatch("recipeGeneration/setOpenOutputPanel", { value: undefined });
  }
});

watch(resetAt, (val) => {
  if (!val) {
    return;
  }

  resetAll();
});

const changePanel = (id) => {
  if (selectedPanelId.value === id) {
    return;
  }
  selectedPanelId.value = id;
  panelButtons.value.forEach((item) => item.isActive = item.id === id);
};

const cancelModify = () => {
  changePanel(RECIPE_OUTPUT_PANEL.RECIPE);
};

const confirmModify = () => {
  changePanel(RECIPE_OUTPUT_PANEL.PROGRESS);
};

const finishModify = () => {
  changePanel(RECIPE_OUTPUT_PANEL.RECIPE);
};

const cancelEdit = () => {
  changePanel(RECIPE_OUTPUT_PANEL.RECIPE);
};

const confirmEdit = () => {
  changePanel(RECIPE_OUTPUT_PANEL.RECIPE);
};

const resetAll = () => {
  changePanel(RECIPE_OUTPUT_PANEL.PROGRESS);
};
</script>
