<template>
  <div class="iq-r-g-container">
    <div class="iq-r-g-container-header">
      <h4 data-test-id="generator-model-image-output-heading" class="iq-r-g-container-header-title display-4 color-green-dark">Images</h4>
      <button
          type="button"
          class="iq-r-g-container-header-btn btn-reset display-4 color-green"
          @click="refreshImages(false)"
          :disabled="isLoading || !isGenerationComplete || (!isGeneratedCorrectly && isGenerationComplete)"
          data-test-id="generator-model-image-refresh-button"
      >Refresh</button>
    </div>

    <div class="iq-r-g-recipe-images">
      <div v-if="!isFoodLMGenerator && imageList?.length" class="iq-r-g-recipe-images-camera-roll">
        <recipe-primary-images
            :imageList="imageList"
            :config="config"
            :isRecipeImageGenerated="isImageGenerationComplete"
            @deleteImage="deleteImage"
            @mainImageSelected="selectMainImage"
        ></recipe-primary-images>
      </div>
      <hr v-if="!isFoodLMGenerator && imageList?.length">
      <div
        class="iq-r-g-recipe-images-selector"
        :class="{'iq-r-g-recipe-images-selector-full-width': isFoodLMGenerator || !imageList?.length}"
      >
        <recipe-image-selector></recipe-image-selector>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance } from "vue";
import AIService from "@/services/AIService";
import { useStore } from "vuex";
import { useNuxtApp } from '#app';
import {
  CONTENT_GENERATION_STEP,
  CONTENT_GENERATION_TYPE,
  getStreamMessageForAdvanceOutput,
  parseEventData,
  RECIPE_GENERATION_FLAG,
  RECIPE_GENERATION_FLOW,
  RECIPE_GENERATION_MESSAGE,
  RECIPE_GENERATION_MESSAGES_KEY,
  RECIPE_OUTPUT_PANEL,
} from "@/models/recipe-generator.model";
import RecipePrimaryImages from "@/components/recipe-primary-images.vue";
import RecipeImageSelector from "@/components/recipe-generator/recipe-image-selector.vue";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { $tracker, $eventBus, $auth } = useNuxtApp();
const store = useStore();
const imagesGenerationCtrl = ref(null);
const imagesGenerationTimeout = ref(null);
const imagesGenerationAbortTime = 180;
const refreshCount = ref(0);
const { isLoading, resetAt, isGenerationComplete, isGeneratedCorrectly, setStreamText, handleStreamError, clearTimer, setInnerProgressSteps, setTraceId, setOnMessageProgressSteps, abortConnection } = useRecipeGenerator();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const config = {
  theme: "recipe-generator-page",
  uploadImage: false,
  confirmDelete: true,
  showErrorMessage: false,
};
const imageList = computed(() => {
  const list = store.getters["recipeGeneration/getImageListForPrimaryImages"];
  return list?.length ? list : [];
});

const isFoodLMGenerator = computed(() => store.getters['recipeGeneration/getFoodLMGenerator']);
const isImageGenerating = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_GENERATING));
const isImageGenerationComplete = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_GENERATION_COMPLETE));
const isImageRefreshing = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING));
const isImageRefreshComplete = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESH_COMPLETE));
const isRefreshImages = computed(() => store.getters["recipeGeneration/getForceImageRefresh"]);
watch(isRefreshImages, (val) => {
  if (val) {
    refreshImages(true);
  }
});

watch(() => resetAt, (val) => {
  if (val) {
    resetAll();
  }
});
const setTimer = () => {
  imagesGenerationTimeout.value = setTimeout(() => cancelImageRefreshing(), imagesGenerationAbortTime * 1000);
};
const refreshImages = (isOpenRecipeTab) => {
  if (!isGenerationComplete.value || isLoading.value) {
    return;
  }

  refreshCount.value += 1;

  store.dispatch('recipeGeneration/resetGenerationProgressSteps');

  // Set flags
  store.dispatch('recipeGeneration/setFlag', {
    flags: [
      { flag: RECIPE_GENERATION_FLAG.FLOW, value: RECIPE_GENERATION_FLOW.RECIPE_REFRESH_IMAGE },
      { flag: RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING, value: true },
      { flag: RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING_RESULT, value: false },
      { flag: RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESH_COMPLETE, value: false },
    ],
  });

  setStreamText(`\n\n\n *** Refresh image *** \n\n`);

  const onMessage = (event) => {
    clearTimeout(imagesGenerationTimeout.value);

    if (event.event === "FatalError" || event.event === "Error") {
      handleStreamError(event, {
        isStreamOnOpen: false,
        isEventFatalError: true,
        isStreamOnError: false,
        progressMessage: RECIPE_GENERATION_MESSAGE[RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_MESSAGE_FATAL_ERROR],
      });
      return;
    }

    setTimer();

    const { type, step, result, isData } = parseEventData(event.data);

    if (!isData) {
      return;
    }

    setOnMessageProgressSteps(type, step, result);
    setStreamText(getStreamMessageForAdvanceOutput(type, result));

    if (type !== CONTENT_GENERATION_TYPE.RESULT) {
      return;
    }

    // Images
    if (step === CONTENT_GENERATION_STEP.GENERATE_RECIPE_IMAGES) {
      store.dispatch('recipeGeneration/setImageList', {
        imageList: result?.model?.images,
      });
      store.dispatch('recipeGeneration/setFlag', {
        flag: RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING_RESULT,
        value: true,
      });
    }

    // Set image details from review recipe image steps
    if (step === CONTENT_GENERATION_STEP.REVIEW_RECIPE_IMAGE) {
      store.dispatch('recipeGeneration/setImagesReviewDetails', {
        result,
        refreshCount: refreshCount.value,
      });
    }
  };

  const onClose = () => {
    const isImageRefreshingResult = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING_RESULT);
    const type = isImageRefreshingResult ? CONTENT_GENERATION_TYPE.RESULT : CONTENT_GENERATION_TYPE.FAILURE;
    const step = isImageRefreshingResult ? RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_CORRECTLY_ON_CLOSE : RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_INCORRECTLY_ON_CLOSE;
    imageRefreshingEndedFlow(type, step);

    if (isOpenRecipeTab) {
      store.dispatch("recipeGeneration/setOpenOutputPanel", { value: RECIPE_OUTPUT_PANEL.RECIPE });
    }
  };

  const onError = (err) => {
    trackErrorEvent($keys.EVENT_KEY_NAMES.EXCEPTIONS_GENERATOR_IMAGES_REFRESH_ON_ERROR, err.message || err.errorMessage);
    handleStreamError(err, {
      isStreamOnOpen: false,
      isEventFatalError: false,
      isStreamOnError: true,
      progressMessage: RECIPE_GENERATION_MESSAGE[RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_ERROR_FATAL_ERROR],
    });
    imageRefreshingEndedFlow(CONTENT_GENERATION_TYPE.FAILURE, RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_ERROR);
    throw err;
  };

  const onOpen = async (response) => {
    setTraceId(response);
    handleStreamError(response, {
      isStreamOnOpen: true,
      isEventFatalError: false,
      isStreamOnError: false,
    });
  };

  imagesGenerationCtrl.value = new AbortController();
  const payload = store.getters["recipeGeneration/getRecipeForImagesRefreshing"];
  
  AIService.getGenerationStreamAsync(
    store,
    $auth,
    payload,
    onOpen,
    onMessage,
    onClose,
    onError,
    imagesGenerationCtrl.value,
    "imageGeneration",
  );

  setTimer();
};
const cancelImageRefreshing = () => {
  trackErrorEvent($keys.EVENT_KEY_NAMES.EXCEPTIONS_GENERATOR_IMAGES_REFRESH_ON_ABORT);
  abortConnection(imagesGenerationCtrl.value);
  imageRefreshingEndedFlow(CONTENT_GENERATION_TYPE.FAILURE, RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_ABORT);
};

const imageRefreshingEndedFlow = (type, step) => {
  clearTimer(imagesGenerationTimeout.value);
  setInnerProgressSteps(type, step);
  store.dispatch('recipeGeneration/setFlag', {
    flags: [
      { flag: RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING, value: false },
      { flag: RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESH_COMPLETE, value: true },
      { flag: RECIPE_GENERATION_FLAG.FLOW, value: RECIPE_GENERATION_FLOW.INTERMEDIATE },
    ],
  });
  // Emit event for data update
  $eventBus.emit($keys.KEY_NAMES.GENERATION_DATA_UPDATED);
};
const resetAll = () => {
  selectedImage.value = undefined;

  abortConnection(imagesGenerationCtrl.value);
  imagesGenerationCtrl.value = null;

  clearTimer(imagesGenerationTimeout.value);
  imagesGenerationTimeout.value = null;

  refreshCount.value = 0;
};
const trackErrorEvent = (eventName, error = null) => {
  const eventData = {
    [t('EVENT_NAMES.REFRESH_COUNT')]: refreshCount.value,
    [t('EVENT_NAMES.TRACE_ID')]: store.getters["recipeGeneration/getTraceId"],
    [t('EVENT_NAMES.IS_IMAGE_REFRESHING_RESULT')]: store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING_RESULT),
  };

  if (error) {
    eventData[t('EVENT_NAMES.ERROR_MESSAGE')] = error;
  }

  $tracker.sendEvent(eventName, eventData);
};
// recipe primary images events handlers
const deleteImage = (key) => {
  if (!key) {
    return;
  }

  store.dispatch('recipeGeneration/setImageSelected', { key, value: false });
};
const selectMainImage = (key, value) => {
  if (!key) {
    return;
  }

  store.dispatch('recipeGeneration/setMainImage', { key, value });
};
</script>
