<template>
  <div class="iq-r-g-recipe-output-panel" :data-panel="RECIPE_OUTPUT_PANEL.EDIT">
    <div class="iq-r-g-recipe-output-edit">
      <div class="iq-r-g-recipe-output-edit-row">
        <div class="iq-r-g-recipe-output-edit-column iq-r-g-recipe-output-edit-column-1">
          <input
              type="text"
              class="iq-r-g-recipe-output-edit-field iq-r-g-recipe-output-edit-title-input"
              v-model.trim="title"
              @input="handleInput()"
          >
        </div>
        <div class="iq-r-g-recipe-output-edit-column iq-r-g-recipe-output-edit-column-2">
          <label>
            Serves:
            <input
                type="number"
                min="1"
                max="1000"
                @keydown="preventInvalidNumberInput"
                @change="limitInputServesCountValue"
                @blur="limitInputServesCountValue"
                @input="handleInput()"
                v-model.trim="servesCount"
                class="iq-r-g-recipe-output-edit-field iq-r-g-recipe-output-edit-serves-input"
            ></label>
        </div>
      </div>
      <div class="iq-r-g-recipe-output-edit-row">
        <div class="iq-r-g-recipe-output-edit-column iq-r-g-recipe-output-edit-column-1">
          <p class="iq-r-g-recipe-output-edit-title">Ingredients</p>
          <textarea
              class="iq-r-g-recipe-output-edit-field"
              ref="ingredientsAreaRef"
              v-model.trim="ingredients"
              @input="handleResizeIngredientsTextArea(), handleInput()"
          ></textarea>
        </div>
        <div class="iq-r-g-recipe-output-edit-column iq-r-g-recipe-output-edit-column-2">
          <p class="iq-r-g-recipe-output-edit-title">Instructions</p>
          <textarea
              class="iq-r-g-recipe-output-edit-field"
              ref="instructionsAreaRef"
              v-model.trim="instructions"
              @input="handleResizeInstructionsTextArea(), handleInput()"
          ></textarea>
        </div>
      </div>
      <div class="iq-r-g-recipe-output-edit-actions">
        <button
            type="button"
            class="btn-reset display-4 color-green"
            @click="cancel"
        >Cancel</button>
        <button
            type="button"
            class="btn-reset display-4 color-green"
            @click="confirm"
            :disabled="buttonDisabled"
        >{{ $t('BUTTONS.CONFIRM_BUTTON') }}</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from "vue";
import { useStore } from "vuex";
import { useEventUtils } from "@/composables/useEventUtils";
import { RECIPE_OUTPUT_PANEL } from "@/models/recipe-generator.model";

const emit = defineEmits(['onCancel', 'onConfirm']);
const store = useStore();
const { isLoading } = useRecipeGenerator();
const { preventInvalidNumberInput } = useEventUtils();
const ingredientsAreaRef = ref(null);
const instructionsAreaRef = ref(null);
const hasEdited = ref(false);

const title = computed({
  get: () => recipeTitle.value,
  set: (value) => store.dispatch('recipeGeneration/setEditedRecipeTitle', { value }),
});

const ingredients = computed({
  get: () => recipeIngredients.value,
  set: (value) => store.dispatch('recipeGeneration/setEditedRecipeIngredients', { value }),
});

const instructions = computed({
  get: () => recipeInstructions.value,
  set: (value) => store.dispatch('recipeGeneration/setEditedRecipeInstructions', { value }),
});

const servesCount = computed({
  get: () => recipeServesCount.value,
  set: (value) => store.dispatch('recipeGeneration/setEditedRecipeServesCount', { value }),
});

const recipeTitle = computed(() => store.getters['recipeGeneration/getEditedRecipeTitle']);
const recipeIngredients = computed(() => store.getters['recipeGeneration/getEditedRecipeIngredients']);
const recipeInstructions = computed(() => store.getters['recipeGeneration/getEditedRecipeInstructions']);
const recipeServesCount = computed(() => store.getters['recipeGeneration/getEditedRecipeServesCount']);

const handleResizeIngredientsTextArea = () => {
  const textarea = ingredientsAreaRef.value;
  if (textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight + 12}px`;
  }
};

const handleResizeInstructionsTextArea = () => {
  const textarea = instructionsAreaRef.value;
  if (textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight + 12}px`;
  }
};

const cancel = () => {
  store.dispatch('recipeGeneration/cancelEditRecipe');
  emit('onCancel');
};

const handleInput = () => {
  hasEdited.value = true;
};

const buttonDisabled = computed(() => {
  return (
    !hasEdited.value ||
    isLoading.value ||
    !title.value?.trim() ||
    !ingredients.value?.trim() ||
    !instructions.value?.trim() ||
    !servesCount.value
  );
});

const confirm = () => {
  hasEdited.value = false;
  store.dispatch('recipeGeneration/confirmEditRecipe');
  emit('onConfirm');
};

const limitInputServesCountValue = (event) => {
  if (!event.target) return;

  const currentValue = Number(event.target.value);
  const minValue = 1;
  const maxValue = 1000;
  if (currentValue > maxValue) {
    event.target.value = maxValue;
    servesCount.value = maxValue;
    return;
  }

  if (currentValue < minValue) {
    event.target.value = minValue;
    servesCount.value = minValue;
  }
};

onMounted(() => {
  handleResizeIngredientsTextArea();
  handleResizeInstructionsTextArea();
});
</script>

