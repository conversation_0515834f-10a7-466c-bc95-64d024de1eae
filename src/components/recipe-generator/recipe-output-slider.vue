<template>
  <div class="iq-r-g-slider">

    <div class="iq-r-g-slider-control">
      <button
          type="button"
          class="btn-reset"
          @click="sliderPrev"
          :disabled="disablePrevBtn"
      > < </button>
    </div>

    <div ref="iq-r-g-slider-container-ref" class="iq-r-g-slider-container">
      <div class="iq-r-g-slider-carousel">
        <template v-for="(item, index) in imageList">
          <div
              class="iq-r-g-slider-slide"
              :class="{
                '__selected': item.key === selectedImage,
              }"
              :style="{
                marginRight: sliderMarginRight + 'px',
              }"
          >
            <div class="iq-r-g-slider-slide-wrapper" @click="zoomInImage($event, item.key)">
              <span class="iq-r-g-slider-slide-number">{{ index + 1 }}</span>
              <div
                  class="iq-r-g-slider-slide-rating bg-shadow-gray"
                  :class="{'__rated': item.assessment}"
              >
                <recipe-rating-buttons
                    :key="item.key"
                    classes="iq-r-g-rating-image-group-button"
                    :active-thumbs="item.assessment"
                    @customClick="(value) => setAssessment(item, value)"
                ></recipe-rating-buttons>
              </div>
              <button v-if="!isFoodLMGenerator" type="button" class="iq-r-g-slider-slide-btn btn-reset" @click="selectImage($event, item.key)">
                <img src="@/assets/images/select-generator.png" alt="select" />
              </button>
              <div class="iq-r-g-slider-slide-checked">
                <img class="selected-image-section" src="@/assets/images/selected-generator.png" alt=""/>
              </div>
              <img
                  class="iq-r-g-slider-slide-image"
                  :src="item.key"
                  :alt="placeholderIconName(item)"
                  loading="lazy"
                  :data-average-value="isAdmin && item?.averageValue"
                  :data-realism-rating-star="isAdmin && item?.review?.realism_rating_star"
                  :data-accuracy-rating-star="isAdmin && item?.review?.accuracy_rating_star"
                  :data-color-rating-star="isAdmin && item?.review?.color_rating_star"
                  :data-model="isAdmin && item?.image?.model"
              >
            </div>
          </div>
        </template>
      </div>
    </div>

    <div class="iq-r-g-slider-control">
      <button
          type="button"
          class="btn-reset"
          @click="sliderNext"
          :disabled="disableNextBtn"
      > > </button>
    </div>

    <div v-if="isOpenDialog" class="iq-r-g-slider-dialog modal-backdrop">
      <div
          ref="slider-dialog-content-ref"
          class="iq-r-g-slider-dialog-content"
          :style="{
            width: dialogContentWidth,
            height: dialogContentHeight
          }"
      >
        <button class="iq-r-g-slider-dialog-content-close btn-reset" @click="zoomOutImage">
          <img alt="" src="@/assets/images/exit-gray.png"/>
        </button>
        <img :src="zoomedImage" alt="">
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { getMarginSlides } from "@/utils/recipe-generator";
import RecipeRatingButtons from "@/components/recipe-generator/recipe-rating-buttons.vue";
import { useStore } from 'vuex';
import { useNuxtApp } from "#app";

const { getRef } = useRefUtils()
const store = useStore()
const sliderContainerWidth = ref(undefined)
const slideWidth = ref(305)
const sliderMarginRight = ref(30)
const slidesPerPage = ref(1)
const slidesShift = ref(0)
const selectedImage = ref(undefined)
const zoomedImage = ref(undefined)
const windowScrollPosition = ref(undefined)
const isOpenDialog = ref(false)
const dialogContentWidth = ref("0")
const dialogContentHeight = ref("0")
const isScrollAvailable = ref(true)
const timer = ref(null)
const isFoodLMGenerator = ref(false)

const imageList = computed(() => {
  const sortedImageList = store.getters["recipeGeneration/getSortedImageListByAverageStarValue"]
  return sortedImageList?.length ? sortedImageList : store.getters["recipeGeneration/getImageList"]
})
const disablePrevBtn = computed(() => slidesShift.value === imageList.value.length)

const disableNextBtn = computed(() => slidesShift.value <= slidesPerPage.value)

const placeholderIconName = (item) => item?.image?.prompt || item?.key


const { $eventBus } = useNuxtApp();

onMounted(() => {
  prepareSlider()
  window.addEventListener('resize', onResize)
  handleEsc()
  handleClickOutside()
  isFoodLMGenerator.value = store.getters['recipeGeneration/getFoodLMGenerator']
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', onResize)
  window.removeEventListener('resize', setDialogContentStyles)
  zoomOutImage()

  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
})
const selectImage = (event, src) => {
  event.stopPropagation()

  if (!src) {
    return
  }

  selectedImage.value = src
  store.dispatch('recipeGeneration/setSelectedImage', { value: src })
}
const setAssessment = (item, assessment) => {
  if (item?.assessment === assessment) {
    return
  }

  store.dispatch('recipeGeneration/updateImageAssessment', { key: item?.key, assessment })
  $eventBus.emit(window.$keys.KEY_NAMES.GENERATION_DATA_UPDATED)
}
const onResize = () => {
  const sliderEl = getRef("iq-r-g-slider-container-ref")
  if (sliderEl) {
    sliderEl.scrollLeft = 0
  }
  prepareSlider()
}
const sliderPrev = () => {
  if (!isScrollAvailable.value) {
    return
  }

  isScrollAvailable.value = false
  const sliderEl = getRef("iq-r-g-slider-container-ref")
  if (sliderEl) {
    const shift = slidesPerPage.value > 1 ? sliderContainerWidth.value + sliderMarginRight.value : sliderContainerWidth.value
    sliderEl.scrollLeft -= shift
    slidesShift.value = slidesShift.value + slidesPerPage.value
  }

  enableScroll()
}
const sliderNext = () => {
  if (!isScrollAvailable.value) {
    return
  }

  isScrollAvailable.value = false
  const sliderEl = getRef("iq-r-g-slider-container-ref")
  if (sliderEl) {
    const shift = slidesPerPage.value > 1 ? sliderContainerWidth.value + sliderMarginRight.value : sliderContainerWidth.value
    sliderEl.scrollLeft += shift
    slidesShift.value = slidesShift.value - slidesPerPage.value
  }

  enableScroll()
}
const enableScroll = () => {
  timer.value = setTimeout(() => {
    isScrollAvailable.value = true
  }, 700)
}
const prepareSlider = () => {
  if (!imageList.value.length) {
    return
  }

  const sliderContainerEl = getRef("iq-r-g-slider-container-ref")
  if (!sliderContainerEl) {
    return
  }

  const containerWidth = sliderContainerEl.clientWidth
  const slideCount = Math.floor(containerWidth / slideWidth)

  const { margin, slides } = getMarginSlides(slideCount, containerWidth, slideWidth)

  sliderContainerWidth.value = containerWidth
  sliderMarginRight.value = margin
  slidesPerPage.value = slides
  slidesShift.value = imageList.value.length
}

// show zoom image in the dialog
const zoomInImage = (event, src) => {
  event.stopPropagation()

  if (!event?.target.matches(".iq-r-g-slider-slide-image") || isOpenDialog.value) {
    return
  }

  zoomedImage.value = src

  disableScrolling()
  setDialogContentStyles()
  window.addEventListener('resize', setDialogContentStyles)

  isOpenDialog.value = true
}
const zoomOutImage = () => {
  if (!isOpenDialog.value) {
    return
  }

  enableScrolling()

  isOpenDialog.value = false
  zoomedImage.value = undefined

  dialogContentWidth.value = "0"
  dialogContentHeight.value = "0"

  window.removeEventListener('resize', setDialogContentStyles)
}
const setDialogContentStyles = () => {
  const isHorizontal = window.innerWidth > window.innerHeight
  dialogContentWidth.value = isHorizontal ? "auto" : `${window.innerWidth * 0.6}px`
  dialogContentHeight.value = isHorizontal ? `${window.innerHeight - 80}px` : "auto"
}
const disableScrolling = () => {
  windowScrollPosition.value = window.scrollY

  const $body = document.querySelector('body')
  $body.style.overflow = 'hidden'
  $body.style.position = 'fixed'
  $body.style.top = `-${windowScrollPosition.value}px`
  $body.style.width = '100%'
}
const enableScrolling = () => {
  const $body = document.querySelector('body')
  $body.style.removeProperty('overflow')
  $body.style.removeProperty('position')
  $body.style.removeProperty('top')
  $body.style.removeProperty('width')

  window.scrollTo(0, windowScrollPosition.value)
}
const handleClickOutside = () => {
  const clickOutside = (event) => {
    const dialogEl = getRef('slider-dialog-content-ref')
    if (!event?.target || dialogEl?.contains(event.target)) {
      return
    }

    zoomOutImage()
  }

  window.addEventListener('click', clickOutside)
  onBeforeUnmount(() => window.removeEventListener('click', clickOutside))
}
const handleEsc = () => {
  const close = (event) => {
    if (event.keyCode !== 27) {
      return
    }
    zoomOutImage()
  }

  document.addEventListener('keyup', close)
  onBeforeUnmount(() => document.removeEventListener('keyup', close))
}
</script>
