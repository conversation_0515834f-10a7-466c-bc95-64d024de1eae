<template>
  <div
      class="iq-r-g-image-selector-slide"
      :class="{
        '__loading': isLoading,
        '__selected': slideData?.isSelected,
        'iq-r-g-image-selector-slide-zoom': slideData?.key,
      }"
      @click.stop="zoomInImage(slideData?.key)"
  >
    <div v-if="slideData?.key" class="iq-r-g-image-selector-slide-rating bg-shadow-gray">
      <recipe-rating-buttons
          :key="slideData?.key"
          isSmallStyle
          classes="iq-r-g-rating-image-group-button"
          :active-thumbs="slideData?.assessment"
          @customClick="(value) => setAssessment(slideData?.key, value)"
      ></recipe-rating-buttons>
    </div>
    <img
        v-if="slideData?.key"
        :src="slideData?.key"
        :alt="slideData?.image?.prompt"
        loading="lazy"
        :data-average-value="isAdmin && slideData?.averageValue"
        :data-realism-rating-star="isAdmin && slideData?.review?.realism_rating_star"
        :data-accuracy-rating-star="isAdmin && slideData?.review?.accuracy_rating_star"
        :data-color-rating-star="isAdmin && slideData?.review?.color_rating_star"
        :data-model="isAdmin && slideData?.image?.model"
    >

    <div v-if="isShowLimitMessage" class="iq-r-g-image-selector-slide-limit">
      <span class="font-weight-semi-bold color-white">{{ $t("GENERATOR.IMAGE_SELECTOR_SLIDE_LIMIT_MESSAGE") }}</span>
    </div>

    <selector-button
        v-if="slideData?.key && !isHiddenActions && !isShowLimitMessage"
        :label="$t('GENERATOR.IMAGE_SELECTOR_SLIDE_SELECT')"
        classes="iq-r-g-image-selector-slide-select"
        :value="slideData?.key"
        :isCenterByAbsolute="true"
        @onSelectValue="select"
    ></selector-button>
  </div>
</template>

<script setup>
import SelectorButton from "@/components/selector-button.vue";
import RecipeRatingButtons from "@/components/recipe-generator/recipe-rating-buttons.vue";

const { isAdmin } = useProjectLang();
const emit = defineEmits();

const props = defineProps({
  isLoading: {
    type: Boolean,
    required: false,
    default: false,
  },
  slideData: {
    type: Object,
    required: true,
  },
  isHiddenActions: {
    type: Boolean,
    required: false,
    default: false,
  },
  isShowLimitMessage: {
    type: Boolean,
    required: false,
    default: false,
  }
});


const zoomInImage = (key) => {
  if (!key) return;
  emit('onZoomInImage', key);
};

const select = (key) => {
  if (!key) return;
  emit('onSelect', { key, value: true });
};

const setAssessment = (key, value) => {
  if (!key) return;
  emit('onAssessment', { key, value });
};
</script>