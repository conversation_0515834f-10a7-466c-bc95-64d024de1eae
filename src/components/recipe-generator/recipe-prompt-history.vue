<template>
  <div class="iq-r-g-history-dropdown-container">
    <div class="iq-r-g-history-dropdown-container-inner-box">
      <div v-for="(section, index) in filteredHistorySections" :key="index">
        <div>
          <div :data-test-id="section.name" class="iq-r-g-history-dropdown-container-inner-box-name">
            {{ section.name }}
          </div>
          <ul>
            <li v-for="item in section.items" :key="item.createTime">
              <button type="button" @click="selectPromptName(item.prompt)"
                class="iq-r-g-history-dropdown-container-inner-box-prompt-name btn-reset">
                <div class="iq-r-g-history-dropdown-container-inner-box-prompt-name-details">
                  <span class="promp-text">{{ item.prompt }}</span>
                  <span class="image-icon-container">
                    <img
                      v-if="item?.isin"
                      src="@/assets/images/prompt-check-mark-icon-grey.png"
                      alt="prompt-check-icon" />
                    <img
                      v-if="item?.assessment === GENERATED_DATA_RESULT.BAD_RATING && !item?.isin"
                      src="@/assets/images/prompt-thumbs-down.png"
                      alt="prompt-thumbs-down-icon" />
                    <img
                      v-if="item?.assessment === GENERATED_DATA_RESULT.GOOD_RATING && !item?.isin"
                      src="@/assets/images/prompt-thumbs-up.png"
                      alt="prompt-thumbs-up-icon" />
                  </span>
                </div>
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, getCurrentInstance } from 'vue';
import { GENERATED_DATA_RESULT } from "@/models/recipe-generator.model";
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';

const store = useStore();
const { triggerLoading } = useCommonUtils();
const promptHistory = ref([]);
const categorizedData = ref({
  today: [],
  yesterday: [],
  last7Days: [],
});

const { t } = useI18n();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

onMounted(() => {
  categorizePromptHistoryData();
});
const filteredHistorySections = computed(() => {
  return [
    {
      name: t('COMMON.TODAY'),
      items: categorizedData.value.today,
    },
    {
      name: t('COMMON.YESTERDAY'),
      items: categorizedData.value.yesterday,
    },
    {
      name: t('COMMON.LAST_7_DAYS'),
      items: categorizedData.value.last7Days,
    },
  ].filter(section => section?.items?.length);
});
const selectPromptName = (value) => {
  store.dispatch("recipeGeneration/setPromptValue", { value });
  triggerLoading($keys.KEY_NAMES.CLOSE_HISTORY_DROPDOWN);
};
const categorizePromptHistoryData = () => {
  const response = store.getters["recipeGeneration/getPromptHistory"];
  promptHistory.value = response?.results || [];
  if (promptHistory.value.length) {
    setRecipeHistoryData();
  }
};
const setRecipeHistoryData = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  yesterday.setHours(0, 0, 0, 0);
  promptHistory.value.forEach((item) => {
    const createTime = new Date(item.createTime * 1000);
    createTime.setHours(0, 0, 0, 0);
    if (createTime.getTime() === today.getTime()) {
      categorizedData.value.today.push(item);
    } else if (createTime.getTime() === yesterday.getTime()) {
      categorizedData.value.yesterday.push(item);
    } else {
      categorizedData.value.last7Days.push(item);
    }
  });
};
</script>
