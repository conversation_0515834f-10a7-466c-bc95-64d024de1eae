<template>
  <client-only>
    <div>
      <div class="iq-r-g-container">
        <div class="iq-r-g-container-header">
          <button
            type="button"
            :class="[
              'btn-reset display-4 color-green iq-r-g-container-header-btn',
              { 'iq-r-g-container-header-btn-active': !isHistoryDropdown },
            ]"
            @click="showPromptHistorySection()"
            data-test-id="generator-prompt-text"
          >
            {{ $t('GENERATOR.PROMPT') }}
          </button>
          <button
            v-if="isHistorySectionVisible"
            type="button"
            :class="[
              'btn-reset display-4 color-green history-btn iq-r-g-container-header-btn',
              { 'iq-r-g-container-header-btn-active': isHistoryDropdown },
            ]"
            @click="showHistoryDropdownAsync()"
            :disabled="isLoading"
            data-test-id="generator-history-button"
          >
            {{ $t('GENERATOR.HISTORY') }}
          </button>
          <button
            type="button"
            class="iq-r-g-container-header-btn btn-reset display-4 color-green iq-r-g-container-header-btn"
            @click="resetAll({})"
            :disabled="isLoading || !isGenerationComplete"
          >
            {{ $t('GENERATOR.RESET') }}
          </button>
        </div>
        <div class="iq-r-g-container-prompt-group" :class="{ 'iq-r-g-container-foodlm-prompt-group': isFoodLMGenerator }">
          <div class="iq-r-g-container-prompt-group-wrapper">
            <recipe-prompt
              ref="dropdown"
              @onGenerate="generateRecipe"
            ></recipe-prompt>
            <recipe-prompt-history
              v-if="isHistoryDropdown"
            ></recipe-prompt-history>
            <recipe-models></recipe-models>
          </div>
          <button
            v-if="!isFoodLMGenerator"
            type="button"
            class="btn-green iq-r-g-save-btn"
            data-test-id="save-generated-recipe-button"
            @click="handleSaveClick"
            :disabled="
              isLoading ||
              !isGenerationComplete ||
              (!isGeneratedCorrectly && isGenerationComplete)
            "
          >
            <img src="@/assets/images/generator-save.png" alt="save" />
            {{ $t('BUTTONS.SAVE_BUTTON') }}
          </button>
        </div>
        <confirmationPopup
          v-if="isUploadingImagePopup"
          :continueImage="confirmSaveWithoutImage"
          :maxFileSize="$t('GENERATOR.SAVE_MESSAGE')"
          :optimalImageSize="$t('GENERATOR.SAVE_WARNING')"
          :closeModal="closeModal"
          :isUploadingImagePopup="isUploadingImagePopup"
        />
      </div>

      <recipe-images></recipe-images>

      <recipe-output></recipe-output>

      <recipe-rating></recipe-rating>
      <savingModal v-if="isGeneratorSaving" :status="'saving'" />
    </div>
  </client-only>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed, getCurrentInstance } from "vue";
import AIService from "@/services/AIService";
import OrganizationsService from "@/services/OrganizationsService";
import confirmationPopup from "@/components/size-limit.vue";
import { useCommonUtils } from '~/composables/useCommonUtils';
import { useI18n } from 'vue-i18n';
import { useNuxtApp } from '#app';
import {
  CONTENT_GENERATION_STEP,
  CONTENT_GENERATION_TYPE,
  getStreamMessageForAdvanceOutput,
  parseEventData,
  RECIPE_GENERATION_FLAG,
  RECIPE_GENERATION_FLOW,
  RECIPE_GENERATION_MESSAGE,
  RECIPE_GENERATION_MESSAGES_KEY,
} from "@/models/recipe-generator.model";
import { useRecipeGenerator } from '~/composables/useRecipeGenerator';
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import RecipeImages from "@/components/recipe-generator/recipe-images.vue";
import RecipePrompt from "@/components/recipe-generator/recipe-prompt.vue";
import RecipePromptHistory from "@/components/recipe-generator/recipe-prompt-history.vue";
import RecipeOutput from "@/components/recipe-generator/recipe-output.vue";
import RecipeRating from "@/components/recipe-generator/recipe-rating.vue";
import RecipeModels from "@/components/recipe-generator/recipe-models.vue";
import savingModal from "@/components/saving-modal";
import { useStore } from 'vuex';
import { useProjectLang } from "@/composables/useProjectLang";
const { $tracker, $eventBus, $axios, $auth } = useNuxtApp();
const { readyProject } = useProjectLang();
// Props
const props = defineProps({
  isFoodLMGenerator: {
    type: Boolean,
    default: false,
  },
});
const { t } = useI18n();
// Refs
const isUploadingImagePopup = ref(false);
const recipeGenerationCtrl = ref(null);
const recipeModificationCtrl = ref(null);
const generatingRecipeTimeout = ref(null);
const recipeModificationTimeout = ref(null);
const recipeGenerationTimeout = ref(null);
const generatingRecipeAbortTime = ref(180);
const isHistoryDropdown = ref(false);
const isHistorySectionVisible = ref(false);
const isGeneratorSaving = ref(false);

const { clearTimer, setInnerProgressSteps, handleStreamError, setTraceId, setOnMessageProgressSteps, setStreamText, isLoading, isGenerationComplete, isGeneratedCorrectly, abortConnection } = useRecipeGenerator();
const { triggerLoading } = useCommonUtils();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const store = useStore();
const lang = computed(() => store.getters["userData/getDefaultLang"]);

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  resetAll({
    keepProgress: false,
    keepTextModels: false,
    keepImageModels: false,
  });
  const generationDataUpdated = async () => {
    await putRecipeGenerationDataAsync();
  };
  const closeHistoryDropdown = () => {
    isHistoryDropdown.value = false;
  };

  $eventBus.on($keys.KEY_NAMES.GENERATION_DATA_UPDATED, generationDataUpdated);
  $eventBus.on($keys.KEY_NAMES.CLOSE_HISTORY_DROPDOWN, closeHistoryDropdown);

  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      getRecipePromptHistoryAsync();
    }
  });
});

onBeforeUnmount(() => {
  resetAll({
    keepProgress: false,
    keepTextModels: false,
    keepImageModels: false,
  });
  $eventBus.off($keys.KEY_NAMES.GENERATION_DATA_UPDATED);
  $eventBus.off($keys.KEY_NAMES.CLOSE_HISTORY_DROPDOWN);
  document.removeEventListener("click", handleClickOutside);
});

// Watchers
watch(() => props.isGenerationComplete, (val) => {
  if (val && isGeneratedCorrectly.value) {
    const description = $keys.EVENT_KEY_NAMES.VIEW_GENERATOR_SUCCESS;
    const includeRecipeDescription = true;
    const includeRecipe = true;

    checkGeneratorEvent({
      description,
      includeRecipeDescription,
      includeRecipe,
    });
  }
});
const markGenerationOnCompleteAsync = async () => {
  await store.dispatch("recipeGeneration/setFlag", {
    flags: [
      { flag: RECIPE_GENERATION_FLAG.IS_GENERATING, value: false },
      { flag: RECIPE_GENERATION_FLAG.IS_GENERATION_COMPLETE, value: true },
      { flag: RECIPE_GENERATION_FLAG.IS_IMAGE_GENERATING, value: false },
      {
        flag: RECIPE_GENERATION_FLAG.IS_IMAGE_GENERATION_COMPLETE,
        value: true,
      },
      {
        flag: RECIPE_GENERATION_FLAG.IS_RECIPE_DETAILS_GENERATING,
        value: false,
      },
      {
        flag: RECIPE_GENERATION_FLAG.IS_RECIPE_DETAILS_GENERATION_COMPLETE,
        value: true,
      },
      {
        flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATING,
        value: false,
      },
      {
        flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATION_COMPLETE,
        value: true,
      },
      {
        flag: RECIPE_GENERATION_FLAG.FLOW,
        value: RECIPE_GENERATION_FLOW.INTERMEDIATE,
      },
    ],
  });
  await postGenerationDataAsync();
};

const setTimer = () => {
  recipeGenerationTimeout.value = setTimeout(() => cancelRecipeGeneration(), generatingRecipeAbortTime.value * 1000);
};
const handleSaveClick = () => {
  if (store.getters["recipeGeneration/getMainImage"]) {
    saveAsync();
    return;
  }
  isUploadingImagePopup.value = true;
};
const postGenerationDataAsync = async () => {
  const payload = store.getters["recipeGeneration/getFirstGenerationData"];
  if (!payload.recipe.title || !payload.recipe.ingredients.length) {
    return;
  }

  try {
    await AIService.postGenerationDataAsync(
      store,
      null,
      lang.value,
      payload,
      props.isFoodLMGenerator,
    );
  } catch (error) {
    console.error("Error while saving the data:", error);
  }
};
const confirmSaveWithoutImage = () => {
  isUploadingImagePopup.value = false;
  saveAsync();
};
const generateRecipe = () => {
  const promptValue = store.getters["recipeGeneration/getPromptValue"];
  if (!promptValue || isLoading.value) {
    return;
  }

  const description = $keys.EVENT_KEY_NAMES.CLICK_GENERATOR_SUBMIT;
  const includeRecipeDescription = true;

  checkGeneratorEvent({
    description,
    includeRecipeDescription,
  });

  const isCompleted = isGenerationComplete.value;

  // regeneration logic
  if (isCompleted) {
    resetAll({
      keepProgress: false,
      keepTextModels: true,
      keepImageModels: true,
    });
    store.dispatch("recipeGeneration/setPromptValue", {
      value: promptValue,
    });
  }

  store.dispatch("recipeGeneration/setPromptValueToArchive", {
    value: promptValue,
  });

  // set flags
  store.dispatch("recipeGeneration/setFlag", {
    flags: [
      {
        flag: RECIPE_GENERATION_FLAG.FLOW,
        value: isCompleted
          ? RECIPE_GENERATION_FLOW.RECIPE_REGENERATING
          : RECIPE_GENERATION_FLOW.RECIPE_GENERATING,
      },
      { flag: RECIPE_GENERATION_FLAG.IS_GENERATING, value: true },
      { flag: RECIPE_GENERATION_FLAG.IS_GENERATED_CORRECTLY, value: true },
      {
        flag: RECIPE_GENERATION_FLAG.IS_RECIPE_DETAILS_GENERATING,
        value: true,
      },
      { flag: RECIPE_GENERATION_FLAG.IS_IMAGE_GENERATING, value: true },
    ],
  });

  store.dispatch("recipeGeneration/resetGenerationProgressSteps");

  const onMessage = (event) => {
    clearTimeout(recipeGenerationTimeout.value);

    if (event.event === "FatalError" || event.event === "Error") {
      handleStreamError(event, {
        isStreamOnOpen: false,
        isEventFatalError: true,
        isStreamOnError: false,
        progressMessage:
          RECIPE_GENERATION_MESSAGE[
            RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_MESSAGE_FATAL_ERROR
          ],
      });
      return;
    }

    setTimer();

    const { type, step, result, isData } = parseEventData(event.data);

    if (!isData) {
      return;
    }

    setOnMessageProgressSteps(type, step, result);
    setStreamText(getStreamMessageForAdvanceOutput(type, result));

    // set tracker event if failure
    if (type === CONTENT_GENERATION_TYPE.FAILURE) {
      checkGeneratorEvent({
        description: $keys.EVENT_KEY_NAMES.VIEW_GENERATOR_ERROR,
        includeRecipeDescription: true,
        errorMessage: result?.reason || `Recipe generation failed. Step: ${step}`,
      });
    }

    if (type !== CONTENT_GENERATION_TYPE.RESULT) {
      return;
    }

    // mark generation steps
    store.dispatch("recipeGeneration/updateStep", {
      step,
      stepValue: true,
    });

    // recipe details
    if (step === CONTENT_GENERATION_STEP.GENERATE_RECIPE_CONTENT) {
      store.dispatch("recipeGeneration/setRecipe", {
        title: result?.model?.title,
        ingredients: result?.model?.ingredients,
        instructions: result?.model?.instructions,
        servesCount: result?.model?.servings,
      });

      store.dispatch("recipeGeneration/setFlag", {
        flags: [
          {
            flag: RECIPE_GENERATION_FLAG.IS_RECIPE_DETAILS_GENERATING,
            value: false,
          },
          {
            flag: RECIPE_GENERATION_FLAG.IS_RECIPE_DETAILS_GENERATION_COMPLETE,
            value: true,
          },
          {
            flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATING,
            value: true,
          },
        ],
      });
    }

    // recipe review
    if (step === CONTENT_GENERATION_STEP.REVIEW_RECIPE_TASTE) {
      store.dispatch("recipeGeneration/setRecipeReview", {
        data: result,
      });
      store.dispatch("recipeGeneration/setFlag", {
        flags: [
          {
            flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATING,
            value: false,
          },
          {
            flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATION_COMPLETE,
            value: true,
          },
        ],
      });
    }

    // recipe images
    if (step === CONTENT_GENERATION_STEP.GENERATE_RECIPE_IMAGES) {
      store.dispatch("recipeGeneration/setImageList", {
        imageList: result?.model?.images,
      });
    }

    // set image details from review recipe image steps
    if (step === CONTENT_GENERATION_STEP.REVIEW_RECIPE_IMAGE) {
      store.dispatch("recipeGeneration/setImagesReviewDetails", {
        result,
      });
    }
  };

  const onClose = () => {
    markGenerationOnCompleteAsync();
    const isCorrect = store.getters["recipeGeneration/isCorrectGenerated"];
    const type = isCorrect
      ? CONTENT_GENERATION_TYPE.RESULT
      : CONTENT_GENERATION_TYPE.FAILURE;
    const step = isCorrect
      ? RECIPE_GENERATION_MESSAGES_KEY.GENERATE_CORRECTLY_ON_CLOSE
      : RECIPE_GENERATION_MESSAGES_KEY.GENERATE_INCORRECTLY_ON_CLOSE;
    recipeGenerationEndedFlow(type, step);
    setGeneratedCorrectly(isCorrect);
  };

  const onError = (err) => {
    markGenerationOnCompleteAsync();
    const isCorrect = store.getters["recipeGeneration/isCorrectGenerated"];
    const type = isCorrect
      ? CONTENT_GENERATION_TYPE.RESULT
      : CONTENT_GENERATION_TYPE.FAILURE;
    const step = isCorrect
      ? RECIPE_GENERATION_MESSAGES_KEY.GENERATE_CORRECTLY_ON_CLOSE
      : RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_ERROR;

    trackErrorEvent(
      $keys.EVENT_KEY_NAMES.EXCEPTIONS_GENERATOR_ON_ERROR,
      isCorrect,
      err.message || err.errorMessage
    );

    recipeGenerationEndedFlow(type, step);
    setGeneratedCorrectly(isCorrect);

    if (!isCorrect) {
      handleStreamError(err, {
        isStreamOnOpen: false,
        isEventFatalError: false,
        isStreamOnError: true,
        progressMessage:
          RECIPE_GENERATION_MESSAGE[
            RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_ERROR_FATAL_ERROR
          ],
      });
      throw err;
    }
  };

  const onOpen = async (response) => {
    setTraceId(response);
    handleStreamError(response, {
      isStreamOnOpen: true,
      isEventFatalError: false,
      isStreamOnError: false,
    });
  };

  recipeGenerationCtrl.value = new AbortController();
  AIService.getGenerationStreamAsync(
    store,
    $auth,
    { input: promptValue },
    onOpen,
    onMessage,
    onClose,
    onError,
    recipeGenerationCtrl.value,
    "generations"
  );

  setTimer();
};
const cancelRecipeGeneration = () => {
  abortConnection(recipeGenerationCtrl.value);
  recipeGenerationEndedFlow(
    CONTENT_GENERATION_TYPE.FAILURE,
    RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_ABORT
  );

  const isCorrect = store.getters["recipeGeneration/isCorrectGenerated"];
  setGeneratedCorrectly(isCorrect);

  if (isCorrect) {
    markGenerationOnCompleteAsync();
    return;
  }

  trackErrorEvent($keys.EVENT_KEY_NAMES.EXCEPTIONS_GENERATOR_ON_ABORT, isCorrect);

  resetAll({ keepProgress: true, keepTextModels: true, keepImageModels: true });
};
const recipeGenerationEndedFlow = (type, step) => {
  clearTimer(recipeGenerationTimeout.value);
  setInnerProgressSteps(type, step);
};

const setGeneratedCorrectly = (value) => {
  store.dispatch("recipeGeneration/setFlag", {
    flag: RECIPE_GENERATION_FLAG.IS_GENERATED_CORRECTLY,
    value,
  });
};

// saving methods
const getIsinAsync = async () => {
  const project = store.getters["userData/getProject"];
  const response = await OrganizationsService.getNewIsins(
    project,
    "recipe",
    lang.value,
    $auth.user.value?.email || "",
    store,
    $auth
  );
  return response?.isin || "";
};
const uploadImagesAsync = async (isin, urls) => {
  try {
    const endpoint = store.getters['config/getClientEndpoint']("flite", "uploadImagesFromUrls");
    const baseURL = store.getters['config/getClientConfig']("flite").host;

    const response = await $axios.post(
      endpoint,
      {
        isin: isin,
        externalUrls: urls,
        entityType: "recipe",
        contentType: "image",
        extension: "jpeg",
      },
      {
        baseURL,
        params: {
          lang: lang.value,
        },
      }
    );

    return response?.data?.results || [];
  } catch (error) {
    console.error("Error uploading images:", error);
    return [];
  }
};
const saveAsync = async () => {
  if (isLoading.value) {
    return;
  }

  isLoading.value = true;
  isGeneratorSaving.value = true;

  await store.dispatch("recipeGeneration/setFlag", {
    flag: RECIPE_GENERATION_FLAG.IS_SAVING,
    value: true,
  });

  try {
    const isin = await getIsinAsync();
    const imagesForSave = store.getters["recipeGeneration/getImagesForSaving"];
    const mainImageUrl = store.getters["recipeGeneration/getMainImage"]?.key || null;
    const urls = imagesForSave?.map((item) => item.key);
    const uploadedImages = await uploadImagesAsync(isin, urls);

    const image = uploadedImages?.find((item) => item?.externalUrl === mainImageUrl)?.url || null;
    const images = uploadedImages?.map((item) => ({
      source: $keys.KEY_NAMES.AI_GENERATED,
      url: item.url,
    }));

    const recipe = store.getters["recipeGeneration/getRecipeForSaving"];
    const endpoint = store.getters['config/getClientEndpoint']("flite", "postSimpleRecipe");
    const baseURL = store.getters['config/getClientConfig']("flite").host;

    const payload = {
      isin,
      image,
      images,
      title: recipe.title,
      servings: recipe.servings,
      ingredients: recipe.ingredients,
      instructions: recipe.instructions,
    };

    await $axios.post(endpoint, payload, {
      baseURL,
      params: {
        lang: lang.value,
        provider: "cmsAI",
      },
    });

    await postRecipeGeneratorActivityAsync(payload);
    await putRecipeGenerationDataAsync(isin);

    triggerLoading($keys.KEY_NAMES.SAVED_SUCCESS);

    const description = $keys.EVENT_KEY_NAMES.CLICK_GENERATOR_SAVE;
    const includeRecipeDescription = true;
    const includeRecipe = true;

    checkGeneratorEvent({
      description,
      includeRecipeDescription,
      includeRecipe,
    });

    await store.dispatch("recipeGeneration/setFlag", {
      flag: RECIPE_GENERATION_FLAG.IS_SAVING,
      value: false,
    });

    resetAll({
      keepProgress: false,
      keepTextModels: false,
      keepImageModels: false,
    });
  } catch (e) {
    const description = $keys.EVENT_KEY_NAMES.CLICK_GENERATOR_SAVE;
    const includeRecipeDescription = true;
    const includeRecipe = true;
    const errorMessage = e.message;

    checkGeneratorEvent({
      description,
      includeRecipeDescription,
      includeRecipe,
      errorMessage,
    });

    await store.dispatch("recipeGeneration/setFlag", {
      flag: RECIPE_GENERATION_FLAG.IS_SAVING,
      value: false,
    });
    console.error(e);
  } finally {
    isLoading.value = false;
    isGeneratorSaving.value = false;
  }
};
const postRecipeGeneratorActivityAsync = async (payloadData) => {
  const payload = {
    type: $keys.RECENT_ACTIVITIES.GENERATED_RECIPES,
    data: {
      recipeIsin: payloadData?.isin,
      recipeName: payloadData?.title,
      recipeImage: payloadData?.image,
    },
  };

  try {
    await store.dispatch("recipe/postRecentActivityAsync", {
      lang: lang.value,
      payload,
    });
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "postRecipeExportActivityAsync:", error);
  }
};
const checkGeneratorEvent = (params) => {
  const {
    description,
    includeRecipeDescription = false,
    includeRecipe = false,
    errorMessage = null,
  } = params;

  const eventProperties = {};

  if (includeRecipeDescription) {
    eventProperties[t('EVENT_NAMES.RECIPE_DESCRIPTION')] =
      store.getters["recipeGeneration/getPromptValue"];
  }

  if (includeRecipe) {
    const recipe = store.getters["recipeGeneration/getRecipeForSaving"];
    eventProperties[t('EVENT_NAMES.GENERATED_RECIPE_TITLE')] = recipe.title;
    eventProperties[t('EVENT_NAMES.GENERATED_RECIPE_INGREDIENTS')] = recipe.ingredients;
    eventProperties[t('EVENT_NAMES.GENERATED_RECIPE_INSTRUCTIONS')] = recipe.instructions;
  }

  if (errorMessage) {
    eventProperties[t('EVENT_NAMES.ERROR_MESSAGE')] = errorMessage;
    eventProperties[t('EVENT_NAMES.TRACE_ID')] = store.getters["recipeGeneration/getTraceId"];
  }

  $tracker.sendEvent(description, eventProperties, {
    ...LOCAL_TRACKER_CONFIG,
  });
};
const trackErrorEvent = (eventName, isCorrect, error = null) => {
  const eventData = {
    [t('EVENT_NAMES.PROMPT')]: store.getters["recipeGeneration/getPromptValue"],
    [t('EVENT_NAMES.TRACE_ID')]: store.getters["recipeGeneration/getTraceId"],
    [t('EVENT_NAMES.IS_RECIPE_DETAILS_GENERATION_COMPLETE')]: isCorrect,
  };

  if (error) {
    eventData[t('EVENT_NAMES.ERROR_MESSAGE')] = error;
  }

  $tracker.sendEvent(eventName, eventData);
};

const resetAll = ({ keepProgress = false, keepTextModels = false, keepImageModels = false }) => {
  abortConnection(recipeGenerationCtrl.value);
  recipeGenerationCtrl.value = null;

  clearTimer(recipeGenerationTimeout);
  generatingRecipeTimeout.value = null;

  store.dispatch("recipeGeneration/reset", {
    keepProgress,
    keepTextModels,
    keepImageModels,
  });

  if (window.location.pathname.includes("foodlm-generator")) {
    store.dispatch("recipeGeneration/setFoodLMGenerator", true);
  }
};
const putRecipeGenerationDataAsync = async (isin = null) => {
  const uuid = store.getters["recipeGeneration/getRecipeUUID"];
  const payload = store.getters["recipeGeneration/getGenerationData"];
  payload.recipe.isin = isin;

  try {
    await AIService.putGenerationDataAsync(
      store,
      $auth,
      lang.value,
      payload,
      uuid,
    );
  } catch (error) {
    console.error("Error while updating the data:", error);
  }
};
const closeModal = () => {
  isUploadingImagePopup.value = false;
};

const showPromptHistorySection = () => {
  isHistoryDropdown.value = false;
};
const getRecipePromptHistoryAsync = async () => {
  await store.dispatch("recipeGeneration/getPromptHistoryAsync", {
    params: {
      lang: lang.value,
      from: 0,
      size: 20,
    },
  });

  const response = store.getters["recipeGeneration/getPromptHistory"];
  if (response?.results?.length) {
    isHistorySectionVisible.value = true;
  }
};

const showHistoryDropdownAsync = async () => {
  if (!isHistoryDropdown.value) {
    await getRecipePromptHistoryAsync();
  }
  isHistoryDropdown.value = !isHistoryDropdown.value;
};

const handleClickOutside = (event) => {
  if (isHistoryDropdown.value) {
    const parentElement = event.target.closest(".history-btn");
    if (!parentElement) {
      isHistoryDropdown.value = false;
    }
  }
};
</script>
