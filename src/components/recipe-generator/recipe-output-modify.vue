<template>
  <div class="iq-r-g-recipe-output-panel" :data-panel="RECIPE_OUTPUT_PANEL.MODIFY">
    <div class="iq-r-g-recipe-output-modify">
      <div class="iq-r-g-recipe-output-modify-field">
        <textarea v-model.trim="modifyPromptValue"></textarea>
        <div class="iq-r-g-recipe-output-modify-actions">
          <button
              type="button"
              class="btn-reset display-4 color-green"
              @click="cancelModify"
          >Cancel</button>
          <button
              type="button"
              class="btn-reset display-4 color-green"
              @click="confirmModify(false)"
              :disabled="isLoading || !modifyPromptValue"
          >Confirm</button>
          <button
              type="button"
              class="btn-reset display-4 color-green"
              @click="confirmModify(true)"
              :disabled="isLoading || !modifyPromptValue"
          >Confirm & Refresh Images</button>
        </div>
      </div>
      <recipe-generated-recipe-data></recipe-generated-recipe-data>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import AIService from "@/services/AIService";
import {
  CONTENT_GENERATION_STEP,
  CONTENT_GENERATION_TYPE,
  getStreamMessageForAdvanceOutput,
  parseEventData,
  RECIPE_GENERATION_FLAG,
  RECIPE_GENERATION_FLOW,
  RECIPE_GENERATION_MESSAGE,
  RECIPE_GENERATION_MESSAGES_KEY,
  RECIPE_OUTPUT_PANEL,
} from "@/models/recipe-generator.model";
import { useNuxtApp } from '#app'
import RecipeGeneratedRecipeData from "@/components/recipe-generator/recipe-generated-recipe-data.vue";
import { useI18n } from 'vue-i18n';

const props = defineProps({
  finishedModifyCallback: {
    type: Function,
    required: false,
  },
});

const emit = defineEmits("cancelModify", "confirmModify");

const {
  abortConnection,
  clearTimer,
  setInnerProgressSteps,
  setOnMessageProgressSteps,
  setStreamText,
  setTraceId,
  isLoading,
  handleStreamError,
} = useRecipeGenerator();

const { $eventBus, $tracker } = useNuxtApp();
const { appContext } = getCurrentInstance();
const $keys = appContext.config.globalProperties.$keys;
const { t } = useI18n();
const store = useStore();

// Reactive state
const modifyPromptValue = ref("");
const recipeModificationCtrl = ref(null);
const recipeModificationTimeout = ref(null);
const recipeModificationAbortTime = 180;

watch(() => store.state.resetAt, (val) => {
  if (!val) {
    resetAll();
  }
});

const cancelModify = () => {
  modifyPromptValue.value = "";
  setModifyPrompt("");
  emit("cancelModify", modifyPromptValue.value);
};

/**
 * @param {boolean} isRefreshImages
 */

const confirmModify = (isRefreshImages) => {
  if (!modifyPromptValue.value) {
    return;
  }

  setModifyPrompt(modifyPromptValue.value);
  emit("confirmModify", modifyPromptValue.value);
  modifyRecipe(isRefreshImages);
  modifyPromptValue.value = "";
};
const setTimer = () => {
  // Kill the connection if we haven't received an event
  recipeModificationTimeout.value = setTimeout(() => {
    cancelRecipeModify();
  }, recipeModificationAbortTime * 1000);
};

/**
 * @param {boolean} isRefreshImages
 */
 const modifyRecipe = (isRefreshImages) => {
  if (isLoading.value) {
    return;
  }

  const payload = store.getters["recipeGeneration/getRecipeForModification"];
  if (!payload.customizations) {
    return;
  }

  store.dispatch('recipeGeneration/resetGenerationProgressSteps');

  // Set flags
  store.dispatch('recipeGeneration/setFlag', {
    flags: [
      { flag: RECIPE_GENERATION_FLAG.FLOW, value: RECIPE_GENERATION_FLOW.RECIPE_MODIFYING },
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING, value: true },
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING_RESULT, value: false },
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFY_COMPLETE, value: false },
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATION_COMPLETE, value: false },
    ],
  });

  setStreamText(`\n\n\n *** Modify prompt: ${payload.customizations} *** \n\n`);

  const onMessage = (event) => {
    clearTimeout(recipeModificationTimeout.value);

    if (event.event === "FatalError" || event.event === "Error") {
      handleStreamError(event, {
        isStreamOnOpen: false,
        isEventFatalError: true,
        isStreamOnError: false,
        progressMessage: RECIPE_GENERATION_MESSAGE[RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_MESSAGE_FATAL_ERROR],
      });
      return;
    }

    setTimer();

    const { type, step, result, isData } = parseEventData(event.data);
    if (!isData) {
      return;
    }

    setOnMessageProgressSteps(type, step, result);
    setStreamText(getStreamMessageForAdvanceOutput(type, result));

    if (type !== CONTENT_GENERATION_TYPE.RESULT) {
      return;
    }

    // Recipe details
    if (step === CONTENT_GENERATION_STEP.GENERATE_RECIPE_CONTENT) {
      store.dispatch('recipeGeneration/setRecipe', {
        title: result?.model?.title,
        ingredients: result?.model?.ingredients,
        instructions: result?.model?.instructions,
        servesCount: result?.model?.servings,
      });
      store.dispatch('recipeGeneration/setFlag', {
        flags: [
          { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING_RESULT, value: true },
          { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATING, value: true },
        ],
      });
    }

    // Recipe review
    if (step === CONTENT_GENERATION_STEP.REVIEW_RECIPE_TASTE) {
      store.dispatch('recipeGeneration/setRecipeReview', {
        data: result,
      });
      store.dispatch('recipeGeneration/setFlag', {
        flags: [
          { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATING, value: false },
          { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATION_COMPLETE, value: true },
        ],
      });
    }
  };

  const onClose = () => {
    const isRecipeModifyingResult = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING_RESULT);
    const type = isRecipeModifyingResult ? CONTENT_GENERATION_TYPE.RESULT : CONTENT_GENERATION_TYPE.FAILURE;
    const step = isRecipeModifyingResult ? RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_CORRECTLY_ON_CLOSE : RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_INCORRECTLY_ON_CLOSE;
    recipeModifyEndedFlow(type, step, isRefreshImages);

    if (isRefreshImages) {
      store.dispatch("recipeGeneration/setForceImageRefresh");
    }
  };

  const onError = (err) => {
    trackErrorEvent($keys.EVENT_KEY_NAMES.EXCEPTIONS_GENERATOR_MODIFY_ON_ERROR, err.message || err.errorMessage);
    handleStreamError(err, {
      isStreamOnOpen: false,
      isEventFatalError: false,
      isStreamOnError: true,
      progressMessage: RECIPE_GENERATION_MESSAGE[RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_ERROR_FATAL_ERROR],
    });
    recipeModifyEndedFlow(CONTENT_GENERATION_TYPE.FAILURE, RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_ERROR, false);
    throw err;
  };

  const onOpen = async (response) => {
    setTraceId(response);
    handleStreamError(response, {
      isStreamOnOpen: true,
      isEventFatalError: false,
      isStreamOnError: false,
    });
  };

  recipeModificationCtrl.value = new AbortController();
  AIService.getGenerationStreamAsync(
    store,
    store.$auth,
    payload,
    onOpen,
    onMessage,
    onClose,
    onError,
    recipeModificationCtrl.value,
    "contentGeneration",
  );

  setTimer();
};
const cancelRecipeModify = () => {
  trackErrorEvent($keys.EVENT_KEY_NAMES.EXCEPTIONS_GENERATOR_MODIFY_ON_ABORT)
  abortConnection(recipeModificationCtrl.value)
  recipeModifyEndedFlow(CONTENT_GENERATION_TYPE.FAILURE, RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_ABORT, false)
}
const recipeModifyEndedFlow = (type, step, isRefreshImages) => {
  clearTimer(recipeModificationTimeout.value)
  setInnerProgressSteps(type, step)

  store.dispatch('recipeGeneration/setFlag', {
    flags: [
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING, value: false },
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFY_COMPLETE, value: true },
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATING, value: false },
      { flag: RECIPE_GENERATION_FLAG.IS_RECIPE_REVIEW_TASTE_GENERATION_COMPLETE, value: true },
      { flag: RECIPE_GENERATION_FLAG.FLOW, value: RECIPE_GENERATION_FLOW.INTERMEDIATE },
    ],
  })

  $eventBus.emit($keys.KEY_NAMES.GENERATION_DATA_UPDATED)

  if (!isRefreshImages) {
    props.finishedModifyCallback?.()
  }
  setModifyPrompt("")
}
const setModifyPrompt = (value) => {
  store.dispatch("recipeGeneration/setModifyPromptValue", { value })
}
const resetAll = () => {
  abortConnection(recipeModificationCtrl.value)
  recipeModificationCtrl.value = null

  clearTimer(recipeModificationTimeout.value)
  recipeModificationTimeout.value = null

  modifyPromptValue.value = ""
}
const trackErrorEvent = (eventName, error = null) => {
  const eventData = {
    [t('EVENT_NAMES.PROMPT')]: store.getters["recipeGeneration/getPromptValue"],
    [t('EVENT_NAMES.MODIFY_PROMPT_VALUE')]: store.getters["recipeGeneration/getModifyPromptValue"],
    [t('EVENT_NAMES.IS_RECIPE_MODIFYING_RESULT')]: store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING_RESULT),
    [t('EVENT_NAMES.TRACE_ID')]: store.getters["recipeGeneration/getTraceId"],
  }

  if (error) {
    eventData[t('EVENT_NAMES.ERROR_MESSAGE')] = error
  }

  $tracker.sendEvent(eventName, eventData)
}
</script>
