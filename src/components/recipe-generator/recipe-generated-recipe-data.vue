<template>
  <div class="iq-r-g-recipe-output-default">
    <div class="iq-r-g-recipe-output-default-row">
      <div class="iq-r-g-recipe-output-default-column iq-r-g-recipe-output-default-column-1">
        <div class="iq-r-g-recipe-output-default-head">
          <p class="iq-r-g-recipe-output-default-head-title display-3">{{ recipeData.title }}</p>
        </div>
        <div class="iq-r-g-recipe-output-default-head">
          <p class="iq-r-g-recipe-output-default-head-sub-title">Ingredients</p>
        </div>
        <p class="iq-r-g-recipe-output-default-body">{{ recipeData.ingredients }}</p>
      </div>
      <div class="iq-r-g-recipe-output-default-column iq-r-g-recipe-output-default-column-2">
        <div class="iq-r-g-recipe-output-default-head">
          <p class="iq-r-g-recipe-output-default-head-second-title">Serves: {{ recipeData.servesCount }}</p>
        </div>
        <div class="iq-r-g-recipe-output-default-head">
          <p class="iq-r-g-recipe-output-default-head-sub-title">Instructions</p>
        </div>
        <p class="iq-r-g-recipe-output-default-body">{{ recipeData.instructions }}</p>
      </div>
    </div>
  </div>
</template>


<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';

const store = useStore();

const recipeData = computed(() => store.getters['recipeGeneration/getRecipeData']);
</script>
