<template>
  <div class="iq-r-g-rating-group-button" :class="classes">
    <button
        v-for="btn in buttons"
        type="button"
        class="btn-reset iq-r-g-rating-button"
        :class="{
          [btn.class]: true,
          'iq-r-g-rating-button-small': isSmallStyle,
          '__active': (btn.value === active) || (btn.value === activeThumbs)
        }"
        @click.stop="onClick(btn.value)"
    >
    </button>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { GENERATED_DATA_RESULT } from "@/models/recipe-generator.model";

const emit = defineEmits();
const props = defineProps({
  isSmallStyle: {
    type: Boolean,
    required: false,
    default: false,
  },
  classes: {
    type: String,
    required: false,
  },
  /**
   * @type {GENERATED_DATA_RESULT}
   */
  activeThumbs: {
    type: String,
    required: false,
  },
});
const buttons = [
  {
    class: "__thumbs-up",
    value: GENERATED_DATA_RESULT.GOOD_RATING,
  },
  {
    class: "__thumbs-down",
    value: GENERATED_DATA_RESULT.BAD_RATING,
  }
];
const active = ref(undefined);

watch(() => props.activeThumbs, (value) => {
  if (value) {
    active.value = value;
  }
});
const onClick = (value) => {
  if (value === props.activeThumbs) {
    return;
  }

  active.value = value;
  emit('customClick', value);
};
</script>
