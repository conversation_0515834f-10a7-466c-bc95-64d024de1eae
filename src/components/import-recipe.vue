<template>
  <div>
    <Modal>
      <template #recipeVariantModal>
        <div
          :class="[
            'import-recipe-main-section',
            { content: isRecipeImportStatus },
          ]"
        >
          <div v-if="isLoading" class="loader-content">
            <loader :isImportRecipe="isImportRecipe" />
          </div>
          <div v-if="!isLoading" class="import-recipe-content">
            <div class="import-recipe-top-section">
              <div
                data-test-id="importer-popup-heading"
                class="import-recipe-header text-h2"
              >
                {{ $t("IMPORT_RECIPE.IMPORT_RECIPE_HEADER") }}
              </div>
              <button
                data-test-id="importer-popup-cross-icon"
                class="close-button"
                type="button"
                @click="closeImporterModal"
              >
                <div class="close-icon">
                  <img alt="exit icon" src="@/assets/images/exit-gray.png" />
                </div>
              </button>
            </div>
            <div
              v-if="isRecipeImportedSuccessfully"
              class="import-recipe-successful-section"
            >
              <img
                class="green-correct-icon"
                alt="correct icon"
                src="@/assets/images/green-correct-icon.png"
              />
              <div
                data-test-id="import-success-message"
                class="import-recipe-successful-text text-title-2 font-weight-semi-bold"
              >
                {{ $t("IMPORT_RECIPE.IMPORT_SUCCESS") }}
              </div>
            </div>
            <div
              v-if="!isAllowed"
              class="import-recipe-error-section"
            >
              <div class="import-recipe-error-container">
                <img
                  alt="error"
                  class="import-recipe-error-image"
                  src="@/assets/images/cross-icon-red.png"
                />
                <div class="import-recipe-error-main-container">
                  <p
                    class="import-recipe-error-access-text text-title-2 font-weight-semi-bold"
                  >
                    {{ $t("ACCESS_DENIED") }}
                  </p>
                  <span class="import-recipe-error-text text-light-h3"
                    >{{ $t("ACCESS_DENIED_MESSAGE_RECIPE_IMPORT") }}
                  </span>
                </div>
              </div>
            </div>
            <div
              v-if="!isRecipeImportedSuccessfully && isAllowed"
              class="input-box-section"
            >
              <div class="input-box">
                <input
                  v-model="url"
                  type="text"
                  placeholder="Enter url"
                  :class="[
                    'input-url text-title-2 font-normal',
                    { error: isRecipeImportStatus },
                  ]"
                  @input="handleInputUrl"
                  data-test-id="importer-url-input"
                />
              </div>
            </div>
            <div
              v-if="isRecipeImportStatus && !isRecipeImportedSuccessfully"
              class="import-recipe-warning"
              data-test-id="import-error-message"
            >
              <img src="@/assets/images/red-info.svg?skipsvgo=true" alt="info icon" />
              <span
                >{{ $t("IMPORT_RECIPE.IMPORT_RECIPE_ERROR_MESSAGE") }}
              </span>
            </div>
            <div :class="['button-section', { content: isRecipeImportStatus }]">
              <div
                v-if="!isRecipeImportedSuccessfully && isAllowed"
                class="cancel-and-import-recipe-button-section"
              >
                <button
                  class="btn-green-outline"
                  type="button"
                  @click="closeModal"
                  data-test-id="close-import-popup-button"
                >
                  {{ $t("BUTTONS.CANCEL_BUTTON") }}
                </button>
                <button
                  @click="postRecipeImportAsync()"
                  data-test-id="import-button"
                  :class="
                    isRecipeImportButtonActive
                      ? 'btn-green'
                      : 'disabled-button btn-green'
                  "
                  type="button"
                >
                  {{ $t("IMPORT_RECIPE.IMPORT") }}
                </button>
              </div>
              <div
                v-if="isRecipeImportedSuccessfully || !isAllowed"
                :class="[
                  'close-button-section',
                  { content: isRecipeImportedSuccessfully },
                ]"
              >
                <button
                  :class="
                    isAllowed
                      ? 'btn-green'
                      : 'btn-red'
                  "
                  type="button"
                  @click="closeImporterModal()"
                >
                  {{ $t("BUTTONS.CLOSE_BUTTON") }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import Modal from "@/components/Modal";
import loader from "@/components/loader";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useI18n } from "vue-i18n";
import { useNuxtApp } from '#app';
import { useProjectLang } from "../composables/useProjectLang";
import { useStore } from "vuex";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useRoute } from 'vue-router';

const props = defineProps({
  closeModal: Function,
});

const url = ref("");
const isImportRecipe = ref(true);
const isRecipeImportStatus = ref(false);
const isRecipeImportButtonActive = ref(false);
const isRecipeImportedSuccessfully = ref(false);
const isLoading = ref(false);


const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { t } = useI18n();
const { $tracker } = useNuxtApp();

const store = useStore();
const { hasContentPermission } = useProjectLang();
const { triggerLoading, validateRecipeURL } = useCommonUtils();
const route = useRoute();
const isAllowed = ref(false);

const lang = computed(() => store.getters["userData/getDefaultLang"]);

// Lifecycle hooks
onMounted(async () => {
  await checkPermission();
  checkEvent($keys.EVENT_KEY_NAMES.VIEW_IMPORTER);
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
});

const checkPermission = async () => {
  isAllowed.value = await hasContentPermission.value;
};

const checkEvent = (description, params = {}) => {
  const eventProperties = {};

  if (params.url) {
    eventProperties[t("EVENT_NAMES.LINK_NAME")] = params.url;
  }

  if (params.errorMessage) {
    eventProperties[t("EVENT_NAMES.URL_STRING_FROM_INPUT_BOX")] =
      params.errorMessage;
  }

  $tracker.sendEvent(description, eventProperties, {
    ...LOCAL_TRACKER_CONFIG,
  });
};

const postRecipeImportAsync = async () => {
  isLoading.value = true;
  checkEvent($keys.EVENT_KEY_NAMES.CLICK_IMPORT_SUBMIT, {
    url: url.value,
  });
  const payload = {
    urls: [url.value],
  };

  try {
    const response = await store.dispatch("recipe/postImportRecipeAsync", {
      payload,
      lang: lang.value,
      onError: errorCallBack,
    });

    response?.data.forEach((recipe) => {
      if (recipe?.status) {
        isRecipeImportedSuccessfully.value = true;
        checkEvent($keys.EVENT_KEY_NAMES.VIEW_IMPORT_SUCCESS, {
          url: url.value,
        });
      }
    });

    if (!isRecipeImportedSuccessfully.value) {
      isRecipeImportStatus.value = true;
      isRecipeImportButtonActive.value = false;
      url.value = "";
    } else {
      isRecipeImportStatus.value = false;
    }
  } catch (error) {
    errorCallBack(error);
  } finally {
    isLoading.value = false;
  }
};

const errorCallBack = (error) => {
  isLoading.value = false;
  checkEvent($keys.EVENT_KEY_NAMES.VIEW_IMPORT_ERROR, {
    url: url.value,
    errorMessage: error.message,
  });
};

const handleInputUrl = (event) => {
  const inputValue = event?.target?.value?.trim();
  const isActive = !!inputValue?.length;
  isRecipeImportStatus.value = isActive
    ? !validateRecipeURL(inputValue)
    : false;
  isRecipeImportButtonActive.value = isActive && !isRecipeImportStatus.value;
};

const closeImporterModal = () => {
  const routePath = route.path;
  switch (routePath) {
    case "/recipes":
      triggerLoading($keys?.KEY_NAMES?.FETCH_RECIPE_DETAILS);
      if (isRecipeImportedSuccessfully.value) {
        triggerLoading($keys?.KEY_NAMES?.UPDATE_RECIPE_LIST);
      }
      break;
    case "/overview":
      triggerLoading($keys?.KEY_NAMES?.RECIPE_COUNT_UPDATE);
      break;
    default:
      console.error(t("COMMON.UNKNOWN_PATH"), routePath);
  }
  props.closeModal();
};

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape" && !isLoading.value) {
    closeImporterModal();
  }
};
</script>
