<template>
    <div>
        <Modal>
            <template #recipeVariantModal>
                <div class="export-main-section">
                    <div v-if="isLoading" class="loader-content">
                        <loader :isExportRecipe="isExportRecipe" />
                    </div>
                    <div class="export-section-main-container" v-if="!isLoading">
                        <div class="export-top-section">
                            <div data-test-id="export-popup-header" class="export-header text-h2">
                            {{ $t('EXPORT_RECIPE.EXPORT_RECIPE_HEADER') }}
                            </div>
                            <button type="button" class="btn-reset" @click="closeModal">
                                <img class="close-icon" alt="close" src="@/assets/images/exit-gray.png" />
                            </button>
                        </div>
                        <div v-if="isExportStartedSuccessfully" class="export-successful-section">
                            <img class="green-correct-icon" alt="correct-icon" src="@/assets/images/green-correct-icon.png" />
                            <div data-test-id="export-popup-success" class="export-successful-text text-title-2 font-weight-semi-bold">
                                {{ $t('EXPORT_RECIPE.EXPORT_SUCCESS') }}
                            </div>
                        </div>
                        <div v-if="!isAllowed" class="export-error-section">
                            <div class="export-error-container">
                                <img alt="error-icon" class="export-error-image"
                                    src="~/assets/images/cross-icon-red.png" />
                                <div class="export-error-main-container">
                                    <p class="export-error-access-text text-title-2 font-weight-semi-bold">{{ $t('ACCESS_DENIED') }}</p>
                                    <span class="export-error-text text-light-h3">{{ $t('ACCESS_DENIED_MESSAGE') }}</span>
                                </div>
                            </div>
                        </div>
                        <div v-if="!isExportStartedSuccessfully && isAllowed"
                            class="export-checkbox-section">
                            <div v-for="(exportList, index) in exportListData" :key="index">
                                <div class="export-category">
                                    <div>{{ exportList.category }}:</div>
                                </div>
                                <div v-for="(item, itemIndex) in exportList.items" :key="itemIndex" class="export-data-list">
                                    <label class="control-radio control-radio-20">
                                        <input type="radio" :checked="item.checked" @click="selectRecipeStatus(item)" />
                                        <span class="checkmark"></span>
                                    </label>
                                    <div class="export-recipe-list-data">
                                        <button
                                            type="button"
                                            @click="selectRecipeStatus(item)"
                                            class="btn-reset export-recipe-name text-h3 font-normal"
                                        >
                                            {{ item.label }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!isExportStartedSuccessfully && isAllowed"
                            class="export-initial-button-section">
                            <button data-test-id="export-popup-cancel-button" type="button" @click="closeModal"
                             class="btn-green-outline">
                                {{ $t('BUTTONS.CANCEL_BUTTON') }}
                            </button>
                            <button data-test-id="export-popup-export-button" type="button" @click="exportRecipeAsync()"
                             class="btn-green">
                                {{ $t('EXPORT_RECIPE.EXPORT_AS_CSV') }}
                            </button>
                        </div>
                        <div v-if="isExportStartedSuccessfully || !isAllowed"
                            class="export-successful-button-section">
                            <button data-test-id="export-popup-close-button" type="button"
                                :class="isAllowed ? 'btn-green' : 'btn-red'"
                                @click="closeModal">
                                {{ $t('BUTTONS.CLOSE_BUTTON') }}
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance, computed } from "vue";
import Modal from "@/components/Modal";
import FileSaver from "file-saver";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useI18n } from "vue-i18n";
import { useNuxtApp } from '#app';
import { useProjectLang } from "../composables/useProjectLang";
import { useStore } from "vuex";
import { useConfigStore } from "@/stores/config.js";


const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { t } = useI18n();
const { $tracker, $eventBus } = useNuxtApp();
const { hasContentPermission } = useProjectLang();
const store = useStore();
const configStore = useConfigStore();

const props = defineProps({
  closeModal: {
    type: Function,
  },
});

const exportListData = ref([
  {
    category: t("COMMON.RECIPES"),
    items: [
      {
        label: t("COMMON.ALL_RECIPES"),
        value: $keys.KEY_NAMES.ALL,
        checked: true,
      },
      {
        label: t("COMMON.ALL_PUBLISHED_RECIPES"),
        value: $keys.KEY_NAMES.PUBLISHED,
        checked: false,
      },
      {
        label: t("COMMON.ALL_UNPUBLISHED_RECIPES"),
        value: $keys.KEY_NAMES.UNPUBLISHED,
        checked: false,
      },
    ],
  },
  {
    category: t("COMMON.OTHER"),
    items: [
      {
        label: t("COMMON.RECIPE_GENERATOR_HISTORY"),
        value: "history",
        checked: false,
      },
    ],
  },
]);

const isExportRecipe = ref(true);
const csvFileStatus = ref(t("COMMON.ALL_RECIPES"));
const isExportStartedSuccessfully = ref(false);
const fileName = ref("Report.csv");
const isLoading = ref(true);
const isAllowed = ref(false);

const lang = computed(() => store.getters["userData/getDefaultLang"]);

const getFeatureConfig = computed(() => {
  return configStore.features || {};
});

onMounted(async () => {
  await checkPermission();
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
  exportListData.value.forEach((exportData) => {
    exportData.isChecked = false;
    if (exportData.name === t("COMMON.ALL_RECIPES")) {
      exportData.isChecked = true;
    }
  });
  if (!getFeatureConfig.value[$keys.KEY_NAMES.GENERATOR]) {
    const itemName = t('COMMON.OTHER');
    const index = exportListData.value.findIndex((item) => item.category === itemName);
    if (index !== -1) {
      exportListData.value.splice(index, 1);
    }
  }
  isLoading.value = false;
  checkEvent($keys.EVENT_KEY_NAMES.VIEW_EXPORT);
});

onBeforeUnmount(() => {
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});

const checkPermission = async () => {
  isAllowed.value = await hasContentPermission.value;
};

const handleESCClickOutside = (event) => {
  if (event?.key === $keys.KEY_NAMES.ESCAPE) {
    props.closeModal();
  }
};

const selectRecipeStatus = (selectedItem) => {
  exportListData.value = exportListData.value.map((category) => {
    category.items = category.items.map((item) => {
      if (item.label === selectedItem.label) {
        item.checked = true;
        csvFileStatus.value = item.label;
      } else {
        item.checked = false;
      }
      return item;
    });
    return category;
  });
};

const exportRecipeAsync = async () => {
  isExportStartedSuccessfully.value = true;
  await getRecipeCSVAsync();
};

const getRecipeCSVAsync = async () => {
  try {
    exportListData.value?.forEach((category) => {
      category.items.forEach((item) => {
        if (item.checked) {
          fileName.value = item.label;
          csvFileStatus.value = item.value;
        }
      });
    });

    checkEvent($keys.EVENT_KEY_NAMES.CLICK_EXPORT_SUBMIT, true);

    const action =
      csvFileStatus.value === $keys.KEY_NAMES.HISTORY
        ? "recipe/getExportPromptHistoryAsync"
        : "recipe/getRecipeCSVAsync";

    const getter =
      csvFileStatus.value === $keys.KEY_NAMES.HISTORY
        ? "recipe/getExportPromptHistory"
        : "recipe/getRecipeCsvData";

    await store.dispatch(action, {
      lang: lang.value,
      ...(csvFileStatus.value !== $keys.KEY_NAMES.HISTORY && {
        status: csvFileStatus.value,
      }),
    });

    const response = store.getters[getter];
    if (response) {
      FileSaver.saveAs(new Blob([response]), `${fileName.value}.csv`);
      checkEvent($keys.EVENT_KEY_NAMES.VIEW_EXPORT_SUCCESS, true);
      postRecipeExportActivityAsync();
    }
  } catch (error) {
    checkEvent($keys.EVENT_KEY_NAMES.VIEW_EXPORT_ERROR, true, error.message);
    console.error($keys.KEY_NAMES.ERROR_IN + "getRecipeCSVAsync:", error);
    props.closeModal();
  }
};

const postRecipeExportActivityAsync = async () => {
  const count = store.getters["recipe/getExportedRecipesCount"];
  const payload = {
    type: $keys.RECENT_ACTIVITIES.EXPORT_RECIPES,
    data: { totalExportRecipe: count },
  };

  const isPromptHistoryExported = exportListData.value.some(
    (exportCategory) =>
      exportCategory?.category === "Other" &&
      exportCategory?.items.some(
        (item) => item?.value === "history" && item?.checked
      )
  );

  if (!isPromptHistoryExported) {
    try {
      await store.dispatch("recipe/postRecentActivityAsync", {
        lang: lang.value,
        payload,
      });
      $eventBus.emit($keys.RECENT_ACTIVITIES.REFRESH_ACTIVITY);
    } catch (error) {
      console.error(
        $keys.KEY_NAMES.ERROR_IN + "postRecipeExportActivityAsync:",
        error
      );
    }
  }
};

const checkEvent = (
  description,
  includeFileName = false,
  errorMessage = false
) => {
  const eventProperties = {};

  if (includeFileName && fileName.value) {
    eventProperties[t("EVENT_NAMES.SELECTED_OPTION")] = fileName.value;
  }

  if (errorMessage) {
    eventProperties[t("EVENT_NAMES.ERROR_MESSAGE")] = errorMessage;
  }

  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};
</script>
