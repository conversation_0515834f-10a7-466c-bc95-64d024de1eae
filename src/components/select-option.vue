<template>
  <div
    ref="selectOptionElRef"
    class="select-option w-100"
    :class="{
      'select-option-open': isOpen,
      'select-option-has-confirm': props.hasConfirm,
      'select-option-has-search': props.hasSearch,
    }"
    aria-label="Select option"
    :aria-expanded="isOpen"
    :aria-label="placeholder"
    :aria-controls="dropdownId"
  >
    <div
      class="select-option-container w-100 cursor-pointer"
      @click.stop="openDropdown"
    >
      <button
        type="button"
        class="btn-reset select-option-btn-icon"
        @click.stop="toggleDropdown"
        :aria-label="isOpen ? 'Close dropdown' : 'Open dropdown'"
      >
        <img src="@/assets/images/arrow-right.png" class="select-option-icon" alt="arrow icon" aria-hidden="true" />
      </button>

      <input
        type="text"
        class="w-100 font-size-base font-normal font-family-averta color-black-mine-shaft"
        :class="{
          'cursor-pointer': !inputModel && !isOpen
        }"
        v-model.trim="inputModel"
        @input="handleSearch"
        :readonly="isInputReadonly"
        :placeholder="placeholder"
        :aria-expanded="isOpen"
        aria-autocomplete="list"
      />
    </div>

    <Teleport to="body">
      <div
        v-if="isOpen"
        :id="dropdownId"
        ref="dropdownRef"
        :style="dropdownStyles"
        class="select-option-dropdown"
        aria-label="Select options list"
        v-on-click-outside="onClickOutsideHandler"
        :data-dropdown-position="dropdownPosition"
      >
        <div v-if="hasSearch && isInputReadonly" class="select-option-dropdown-search">
          <input
            ref="searchInputRef"
            type="text"
            class="w-100 font-size-base font-normal font-family-averta color-black-mine-shaft"
            @input="handleSearch"
            :placeholder="placeholder"
            aria-label="Search options"
          >
        </div>
        <div class="select-option-dropdown-list">
          <button
            v-if="hasAddOption"
            type="button"
            class="btn-reset select-option-option select-option-option-add w-100"
            @click.stop="addNewOption"
          >
            <span class="select-option-option-add-icon font-size-28 font-normal color-slate-gray">+</span>
            <span class="font-size-base font-normal color-black-mine-shaft">{{ addOptionLabel }}</span>
          </button>
          <div
            v-for="(option, index) in filteredOptions"
            :key="option.uuid"
            :ref="el => setOptionRef(option.uuid, el)"
            class="select-option-option"
            :class="{
              'select-option-option-selected': radioModel?.uuid === option.uuid,
              'select-option-option-focused': focusedOptionIndex === index
            }"
            :aria-selected="radioModel?.uuid === option.uuid"
            :aria-label="option.name"
          >
            <label class="control-radio w-100">
              <span class="font-size-base font-normal color-black-mine-shaft">{{ option.name }}</span>
              <input
                type="radio"
                name="dropdown-option"
                :value="option"
                v-model="radioModel"
              >
              <span class="checkmark"></span>
            </label>
          </div>
        </div>
        <div v-if="hasConfirm" class="select-option-dropdown-confirm">
          <button
            type="button"
            class="btn-green btn-small"
            @click.stop="selectOption"
            :disabled="!radioModel || radioModel.uuid === model"
          >{{ $t('BUTTONS.APPLY_BUTTON') }}</button>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { vOnClickOutside } from '@vueuse/components';

const DROPDOWN_POSITIONS = {
  TOP: 'top',
  BOTTOM: 'bottom'
};

const MAX_DROPDOWN_HEIGHT = 380;

const props = defineProps({
  options: {
    type: Array,
    default: [], // [{uuid: "", name: ""}]
  },
  placeholder: {
    type: String,
    default: 'Select option',
  },
  hasConfirm: {
    type: Boolean,
    default: true,
  },
  hasSearch: {
    type: Boolean,
    default: true,
  },
  hasAddOption: {
    type: Boolean,
    default: true,
  },
  addOptionLabel: {
    type: String,
    default: 'Add a new category',
  },
});

const emit = defineEmits(['select-option:change', 'select-option:add-new']);
const model = defineModel();

const selectOptionElRef = ref();
const searchInputRef = ref();
const dropdownRef = ref();

const dropdownId = ref(`dropdown-${Date.now()}`);
const isOpen = ref(false);
const inputModel = ref("");
const radioModel = ref();
const searchQuery = ref('');
const isConfirmed = ref(!!model.value);
const dropdownPosition = ref(DROPDOWN_POSITIONS.BOTTOM); // top, bottom
const focusedOptionIndex = ref(-1); // Track focused index
const optionRefs = ref(new Map());

const dropdownStyles = reactive({
  zIndex: '1000',
  position: 'fixed',
  top: '0px',
  left: '0px',
  width: '0px',
  margin: '0px',
});

const onClickOutsideHandler = [() => closeDropdown(), { ignore: [selectOptionElRef] }];

const isInputReadonly = computed(() => {
  if (!props.hasConfirm) {
    return false;
  }

  return !!(inputModel.value && isConfirmed.value);
});
const filteredOptions = computed(() => props.options.filter(
  (option) => option.name?.toLowerCase()?.includes(searchQuery.value?.toLowerCase())
));

const setOptionRef = (uuid, el) => {
  if (el) {
    optionRefs.value.set(uuid, el);
  }
};

const scrollToSelectedOption = () => {
  nextTick(() => {
    const selectedEl = optionRefs.value.get(radioModel.value?.uuid);
    selectedEl?.scrollIntoView({ block: 'nearest' });
  });
};


const setDropdownPosition = () => {
  const triggerEl = selectOptionElRef.value;
  const dropdownEl = dropdownRef.value;
  if (!triggerEl || !dropdownEl) return;

  const rect = triggerEl.getBoundingClientRect();
  const spaceBelow = window.innerHeight - rect.bottom;
  const spaceAbove = rect.top;

  // Decide dropdown placement
  const position =
    (spaceBelow >= MAX_DROPDOWN_HEIGHT || spaceBelow >= spaceAbove)
      ? DROPDOWN_POSITIONS.BOTTOM
      : DROPDOWN_POSITIONS.TOP;

  dropdownPosition.value = position;

  // Apply styles
  const top = position === DROPDOWN_POSITIONS.BOTTOM
    ? rect.bottom + 5
    : rect.top - dropdownEl.offsetHeight - 5;

  dropdownStyles.top = `${Math.max(top, 0)}px`;
  dropdownStyles.left = `${rect.left}px`;
  dropdownStyles.width = `${rect.width}px`;
  dropdownStyles.margin = (position === DROPDOWN_POSITIONS.TOP) ? '0 0 5px' : '5px 0 0';
};

const focusSearchInput = () => props.hasSearch && isInputReadonly.value && nextTick(() => searchInputRef.value?.focus());

const setOpen = (val) => isOpen.value = val;
const openDropdown = async () => {
  setOpen(true);
  await nextTick();
  setDropdownPosition();
  focusSearchInput();
  const initialIndex = filteredOptions.value.findIndex(opt => opt.uuid === radioModel.value?.uuid);
  focusedOptionIndex.value = initialIndex >= 0 ? initialIndex : -1;
  scrollToSelectedOption();
};
const closeDropdown = () => {
  setOpen(false);
  clearSearch();
  focusedOptionIndex.value = -1;
};
const toggleDropdown = () => !isOpen.value ? openDropdown() : closeDropdown();

const handleSearch = (event) => searchQuery.value = event.target.value;
const clearSearch = () => searchQuery.value = "";

const selectOption = () => {
  if (radioModel.value?.['uuid']) {
    model.value = radioModel.value?.["uuid"];
    inputModel.value = radioModel.value?.['name'];
    emit("select-option:change", radioModel.value);
    isConfirmed.value = true;
    focusedOptionIndex.value = filteredOptions.value.findIndex(opt => opt.uuid === radioModel.value?.uuid);

    // Reset focus so Enter doesn't trigger anything
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }

    closeDropdown();
  }
};

const addNewOption = () => {
  emit('select-option:add-new', true);
  closeDropdown();
};

const focusOptionByIndex = (index) => {
  const options = filteredOptions.value;
  if (index < 0 || index >= options.length) return;

  focusedOptionIndex.value = index;
  radioModel.value = options[index];

  nextTick(() => {
    const dropdownEl = dropdownRef.value;
    const optionEls = dropdownEl?.querySelectorAll('.select-option-option');
    if (optionEls && optionEls.length > 0) {
      const el = optionEls[index];
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  });
};

watch(() => [model, props.options], (data) => {
  const [modelVal, optionsVal] = data;
  const item = optionsVal?.find((item) => item.uuid === modelVal.value);
  inputModel.value = item?.name || "";
  radioModel.value = item;
}, { immediate: true });

watch(radioModel, () => {
  if (!props.hasConfirm) {
    selectOption();
  }
});

onKeyStroke('ArrowDown', (e) => {
  if (!isOpen.value) return;
  e.preventDefault();
  const options = filteredOptions.value;
  const nextIndex = (focusedOptionIndex.value + 1) % options.length;
  focusOptionByIndex(nextIndex);
});

onKeyStroke('ArrowUp', (e) => {
  if (!isOpen.value) return;
  e.preventDefault();
  const options = filteredOptions.value;
  const prevIndex = (focusedOptionIndex.value - 1 + options.length) % options.length;
  focusOptionByIndex(prevIndex);
});


useEventListener('resize', setDropdownPosition);
useEventListener('scroll', setDropdownPosition);
onMounted(() => {
  setDropdownPosition();
});
</script>