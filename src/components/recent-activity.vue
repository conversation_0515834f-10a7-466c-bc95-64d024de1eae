<template>
  <div class="recent-activity-list-container">
    <div
      class="recent-activity-list-main"
      v-for="(item, index) in recentActivityList"
      :key="index"
    >
      <div class="btn-container">
        <button
          type="button"
          @click="item.isDeleteFlagVisible ? deleteActivityAsync(item?.uuid) : deleteActivityButton(item)"
          class="btn-reset"
          :class="item.isDeleteFlagVisible ? 'delete-flag' : 'delete-icon'"
        >
          <span v-if="item.isDeleteFlagVisible">{{ $t('BUTTONS.DELETE_BUTTON') }}</span>
          <img
            class="delete-button-image"
            :src="deleteIcon"
            alt="delete"
          />
        </button>
      </div>
      <div class="recent-activity-list">
        <div class="recent-activity">
          <div>
            <img
              class="recent-activity-image"
              :src="categorizeImage(item?.type)"
              alt="recent-activity"
            />
          </div>
        </div>
        <div v-if="isCountBox(item)">
          <div class="total-recipe-count">{{ getTotalCount(item) }}</div>
          <div class="line"></div>
          <div class="recent-activity-recipe">{{ item?.recipe || '' }}</div>
        </div>
        <div v-else>
          <img
            class="recipe-image"
            :src="item?.recipeImage || defaultImage"
            alt="recipe-icon"
          />
          <div class="recent-activity-recipe">{{ item?.recipe || '' }}</div>
        </div>
        <div v-if="!item.progressState" class="recent-activity-time">
          {{ item?.time || '' }}
        </div>
        <div v-else class="recent-activity-in-progress">
          {{ $t("BATCH_GENERATOR.PROCESSING") }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { getCurrentInstance } from 'vue';
import defaultImage from '@/assets/images/default_recipe_image.png';
import deleteIcon from '@/assets/images/icons/remove-icon.svg?skipsvgo=true';
import manualAddedRecipe from '@/assets/images/manual-added-recipe.svg?skipsvgo=true';
import exportRecipe from '@/assets/images/export-recipe.svg?skipsvgo=true';
import generatedRecipe from '@/assets/images/generated-recipe.svg?skipsvgo=true';
import batchGeneratedRecipe from '@/assets/images/batch-generated-recipe.svg?skipsvgo=true';

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const props = defineProps({
  recentActivityList: {
    type: Array,
    default: () => [],
    required: true,
  },
  deleteActivityAsync: {
    type: Function,
    default: () => {},
  },
});

const activityImages = {
  [$keys.RECENT_ACTIVITIES.ADD_RECIPES]: manualAddedRecipe,
  [$keys.RECENT_ACTIVITIES.EXPORT_RECIPES]: exportRecipe,
  [$keys.RECENT_ACTIVITIES.GENERATED_RECIPES]: generatedRecipe,
  [$keys.RECENT_ACTIVITIES.BATCH_GENERATED]: batchGeneratedRecipe
};


const deleteActivityButton = (item) => {
  props.recentActivityList.forEach((activity) => {
    activity.isDeleteFlagVisible = false;
  });
  item.isDeleteFlagVisible = !item.isDeleteFlagVisible;
};

const categorizeImage = (type) => {
  return activityImages[type] || '';
};

const isCountBox = (item) => {
  return (
    item.value === $keys.RECENT_ACTIVITIES.BATCH_GENERATOR_CARD_POSITION ||
    item.value === $keys.RECENT_ACTIVITIES.EXPORT_RECIPES_CARD_POSTITION
  );
};

const getTotalCount = (item) => {
  return item.totalCount || 0;
};
</script>
