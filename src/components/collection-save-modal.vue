
<template>
    <Modal @close="closeModal">
        <template #saveModal>
            <div class="collection-save-modal">
                <div class="collection-save-info-popup-container">
                    <div class="collection-save-image">
                        <div class="collection-save-image-container">
                            <img alt="" class="collection-save-icon" :src="imageName" />
                        </div>
                    </div>
                </div>
                <div class="collection-publish-content">
                    <div class="collection-publish-head"> {{ description }} </div>
                    <div v-show="buttonName === $t('COMMON.PUBLISH')" class="collection-publish-head-desc">{{ descriptionTwo }}</div>
                     <div class="collection-button-container">
                        <button
                            type="button"
                            class="btn-green-outline"
                            data-test-id="confirm-publish-cancel-button"
                            @click="closeModal"
                        >
                            {{ $t('BUTTONS.CANCEL_BUTTON') }}
                        </button>
                        <button
                            type="button"
                            class="btn-green"
                            data-test-id="confirm-publish-button"
                            @click="saveAndPublishFunction"
                        >
                            <span> {{ buttonName }} </span>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </Modal>
</template>
<script>
import Modal from "@/components/Modal";

/**
 * @deprecated We should use useBaseModal({ "collectionSaveModal": { component: ConfirmModal } })!
 */
export default {
    name: "collection-save-modal",
    components: {
        Modal,
    },
    data() {
        return {};
    },
    props: {
        closeModal: {
            type: Function,
        },
        saveAndPublishFunction: {
            type: Function,
        },
        description: {
            type: String,
            default: "",
        },
        descriptionTwo: {
            type: String,
            default: "",
        },
        buttonName: {
            type: String,
            default: "",
        },
        imageName: {
            type: String,
            default: "",
        },
    },
};
</script>
