<template>
  <div class="table-content">
    <table class="table" aria-label="master-table">
      <thead class="table-head-row">
        <tr class="table-head">
          <th
            v-for="(tableHead, index) in masterTableHeadData"
            :key="index"
            class="table-head-name"
            :class="{
              'table-head-title':
                (index === 2 && !isIngredientPage) ||
                (index === 3 && isCategoryGroupPage) ||
                (index === 0 && isIngredientPage),
              'table-head-ingredient-name':
                (index === 0 && isIngredientPage),
              'table-head-ingredient-product':
                (index === 1 && isIngredientPage),
              'table-head-ingredient-edit':
                (index === 2 && isIngredientPage),
            }"
          >
            {{ tableHead }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="table-body"
          v-for="(tableData, index) in masterTableData"
          :key="index"
          :class="
            isIngredientPage && !tableData.isShoppable
              ? 'table-non-shoppable-ingredient-row'
              : ''
          "
        >
          <td
            class="table-image"
            v-if="isOrganizationPage || isCategoryPage || isCategoryGroupPage"
          >
            <v-lazy-image
              alt=""
              :src-placeholder="defaultImage"
              :src="
                tableData && tableData.image ? tableData.image : defaultImage
              "
            />
          </td>
          <td class="table-number" v-if="!isIngredientPage">
            <div v-if="tableData.isin" class="table-isin">
              {{ tableData.isin }}
            </div>
          </td>
          <td
            class="table-name"
            v-if="!isIngredientPage && !isCategoryPage && !isCategoryGroupPage && !isTagPage"
          >
            <div v-if="tableData.name" class="table-title">
              {{ tableData.name }}
            </div>
          </td>
          <td class="table-content-name-section" v-if="isIngredientPage">
            <div class="content-name-section">
              <div v-if="tableData.name" class="table-title">
                {{ tableData.name }}
              </div>
              <div
                v-if="tableData && tableData.isOnlyIncluded && isAdminCheck"
                class="table-tooltip-info"
              >
                <div class="tool-tip-locked-campaign">
                  This ingredient has a locked campaign. It will not be
                  automatically updated with new products.
                </div>
                <img
                  alt=""
                  src="@/assets/images/lockicon-ingredients.png"
                  class="symbol-icon"
                />
              </div>
            </div>
          </td>
          <td
            class="table-content-name-section"
            v-if="isCategoryPage || isCategoryGroupPage || isTagPage"
          >
            <div class="content-name-section">
              <div v-if="tableData.name" class="table-title">
                {{ tableData.name }}
              </div>
              <div
                v-if="isLanguageAvailable(tableData)"
                class="table-tooltip-info"
              >
                <div
                  class="tool-tip-locked-campaign tool-tip-available-language"
                >
                  Available in
                  <span
                    v-for="(availableLanguage, index) in tableData.langs"
                    :key="availableLanguage"
                  >
                    {{
                      displayTooltipLanguage(
                        availableLanguage,
                        index + 1,
                        tableData.langs.length
                      )
                    }}
                  </span>
                </div>
                <img alt="" src="@/assets/images/language-icon.png" />
              </div>
              <div v-if="tableData.hasAlert" class="table-tooltip-info">
                <div class="tool-tip-locked-campaign alert-tooltip-info">
                  Some recipes have a language variant. Add the language variant
                  of the category.
                </div>
                <img
                  alt=""
                  src="@/assets/images/red-info.svg?skipsvgo=true"
                  class="alert-icon"
                />
              </div>
            </div>
          </td>
          <td class="table-count" v-if="isTagPage || isCategoryPage">
            <div class="table-recipe-count">
              {{ tableData.totalRecipes }}
              <span v-if="tableData.totalRecipes > 1">Recipes</span>
              <span v-else>Recipe</span>
            </div>
          </td>
          <td
            class="table-category-and-recipe-count"
            v-if="isCategoryGroupPage"
          >
            <div class="table-count">
              <div class="table-category-count">
                {{ tableData.totalCategories }}
                <span v-if="tableData.totalCategories > 1">Categories</span>
                <span v-else>Category</span>
              </div>
              <span>|</span>
              <div class="table-recipe-count">
                {{ tableData.totalRecipes }}
                <span v-if="tableData.totalRecipes > 1">Recipes</span>
                <span v-else>Recipe</span>
              </div>
            </div>
            <div class="table-category-name">
              {{ tableData.categoryName }}
            </div>
          </td>
          <td class="table-ingredient-count text-light-h3" v-if="isIngredientPage">
            <div v-if="tableData.isShoppable" class="table-count">
              <div :class="tableData.isPromoted ? 'bold-text' : ''">
                {{ tableData.promotedCount }} {{ $t('COMMON.PROMOTED') }}
              </div>
              <div class="product-matches">
                <span :class="{ 'gray-faded-divider': tableData.productCount === 0 }">|</span>
                <span class="matches-text common-matches-text" :class="{ 'gray-faded-text': tableData.productCount === 0 }">
                  {{ tableData.productCount }} {{ $t('COMMON.MATCHES') }}
                </span>
                <div v-if="tableData.isPromoted" class="info-icon-container">
                  <img alt="icon" src="@/assets/images/wrong-icon-cta.png" />
                  <div class="tooltip text-h4 font-light">
                    {{ $t('PROMOTED_PRODUCTS_TOOLTIP') }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="table-count">{{ $t('COMMON.NOT_SHOPPABLE') }}</div>
          </td>
          <td class="table-published" v-if="!isIngredientPage">
            <div
              class="publishing-state"
              v-if="tableData && tableData.state === 'publishing'"
            >
              <span>
                <img alt="" src="@/assets/images/updating-icon.png" />{{
                  $t('UPDATING')
                }}
              </span>
            </div>
            <div
              class="published-state"
              v-if="
                tableData &&
                ['published', 'readyToPublish'].includes(tableData.state) &&
                (!isCategoryGroupPage ||
                  (tableData.status === 'active' &&
                    tableData.state === 'published'))
              "
            >
              <span>
                <img alt="" src="@/assets/images/published-icon.png" />{{
                  $t('COMMON.PUBLISHED')
                }}
              </span>
            </div>
            <div
              class="unpublished-state"
              v-if="
                tableData &&
                (['unpublished', 'hidden', 'readyToPublish'].includes(
                  tableData.state
                ) ||
                  isCategoryGroupPage) &&
                (!isCategoryGroupPage ||
                  (tableData.status === 'hidden' &&
                    ['published', 'readyToPublish'].includes(tableData.state)))
              "
            >
              <span
                ><img alt="" src="@/assets/images/unpublished-icon.png" />{{
                  $t('COMMON.UNPUBLISHED')
                }}</span
              >
            </div>
            <div
              class="timeout-state"
              v-if="tableData && tableData.state === 'failed'"
            >
              <span>{{ $t('COMMON.FAILED') }}</span>
            </div>
          </td>
          <td class="buttons-section">
            <div class="button-zone">
              <button type="button" @click="handleEdit(tableData)" class="edit">
                <div
                  v-if="tableData && tableData.state === 'publishing'"
                  class="updating-tooltip-wrapper"
                >
                  <span class="tooltip-text">Cannot edit while updating</span>
                </div>
                <img
                  alt=""
                  src="@/assets/images/edit-icon.png"
                  class="edit-icon"
                  :class="{
                    'disabled-image':
                      tableData && tableData.state === 'publishing',
                  }"
                />
              </button>
              <button type="button"
                v-if="!isIngredientPage"
                @click="
                  tableData && tableData.state === 'publishing'
                    ? ''
                    : handleDelete(tableData)
                "
                class="delete"
                :disabled="
                  tableData &&
                  tableData.state === 'publishing' &&
                  (tableData.totalRecipes !== 0 || isOrganizationPage)
                "
              >
                <div
                  v-if="tableData && tableData.state === 'publishing'"
                  class="updating-tooltip-wrapper"
                >
                  <span class="tooltip-text">Cannot delete while updating</span>
                </div>
                <div
                  v-if="
                    (isTagPage || isCategoryPage || isCategoryGroupPage) &&
                    tableData &&
                    tableData.state !== 'publishing' &&
                    tableData.totalRecipes > 0
                  "
                  class="tooltip-wrapper"
                >
                  <img
                    alt=""
                    class="tooltip-image"
                    src="@/assets/images/info.svg?skipsvgo=true"
                  />
                  <span v-if="isTagPage" class="tooltip-text"
                    >Tags associated to recipes cannot be deleted. Please remove
                    all recipes to delete the tag.</span
                  >
                  <span v-if="isCategoryPage" class="tooltip-text"
                    >Categories associated to recipes cannot be deleted. Please
                    remove all recipes to delete the categories.</span
                  >
                  <span v-if="isCategoryGroupPage" class="tooltip-text"
                    >Category Group associated to Categories cannot be deleted.
                    Please remove all Categories to delete the Category
                    Group.</span
                  >
                </div>
                <img
                  alt=""
                  src="@/assets/images/delete-icon.png"
                  class="delete-icon"
                  :class="{
                    'disabled-image':
                      ((tableData && tableData.state === 'publishing') || (tableData && tableData.totalRecipes !== 0) && !isOrganizationPage),
                  }"
                />
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<script>
/**
 * @deprecated We should use SimpleTable component
 */

import VLazyImage from 'v-lazy-image'
export default {
  name: "MasterTable",
  components: {
    VLazyImage,
  },
  data() {
    return {
      masterTableHeadData: [],
      masterTableData: [],
    };
  },
  props: {
    defaultImage: {
      type: String,
      default: "",
    },
    masterList: {
      type: Array,
      default: () => [],
    },
    isOrganizationPage: {
      type: Boolean,
      default: false,
    },
    isTagPage: {
      type: Boolean,
      default: false,
    },
    isCategoryGroupPage: {
      type: Boolean,
      default: false,
    },
    isCategoryPage: {
      type: Boolean,
      default: false,
    },
    isIngredientPage: {
      type: Boolean,
      default: false,
    },
    lang: {
      type: String,
      default: "en-US",
    },
    isAdminCheck: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    masterList: {
      immediate: true,
      handler(newList) {
        this.rearrangeList(newList);
      },
    },
  },
  methods: {
    rearrangeList(list) {
      if (this.isOrganizationPage) {
        this.rearrangeOrganizationList(list);
      }
      if (this.isTagPage) {
        this.rearrangeTagList(list);
      }
      if (this.isCategoryPage) {
        this.rearrangeCategoryList(list);
      }
      if (this.isCategoryGroupPage) {
        this.rearrangeCategoryGroupList(list);
      }
      if (this.isIngredientPage) {
        this.rearrangeIngredientList(list);
      }
    },
    rearrangeOrganizationList(list) {
      this.masterTableHeadData = [
        "",
        this.$t('ISIN'),
        this.$t('USERS.NAME'),
        this.$t('COMMON.STATUS'),
        "",
      ];
      this.masterTableData = list.map((item) => {
        return {
          image: item.image && item.image.url ? item.image.url : "",
          isin: item.isin,
          name: item.name,
          state: item.state,
        };
      });
    },
    rearrangeTagList(list) {
      this.masterTableHeadData = [
        this.$t('TAG.TAG_ISIN'),
        this.$t('TAG.TAG_TITLE'),
        this.$t('COMMON.RECIPE_COUNT'),
        this.$t('COMMON.STATUS'),
        "",
      ];
      this.masterTableData = list.map((item) => {
        return {
          isin: item.isin,
          name: item.name,
          totalRecipes: item.totalRecipes || 0,
          state: item.state,
          hasAlert: item.hasAlert || false,
          langs: item.langs || [],
        };
      });
    },
    rearrangeCategoryList(list) {
      this.masterTableHeadData = [
        "",
        this.$t('CATEGORY.CATEGORY_ISIN'),
        this.$t('CATEGORY.CATEGORY_TITLE'),
        this.$t('COMMON.RECIPE_COUNT'),
        this.$t('COMMON.STATUS'),
        "",
      ];
      this.masterTableData = list.map((item) => {
        return {
          image: item.image ?? "",
          isin: item.isin,
          name: item.name ?? "",
          totalRecipes: item.totalRecipes || 0,
          state: item.state ?? "",
          langs: item.langs ?? [],
          hasAlert: item.hasAlert ?? false,
        };
      });
    },
    rearrangeCategoryGroupList(list) {
      this.masterTableHeadData = [
        "",
        this.$t('CATEGORY_GROUP.GROUP_ISIN'),
        this.$t('CATEGORY_GROUP.GROUP_TITLE'),
        this.$t('CATEGORY_GROUP.CATEGORY_AND_RECIPE_COUNT'),
        this.$t('COMMON.STATUS'),
        "",
      ];
      this.masterTableData = list.map((item) => {
        return {
          image: item.data?.[this.lang]?.image || "",
          isin: item.isin,
          name: item.data?.[this.lang]?.name || "",
          totalCategories: item.data?.[this.lang]?.categories?.length || 0,
          totalRecipes: item.totalRecipes || 0,
          state: item.state,
          categoryName: item.mainName || "",
          langs: item.langs ?? [],
          hasAlert: item.hasAlert ?? false,
          status: item.status,
        };
      });
    },
    rearrangeIngredientList(list) {
      this.masterTableHeadData = [
        this.$t('INGREDIENT.INGREDIENT_NAME'),
        this.$t('INGREDIENT.INGREDIENT_PRODUCTS'),
        "",
      ];
      this.masterTableData = list.map((item) => {
        return {
          name: item.names[0] || "",
          promotedCount: item.totalPromotedProducts || 0,
          productCount: item.productCount || 0,
          isShoppable: item.shoppable || false,
          isOnlyIncluded: item.onlyIncluded || false,
          isPromoted: item.onlyPromoted || false,
        };
      });
    },
    isLanguageAvailable(tableData) {
      const language = tableData?.langs?.[1];
      return language && language !== this.$keys.LANGUAGE.FRENCH;
    },
    displayTooltipLanguage(item, index, languageLength) {
      let arr = [];
      arr = item.split("-");
      if (item != this.lang) {
        if (index < languageLength) return arr[0].toUpperCase() + ",";
        else return arr[0].toUpperCase() + ".";
      }
    },
    handleEdit(tableData) {
      if (tableData.state !== "publishing" || this.isIngredientPage) {
        this.$emit("handleEdit", tableData);
      }
    },
    handleDelete(tableData) {
      if (
        tableData.state !== "publishing" &&
        (tableData.totalRecipes === 0 || this.isOrganizationPage)
      ) {
        this.$emit("handleDelete", tableData);
      }
    },
  },
};
</script>
