<template>
    <Modal @close="closeModal">
        <template #saveModal>
            <div class="save-recipe-variant-modal">
                <div class="save-info-popup-container">
                    <div class="save-image">
                        <div class="save-image-container">
                            <img alt="unpublished" class="save-icon" src="@/assets/images/unpublished.png" />
                        </div>
                    </div>
                </div>
                <div class="publish-content">
                    <div class="publish-head"> {{ description }} </div>
                    <div v-if="showUnpublishingText &&
                        recipeData &&
                        recipeData.status &&
                        recipeData.status === 'active'
                        " class="note-message">
                        Unpublishing a recipe will hide it from all associated
                        categories and tags
                    </div>
                    <div v-show="slugCheckConfirm" class="slug-warnings">
                        Checking Slug. Please wait.
                    </div>
                    <div v-show="hasSlugExist && !slugCheckConfirm" class="slug-warnings">
                        Warning: this slug already exists. Continue saving to
                        automatically append a number to the slug. Cancel to edit the
                        slug.
                    </div>
                    <div v-if="availableLang.length > 1" class="note-message">
                        All changes to this recipe as well as saved changes to its
                        variants will be saved.
                    </div>
                    <div class="button-container">
                        <button type="button" class="btn-green-outline" @click="closeModal">{{
                            $t('BUTTONS.CANCEL_BUTTON') }}</button>
                        <button type="button" :class="!slugCheckConfirm
                            ? 'btn-green'
                            : 'disabled-button btn-green'
                            " @click="saveAndPublishFunction">
                            <span> {{ buttonName }} </span>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import Modal from "@/components/Modal";

defineProps({
  saveAndPublishFunction: {
    type: Function,
  },
  closeModal: {
    type: Function,
  },
  slugCheckConfirm: {
    type: Boolean,
    default: false,
  },
  hasSlugExist: {
    type: Boolean,
    default: false,
  },
  availableLang: {
    type: Array,
    default: [],
  },
  showUnpublishingText: {
    type: Boolean,
    default: false,
  },
  recipeData: {
    type: Object,
  },
  description: {
    type: String,
    default: "",
  },
  buttonName: {
    type: String,
    default: "",
  },
  imageName: {
    type: String,
    default: "",
  },
});
</script>
