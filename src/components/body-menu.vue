<template>
  <div
    ref="bodyMenuElementRef"
    class="body-menu"
    :class="{
      '__open': isOpen,
    }"
  >
    <button
      type="button"
      class="btn-reset body-menu-toggle"
      @click="toggleMenu()"
    >
      <img v-show="isOpen" alt="grin three dot icon" src="@/assets/images/green-edit-btn.svg?skipsvgo=true" />
      <img v-show="!isOpen" alt="three dot icon" src="@/assets/images/edit-btn.svg?skipsvgo=true" />
    </button>
    <div class="body-menu-actions" :class="{ 'body-menu-actions-full-text': props.isFullText }" :style="actionsStyle">
      <ul>
        <template v-for="item in props.actions">
          <li
            v-if="!item.isDisable"
            class="body-menu-item"
            :class="[item.tooltipPosition, {
              'simple-data-tooltip': item.isInactive,
            }]"
            :data-tooltip-text="item.isInactive && item.tooltip"
          >
            <button
              type="button"
              class="btn-reset"
              @click="callAction(item.key, item)"
              :disabled="item.isInactive"
            >
              {{ item.label }}
            </button>
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { useNuxtApp } from "nuxt/app";
import { ref } from "vue";

const props = defineProps({

  // [{
  //  isDisable: boolean,
  //  isInactive: boolean,
  //  key: string | boolean | number | array | object,
  //  label: string,
  //  tooltip: string,
  //  tooltipPosition: string,
  // }]
  actions: {
    type: Array,
    required: true
  },
  isFullText: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const { $keys } = useNuxtApp();

const bodyMenuElementRef = ref();
const isOpen = ref(false);
const actionsStyle = ref({});

const emit = defineEmits(["callActions"])

const callAction = (key) => {
  emit("callActions", key);
  toggleMenu();
};

const toggleMenu = () => {
  isOpen.value = !isOpen.value;
  calculateActionsPositions();
};

const handleClickOutside = (event) => {
  if (isOpen.value) {
    const el = bodyMenuElementRef.value;
    if (el && !el.contains(event.target)) {
      toggleMenu();
    }
  }
};

const calculateActionsPositions = (isInnit = false) => {
  try {
    if (isOpen.value || isInnit === true) {
      const el = bodyMenuElementRef.value;

      if (!el) {
        return;
      }

      const { top, left } = el.getBoundingClientRect();
      const elHeightGap = el.offsetHeight + 11;

      const actionsEl = el.querySelector(".body-menu-actions");

      const isEnoughHeight = window.innerHeight > (top + actionsEl.offsetHeight + elHeightGap);
      const topPosition = isEnoughHeight ? `${top + elHeightGap}px` : `${top - (11 + actionsEl.offsetHeight)}px`;
      const leftPosition = `${left - ((props.isFullText ? 175 : 152) - el.offsetWidth)}px`;

      actionsStyle.value = {
        top: topPosition,
        left: leftPosition,
      };
    }
  } catch (error) {
    console.warn(error);
  }
};

onMounted(() => {
  calculateActionsPositions(true);
  document.addEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
  document.addEventListener($keys.KEY_NAMES.SCROLL, calculateActionsPositions);
});
onUnmounted(() => {
  document.removeEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
  document.addEventListener($keys.KEY_NAMES.SCROLL, calculateActionsPositions);
});

</script>
