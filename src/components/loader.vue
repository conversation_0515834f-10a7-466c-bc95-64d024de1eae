<template>
  <div class="ingredient-loader-section">
    <div class="content">
      <div class="loading">
        <div class="input-loading">
          <div class="loader-image"></div>
        </div>
        <div :class="props.isExportRecipe || props.isImportRecipe ? 'loading-text-export-recipe' : 'loading-text'">
          <p>{{ $t('LOADER.LOADING') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  isExportRecipe: {
    type: Boolean,
    default: false,
  },
  isImportRecipe: {
    type: Boolean,
    default: false,
  },
});
</script>
