<template>
  <div
    class="calendar-picker-wrapper"
    :class="{
      'range-mode': isRange,
      'recipe-details-range-calendar': isInRecipeDetails,
      'simple-data-tooltip': showTooltip,
    }"
    :data-tooltip-text="showTooltip && noFreeDatesTooltip"
    @click="handleCalendarClick"
  >
    <Datepicker
      ref="calendarRef"
      :class="isRange ? 'custom-datepicker-range' : ''"
      v-model="internalValue"
      :markers="markers"
      :range="isRange ? { partialRange: false, noDisabledRange: true } : false"
      :start-date="
        bannerIsLive || hasRecipeScheduled || hasStartDatePassed
          ? parsedStartDate
          : null
      "
      :highlight="highlightedDates"
      :disabled-dates="computedDisabledDates"
      :min-date="
        bannerIsLive || hasRecipeScheduled || hasStartDatePassed
          ? parsedStartDate
          : minDate
      "
      :enable-time-picker="false"
      :clearable="false"
      auto-apply
      :format="formatDateRangeOrSingle"
      :day-names="['M', 'T', 'W', 'T', 'F', 'S', 'S']"
      :month-change-on-scroll="false"
      :disabled="
        isLiveHeroReplaced ||
        isHeroLive ||
        hasPublishedDateTimeout ||
        hasUnpublishedDateTimeout ||
        isInRecipePopup ||
        disableFutureDatesValue
      "
      @update:model-value="handleDateChange"
    >
      <template #input-icon>
        <div v-if="isRange && isInBanner" class="date-selection-container">
          <div
            class="select-date-text"
            :class="{ 'banner-live': bannerIsLive }"
          >
            <span
              v-if="isCreateDuplicate && !internalValue[0] && !startDate"
              class="banner-start-date-text"
            >
              Start
            </span>
            <span
              v-else-if="bannerIsLive && !isCreateDuplicate"
              class="banner-start-date-text"
            >
              Live
            </span>
            <span v-else>
              {{
                internalValue[0]
                  ? formatDateRangeOrSingle(internalValue[0])
                  : startDate || "Start"
              }}
            </span>
          </div>
          <div class="vertical-line"></div>
          <div class="select-date-text">
            <span
              v-if="!internalValue[1] && !endDate"
              class="banner-end-date-text"
              >End</span
            >
            <span class="selected-end-date">{{
              internalValue[1]
                ? formatDateRangeOrSingle(internalValue[1])
                : endDate
            }}</span>
          </div>
        </div>
        <div v-if="isRange && isInRecipe" class="date-selection-container">
          <div class="selected-dates-container">
            <div class="date-select-text">Start:</div>
            <span
              v-if="!internalValue[0] && !startDate"
              class="enter-date-text"
            >
              Enter date<img
                alt="compulsory"
                class="asterisk-input"
                src="~/assets/images/asterisk.svg?skipsvgo=true"
              />
            </span>
            <span class="selected-dates">{{
              internalValue[0]
                ? formatDateRangeOrSingle(internalValue[0])
                : startDate
            }}</span>
          </div>
          <div class="vertical-line"></div>
          <div class="selected-dates-container" @click="selectEndDateAsync">
            <div class="date-select-text">End:</div>
            <span v-if="!internalValue[1] && !endDate" class="enter-date-text"
              >Enter date<img
                alt="compulsory"
                class="asterisk-input"
                src="~/assets/images/asterisk.svg?skipsvgo=true"
            /></span>
            <span class="selected-dates">{{
              internalValue[1]
                ? formatDateRangeOrSingle(internalValue[1])
                : endDate
            }}</span>
          </div>
        </div>
        <div
          v-if="!isRange && !isInRecipe && !isInBanner && !isInRecipeDetails"
        >
          <span
            v-if="
              (!internalValue ||
                (Array.isArray(internalValue) && internalValue.length === 0)) &&
              !isLiveHeroReplaced &&
              !isHeroLive
            "
            class="select-date-text"
          >
            {{ $t("DYNAMIC_HERO.SELECT_DATE") }}
          </span>

          <span
            v-else-if="isHeroLive || isLiveHeroReplaced"
            class="select-date-text inactive-button"
          >
            {{ $t("DYNAMIC_HERO.TODAY") }}
          </span>

          <span v-else class="select-date-text">
            {{
              !!formatDateRangeOrSingle(internalValue)
                ? formatDateRangeOrSingle(internalValue)
                : internalValue
            }}
          </span>
        </div>
        <img
          v-if="!isInRecipeDetails"
          alt="calendar"
          class="input-slot-image"
          src="@/assets/images/calendar-icon.png"
        />
        <div
          v-if="isRange && isInRecipeDetails"
          class="recipe-details-date-selection-container"
        >
          <div
            class="selected-dates-container"
            :class="{
              'inactive-button': hasRecipeScheduled || hasStartDatePassed,
            }"
          >
            <div class="date-value-select">
              <div class="date-select-text">Start:</div>
              <span
                v-if="!internalValue[0] && !startDate"
                class="enter-date-text"
              >
                Enter date
                <img
                  alt="compulsory"
                  class="asterisk-input"
                  src="~/assets/images/asterisk.svg?skipsvgo=true"
                />
              </span>
              <span class="selected-dates">
                {{
                  internalValue[0]
                    ? formatDateRangeOrSingle(internalValue[0])
                    : startDate || ""
                }}
              </span>
            </div>
            <span
              :class="{
                'calendar-icon': !hasPublishedDateTimeout,
                'publish-toggle-section-start': hasPublishedDateTimeout,
              }"
            >
              <img
                alt="calendar-icon"
                :class="{
                  'calendar-icon': !hasPublishedDateTimeout,
                  'calendar-icon-start': hasPublishedDateTimeout,
                }"
                :src="!hasPublishedDateTimeout ? calendarIcon : redInfo"
              />
              <span
                v-if="hasPublishedDateTimeout"
                class="publish-hover-text-start"
              >
                <p class="tool-tip-text">
                  {{ getErrorMessage(false) }}
                </p>
                <br />
                <button type="button" @click="viewMoreDetail()">
                  {{ $t("COMMON.VIEW_MORE") }}
                </button>
              </span>
            </span>
          </div>
          <div class="selected-dates-container" @click="selectEndDateAsync">
            <div class="date-value-select">
              <div class="date-select-text">End:</div>
              <span
                v-if="!internalValue[1] && !endDate && !isSelectingEndDateOnly"
                class="enter-date-text"
              >
                Enter date
                <img
                  alt="compulsory"
                  class="asterisk-input"
                  src="~/assets/images/asterisk.svg?skipsvgo=true"
                />
              </span>
              <span class="selected-dates">
                {{
                  internalValue[1]
                    ? formatDateRangeOrSingle(internalValue[1])
                    : (props.isInRecipeDetails && isSelectingEndDateOnly && props.endDate)
                      ? props.endDate
                      : endDate || ""
                }}
              </span>
            </div>
            <span
              :class="{
                'calendar-icon': !hasUnpublishedDateTimeout,
                'publish-toggle-section-start': hasUnpublishedDateTimeout,
              }"
            >
              <img
                alt="calendar-icon"
                :class="{
                  'calendar-icon': !hasUnpublishedDateTimeout,
                  'calendar-icon-start': hasUnpublishedDateTimeout,
                }"
                :src="!hasUnpublishedDateTimeout ? calendarIcon : redInfo"
              />
              <span
                v-if="hasUnpublishedDateTimeout"
                class="publish-hover-text-start"
              >
                <p class="tool-tip-text">
                  {{ getErrorMessage(false) }}
                </p>
                <br />
                <button type="button" @click="viewMoreDetail()">
                  {{ $t("COMMON.VIEW_MORE") }}
                </button>
              </span>
            </span>
          </div>
        </div>
      </template>
    </Datepicker>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted } from "vue";
import calendarIcon from "@/assets/images/calendar-icon.png";
import redInfo from "@/assets/images/red-info.svg?skipsvgo=true";

const props = defineProps({
  isInBanner: {
    type: Boolean,
    default: false,
  },
  isInRecipe: {
    type: Boolean,
    default: false,
  },
  isInRecipeDetails: {
    type: Boolean,
    default: false,
  },
  hasPublishedDateTimeout: {
    type: Boolean,
    default: false,
  },
  hasUnpublishedDateTimeout: {
    type: Boolean,
    default: false,
  },
  isInRecipePopup: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: [Date, Array, String, Object],
    default: null,
  },
  isRange: {
    type: Boolean,
    default: false,
  },
  markers: {
    type: Array,
    default: () => [],
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
  minDate: {
    type: Date,
    default: () => new Date(),
  },
  startDate: {
    type: [Date, Array, String, Object],
  },
  endDate: {
    type: [Date, Array, String, Object],
  },
  disableFutureDates: {
    type: [Date, String, Boolean],
    default: null,
  },
  isReplaceLiveHero: {
    type: Boolean,
    default: false,
  },
  bannerIsLive: {
    type: Boolean,
    default: false,
  },
  isCreateDuplicate: {
    type: Boolean,
    default: false,
  },
  isHeroLive: {
    type: Boolean,
    default: false,
  },
  isLiveHeroReplaced: {
    type: Boolean,
    default: false,
  },
  getErrorMessage: {
    type: Function,
  },
  viewMoreDetail: {
    type: Function,
  },
});

const model = defineModel("modelValue");

const emit = defineEmits(["update:modelValue"]);

const { formatDateRangeOrSingle } = useTimeUtils();
const { delay } = useDelayTimer();

const startDate = ref(props.startDate);
const endDate = ref(props.endDate);
const calendarRef = ref(null);
const internalValue = ref(model.value || []);
const disableFutureDatesValue = ref(false);
const isSelectingEndDateOnly = ref(false);
const noFreeDatesTooltip = ref(
  "There are no free dates to edit. You should change the dates in the next scheduled banner first."
);

const hasRecipeScheduled = computed(() => {
  const recipeStartDate =
    props.startDate === formatDateRangeOrSingle(new Date());
  return recipeStartDate;
});
const parsedStartDate = computed(() => {
  const parsed = props.startDate ? new Date(props.startDate) : new Date();
  return isNaN(parsed) ? new Date() : parsed;
});

const highlightedDates = computed(() => {
  return props.bannerIsLive && !props.isCreateDuplicate && parsedStartDate.value
    ? [parsedStartDate.value]
    : [];
});
const hasStartDatePassed = computed(() => {
  if (!props.isInRecipeDetails) return;
  const start = parsedStartDate.value;
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return start && new Date(start) <= today;
});

const showTooltip = computed(() => {
  return disableFutureDatesValue.value === true;
});

const computedDisabledDates = computed(() => {
  if (hasStartDatePassed.value) return;
  const baseDisabled = props.disabledDates || [];

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const shouldDisableToday =
    props.isRange && (props.isInRecipeDetails || props.isInRecipe);

  if (props.bannerIsLive && !props.isCreateDuplicate && parsedStartDate.value) {
    return (date) => {
      const d = new Date(date);
      d.setHours(0, 0, 0, 0);

      const isBeforeStart = d < parsedStartDate.value;

      const isInDisabledArray = baseDisabled.some(
        (disabledDate) =>
          disabledDate instanceof Date &&
          disabledDate.getFullYear() === d.getFullYear() &&
          disabledDate.getMonth() === d.getMonth() &&
          disabledDate.getDate() === d.getDate()
      );

      const isToday = d.getTime() === today.getTime();

      return (
        isBeforeStart || isInDisabledArray || (shouldDisableToday && isToday)
      );
    };
  }
  if (shouldDisableToday) {
    return [...baseDisabled, today];
  }
  return baseDisabled;
});

onMounted(() => {
  disableFutureDatesValue.value = Boolean(props.disableFutureDates);
  const hasValidModelValue =
    props.modelValue &&
    ((Array.isArray(props.modelValue) && props.modelValue.length > 0) ||
      (typeof props.modelValue === "object" &&
        (props.modelValue.start || props.modelValue.end)));

  const hasValidStartDate = !!props.startDate;
  const hasValidEndDate = !!props.endDate;

  if (!hasValidModelValue && !hasValidStartDate && !hasValidEndDate && props.isInBanner) {
    internalValue.value = [];
    return;
  }

  const val = internalValue.value;
  if (!hasStartDatePassed.value && props.isInRecipeDetails && val?.end) {
    internalValue.value = [val.start, val.end];
  }
});

watch(
  () => internalValue.value,
  (newVal) => {
    if (
      (props.isInRecipe || props.isInRecipeDetails) &&
      Array.isArray(newVal) &&
      newVal.length === 2
    ) {
      const start = new Date(newVal[0]).setHours(0, 0, 0, 0);
      const end = new Date(newVal[1]).setHours(0, 0, 0, 0);

      if (start === end) {
        internalValue.value = [newVal[0]];
        nextTick(() => {
          calendarRef.value?.openMenu?.();
        });
      }
    }
  },
  { immediate: false, deep: true }
);

watch(
  () => model.value,
  (val) => {
    if (
      !val ||
      (Array.isArray(val) && val.length === 0) ||
      (typeof val === "object" && !val.start && !val.end)
    ) {
      const internalValueIsEmpty =
        !internalValue.value ||
        (Array.isArray(internalValue.value) &&
          internalValue.value.length === 0) ||
        (typeof internalValue.value === "object" &&
          !internalValue.value.start &&
          !internalValue.value.end);

      if (!internalValueIsEmpty) {
        internalValue.value = [];
      }
      return;
    }

    if (
      (props.bannerIsLive && !props.isCreateDuplicate) ||
      hasStartDatePassed.value
    ) {
      const endDate = hasStartDatePassed.value ? val?.[1] || null : null;
      const newValue = [parsedStartDate.value, endDate];
      const currentValue = internalValue.value;

      const isSame =
        currentValue?.[0]?.toString() === newValue[0]?.toString() &&
        currentValue?.[1]?.toString() === newValue[1]?.toString();

      if (!isSame) {
        internalValue.value = newValue;
      }
    } else if (JSON.stringify(internalValue.value) !== JSON.stringify(val)) {
      internalValue.value = val || [];
    }
  },
  { immediate: true }
);

watch(internalValue, (val) => {
  if (
    !val ||
    (Array.isArray(val) && val.length === 0) ||
    (typeof val === "object" && !val.start && !val.end)
  ) {
    const modelIsEmpty =
      !model.value ||
      (Array.isArray(model.value) && model.value.length === 0) ||
      (typeof model.value === "object" &&
        !model.value.start &&
        !model.value.end);

    if (!modelIsEmpty) {
      emit("update:modelValue", []);
    }
    return;
  }

  let newValue;
  if (
    (props.bannerIsLive && !props.isCreateDuplicate) ||
    hasStartDatePassed.value
  ) {
    const endDate = hasStartDatePassed.value ? val?.[1] || null : null;
    newValue = [parsedStartDate.value, endDate];

    const modelStart = model.value?.[0]?.toString();
    const modelEnd = model.value?.[1]?.toString();
    const newStart = newValue[0]?.toString();
    const newEnd = newValue[1]?.toString();

    if (modelStart !== newStart || modelEnd !== newEnd) {
      emit("update:modelValue", newValue);
    }
    return;
  }
  if (
    val?.toString() !== model.value?.toString() &&
    JSON.stringify(val) !== JSON.stringify(model.value)
  ) {
    emit("update:modelValue", val);
  }
});
const isValueEmpty = (value) => {
  return (
    !value ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === "object" && !value.start && !value.end)
  );
};

const resetDates = () => {
  if (startDate.value !== null) startDate.value = null;
  if (endDate.value !== null) endDate.value = null;
};

const updateDatesFromArray = (dateArray) => {
  const newStartDate = dateArray[0] ? formatDateRangeOrSingle(dateArray[0]) : null;
  const newEndDate = dateArray[1] ? formatDateRangeOrSingle(dateArray[1]) : null;

  if (startDate.value !== newStartDate) startDate.value = newStartDate;
  if (endDate.value !== newEndDate) endDate.value = newEndDate;
};

const updateDatesFromObject = (dateObject) => {
  const newStartDate = dateObject.start ? formatDateRangeOrSingle(dateObject.start) : null;
  const newEndDate = dateObject.end ? formatDateRangeOrSingle(dateObject.end) : null;

  if (startDate.value !== newStartDate) startDate.value = newStartDate;
  if (endDate.value !== newEndDate) endDate.value = newEndDate;
};

const syncModelIfChanged = (newVal) => {
  if (JSON.stringify(newVal) !== JSON.stringify(model.value)) {
    emit("update:modelValue", newVal);
  }
};

watch(internalValue, (newVal) => {
  if (isValueEmpty(newVal)) {
    resetDates();

    if (!isValueEmpty(model.value)) {
      emit("update:modelValue", []);
    }
    return;
  }

  if (Array.isArray(newVal)) {
    updateDatesFromArray(newVal);
  }
  else if (newVal?.start && newVal?.end) {
    updateDatesFromObject(newVal);
  }
  else {
    resetDates();
  }
  syncModelIfChanged(newVal);
});

watch(
  () => props.disableFutureDates,
  (newVal) => {
    disableFutureDatesValue.value = Boolean(newVal);
  },
  { immediate: true, deep: true }
);

const selectEndDateAsync = async () => {
  if (props.isInRecipePopup || disableFutureDatesValue.value) return;

  const value = internalValue.value;
  let startDateLocal = null;
  if (Array.isArray(value) && value.length === 2 && value[0]) {
    startDateLocal = value[0];
  } else if (value?.start) {
    startDateLocal = value.start;
  }
  if (startDateLocal) {
    await delay(50);
    if (calendarRef.value?.openMenu) {
      if (props.isInRecipeDetails) {
        isSelectingEndDateOnly.value = true;
      }
      calendarRef.value.openMenu();
      nextTick(() => {
        internalValue.value = [startDateLocal];
      });
    }
  }
};

const handleCalendarClick = (event) => {
  if (disableFutureDatesValue.value) {
    event.preventDefault();
    event.stopPropagation();
  }
};

const handleDateChange = (newValue) => {
  if (props.isInRecipeDetails && isSelectingEndDateOnly.value && Array.isArray(newValue) && newValue.length === 2) {
    isSelectingEndDateOnly.value = false;
    const originalValue = internalValue.value;
    const originalStartDate = Array.isArray(originalValue) && originalValue[0] ? originalValue[0] : null;
    if (originalStartDate) {
      const updatedValue = [originalStartDate, newValue[1]];
      internalValue.value = updatedValue;
      emit("update:modelValue", updatedValue);
      return;
    }
  }
  if (isSelectingEndDateOnly.value) {
    isSelectingEndDateOnly.value = false;
  }
  internalValue.value = newValue;
  emit("update:modelValue", newValue);
};
</script>
