<template>
  <div class="result-not-found">
    <p class="text-title-2 font-bold">{{ title || $t('COMMON.NO_RESULTS_FOUND') }}.</p>
    <p v-if="description" class="text-title-2 font-bold fade-in-delayed-50ms">{{ description }}</p>
  </div>
</template>
<script setup>
defineProps({
  title: {
    type: String,
    required: false,
    default: "",
  },
  description: {
    type: String,
    required: false,
    default: "",
  },
});
</script>
