<template>
  <div class="url-field">
    <div class="cta-url-section">
      <div class="input-section">
        <input
          class="form-control url-input"
          ref="urlInputRef"
          v-model="urlValue"
          autocomplete="off"
          @input="validateUrl"
          :placeholder="placeholder"
        />
      </div>
      <div class="cta-link-input-verify-section">
        <div
          v-if="isValidating"
          class="cta-link-progress-check"
        >
          <div class="loader-image"></div>
        </div>
        <div
          class="cta-link-correct-check"
        >
          <img
            v-if="!isValidating && isValid"
            class="correct-icon"
            alt=""
            src="@/assets/images/tick-icon.png"
          />
        </div>
        <div
          class="cta-link-wrong-check"
        >
          <img
            v-if="!isValidating && !isValid && isBroken"
            class="wrong-icon"
            alt=""
            src="@/assets/images/red-info.svg?skipsvgo=true"
          />
        </div>
      </div>
    </div>

    <div
      v-if="!isValid && isBroken"
      class="cta-broken-link-validation-section"
    >
      <span class="cta-link-broken-message font-size-14 font-normal">{{ errorMessage }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Enter link'
  },
  errorMessage: {
    type: String,
    default: 'This link is broken.'
  },
  validationDelay: {
    type: Number,
    default: 1000
  }
});

const emit = defineEmits(['update:modelValue', 'update:validation', 'input']);

const urlValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isValidating = ref(false);
const isValid = ref(false);
const isBroken = ref(false);

const validateUrl = () => {
  isValidating.value = true;
  isValid.value = false;
  isBroken.value = false;

  emit('input');
  emit('update:validation', { isValidating: true, isValid: false, isBroken: false });

  if (urlValue.value.trim() === '') {
    isValidating.value = false;
    emit('update:validation', { isValidating: false, isValid: false, isBroken: false });
    return;
  }

  setTimeout(() => {
    const isValidUrl = /^(http|https):\/\/[^ "]+$/.test(urlValue.value);
    isValid.value = isValidUrl;
    isBroken.value = !isValidUrl && urlValue.value.trim() !== '';
    isValidating.value = false;

    emit('update:validation', {
      isValidating: false,
      isValid: isValid.value,
      isBroken: isBroken.value
    });
  }, props.validationDelay);
};

watch(() => props.modelValue, (newValue) => {
  if (newValue !== '') {
    validateUrl();
  }
}, { immediate: true });
</script>
