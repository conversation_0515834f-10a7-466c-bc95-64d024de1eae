<template>
    <div>
        <Modal @close="props.closeModal()">
            <template #recipeVariant>
                <div class="select-the-language-modal">
                    <div class="select-the-language-text">
                        <span>Select the language</span>
                        <img alt="" src="@/assets/images/exit-gray.png" @click="props.closeModal()" />
                    </div>
                    <div class="select-the-language-group-dropdown">
                        <div class="select-the-language-group-variant-selected-language" :class="props.recipeVariantLanguageList.length > 1
                            ? 'selected-language-disable-cursor'
                            : ''
                            " @click="showRecipeVariantLanguageMatches()">
                            <span>
                              <img :src="props.recipeVariantLanguageList?.[0]?.languageFlag || props.recipeVariantLanguageList?.[0]?.language_flag || ''" alt="flag">
                                <p>
                                    {{
                                    props.recipeVariantLanguageList &&
                                    props.recipeVariantLanguageList[0] &&
                                    props.recipeVariantLanguageList[0].language &&
                                    props.recipeVariantLanguageList[0].language_name
                                        ? props.recipeVariantLanguageList[0].language_name
                                        : ""
                                    }}
                                </p>
                            </span>
                            <img alt="" v-if="props.recipeVariantLanguageList.length > 1" src="@/assets/images/arrow-right.png"
                                class="select-the-language-group-dropdown-icon" :class="{
                                    rotate: props.hasRecipeVariantLanguageResult,
                                }" />
                        </div>
                        <ul v-if="props.hasRecipeVariantLanguageResult &&
                            props.recipeVariantLanguageList.length > 1
                            " class="select-the-language-group-autocomplete-results">
                            <li v-for="(result, index) in props.recipeVariantLanguageList" :key="result.language"
                                class="select-the-language-group-language-list" :class="{
                                    'select-the-language-group-autocomplete-result': true,
                                    'is-active': '',
                                }" @click="setRecipeVariantLanguageMatches(result, index)">
                                <span>
                                    <img alt="" :src="result && result.languageFlag
                                        ? result.languageFlag
                                        : ''
                                        " />
                                    <p>
                                        {{
                                            result && result.language_name
                                            ? result.language_name
                                            : ""
                                        }}
                                    </p>
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="select-the-language-group-variant-next-button">
                        <button type="button" @click="
                            props.hasRecipeVariantLanguageResult
                                ? ''
                                : nextVariantPopUp(selectedLanguage)
                            " class="next-button" @keydown="preventEnterAndSpaceKeyPress($event)">
                            <p>NEXT</p>
                        </button>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>
<script setup>
import Modal from "@/components/Modal";
import { ref } from 'vue';
import { useCommonUtils } from "../composables/useCommonUtils.js";

const props = defineProps({
  closeModal: {
    type: Function,
  },
  recipeVariantLanguageList: {
    type: Array,
  },
  hasRecipeVariantLanguageResult: {
    type: Boolean,
  },
});

const emit = defineEmits(["nextVariantPopUp", "showRecipeVariantLanguageMatches", "setRecipeVariantLanguageMatches"]);

const { triggerLoading } = useCommonUtils();

const selectedLanguage = ref("");

const preventEnterAndSpaceKeyPress = (event) => {
  triggerLoading('preventEnterAndSpaceKeyPress', event);
};
const nextVariantPopUp = (lang) => {
  emit('nextVariantPopUp', lang);
};
const showRecipeVariantLanguageMatches = () => {
  if (props.recipeVariantLanguageList.length > 1) {
    emit('showRecipeVariantLanguageMatches')
  }
};
const setRecipeVariantLanguageMatches = (result, index) => {
  selectedLanguage.value = result.language;
  emit('setRecipeVariantLanguageMatches', result, index)
};
</script>
