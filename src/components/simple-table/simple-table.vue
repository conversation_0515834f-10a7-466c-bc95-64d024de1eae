<template>
  <div class="simple-table-wrapper">
    <table
      class="simple-table"
      :class="tableClass"
      data-test-id="simple-table"
    >
      <thead class="simple-table-head">
        <tr class="simple-table-head-row">
          <template v-for="(columnName, index) in columnNames" :key="`head-${index}`">
            <th
              scope="col"
              class="simple-table-head-column"
              :class="[
                prepareKeyToAttrName('simple-table-head-column', index, columnName),
                tableHeadColumnClass,
                {
                  'simple-table-head-column-sortable cursor-pointer': isSortable(columnKeys[index]),
                  'simple-table-head-column-sortable-active': isSortable(columnKeys[index]) && sortableColumnsState[columnKeys[index]]?.direction !== SORT_DIRECTIONS.NONE,
                }
              ]"
              :aria-sort="sortableColumnsState[columnKeys[index]]?.direction || 'none'"
              :data-test-id="prepareKeyToAttrName('simple-table-head-column', index, columnName)"
              @click="handleSortClick(columnKeys[index])"
            >
              {{ columnName }}
              <template v-if="isSortable(columnKeys[index])">
                <IconArrowDownDefault
                  class="simple-table-head-column-sortable-icon"
                  :class="{
                    'simple-table-head-column-sortable-icon-desc': sortableColumnsState[columnKeys[index]]?.direction === SORT_DIRECTIONS.DESC,
                    'simple-table-head-column-sortable-icon-asc': sortableColumnsState[columnKeys[index]]?.direction === SORT_DIRECTIONS.ASC,
                  }"
                />
              </template>
            </th>
          </template>
        </tr>
      </thead>
      <tbody class="simple-table-body">
        <template v-for="item in dataSource">
          <tr class="simple-table-row" @click="rowClickAction(item)">
            <template v-for="(columnKey, index) in columnKeys">
              <td
                class="simple-table-column"
                :class="[
                  prepareKeyToAttrName('simple-table-column', index, columnKey),
                  tableColumnClass
                ]"
                :data-column-key="columnKey"
                :data-test-id="prepareKeyToAttrName('simple-table-column', index, columnKey)"
              >
                <slot :name="columnKey" :data="item"></slot>
              </td>
            </template>
          </tr>
        </template>

        <template v-if="expiredDataSource?.length">
          <tr class="simple-table-row simple-table-row-expired-toggle">
            <td class="simple-table-column simple-table-column-expired-toggle" :colspan="columnNames?.length">
              <button
                type="button"
                class="btn-green-text btn-small"
                @click="toggleExpiredRowsBlock"
              >
                {{ !isExpiredRowsOpened ? expiredRowsBtnOpenLabel : expiredRowsBtnHideLabel }}
              </button>
            </td>
          </tr>

          <template v-if="isExpiredRowsOpened">
            <template v-for="item in expiredDataSource">
              <tr class="simple-table-row simple-table-row-expired" @click="rowClickAction(item)">
                <template v-for="(columnKey, index) in columnKeys">
                  <td
                    class="simple-table-column simple-table-column-expired"
                    :class="[
                      prepareKeyToAttrName('simple-table-column-expired', index, columnKey),
                      tableColumnClass
                    ]"
                    :data-column-key="columnKey"
                    :data-test-id="prepareKeyToAttrName('simple-table-column-expired', index, columnKey)"
                  >
                    <slot :name="columnKey" :data="item"></slot>
                  </td>
                </template>
              </tr>
            </template>
          </template>
        </template>
      </tbody>
    </table>

  </div>
</template>

<script setup>
import IconArrowDownDefault from '~/assets/images/icons/arrow-down-default.svg';

const SORT_DIRECTIONS = {
  ASC: "asc",
  DESC: "desc",
  NONE: "",
};

const props = defineProps({
  /**
   * Column display names shown in <th>
   *
   * @example ["Title", "Isin", ...]
   */
  columnNames: {
    type: Array,
    default: () => [],
    required: true,
  },

  /**
   * Column keys for slot names and identifying fields
   *
   * @example ["key1", "key2", ...]
   */
  columnKeys: {
    type: Array,
    default: () => [],
    required: true,
  },

  /**
   * Defines the current sorting state for each sortable column.
   *
   * Keys are column identifiers (e.g., "title", "isin").
   * Values are objects with a `direction` property that can be:
   * - `'asc'`: ascending sort
   * - `'desc'`: descending sort
   * - `''`: no sorting applied
   *
   * @property {Object} sortableColumns
   * @property {'asc' | 'desc' | ''} sortableColumns[<columnKey>].direction - Sort direction for the given column.
   *
   * @example { title: { direction: '' } }
   */
  sortableColumns: {
    type: Object,
    default: () => ({}),
    required: false,
  },

  /**
   * Array of data items rendered in <tbody>
   */
  dataSource: {
    type: Array,
    default: () => [],
    required: true,
  },
  tableClass: {
    type: String,
    default: "",
  },
  tableHeadColumnClass: {
    type: String,
    default: "",
  },
  tableColumnClass: {
    type: String,
    default: "",
  },

  /**
   * Array of expired data items rendered in <tbody>
   */
  expiredDataSource: {
    type: Array,
    default: () => [],
    required: false,
  },
  expiredRowsBtnOpenLabel: {
    type: String,
    default: "",
  },
  expiredRowsBtnHideLabel: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(['sortChange', 'rowClick']);

const isExpiredRowsOpened = ref(false);
const sortableColumnsState = reactive(props.sortableColumns);

const sortableColumnsIdList = computed(() => Object.keys(sortableColumnsState));

const prepareKeyToAttrName = (attr, index, key) => `${attr}-${key?.trim().replaceAll(' ', '-').toLowerCase() || index}`;
const isSortable = (key) => sortableColumnsIdList.value.includes(key);

const toggleExpiredRowsBlock = () => isExpiredRowsOpened.value = !isExpiredRowsOpened.value;

/**
 * Returns the next sort direction in the cycle
 * @param {'asc' | 'desc' | ''} current
 * @returns {'asc' | 'desc' | ''}
 */
const getNextSortDirection = (current) => {
  switch (current) {
    case SORT_DIRECTIONS.NONE:
      return SORT_DIRECTIONS.DESC
    case SORT_DIRECTIONS.DESC:
      return SORT_DIRECTIONS.ASC
    case SORT_DIRECTIONS.ASC:
    default:
      return SORT_DIRECTIONS.NONE
  }
};

/**
 * Handles header click to toggle sort direction logic: "" → "desc" → "asc" → ""
 *
 * @param {string} key
 */
const handleSortClick = (key) => {
  if (!isSortable(key)) return

  const current = sortableColumnsState[key]?.direction || SORT_DIRECTIONS.NONE
  const next = getNextSortDirection(current)

  // Reset all others
  for (const colKey of sortableColumnsIdList.value) {
    sortableColumnsState[colKey].direction = colKey === key ? next : SORT_DIRECTIONS.NONE
  }

  emit('sortChange', { key, direction: next })
};

const rowClickAction = (data) => {
  emit("rowClick", data);
};
</script>
