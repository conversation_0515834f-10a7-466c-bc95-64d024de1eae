<template>
  <div class="progress-loader-container">
    <div class="inner-container">
      <div class="circular-loader">
        <svg class="circular-progress" viewBox="0 0 36 36">
          <path
            class="circle-bg"
            d="M18 2.0845
              a 15.9155 15.9155 0 0 1 0 31.831
              a 15.9155 15.9155 0 0 1 0 -31.831"
          />
          <path
            class="circle"
            :style="{ strokeDasharray: `${uploadPercentage}, 100` }"
            d="M18 2.0845
              a 15.9155 15.9155 0 0 1 0 31.831
              a 15.9155 15.9155 0 0 1 0 -31.831"
          />
        </svg>
      </div>
      <div class="upload-loader-context">
        <div v-if="uploadPercentage < 99" class="uploading-text text-light-h4">
          {{ $t("RECIPE_UPLOAD_LOADER.UPLOAD_IN_PROGRESS") }}
        </div>
        <div v-else class="uploading-text text-light-h4">
          {{ $t("RECIPE_UPLOAD_LOADER.UPLOADED") }}
        </div>
        <div class="uploading-info">
          <span class="upload-media text-light-h6"
            >{{ (loadedSize / 1024000).toFixed(1) }}
            {{ $t("RECIPE_UPLOAD_LOADER.OF") }}
            {{ (uploadSize / 1024000).toFixed(1) }}
            {{ $t("RECIPE_UPLOAD_LOADER.MB") }}</span
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ImageUploadLoader",
  props: {
    uploadPercentage: {
      type: Number,
      default: 0,
    },
    loadedSize: {
      type: Number,
      default: 0,
    },
    uploadSize: {
      type: Number,
      default: 0,
    },
  },
};
</script>