<template>
  <button
    type="button"
    class="selector-button btn-reset color-white font-weight-extra-bold"
    :class="[
      classes,
      {
        'selector-button-absolute-center': isCenterByAbsolute,
      },
    ]"
    :style="styles"
    data-test-id="select-image"
    @click.stop="click(value)"
    :data-selected-value="value"
  >
  <span v-if="!isCircularButton" class="button-context">
    <span class="selector-button-container">
      <span></span>
      <span>{{ label }}</span>
    </span>
    <span class="selector-button-bg"></span>
  </span>
  <span v-else class="button-context">
    <span class="selector-button-circular">
      <span>
        <img src="@/assets/images/icons/check-select.svg?skipsvgo=true" alt="check">
      </span>
    </span>
  </span>
  </button>
</template>

<script>
export default {
  name: "selector-button",
  props: {
    styles: {
      type: Object,
      required: false,
    },
    label: {
      type: String,
      required: false,
    },
    classes: {
      type: String,
      required: false,
      default: "",
    },
    value: {
      type: String,
      required: true,
      default: "",
    },
    isCenterByAbsolute: {
      type: Boolean,
      required: false,
      default: false,
    },
    isCircularButton: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  methods: {
    click(val) {
      if (!val) {
        return;
      }
      this.$emit("onSelectValue", val);
    },
  },
};
</script>
