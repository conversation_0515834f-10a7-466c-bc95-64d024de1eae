<template>
  <div class="page-header-with-calendar">
    <div class="page-header-container">
      <div class="page-header-main-section">
        <div>
          <img :alt="iconAlt" class="page-header-icon" :src="iconSrc" />
        </div>
        <div class="font-size-20 font-bold">{{ title }}</div>
      </div>
      <div v-if="showCalendar" class="date-picker-container">
        <div class="font-size-base font-bold">{{ calendarLabel }}</div>
        <CalendarPicker
          v-model="localRange"
          :isRange="isRange"
          :isInBanner="isInBanner"
          :startDate="formattedStartDate"
          :endDate="formattedEndDate"
          :markers="markers"
          :disabled-dates="disabledDates"
          :bannerIsLive="bannerIsLive"
          :isCreateDuplicate="isCreateDuplicate"
          :disableFutureDates="disableFutureDates"
          @update:modelValue="handleDateChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import CalendarPicker from "@/components/calendar-picker.vue";
import { useTimeUtils } from "~/composables/useTimeUtils";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  iconSrc: {
    type: String,
    default: "",
  },
  iconAlt: {
    type: String,
    default: "icon",
  },
  showCalendar: {
    type: Boolean,
    default: true,
  },
  calendarLabel: {
    type: String,
    default: "Dates:",
  },
  range: {
    type: Object,
    default: () => ({
      start: null,
      end: null,
    }),
  },
  isRange: {
    type: Boolean,
    default: true,
  },
  isInBanner: {
    type: Boolean,
    default: false,
  },
  startDate: {
    type: [String, Date, Number],
    default: "",
  },
  endDate: {
    type: [String, Date, Number],
    default: "",
  },
  markers: {
    type: Array,
    default: () => [],
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
  bannerIsLive: {
    type: Boolean,
    default: false,
  },
  isCreateDuplicate: {
    type: Boolean,
    default: false,
  },
  disableFutureDates: {
    type: [Boolean, Date, String, null],
    default: null,
  },
});

const emit = defineEmits(["update:range", "date-change"]);

const { formatDateRangeOrSingle } = useTimeUtils();

const localRange = computed({
  get: () => props.range,
  set: (value) => {
    emit("update:range", value);
  },
});

const handleDateChange = (newValue) => {
  emit("date-change", newValue);
};

const formatDateToString = (date) => {
  if (!date) return "";
  return formatDateRangeOrSingle(date);
};

const formattedStartDate = computed(() => {
  if (typeof props.startDate === "string") {
    return props.startDate;
  }

  return props.startDate ? formatDateToString(props.startDate) : "";
});

const formattedEndDate = computed(() => {
  if (typeof props.endDate === "string") {
    return props.endDate;
  }

  return props.endDate ? formatDateToString(props.endDate) : "";
});
</script>
