<template>
  <div>
    <Modal @close="$emit('closeModal')">
      <template #nutrition>
        <div class="edit-filter-publish-modal">
          <div class="content">
            <div class="input-loading">
              <div class="loader-image"></div>
            </div>
            <div class="publishing-text">
              <p v-if="props.status == 'saving'">{{ $t('LOADER.SAVING_MESSAGE') }}</p>
              <p v-if="props.status == 'publishing'">{{ $t('LOADER.PUBLISHING_MESSAGE') }}</p>
              <p v-if="props.status == 'unpublishing'">{{ $t('LOADER.UNPUBLISHING_MESSAGE') }}</p>
              <p v-if="props.status == 'scheduling'">{{ $t('LOADER.SCHEDULING_MESSAGE') }}</p>
              <p v-if="props.status == 'unscheduling'">{{ $t('LOADER.UNSCHEDULING_MESSAGE') }}</p>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import Modal from "@/components/Modal";

const props = defineProps({
  status: {
    type: String,
    default: "",
  },
});
</script>
