<template>
  <paginate
    v-if="isPaginationEnabled"
    id="pagination-block"
    v-model="currentPageModel"
    :total-rows="props.paginationTotal"
    :page-range="props.pageRange"
    :per-page="props.paginationSize"
    :page-count="pageCount"
    :margin-pages="props.marginPages"
    :click-handler="pageChange"
    :first-last-button="true"
    first-button-text="<<"
    prev-text="<"
    next-text=">"
    last-button-text=">>"
    prev-class="prev"
    next-class="next"
    container-class="pagination"
    page-class="page-item"
    page-link-class="page-link"
    disabled-class="disabled-pagination"
    active-class="active"
  />
</template>

<script setup>
import { watch } from "vue";
import { useRouter } from "vue-router";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";

const props = defineProps({
  isPaginationEnabled: {
    type: Boolean,
    required: false,
    default: true,
  },
  pageRange: {
    type: Number,
    required: false,
    default: 6,
  },
  marginPages: {
    type: Number,
    required: false,
    default: 0,
  },
  paginationSize: {
    type: Number,
    required: true,
  },
  paginationTotal: {
    type: Number,
    required: true,
  },
  currentPage: {
    type: Number,
    required: false,
    default: 1
  },
});

const emit = defineEmits(["pageChange"]);

const router = useRouter();
const route = useRoute();

const currentPageModel = ref(props.currentPage);

const pageCount = computed(() => Math.ceil(props.paginationTotal/props.paginationSize));

const setCurrentPageModel = (value) => {
  const val = Number(value);
  if (val !== Number(currentPageModel.value)) {
    currentPageModel.value = val;
  }
};
const pageChange = (newPage) => {
  setCurrentPageModel(newPage);
  emit("pageChange", newPage);
  router.push({
    query: {
      ...route.query,
      [QUERY_PARAM_KEY.PAGE]: newPage > 1 ? newPage : undefined,
    }
  })?.catch();
};

watch(() => props.currentPage, (newPage) => setCurrentPageModel(newPage));
watch(
  () => route.query[QUERY_PARAM_KEY.PAGE],
  (val) => setCurrentPageModel(val),
  { immediate: true },
);
</script>
