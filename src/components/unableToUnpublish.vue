<template>
    <div>
        <Modal @close="$emit('closeModal')">
            <template #nutrition>
                <div class="unable-to-unpublish-model">
                    <div class="unable-to-unpublish-popup">
                        <div class="unable-to-unpublish-cross-image-div">
                            <img class="unable-to-unpublish-cross-image" src="../assets/images/Group 2.png"
                                alt="cross" />
                        </div>
                        <div class="unable-to-unpublish-text-heading">
                            <span>{{ title }}</span>
                            <div class="unable-to-unpublish-error-description">
                                <p>{{ description }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="unable-to-unpublish-btn-div">
                        <button type="button" class="btn-red" @click="closeModal()">Ok</button>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import Modal from "@/components/Modal";
export default {
    name: "unable-to-content-modal",
    components: {
        Modal,
    },
    data() {
        return {};
    },
    props: {
        closeModal: {
            type: Function,
        },
        title: {
            type: String,
        },
        description: {
            type: String,
        },
    },
};
</script>
