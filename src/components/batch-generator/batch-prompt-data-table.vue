<template>
  <div class="batch-prompt-data-table-main">
    <div
      v-if="
        isCheckBatchGenerationProgress ||
        (!isNewBatch && !isCheckBatchGenerationProgress)
      "
      class="iq-r-g-recipe-output-loader batch-loader batch-loader-icon"
    >
      <img
        :src="isCheckBatchGenerationProgress ? wandIconImage : checkmarkImage"
        :data-test-id="isCheckBatchGenerationProgress ? 'wand-icon' : 'checkmark-image'"
        alt="check"
      />
      <div class="batch-prompt-Progress-bar">
        <p
          class="color-gray-tundora font-weight-semi-bold batch-generation-heading"
          :data-test-id="isCheckBatchGenerationProgress ? 'batch-generation-in-progress' :'batch-generation-completed'"
        >
          {{
            isCheckBatchGenerationProgress
              ? $t("BATCH_GENERATOR.BATCH_GENERATION_IN_PROGRESS")
              : $t("BATCH_GENERATOR.BATCH_GENERATION_COMPLETE")
          }}
        </p>
        <div v-if="isCheckBatchGenerationProgress">
          <p class="color-gray-tundora" data-test-id="batch-generation-leave-page-text">
            {{ $t("BATCH_GENERATOR.LEAVE_PAGE") }}
          </p>
        </div>
        <div v-else>
          <p class="color-gray-tundora">
            <span data-test-id="batch-generated-recipe-saved-text">{{ $t("BATCH_GENERATOR.GENERATED_RECIPES_SAVED") }}</span>
            <button
              type="button"
              @click="navigateToRecipeMaster()"
              class="btn-reset redirect-recipes-page"
              data-test-id="redirect-to-recipes-page"
            >
              &nbsp;{{ $t("COMMON.RECIPES") }}&nbsp;
            </button>
            <span data-test-id="batch-generator-click-link-text">{{ $t("BATCH_GENERATOR.CLICK_LINKS_BELOW") }}</span>
          </p>
          <p class="color-gray-tundora">
            <span data-test-id="batch-generator-start-new-batch">{{ $t("BATCH_GENERATOR.START_NEW_BATCH") }}</span>
          </p>
        </div>
      </div>
      <button
        v-if="!isCheckBatchGenerationProgress"
        type="button"
        class="btn-green new-batch-button"
        data-test-id="create-new-batch"
        @click="$emit('createNewBatch')"
      >
        {{ $t("BATCH_GENERATOR.NEW_BATCH") }}
      </button>
    </div>
    <div class="batch-prompt-table-container">
      <table class="batch-prompt-table">
        <thead class="table-head-row">
          <tr class="table-head">
            <th :data-test-id="`table-head-${tableHead}`" v-for="(tableHead, index) in dynamicTableHeadData" :key="index">
              {{ tableHead }}
            </th>
          </tr>
        </thead>
        <tbody class="batch-table-body">
          <tr
            v-for="(row, rowIndex) in tableData"
            :key="rowIndex"
            class="table-body"
          >
            <td class="serial-no" :data-test-id="`serial-no-${row.prompt}`" >{{ rowIndex + 1 }}</td>
            <td class="prompt" :data-test-id="`prompt-${rowIndex + 1}`" >{{ row.prompt }}</td>
            <td class="status" :data-test-id="`status-${rowIndex + 1}`" :class="getStatusData(row.progressState).class">
              {{ getStatusData(row.progressState).text }}
            </td>
            <td class="ready-to-review" :class="{ 'expand-width': !isDataAdded }">
              <div v-if="row.progressState === keys.BATCH_GENERATOR_KEYS.STATUS_FAILED_LOWER" class="not-generated">
                <span data-test-id="could-not-genarate-text">{{ $t("BATCH_GENERATOR.COULD_NOT_GENERATE") }}</span>
              </div>
              <button
                v-else
                type="button"
                class="btn-reset text-underline"
                @click="navigateToRecipe(row.recipeIsin)"
                :data-test-id="`recipe-${row.recipeTitle}-link-button`"
              >
                {{ row.recipeTitle }}
              </button>
            </td>
            <td class="remove-container">
              <div v-if="isDataAdded && !row.hasDeleteClicked">
                <button
                  type="button"
                  class="btn-reset basket"
                  @click="handleDeletePrompt(rowIndex)"
                >
                  <img src="@/assets/images/delete.svg?skipsvgo=true" alt="remove-icon" />
                </button>
              </div>
              <div
                v-if="row.hasDeleteClicked"
                class="button-container batch-delete-button"
              >
                <button
                  type="button"
                  class="btn-green-outline"
                  @click="cancelDelete(rowIndex)"
                >
                  {{ $t("BUTTONS.CANCEL_BUTTON") }}
                </button>
                <button
                  type="button"
                  class="btn-red"
                  @click="removePrompt(rowIndex)"
                >
                  {{ $t("BUTTONS.REMOVE_BUTTON") }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div
      v-if="!isCheckBatchGenerationProgress && isNewBatch"
      class="limit-prompt-section"
      data-test-id="prompt-limit"
    >
      {{ $t("BATCH_GENERATOR.PROMPT_LIMIT") }}
    </div>
    <button
      v-if="!isCheckBatchGenerationProgress && isNewBatch"
      type="submit"
      :class="{ 'disabled-button': !tableData.length }"
      :data-test-id="!tableData.length ? 'disabled-batch-generate-button':'batch-generate-button'"
      :disabled="!tableData.length"
      class="btn-green button-container"
      @click="$emit('generateBatch')"
    >
      <img src="@/assets/images/generator-white-icon.png" alt="generate-icon" />
      {{ $t("BUTTONS.GENERATE_BUTTON") }}
    </button>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance } from 'vue';
import { useCommonUtils } from '~/composables/useCommonUtils';
import checkmarkImage from "~/assets/images/checkmark.svg?skipsvgo=true";
import wandIconImage from "~/assets/images/wand-icon.svg?skipsvgo=true";
import { useRouter } from "vue-router";
import { useStore } from 'vuex';

// utility define
const { routeToPage } = useCommonUtils();
const router = useRouter();

const instance = getCurrentInstance();
const keys = instance.appContext.config.globalProperties.$keys;

const props = defineProps({
  tableData: {
    type: Array,
    required: true,
  },
  isNewBatch: {
    type: Boolean,
    default: false,
  },
});
const isDataAdded = computed(() => {
  return props.tableData.some(row => row.isDeleteVisible && row.progressState === keys.BATCH_GENERATOR_KEYS.STATUS_ADDED);
});
const dynamicTableHeadData = computed(() => {
  const promptCount = props.tableData.length;
  const maxPrompts = 10;
  const promptHeader = (!props.isNewBatch && !isCheckBatchGenerationProgress.value)
    ? `${promptCount} ${keys.BATCH_GENERATOR_KEYS.PROMPT_LABEL}s (${keys.BATCH_GENERATOR_KEYS.LIMIT}: ${maxPrompts})`
    : `${keys.BATCH_GENERATOR_KEYS.PROMPT_LABEL} (${promptCount}/${maxPrompts})*`;

  return [
    keys.BATCH_GENERATOR_KEYS.EMPTY,
    promptHeader,
    keys.BATCH_GENERATOR_KEYS.STATUS_LABEL,
    keys.BATCH_GENERATOR_KEYS.RECIPE_TO_REVIEW_LABEL,
    keys.BATCH_GENERATOR_KEYS.EMPTY,
  ];
});


const isCheckBatchGenerationProgress = computed(() => {
  return store.getters["batchGenerator/getBatchGenerationInProgress"];
});

const store = useStore();

const navigateToRecipeMaster = () => {
  routeToPage("recipes"); // Use directly from commonUtils
};
const handleDeletePrompt = (index) => {
  props.tableData.forEach((row, i) => {
    if (i === index) {
      row.hasDeleteClicked = true;
      row.isDeleteVisible = false;
    } else {
      row.hasDeleteClicked = false;
      row.isDeleteVisible = true;
    }
  });
};
const cancelDelete = (index) => {
  props.tableData[index].hasDeleteClicked = false;
  props.tableData[index].isDeleteVisible = true;
};
const removePrompt = (index) => {
  props.tableData.splice(index, 1);
};
const getStatusData = (status) => {
  const lowerStatus = status.toLowerCase();
  let statusInfo = {
    text: keys.BATCH_GENERATOR_KEYS.STATUS_ADDED,
    class: keys.BATCH_GENERATOR_KEYS.STATUS_DEFAULT_CLASS,
  };

  if (lowerStatus === keys.BATCH_GENERATOR_KEYS.STATUS_DONE_LOWER) {
    statusInfo = {
      text: keys.BATCH_GENERATOR_KEYS.STATUS_DONE,
      class: keys.BATCH_GENERATOR_KEYS.STATUS_DONE_CLASS,
    };
  } else if (lowerStatus === keys.BATCH_GENERATOR_KEYS.STATUS_FAILED_LOWER) {
    statusInfo = {
      text: keys.BATCH_GENERATOR_KEYS.STATUS_FAILED,
      class: keys.BATCH_GENERATOR_KEYS.STATUS_FAILED_CLASS,
    };
  } else if (lowerStatus === keys.BATCH_GENERATOR_KEYS.STATUS_RUNNING_LOWER) {
    statusInfo = {
      text: keys.BATCH_GENERATOR_KEYS.STATUS_RUNNING,
      class: keys.BATCH_GENERATOR_KEYS.STATUS_RUNNING_CLASS,
    };
  } else if (lowerStatus === keys.BATCH_GENERATOR_KEYS.STATUS_PENDING_LOWER) {
    statusInfo = {
      text: keys.BATCH_GENERATOR_KEYS.STATUS_PENDING,
      class: keys.BATCH_GENERATOR_KEYS.STATUS_PENDING_CLASS,
    };
  }

  return statusInfo;
};
const navigateToRecipe = (isin) => {
  router.push({
    path: keys.BATCH_GENERATOR_KEYS.RECIPE_DETAIL,
    query: {
      isin: isin,
      backFrom: keys.BATCH_GENERATOR_KEYS.IQ_BATCH_GENERATOR,
    },
  });
};
</script>
