<template>
  <client-only>
    <div class="batch-generator-main">
      <div class="batch-generator-header-container">
        <div class="button-container">
          <button
            type="button"
            class="btn-reset display-4 color-green iq-r-g-container-header-btn iq-r-g-container-header-btn-active"
            data-test-id="batch-generator-prompt-button"
          >
            {{ $t("BATCH_GENERATOR.PROMPTS") }}
          </button>
          <button
            type="button"
            :class="{ 'disabled-button': checkResetStatus() }"
            :disabled="checkResetStatus()"
            class="btn-reset display-4 color-green iq-r-g-container-header-btn"
            data-test-id="batch-generator-reset-button"
            @click="resetBatch()"
          >
            {{ $t("GENERATOR.RESET") }}
          </button>
        </div>
        <div
          v-if="isNewBatch && !isCheckBatchGenerationProgress"
          class="input-container"
        >
          <form class="iq-r-g-prompt" @submit.prevent="submit">
            <input
              v-model="newPrompt"
              type="text"
              class="input-field"
              data-test-id="batch-generator-test-input"
              autocomplete="off"
              placeholder="Enter prompt"
            />
            <button
              type="submit"
              class="iq-r-g-prompt-btn"
              :class="{ 'disabled-button': promptCount >= 10 }"
              :disabled="promptCount >= 10"
              data-test-id="add-prompt-button"
            >
              {{ $t("BATCH_GENERATOR.ADD_PROMPT") }}
            </button>
          </form>
        </div>
      </div>
    </div>
  </client-only>
</template>
<script setup>
import { ref, computed, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { useNuxtApp } from "#app";



const { $eventBus } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const store = useStore();

const props = defineProps({
  promptCount: {
    type: Number,
    default: 0,
  },
  isNewBatch: {
    type: Boolean,
    default: false,
  },
});


const newPrompt = ref("");
const emit = defineEmits();

const isCheckBatchGenerationProgress = computed(() => {
  return store.getters["batchGenerator/getBatchGenerationInProgress"];
});

const submit = () => {
  if (newPrompt.value?.trim()) {
    emit($keys.BATCH_GENERATOR_KEYS.ADD_PROMPT_EVENT, newPrompt.value.trim());
    newPrompt.value = "";
  }
};

const resetBatch = () => {
  emit($keys.BATCH_GENERATOR_KEYS.CREATE_NEW_BATCH);
};

const checkResetStatus = () => {
  return isCheckBatchGenerationProgress.value || !props.isNewBatch || (props.isNewBatch && !props.promptCount);
};
</script>
