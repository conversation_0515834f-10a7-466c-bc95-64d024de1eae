<template>
  <div class="text-field">
    <div class="container-text">
      <p class="text-title-2 font-bold">
        {{ label }}<span v-if="isRequired" class="compulsory-field">*</span>
      </p>
      <slot name="action"></slot>
    </div>
    <div
      class="description-section"
      :id="inputId"
    >
      <input
        class="form-control"
        :class="{ 'form-control-rounded-green': isRoundedGreen }"
        :ref="inputRef"
        v-model="inputValue"
        :maxlength="maxLength"
        autocomplete="off"
        :placeholder="placeholder"
        @input="handleInput"
        @keydown="onKeyDown"
      />
      <div
        v-if="showCounter && inputValue.length > 0"
        class="description-length font-size-12 font-normal line-height-14"
      >
        {{ displayCount }}/{{ maxLength }}
      </div>
      <slot name="additional-content"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    required: true
  },
  isRequired: {
    type: Boolean,
    default: false
  },
  maxLength: {
    type: Number,
    default: 42
  },
  inputId: {
    type: String,
    default: 'banner_text_field'
  },
  inputRef: {
    type: String,
    default: 'inputRef'
  },
  placeholder: {
    type: String,
    default: ''
  },
  showCounter: {
    type: Boolean,
    default: true
  },
  combinedLength: {
    type: Number,
    default: null
  },
  validateInput: {
    type: Function,
    default: null
  },
  isRoundedGreen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'input']);

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const displayCount = computed(() => {
  if (props.combinedLength !== null) {
    return props.combinedLength >= props.maxLength ? props.maxLength : props.combinedLength;
  }
  return inputValue.value.length;
});

const handleInput = () => {
  if (props.validateInput) {
    props.validateInput(inputValue.value);
  }
  emit('input');
};

const onKeyDown = (event) => {
  if (props.combinedLength !== null && 
      props.combinedLength >= props.maxLength && 
      event.key !== 'Backspace' && 
      event.key !== 'Delete') {
    event.preventDefault();
  }
};

watch(() => props.modelValue, (newValue) => {
  if (props.validateInput) {
    props.validateInput(newValue);
  }
});
</script>
