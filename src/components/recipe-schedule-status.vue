<template>
  <div class="recipe-to-schedule-popup-main">
    <div class="recipe-schedule-table">
      <table class="main-table" aria-label="main-table">
        <thead class="table-head">
          <tr class="title">
            <th></th>
            <th>{{ $t('ISIN') }}</th>
            <th>{{ $t('COMMON.RECIPE_TITLE') }}</th>
            <th>{{ $t('MODIFIED') }}</th>
            <th>{{ $t('EXTERNAL_ID') }}</th>
          </tr>
        </thead>
        <tbody class="table-body">
          <tr v-for="(info, index) in toBePublished" :key="index" class="body">
            <td class="recipe-image">
              <img
                alt=""
                v-if="
                  info.media &&
                  info.media[defaultLang] &&
                  info.media[defaultLang].image &&
                  !info.media[defaultLang].externalImageUrl
                "
                class="table-image"
                :src="
                  info.media &&
                  info.media[defaultLang] &&
                  info.media[defaultLang].image
                    ? info.media[defaultLang].image
                    : ''
                "
              />
              <img
                alt=""
                v-if="
                  info.media &&
                  info.media[defaultLang] &&
                  info.media[defaultLang].externalImageUrl
                "
                class="table-image"
                :src="
                  info.media &&
                  info.media[defaultLang] &&
                  info.media[defaultLang].externalImageUrl
                    ? info.media[defaultLang].externalImageUrl
                    : ''
                "
                @error="$event.target.src = `${defaultImage}`"
              />
              <img
                alt=""
                v-if="
                  (!info.media ||
                    !info.media[defaultLang] ||
                    !info.media[defaultLang].image) &&
                  (!info.media ||
                    !info.media[defaultLang] ||
                    !info.media[defaultLang].externalImageUrl)
                "
                class="table-image"
                :src="defaultImage"
              />
            </td>
            <td class="recipe-number">{{ info && info.isin }}</td>
            <td class="recipe-name">
              <div class="recipe-name-text">
                {{
                  info && info.title && info.title[defaultLang]
                    ? info.title[defaultLang]
                    : ""
                }}
              </div>
              <p
                class="recipe-subtitle-name"
                v-if="info && info.subtitle && info.subtitle[defaultLang]"
              >
                <span>
                  {{
                    info && info.subtitle && info.subtitle[defaultLang]
                      ? info.subtitle[defaultLang]
                      : ""
                  }}
                </span>
              </p>
            </td>
            <td
              class="recipe-modified"
              v-if="finalAvailableLangs && finalAvailableLangs.length > 1"
            >
              <span
                v-if="info && info.langs && info.langs.length > 1"
                class="langs-data"
                >{{ info && info.langs && info.langs.length - 1 }}</span
              >
            </td>
            <td class="recipe-modified">
              {{ info && info.lastMod ? getTime(info.lastMod) : "" }}
            </td>
            <td class="recipe-modified">
              {{ info && info.externalId ? info.externalId : "" }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
/**
 * @deprecated We should use RecipesSchedulePublishModal component: /src/components/pages/recipes/recipes-schedule-publish-modal.vue
 */

import defaultImage from '~/assets/images/default_recipe_image.png';
import { useStore } from 'vuex';

// Props
const props = defineProps({
  toBePublished: {
    type: Array,
  },
});


const store = useStore();


// Get formatted date
const getTime = (jsonTimestamp) => {
  const date = new Date(jsonTimestamp);
  const options = { month: "short", day: "numeric", year: "numeric" };
  const formattedDate = date.toLocaleString("en-US", options);
  const checkComma = formattedDate.replace(",", "");
  return checkComma;
};

const lang = computed(() => store.getters["userData/getDefaultLang"]);
const defaultLang = computed(() => store.getters["userData/getDefaultLang"]);
const finalAvailableLangs = computed(() => store.getters['userData/getAvailableLangs']);

</script>
