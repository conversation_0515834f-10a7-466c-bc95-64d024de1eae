<template>
  <div class="recipe-preview-detail-modal">
    <div class="recipe-preview-detail-modal-title text-h2">{{ $t('COMMON.RECIPE_PREVIEW') }}</div>

    <div class="recipe-preview-detail-modal-body">
      <recipePreviewDetail
        :rISIN="recipeIsin"
        :checkRecipePreviewVideo="checkRecipePreviewVideo"
      />
    </div>
  </div>
</template>

<script setup>
import RecipePreviewDetail from "../../recipe-preview-detail.vue";

defineProps({
  recipeIsin: {
    type: String,
    required: true,
  },
  checkRecipePreviewVideo: Function,
});
</script>
