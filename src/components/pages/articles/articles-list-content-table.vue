<template>
  <div class="articles-list-content-table">

    <div class="articles-list-content-table-wrapper">
      <div class="articles-list-content-table-head articles-list-content-table-row">
        <div class="articles-list-content-table-cell">{{ $t('ARTICLE.NO') }}</div>
        <div class="articles-list-content-table-cell"></div>
        <div class="articles-list-content-table-cell">{{ $t('ARTICLE.ARTICLE_ID') }}</div>
        <div class="articles-list-content-table-cell">{{ $t('ARTICLE.ARTICLE_TITLE') }}</div>
        <div class="articles-list-content-table-cell">{{ $t('MODIFIED') }}</div>
        <div class="articles-list-content-table-cell">{{ $t('COMMON.STATUS') }}</div>
        <div class="articles-list-content-table-cell"></div>
      </div>
      <draggable
        :list="props.contentList"
        class="articles-list-content-table-group"
        ghost-class="articles-list-content-table-hidden"
        :scroll-sensitivity="200"
        :force-fallback="true"
        @start="drag = true"
        @end="drag = false"
        @update="draggableUpdate()"
        handle=".articles-list-content-table-row-drag"
      >
        <template v-for="(item, index) in props.contentList">
          <div class="articles-list-content-table-body articles-list-content-table-row">
            <div class="articles-list-content-table-row-drag">
              <img alt="drag vertically icon" src="@/assets/images/drag-vertically.svg?skipsvgo=true"/>
            </div>
            <div class="articles-list-content-table-cell">{{ index + 1 }}</div>
            <div class="articles-list-content-table-cell">
              <div class="articles-list-content-image">
                <img
                  v-if="item.isNew"
                  src="@/assets/images/Newlabel.svg?skipsvgo=true"
                  class="__icon-new"
                  alt="new label"
                  loading="lazy"
                />
                <img
                  :src="item?.image || ''"
                  class="__main-image"
                  :alt="item.title"
                  loading="lazy"
                />
                <img
                  v-if="item.hasVideo"
                  src="@/assets/images/Videolabel.svg?skipsvgo=true"
                  class="__icon-video"
                  alt="video label"
                  loading="lazy"
                />
              </div>
            </div>
            <div class="articles-list-content-table-cell">
              <p class="articles-list-content-uuid">{{ item.uuid }}</p>
            </div>
            <div class="articles-list-content-table-cell">
              <p class="articles-list-content-title">{{ item.title }}</p>
            </div>
            <div class="articles-list-content-table-cell">
              <p class="articles-list-content-last-update">
                {{ item.lastUpdate ? formatTimestampToDate(item.lastUpdate) : "" }}
              </p>
            </div>
            <div class="articles-list-content-table-cell">
              <badge
                :label="$t(STATE_MAPPING[item.state].tKey)"
                :badge-type="STATE_MAPPING[item.state].badgeType"
                :img-src="STATE_MAPPING[item.state].icon"
              ></badge>
            </div>
            <div class="articles-list-content-table-cell">
              <body-menu
                :actions="prepareActions(item.state, item.usedInHero)"
                @call-actions="(key) => callAction(key, item.uuid, item.usedInHero)"
              ></body-menu>
            </div>
          </div>
        </template>
      </draggable>
    </div>

  </div>
</template>

<script setup>

import { useTimeUtils } from "../../../composables/useTimeUtils.js";
import { STATE_MAPPING } from "../../../сonstants/state-mapping.js";
import { useNuxtApp } from "nuxt/app";

const props = defineProps({
  articleId: {
    type: String,
    required: true,
  },
  contentList: {
    type: Array,
    required: true,
  },
});

const { $t } = useNuxtApp();

const emit = defineEmits(["draggableUpdate", "callAction"])

const { formatTimestampToDate } = useTimeUtils();

const draggableUpdate = () => emit("draggableUpdate", true);
const callAction = (key, uuid, usedInHero) => emit("callAction", { key, uuid, isArticleIncludedInHero: usedInHero });

const prepareActions = (state, usedInHero) => {
  return [
    {
      isDisable: false,
      key: "preview",
      label: $t('BUTTONS.PREVIEW_BUTTON'),
    },
    {
      isDisable: state !== 'published',
      key: "unpublish",
      label: "Unpublish",
    },
    {
      isDisable: state !== 'unpublished',
      key: "publish",
      label: $t('COMMON.PUBLISH'),
    },
    {
      isDisable: false,
      key: "edit",
      label: $t('BUTTONS.EDIT_BUTTON'),
    },
    {
      isDisable: false,
      isInactive: usedInHero,
      key: "delete",
      label: "Delete",
      tooltip: usedInHero && $t('ARTICLE_IN_HERO')
    },
  ];
};

</script>
