<template>
  <div class="article-item-pages">
    <div class="article-item-pages-header">
      <h2 class="font-size-20 font-bold color-black">Article pages <span class="color-red">*</span></h2>

      <button
        type="button"
        class="btn-green-text btn-text-14"
        :disabled="isUploadZipProcessLoading || isDragging || !pages?.length || !title"
        @click="previewArticle"
      >
        <img alt="article preview" src="@/assets/images/article_green_image.svg?skipsvgo=true" />
        <span>{{ $t('ARTICLE.ARTICLE_PREVIEW') }}</span>
      </button>
    </div>

    <div class="article-item-pages-body">
      <div
        v-if="!isUploadZipProcessLoading && !pages?.length"
        class="article-item-pages-drop"
        @dragover="dragover"
        @dragleave="dragleave"
        @drop="drop"
      >
        <div v-if="!isDragging" class="article-item-pages-drop-details">
          <div>
            <p class="article-item-pages-drop-details-title font-size-20 font-normal color-black">Drop file here, or click on Upload file</p>
            <p class="article-item-pages-drop-details-sub-title font-size-base font-normal color-grey">ZIP format (max 40 MB)</p>
            <div class="article-item-pages-drop-details-zip-upload-btn">
              <label for="zip-file" class="btn-green">{{ $t('ARTICLE.UPLOAD_FILE') }}</label>
              <input
                id="zip-file"
                type="file"
                class="visibility-hidden"
                title="UPLOAD FILE"
                @click="cleanEventTargetValue($event)"
                @change="checkUploadedZipFile"
                accept=".zip"
              />
            </div>
          </div>
          <div>
            <img alt="empty article hero" class="article-item-pages-drop-details-img" src="@/assets/images/empty_article_hero_image.png"/>
          </div>
        </div>
        <div v-if="isDragging" class="article-item-pages-drop-over">
          <div class="article-item-pages-drop-over-body">
            <img alt="drag article" src="@/assets/images/drag_article_image.svg?skipsvgo=true" />
            <span class="font-size-14 color-vivid-green">{{ $t('ARTICLE.DROP_FILE_HERE') }}</span>
          </div>
        </div>
      </div>

      <div v-if="isUploadZipProcessLoading" class="article-item-pages-zip-upload">
        <div class="article-item-pages-zip-upload-loader"></div>
        <div class="article-item-pages-zip-upload-alert">
          <span class="font-size-base font-normal color-shadow-gray">Upload is in progress...</span>
        </div>
      </div>

      <div
        v-if="!isUploadZipProcessLoading && pages?.length"
        class="article-item-pages-zip-content"
      >
        <div class="article-item-pages-zip-content-body">
          <div
            v-for="(data, index) in pages"
            class="article-item-pages-single-article"
          >
            <div class="article-item-pages-iframe-wrapper">
              <iframe
                tabindex="-1"
                title="website-frame"
                class="iframe-class"
                :src="`${data}`"
              ></iframe>
            </div>
            <div class="article-item-pages-single-article-index font-size-14 font-bold color-white">
              <span>{{ index + 1 }}</span>
            </div>
          </div>
        </div>
        <div class="article-item-pages-zip-content-action">
          <button
            type="button"
            class="btn-white btn-small"
            @click="openReplaceZipFile()"
          >
            {{ $t('ARTICLE.REPLACE_ZIP') }}
          </button>
          <button
            type="button"
            class="btn-green-text btn-small"
            @click="downloadZipFile()"
          >
            <img alt="download" src="@/assets/images/icons/download.svg?skipsvgo=true" />
            <span>{{ $t("ARTICLE.DOWNLOAD_ZIP") }}</span>
          </button>
        </div>
      </div>
    </div>

    <div class="article-item-pages-footer">
      <p class="font-size-14 font-normal color-gunmetal-grey"><span class="color-red">*</span> Required data for publishing article</p>
    </div>
  </div>
</template>

<script setup>

import { ref } from "vue";
import JSZip from "jszip";
import { useArticleItemStore } from "../../../stores/article-item.js";
import { useStore } from "vuex";
import AlertModal from "../../modals/alert-modal.vue";
import ArticleItemReplaceZipModal from "./article-item-replace-zip-modal.vue";
import { cleanEventTargetValue } from "../../../utils/clean-event-target-value.js";
import ArticleItemPreviewModal from "./article-item-preview-modal.vue";

const { $keys } = useNuxtApp();
const store = useStore();
const {
  title,
  pages,
  source,
  fliteSource,
  uploadZipFileAsync,
} = useArticleItemStore();
const { triggerLoading } = useCommonUtils();
const { openModal, closeModal } = useBaseModal({
  "AlertModal": AlertModal,
  "ArticleItemReplaceZipModal": ArticleItemReplaceZipModal,
  "ArticleItemPreviewModal": {
    component: ArticleItemPreviewModal,
    hideCloseBtn: false,
  },
});

const isDragging = ref(false);
const isUploadZipProcessLoading = ref(false);
const acceptedMediaTypes = ref(["application/x-zip-compressed", "application/zip"]);
const maxFileSize = ref(40 * 1024 * 1024);

const dragover = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = true;

  // Set the drop effect to 'copy' to show the correct cursor
  if (e.dataTransfer) {
    e.dataTransfer.dropEffect = 'copy';
  }
};

const dragleave = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = false;
};

const validateFiles = (files) => {
  if (!files || files.length === 0) {
    return false;
  }

  if (files.length > 1) {
    openModal({
      name: "AlertModal",
      props: {
        title: "Multiple files cannot be uploaded",
        text: "Accepted file formats: zip",
      },
    });
    return false;
  }

  return true;
};

const validateFileTypeSize = (file) => {
  if (!acceptedMediaTypes.value.includes(file.type)) {
    openModal({
      name: "AlertModal",
      props: {
        title: "Invalid zip file format",
        text: "Accepted file formats: zip",
      },
    });
    return false;
  }
  if (file.size >= maxFileSize.value) {
    openModal({
      name: "AlertModal",
      props: {
        title: "Your uploaded zip size is larger than 40 MB.",
        text: "Max. size for zip: 40 MB",
      },
    });
    return false;
  }

  return true;
};

const drop = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = false;

  const files = e.dataTransfer?.files;
  if (validateFiles(files) && validateFileTypeSize(files[0])) {
    const formData = new FormData();
    formData.append("file", files[0]);
    handleZipUpload(formData, files[0]);
  }
};

const checkUploadedZipFile = (event) => {
  const files = event.target.files || event.srcElement.files;
  if (validateFiles(files) && validateFileTypeSize(files[0])) {
    const formData = new FormData();
    formData.append("file", files[0]);
    handleZipUpload(formData, files[0]);
  }
};

const handleZipUpload = (formData, file) => {
  isUploadZipProcessLoading.value = true;
  const showHtmlAlert = () => {
    openModal({
      name: "AlertModal",
      props: {
        title: "No HTML page found",
        text: "Accepted zip file including html page",
      },
    });
  };
  const reader = new FileReader();
  reader.onload = async () => {
    const zip = new JSZip();
    try {
      const zipFiles = await zip.loadAsync(reader.result);

      const htmlFile = Object.values(zipFiles.files).find((file) => file.name.endsWith(".html"));
      if (!htmlFile) {
        showHtmlAlert();
        return;
      }

      const content = await htmlFile.async("string");
      if (!content.trim()) {
        showHtmlAlert();
        return;
      }

      await uploadZipAsync(formData);
    } catch (e) {
      openModal({
        name: "AlertModal",
        props: {
          title: "File is Encrypted",
        },
      });
      isUploadZipProcessLoading.value = false;
      console.error(`[IQ][ArticleItemPages] ${$keys.KEY_NAMES.ERROR_IN} handleZipUpload`, e);
    }
  };
  reader.onerror = (e) => {
    isUploadZipProcessLoading.value = false;
    console.warn("[IQ][ArticleItemPages] Error reading the file. Please try again.", e);
    openModal({
      name: "AlertModal",
      props: {
        title: "Cannot read file",
      },
    });
  };
  reader.readAsArrayBuffer(file);
};

const uploadZipAsync = async (formData) => {
  try {
    const lang = store.getters["userData/getDefaultLang"];
    await uploadZipFileAsync({ lang, formData });
    triggerLoading($keys.KEY_NAMES.VIDEO_UPLOADED);
  } catch {
    triggerLoading($keys.KEY_NAMES.ERROR_OCCURRED);
  } finally {
    isUploadZipProcessLoading.value = false;
  }
};

const openReplaceZipFile = () => {
  openModal({
    name: "ArticleItemReplaceZipModal",
    onCallback: (event) => {
      closeModal("ArticleItemReplaceZipModal");
      checkUploadedZipFile(event);
    },
  });
};

const downloadZipFile = () => {
  const url = fliteSource.value || source.value || "";
  window.open(url, "_blank");
};

const previewArticle = () => {
  if (pages.value?.length) {
    openModal({ name: "ArticleItemPreviewModal" });
  }
};

defineExpose({
  isUploadZipProcessLoading
});
</script>
