<template>
  <div class="article-item-preview-modal font-family-averta">
    <div class="font-size-20 font-bold color-black">{{ modalTitle }}</div>
    <div class="article-item-preview-modal-body">
      <div>
        <div class="article-item-preview-modal-article-details">
          <img :src="image || defaultImage" :alt="title">
          <div class="">
            <p class="font-size-20 font-bold color-black">{{ title }}</p>
            <p class="font-size-14 font-weight-semi-bold color-stone-gray clamp-3">
              <span v-if="category">{{ category }}</span>
              <span v-else>No category</span>
            </p>
          </div>
        </div>
        <div class="article-item-preview-modal-devices">
          <div class="font-size-20 font-bold color-black">Device:</div>
          <div class="article-item-preview-modal-devices-actions">
            <div v-for="(item, index) in articlePreviewSize" :key="index">
              <label class="control-radio">
                <span class="font-size-base font-normal color-black">{{ item.size }}</span>
                <input type="radio" name="device-radio-button" v-model="selectedDeviceModel" :value="item.device">
                <span class="checkmark"></span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="article-item-preview-modal-preview">
          <button
            v-if="pages?.length > 1"
            type="button"
            class="btn-reset article-item-preview-modal-preview-btn-prev"
            :disabled="!isPrevBtnEnabled"
            @click="displayPrevArticle"
          >
            <img src="@/assets/images/green-right-arrow.png" alt="arrow-icon"/>
          </button>
          <div class="article-item-preview-modal-preview-container">
            <div
              class="article-item-preview-modal-preview-screen"
              :class="{
                'article-item-preview-modal-preview-screen-small': selectedDeviceModel === articlePreviewSize[0].device,
                'article-item-preview-modal-preview-screen-large': selectedDeviceModel === articlePreviewSize[1].device
              }"
            >
              <div class="article-item-preview-modal-preview-iframe-container">
                <iframe
                  tabindex="-1"
                  title="website-frame"
                  class="website-frame"
                  :src="pageOnScreen"
                ></iframe>
              </div>
              <div class="article-item-preview-modal-preview-iframe-hide-scroll"></div>
              <button
                type="button"
                class="btn-reset article-item-preview-modal-preview-btn-refresh"
                @click="refreshArticleOnScreenAsync"
              >
                <img src="@/assets/images/white_arrow.png" alt="arrow-icon"/>
              </button>
            </div>
          </div>
          <button
            v-if="pages?.length > 1"
            type="button"
            class="btn-reset article-item-preview-modal-preview-btn-next"
            :disabled="!isNextBtnEnabled"
            @click="displayNextArticle"
          >
            <img src="@/assets/images/green-right-arrow.png" alt="arrow-icon"/>
          </button>
        </div>

      </div>
    </div>
    <div v-if="isActionsEnabled" class="article-item-preview-modal-actions">
      <button
        type="button"
        class="btn-white"
        @click="cancelAction"
      >
        {{ $t('BUTTONS.CANCEL_BUTTON') }}
      </button>
      <button
        type="button"
        class="btn-green"
        @click="confirmAction"
      >
        {{ confirmBtnLabel || $t('BUTTONS.SAVE_BUTTON')}}
      </button>
    </div>
  </div>
</template>

<script setup>
import { useArticleItemStore } from "../../../stores/article-item.js";
import { reactive, ref } from "vue";
import { useDelayTimer } from "../../../composables/useDelayTimer.js";

const props = defineProps({
  isActionsEnabled: {
    type: Boolean,
    default: false,
  },
  confirmBtnLabel: String,
  modalTitle: {
    type: String,
    default: "Article Preview",
  },
});

const emit = defineEmits(["close"]);

const {
  title,
  image,
  category,
  pages,
} = useArticleItemStore();
const { delay } = useDelayTimer();

const defaultImage = ref("~/assets/images/default_recipe_image.png");
const articlePreviewSize = reactive([
  {
    size: "Small device (360*640)",
    device: 1,
    isChecked: true,
  },
  {
    size: "Large device (360*800)",
    device: 2,
    isChecked: false,
  },
]);
const selectedDeviceModel = ref(articlePreviewSize[0].device);
const pageOnScreenIndex = ref(0);

const isPrevBtnEnabled = computed(() => pageOnScreenIndex.value !== 0);
const isNextBtnEnabled = computed(() => (pageOnScreenIndex.value + 1) !== pages.value?.length);
const pageOnScreen = computed(() => pages.value?.[pageOnScreenIndex.value]);

const cancelAction = () => emit("close", false);
const confirmAction = () => emit("close", true);

const displayPrevArticle = () => {
  if (pages.value?.length > 1 && pageOnScreenIndex.value !== 0) {
    pageOnScreenIndex.value = pageOnScreenIndex.value - 1;
  }
};

const displayNextArticle = () => {
  if (pages.value?.length > 1 && pages.value?.length !== (pageOnScreenIndex.value + 1)) {
    pageOnScreenIndex.value = pageOnScreenIndex.value + 1;
  }
};

const refreshArticleOnScreenAsync = async () => {
  const current = pageOnScreenIndex.value;
  pageOnScreenIndex.value = null;
  await delay(10);
  pageOnScreenIndex.value = current;
};

onKeyStroke(['ArrowLeft'], () => displayPrevArticle(), { dedupe: true });
onKeyStroke(['ArrowRight'], () => displayNextArticle(), { dedupe: true });
</script>
