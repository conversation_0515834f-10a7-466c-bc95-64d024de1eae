<template>
  <div :id="props.articleId" class="articles-list-head">
    <div class="articles-list-head-title">
      <p>{{ props.title }}</p>
    </div>
    <div class="articles-list-head-description">
      <p v-if="!hideDescription" class="text-overflow">{{ props.description }}</p>
    </div>
    <div class="articles-list-head-actions">
      <simple-actions
        @editOnClick="editArticle()"
        @deleteOnClick="deleteArticle()"
      ></simple-actions>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  articleId: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  hideDescription: {
    type: Boolean,
    required: false,
    default: false,
  }
});

const emit = defineEmits(["editArticle", "deleteArticle"]);

const editArticle = () => emit("editArticle", props.articleId);
const deleteArticle = () => emit("deleteArticle", props.articleId);

</script>
