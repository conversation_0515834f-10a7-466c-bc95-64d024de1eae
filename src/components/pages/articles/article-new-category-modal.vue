<template>
  <div class="article-new-category-modal font-family-averta">
    <div class="font-size-24 font-bold color-black">
      {{ isEdit ? 'Edit category name' : 'Add a new category'}}
    </div>

    <div v-if="!isLoading" class="article-new-category-modal-body">
      <label for="add-new-category-input" class="font-size-base font-normal color-grey">
        <span v-if="!isEdit">Enter a name to your category</span>
        <span v-else>Category name: <span>{{ categoryName }}</span></span>
      </label>
      <input
        ref="inputRef"
        type="text"
        id="add-new-category-input"
        class="form-control"
        :placeholder="isEdit ? 'Enter new category name' : 'Enter name'"
        v-model.trim="inputModel"
      >
      <template v-if="hasError">
        <img alt="red info" class="article-new-category-modal-form-control-suffix" src="@/assets/images/red-info.svg?skipsvgo=true" />
        <div class="article-new-category-modal-form-control-error">
          <span class="font-size-14 font-normal color-red-d">{{ $t('COMMON.NAME_ALREADY_EXIST') }}</span>
        </div>
      </template>
    </div>
    <loading-block v-else></loading-block>

    <div class="article-new-category-modal-actions">
      <button
        type="button"
        class="btn-green-outline"
        :disabled="isLoading"
        @click="closeModal"
      >
        {{ $t('BUTTONS.CANCEL_BUTTON') }}
      </button>
      <button
        type="button"
        class="btn-green"
        :disabled="!inputModel || hasError || isLoading"
        @click="() => isEdit ? changeNameAsync() : postNewArticleCategoryAsync()"
      >
        {{ isEdit ? $t('BUTTONS.APPLY_BUTTON') : $t('COMMON.ADD') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { useArticlesStore } from "../../../stores/articles.js";
import LoadingBlock from "../../loading-block/loading-block.vue";

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  categoryName: String,
  categoryOrder: Number,
  categoryUUID: String,
});

const emit = defineEmits(["close"]);

const { $keys } = useNuxtApp();
const { triggerLoading } = useCommonUtils();
const {
  articlesCategories,
  postArticleCategoryAsync,
  changeCategoryNameAsync,
} = useArticlesStore();

const inputModel = ref("");
const isLoading = ref(false);
const inputRef = ref();

const validationOptions = computed(() => articlesCategories.value?.map((item) => item.name?.toLowerCase()));
const hasError = computed(() => validationOptions.value?.includes(inputModel.value?.toLowerCase()));

const closeModal = () => emit("close", false);

const postNewArticleCategoryAsync = async () => {
  isLoading.value = true;
  const isSuccess = await postArticleCategoryAsync({
    name: inputModel.value,
    order: "",
  });
  closeModal();
  triggerLoading(isSuccess ? $keys.KEY_NAMES.ARTICLE_CREATED : $keys.KEY_NAMES.ARTICLE_WRONG);
  isLoading.value = false;
};

const changeNameAsync = async () => {
  isLoading.value = true;
  const isSuccess = await changeCategoryNameAsync({
    name: inputModel.value,
    order: props.categoryOrder,
    uuid: props.categoryUUID,
  });
  closeModal();
  triggerLoading(isSuccess ? $keys.KEY_NAMES.ARTICLE_NAME_CHANGED : $keys.KEY_NAMES.ARTICLE_WRONG);
  isLoading.value = false;
};
onMounted(() => {
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus();
    }
  });
});

onKeyStroke(['Enter'], () => {
  if (!hasError.value && inputModel.value && !isLoading.value) {
    if (props.isEdit) {
      changeNameAsync();
    } else {
      postNewArticleCategoryAsync();
    }
  }
}, { dedupe: true });
</script>
