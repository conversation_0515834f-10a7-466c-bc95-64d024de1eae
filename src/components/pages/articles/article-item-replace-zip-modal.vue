<template>
  <div class="article-item-replace-zip-modal font-family-averta">
    <div class="article-item-replace-zip-modal-img">
      <img alt="replace" src="@/assets/images/replace_zip.png" width="80px" height="80px" />
    </div>
    <div class="article-item-replace-zip-modal-content">
      <div class="article-item-replace-zip-modal-title font-size-20 font-bold color-black">
        <span>Do you want to replace the file?</span>
      </div>
      <div class="font-size-12 font-normal color-red-d">
        <span>Uploading a new file will replace all article pages.<br />Requirements for the file: ZIP format (max 40 MB).</span>
      </div>
      <div class="article-item-replace-zip-modal-actions">
        <button
          type="button"
          class="btn-white btn-text-14"
          @click="closeAction"
        >
          {{ $t('BUTTONS.CANCEL_BUTTON') }}
        </button>
        <div class="article-item-replace-zip-modal-file-btn">
          <label for="file" class="btn-green">{{ $t('BUTTONS.REPLACE') }}</label>
          <input
            id="file"
            type="file"
            class="visibility-hidden"
            title="REPLACE"
            @click="cleanEventTargetValue($event)"
            @change="checkReplacedZipFile"
            accept=".zip"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { cleanEventTargetValue } from "../../../utils/clean-event-target-value.js";

const emit = defineEmits(["callback", "close"]);

const closeAction = () => emit("close", false);
const checkReplacedZipFile = (event) => emit("callback", event);
</script>
