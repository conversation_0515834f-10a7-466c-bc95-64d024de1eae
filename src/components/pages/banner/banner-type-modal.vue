<template>
  <div class="select-banner-modal">
    <div class="select-banner-modal-content">
      <div class="font-bold font-size-20 display-3 color-black">
        Please select a type of Banner
      </div>
      <div class="select-banner-modal-checkbox">
        <div class="banner-text color-black font-size-base font-bold">Banner:</div>
        <div class="control-radio-group">
          <label
            v-for="(bannerType, index) in bannerTypes"
            :key="index"
            class="control-radio"
          >
            {{ bannerType.options }}
            <input
              type="radio"
              name="banner-type"
              :id="'banner-type-' + index"
              :value="bannerType.key"
              :checked="localSelectedType === bannerType.key"
              @change="selectBanner(bannerType.key)"
            />
            <span class="checkmark"></span>
          </label>
        </div>
      </div>
      <div class="select-banner-modal-btn-container">
        <div class="btn-green-outline" @click="$emit('close', false)">
          {{ $t('BUTTONS.CANCEL_BUTTON') }}
        </div>
        <div class="btn-green" @click="confirmSelection">
          {{ $t('COMMON.NEXT') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useNuxtApp } from '#app';

const { $t } = useNuxtApp();

const props = defineProps({
  bannerTypes: {
    type: Array,
    required: true,
    default: () => []
  },
  selectedType: {
    type: String,
    required: true,
    default: 'all text'
  }
});

const emit = defineEmits(['close', 'update:selectedType']);

const localSelectedType = ref(props.selectedType);

const selectBanner = (key) => {
  localSelectedType.value = key;
  emit('update:selectedType', key);
};

const confirmSelection = () => {
  const finalSelection = localSelectedType.value;
  emit('update:selectedType', finalSelection);
  emit('close', finalSelection);
};
</script>
