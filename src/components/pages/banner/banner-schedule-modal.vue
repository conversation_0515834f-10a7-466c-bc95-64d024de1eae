<template>
  <div class="banner-schedule-form-modal-container">
    <div class="banner-schedule-sub-container">
      <div class="banner-schedule-top-container">
        <div class="font-bold font-size-20 color-black">
          {{ modalTitle }}
        </div>
      </div>
      <div class="banner-schedule-date-picker-container">
        <CalendarPicker
          class="banner-datepicker-popup"
          :modelValue="localRange"
          @update:modelValue="updateRange"
          :isRange="true"
          :isInBanner="true"
          :startDate="startDate"
          :endDate="endDate"
          :markers="safeMarkers"
          :disabled-dates="disabledDates"
          :disableFutureDates="disableFutureDates"
        />
      </div>
      <div class="banner-schedule-bottom-container">
        <div class="banner-schedule-button-section">
          <button
            type="button"
            class="btn-green-outline"
            @click="$emit('close', false)"
          >
            {{ $t("BUTTONS.CANCEL_BUTTON") }}
          </button>
          <button
            type="button"
            class="btn-green"
            :disabled="!canSchedule"
            @click="$emit('close', true)"
          >
            {{ buttonText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useNuxtApp } from "#app";
import CalendarPicker from "@/components/calendar-picker.vue";

const { $t } = useNuxtApp();

const props = defineProps({
  range: {
    type: Object,
    required: true,
    default: () => ({ start: null, end: null }),
  },
  startDate: {
    type: String,
    default: "",
  },
  endDate: {
    type: String,
    default: "",
  },
  markers: {
    type: Array,
    default: () => [],
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
  disableFutureDates: {
    type: [Date, String, Boolean],
    default: null,
  },
  onDateChange: {
    type: Function,
    required: true,
  },
});

const emit = defineEmits(["close", "update:range"]);

const localRange = ref({ ...props.range });

const canSchedule = computed(() => {
  let hasStartDate = false;
  let hasEndDate = false;
  if (Array.isArray(localRange.value)) {
    hasStartDate = localRange.value.length > 0 && !!localRange.value[0];
    hasEndDate = localRange.value.length > 1 && !!localRange.value[1];
  } else if (localRange.value && typeof localRange.value === "object") {
    hasStartDate = !!localRange.value.start;
    hasEndDate = !!localRange.value.end;
  }

  return hasStartDate && hasEndDate;
});

const modalTitle = computed(() => {
  return "Please select date to schedule the banner";
});

const buttonText = computed(() => {
  return $t("BUTTONS.SCHEDULE");
});

const safeMarkers = computed(() => {
  if (Array.isArray(props.markers)) {
    return props.markers;
  }
  return [];
});

const updateRange = (newValue) => {
  if (Array.isArray(newValue) && newValue.length === 2) {
    const [start, end] = newValue;
    if (start && end) {
      localRange.value = { start, end };
      props.onDateChange(newValue);
      emit("update:range", localRange.value);
    }
  } else if (newValue && typeof newValue === 'object') {
    if (newValue.start && newValue.end) {
      localRange.value = { ...newValue };
      props.onDateChange([newValue.start, newValue.end]);
      emit("update:range", localRange.value);
    }
  }
};

const setRangeFromArray = (rangeArray) => {
  if (rangeArray.length >= 2 && rangeArray[0] && rangeArray[1]) {
    localRange.value = [...rangeArray];
    return true;
  }
  return false;
};

const setRangeFromObject = (rangeObject) => {
  if (rangeObject && typeof rangeObject === "object" &&
      (rangeObject.start || rangeObject.end)) {
    localRange.value = { ...rangeObject };
    return true;
  }
  return false;
};

const parseDateSafely = (dateString, errorLabel) => {
  if (!dateString) return null;

  try {
    const dateObj = new Date(dateString);
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid ${errorLabel}:`, dateString);
      return null;
    }
    return dateObj;
  } catch (error) {
    console.warn(`Error parsing ${errorLabel}:`, error);
    return null;
  }
};

const isRangeEmpty = (range) => {
  return !range || (!range.start && !range.end);
};

const initializeFromDates = () => {
  if (!isRangeEmpty(localRange.value) || (!props.startDate && !props.endDate)) {
    return;
  }

  let startDateObj = parseDateSafely(props.startDate, "start date");
  let endDateObj = parseDateSafely(props.endDate, "end date");

  if (!startDateObj && props.range?.start) {
    startDateObj = props.range.start;
  }

  if (!endDateObj && props.range?.end) {
    endDateObj = props.range.end;
  }

  if (startDateObj || endDateObj) {
    localRange.value = {
      start: startDateObj,
      end: endDateObj,
    };
  }
};

const initializeRange = () => {
  if (!props.range) {
    initializeFromDates();
    return;
  }

  if (Array.isArray(props.range) && setRangeFromArray(props.range)) {
    return;
  }

  if (!Array.isArray(props.range) && setRangeFromObject(props.range)) {
    return;
  }

  initializeFromDates();
};
onMounted(initializeRange);
</script>
