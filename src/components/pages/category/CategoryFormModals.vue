<template>
  <div>
    <select-the-language-modal
      v-if="hasRecipeVariantLanguagePopup"
      :closeModal="() => $emit('close-variant-language-popup')"
      @preventEnterAndSpaceKeyPress="(event) => event.preventDefault()"
      @nextVariantPopUp="$emit('next-variant-popup', $event)"
      @setRecipeVariantLanguageMatches="$emit('set-recipe-variant-language-matches', $event)"
      @showRecipeVariantLanguageMatches="$emit('show-recipe-variant-language-matches')"
      :recipeVariantLanguageList="recipeVariantLanguageList"
      :hasRecipeVariantLanguageResult="hasRecipeVariantLanguageResult"
    />

    <add-variant
      v-if="isAddVariantCategoryNamePopup"
      :closeModal="() => $emit('close-add-variant-popup')"
      :typeName="'Category'"
      :addVariantSelectedLanguage="recipeVariantSelectedLanguage"
      :itemName="categoriesName"
      @addConfirmVariant="$emit('add-confirm-variant', $event)"
      @preventEnterAndSpaceKeyPress="(event) => event.preventDefault()"
      @backToRoute="$emit('back-to-route')"
    />
  </div>
</template>

<script setup>
import SelectTheLanguageModal from "@/components/select-the-language.vue";
import AddVariant from "@/components/add-variant.vue";

const props = defineProps({
  hasRecipeVariantLanguagePopup: {
    type: Boolean,
    default: false,
  },
  isAddVariantCategoryNamePopup: {
    type: Boolean,
    default: false,
  },
  recipeVariantLanguageList: {
    type: Array,
    default: () => [],
  },
  hasRecipeVariantLanguageResult: {
    type: Boolean,
    default: false,
  },
  recipeVariantSelectedLanguage: {
    type: String,
    default: "",
  },
  categoriesName: {
    type: String,
    default: "",
  },
});

defineEmits([
  'close-variant-language-popup',
  'close-add-variant-popup',
  'next-variant-popup',
  'set-recipe-variant-language-matches',
  'show-recipe-variant-language-matches',
  'add-confirm-variant',
  'back-to-route'
]);
</script>
