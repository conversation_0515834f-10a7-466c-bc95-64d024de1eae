<template>
  <div class="content-header">
    <image-box
      :image-src="image"
      :loaded-image-size="imageLoadedSize"
      :uploadImagePercentage="imageUploadPercentage"
      @uploadedFile="$emit('file-upload', $event)"
    />
    <div class="content-details">
      <div class="content-details-top border-bottom-dashed">
        <name-field
          :name="name"
          @update:name="$emit('update:name', $event)"
          input-id="category-name"
          :input-placeholder="'Name your category'"
        />
        <button-with-switcher
          class="content-details-publish-btn"
          :label="$t('COMMON.PUBLISH')"
          :is-checked="status === $t('PUBLISHED')"
          @action="handlePublishToggle"
        />
      </div>

      <div class="content-settings">
        <div class="content-settings-container">
          <div class="content-settings-name">
            <span class="text-title-3">{{
              $t("RECIPE_PREVIEW.SLUG")
            }}</span>
          </div>
          <div class="content-settings-actions border-bottom-dashed">
            <name-field
              :name="slug"
              @update:name="$emit('update:slug', $event)"
              input-id="slug-category"
              @input="$emit('slug-input')"
              :isRequired="false"
            />
            <div v-if="hasSlugExist" class="slug-exist-main">
              <p class="slug-exist text-light-h4 font-size-12 color-red">
                {{ $t("COMMON.SLUG_ALREADY_EXISTS") }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="content-details-body">
        <div class="content-details-text font-size-base">
          <span class="font-bold color-grey"
            >{{ $t("CATEGORY.CATEGORY_IMAGE") }}:</span
          >
          <span class="font-normal color-grey">{{
            $t("CATEGORY.IMAGE_FORMAT")
          }}</span>
          <span class="content-details-text-mark color-red">*</span>
        </div>
        <div
          v-if="isEdit"
          :class="{
            'simple-data-tooltip simple-data-tooltip-warn':
              recipeDataForCategories.length ||
              categoryPromotedRecipes.length,
          }"
        >
          <div
            class="simple-data-tooltip-content"
            v-if="
              recipeDataForCategories.length ||
              categoryPromotedRecipes.length
            "
          >
          <IconInfoOutlineDefault
            filled
            :fontControlled="false"
            :style="iconInfoOutlineDefaultStyle"
          />
            <span>{{ $t("CATEGORY.CATEGORIES_DELETE_INFO") }}</span>
          </div>
          <button
            type="button"
            class="btn-red-text btn-small"
            @click="$emit('delete-category')"
            :class="{
              'simple-data-tooltip': isCategoryIncludeInHero,
            }"
            :disabled="
              isCategoryIncludeInHero ||
              recipeDataForCategories.length > 0 ||
              categoryPromotedRecipes.length > 0
            "
            :data-tooltip-text="
              isCategoryIncludeInHero && $t('ARTICLE_IN_HERO')
            "
          >
            <img alt="delete-icon" src="@/assets/images/delete-icon.png" />
            <span>{{ $t("CATEGORY.DELETE_CATEGORY") }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useNuxtApp } from "#app";
import ImageBox from "@/components/image-box.vue";
import NameField from "@/components/name-field.vue";
import ButtonWithSwitcher from "@/components/button-with-switcher.vue";
import IconInfoOutlineDefault from '~/assets/images/icons/info-outline-default.svg';

const { $t } = useNuxtApp();

const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  slug: {
    type: String,
    default: "",
  },
  image: {
    type: String,
    default: "",
  },
  status: {
    type: String,
    default: "",
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  categoriesState: {
    type: String,
    default: "",
  },
  hasSlugExist: {
    type: Boolean,
    default: false,
  },
  isCategoryIncludeInHero: {
    type: Boolean,
    default: false,
  },
  recipeDataForCategories: {
    type: Array,
    default: () => [],
  },
  categoryPromotedRecipes: {
    type: Array,
    default: () => [],
  },
  imageLoadedSize: {
    type: Number,
    default: 0,
  },
  imageUploadPercentage: {
    type: Number,
    default: 0,
  },
  iconInfoOutlineDefaultStyle: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits([
  'update:name',
  'update:slug',
  'update:image',
  'update:status',
  'slug-input',
  'publish-toggle',
  'publish-toggle-popup',
  'publish-toggle-confirmation',
  'delete-category',
  'file-upload'
]);

const handlePublishToggle = () => {
  const isValidForPublish = props.name?.trim() && props.image;

  if (isValidForPublish) {
    emit('publish-toggle-confirmation');
  } else {
    emit('publish-toggle-popup');
  }
};
</script>
