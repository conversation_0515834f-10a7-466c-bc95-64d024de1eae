<template>
  <simple-sticky-wrapper
    :top="70"
    :distance="-60"
    class="edit-selection-container"
  >
    <div class="edit-selection-panel">
      <div class="edit-select-all-checkbox-section">
        <label class="checkbox checkbox-20 checkbox-without-text" :for="'checkbox-' + props.index">
          <input
            type="checkbox"
            :id="'checkbox-' + props.index"
            :checked="selectionOfRecipes[0].isSelected"
            @click="$emit('select-all-matches')"
            :aria-label="'Select item ' + (props.index + 1)"
          />
          <span class="checkmark"></span>
        </label>
      </div>
      <button
        type="button"
        @click="$emit('select-all-matches')"
        class="btn-reset text-h3"
      >
        {{ $t("PAGE.RECIPES.SELECT_ALL") }}
      </button>
      <div class="edit-selection">
        <div class="edit-selected-text">
          {{ checkSelectedRecipes }} {{ $t("COMMON.SELECTED") }}
          <span
            v-if="checkSelectedRecipes > 0"
            class="edit-selected-cross-icon"
          >
            <img
              src="@/assets/images/close.svg?skipsvgo=true"
              @click="$emit('remove-all-selected')"
              alt="edit-close-icon"
            />
          </span>
        </div>
      </div>
      <div class="edit-btn-container">
        <button
          type="button"
          class="btn-red"
          :disabled="checkSelectedRecipes == 0"
          @click="$emit('delete-select')"
        >
          {{ $t("BUTTONS.REMOVE_BUTTON") }}
        </button>
        <button
          type="button"
          class="btn-green-text btn-small"
          @click="$emit('cancel-select')"
        >
          {{ $t("BUTTONS.CANCEL_BUTTON") }}
        </button>
      </div>
    </div>
  </simple-sticky-wrapper>
</template>

<script setup>
import { useNuxtApp } from "#app";
import SimpleStickyWrapper from "@/components/simple-sticky-wrapper.vue";

const { $t } = useNuxtApp();

const props = defineProps({
  selectionOfRecipes: {
    type: Array,
    default: () => [],
  },
  checkSelectedRecipes: {
    type: Number,
    default: 0,
  },
});

defineEmits([
  'select-all-matches',
  'remove-all-selected',
  'delete-select',
  'cancel-select'
]);
</script>
