<template>
  <div class="content-variant-section">
    <div class="content-variants-main">
      <div class="content-variants text-h5 font-normal">
        {{ $t("CATEGORY.CATEGORY_VARIANTS") }}:
        <div
          v-show="isCategoryAlertIcon"
          class="tag-variant-tooltip-section"
        >
          <div
            class="tooltip-main-container-for-tag-variant simple-data-tooltip"
            :data-tooltip-text="$t('CATEGORY_VARIANT_LANG_ALERT')"
          >
            <IconRedInfo
              filled
              :fontControlled="false"
              :style="iconInfoOutlineDefaultStyle"
            />
          </div>
        </div>
      </div>
      <div
        class="add-variant-section"
        :class="{
          'simple-data-tooltip': recipeVariantLanguageList.length < 1,
        }"
        :data-tooltip-text="
          recipeVariantLanguageList.length < 1 &&
          $t('COMMON.ADD_ONLY_ONE_VARIANT')
        "
      >
        <button
          type="button"
          class="btn-green-text btn-small"
          :disabled="recipeVariantLanguageList.length < 1"
          @click="$emit('open-variant-popup')"
        >
          <img alt="download" src="@/assets/images/category-add.png" />
          <span>{{ $t("BUTTONS.ADD_VARIANT") }}</span>
        </button>
      </div>
    </div>
    <div
      class="add-content-variant color-gray"
      v-if="variants.length <= 0"
    >
      {{ $t("CATEGORY.CATEGORY_VARIANTS_INFO") }}
    </div>
    <div class="content-variants-card-main" v-else>
      <template
        v-for="(categoryVariant, index) in variants"
        :key="categoryVariant.lang"
      >
        <variant-card-field
          v-if="categoryVariant?.lang !== lang"
          :model-value="categoryVariant.name"
          @update:model-value="updateVariantName(index, $event)"
          :prefix-label="displayLanguageCode(categoryVariant.lang)"
          :input-placeholder="$t('ENTER_NAME_BASED_ON_TAGS')"
          :is-delete-action-disabled="
            isDeleteVariantVisible(categoryVariant)
          "
          :delete-action-tooltip-text='$t("COMMON.CATEGORY_IS_IN_USED")'
          @input-change="$emit('input-content-changed')"
          @delete-action="$emit('delete-variant', categoryVariant, index)"
        ></variant-card-field>
      </template>
    </div>
  </div>
</template>

<script setup>
import { useNuxtApp } from "#app";
import VariantCardField from "@/components/variant-card-field/variant-card-field.vue";
import IconRedInfo from '~/assets/images/icons/red-info.svg';

const { $t } = useNuxtApp();

const props = defineProps({
  variants: {
    type: Array,
    default: () => [],
  },
  lang: {
    type: String,
    default: "",
  },
  isCategoryAlertIcon: {
    type: Boolean,
    default: false,
  },
  recipeVariantLanguageList: {
    type: Array,
    default: () => [],
  },
  iconInfoOutlineDefaultStyle: {
    type: Object,
    default: () => ({}),
  },
  categoryAssociations: {
    type: Object,
    default: () => ({}),
  },
});

const $emit = defineEmits([
  'update:variants',
  'open-variant-popup',
  'input-content-changed',
  'delete-variant'
]);

const displayLanguageCode = (item) => {
  if (item) {
    const arr = item.split("-");
    return arr[0].toUpperCase();
  }
  return "";
};

const isDeleteVariantVisible = (categoryVariant) => {
  return props.categoryAssociations[categoryVariant.lang] > 0;
};

const updateVariantName = (index, newName) => {
  const updatedVariants = [...props.variants];
  updatedVariants[index] = { ...updatedVariants[index], name: newName };
  $emit('update:variants', updatedVariants);
};
</script>
