<template>
  <div class="recipe-main-section">
    <div class="ai-generated-container">
      <div class="recipe-detail-label-section">
        <div class="recipe-variant-flag-section">
          <img
            v-if="activeFlag"
            :alt="activeFlag.alt"
            :src="activeFlag.src"
            class="recipe-flag"
          />
        </div>
        <div class="recipe-detail-label">
          <div>{{ recipeID ? $t("RECIPE_DETAIL.EDIT_RECIPE") : $t("RECIPE_DETAIL.CREATE_RECIPE") }}</div>
        </div>
        <div
          v-if="recipeVariantSelectedLanguage !== defaultLang"
          class="info-icon-main simple-data-tooltip"
          :data-tooltip-text="$t('VARIANT_LOCKED_TOOLTIP')"
        >
          <img
            alt="info"
            class="info-icon"
            src="@/assets/images/informationSymbol.png"
          />
        </div>
      </div>
      <recipeTypeBadge
       :generatorPromptData="generatorPromptData"
       :recipeData="recipeData"
       >
      </recipeTypeBadge>
    </div>
    <div class="recipe-title-section">
      <div class="recipe-title-container">
        <div class="label">
          {{ $t("RECIPE_DETAIL.TITLE") }}<span class="astrisk">*</span>
        </div>
        <div
          class="recipe-title-input"
          :class="{
            'simple-data-tooltip': isRecipeNameFocus,
          }"
          :data-tooltip-text="isRecipeNameFocus && localRecipeName"
        >
          <input
            @click="toggleDropdownOff()"
            @input="getRecipeTitle($event.target.value)"
            v-on:blur="setRecipeTitleInput()"
            @mouseover="checkEditRecipeName()"
            @mouseleave="hideRecipeNameTip()"
            @keydown="hideRecipeNameTip()"
            id="titleName"
            autocomplete="off"
            v-model.trim="localRecipeName"
            class="input"
            type="text"
            placeholder="Enter Recipe Title"
            data-test-id="add-recipe-title"
          />
        </div>
      </div>
      <div class="recipe-subtitle-section">
        <div class="label">{{ $t("RECIPE_DETAIL.SUBTITLE") }}</div>
        <div
          class="recipe-subtitle-input"
          :class="{
            'simple-data-tooltip': isRecipeSubtitleFocus,
          }"
          :data-tooltip-text="isRecipeSubtitleFocus && localRecipeSubtitle"
        >
          <input
            @click="toggleDropdownOff"
            @input="getRecipeSubTitle($event.target.value)"
            @mouseover="checkEditRecipeSubtitle()"
            @mouseleave="hideRecipeSubtitleTip()"
            v-on:blur="setRecipeSubtitleInput()"
            autocomplete="off"
            @keydown="hideRecipeSubtitleTip()"
            id="subtitleName"
            v-model.trim="localRecipeSubtitle"
            class="input subtitle"
            type="text"
            placeholder="Enter Recipe Subtitle"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, computed } from 'vue';
import recipeTypeBadge from './recipe-type-badge.vue';
import { useRefUtils } from '@/composables/useRefUtils';
import usFlagImage from '~/assets/images/us-flag.png';
import frFlagImage from '~/assets/images/france-flag.png';
import esFlagImage from '~/assets/images/spain-flag.png';

// Props
const props = defineProps({
  recipeName: {
    type: String,
    required: true,
  },
  recipeSubtitle: {
    type: String,
    required: true,
  },
  generatorPromptData: {
    type: [String, null],
    required: true,
  },
  saveButtonEnable: {
    type: Function,
    required: true,
  },
  toggleDropdownOff: {
    type: Function,
    required: true,
  },
  setRecipeTitleInput: {
    type: Function,
    required: true,
  },
  setRecipeSubtitleInput: {
    type: Function,
    required: true,
  },
  recipeID: {
    type: String,
    required: false,
  },
  recipeData: {
    type: Object,
    required: true
  },
  recipeVariantSelectedLanguage: {
    type: String,
    default: "",
  },
  defaultLang: {
    type: String,
    default: "",
  },
});
const { $keys } = useNuxtApp();
const { getRef } = useRefUtils();

const flagMap = {
  [$keys.LANGUAGE.ENGLISH]: {
    src: usFlagImage,
    alt: "us-flag",
  },
  [$keys.LANGUAGE.FRENCH]: {
    src: frFlagImage,
    alt: "france-flag",
  },
  [$keys.LANGUAGE.SPANISH]: {
    src: esFlagImage,
    alt: "spain-flag",
  },
};

// Emits
const emit = defineEmits(['update:recipeName', 'update:recipeSubtitle']);

// Refs
const localRecipeName = ref(props.recipeName);
const localRecipeSubtitle = ref(props.recipeSubtitle);
const isRecipeNameFocus = ref(false);
const isRecipeSubtitleFocus = ref(false);

const activeFlag = computed(() => {
  return flagMap[props.recipeVariantSelectedLanguage] || null;
});

// Watchers
watch(() => props.recipeName, (newVal) => {
  localRecipeName.value = newVal;
});

watch(() => props.recipeSubtitle, (newVal) => {
  localRecipeSubtitle.value = newVal;
});

// Methods
const checkEditRecipeName = () => {
  const name = getRef('titleName');
  if (name.value.scrollWidth > name.value.clientWidth && name.value !== document.activeElement && localRecipeName.value.trim().length > 0) {
    isRecipeNameFocus.value = true;
  }
};

const hideRecipeNameTip = () => {
  isRecipeNameFocus.value = false;
};

const checkEditRecipeSubtitle = () => {
  const name = getRef('subtitleName');
  if (name.value.scrollWidth > name.value.clientWidth && name.value !== document.activeElement && localRecipeSubtitle.value.trim().length > 0) {
    isRecipeSubtitleFocus.value = true;
  }
};

const hideRecipeSubtitleTip = () => {
  isRecipeSubtitleFocus.value = false;
};

const getRecipeTitle = (updatedName) => {
  localRecipeName.value = updatedName;
  emit('update:recipeName', updatedName);
  props.saveButtonEnable();
  hideRecipeNameTip();
};

const getRecipeSubTitle = (updatedName) => {
  localRecipeSubtitle.value = updatedName;
  emit('update:recipeSubtitle', updatedName);
  props.saveButtonEnable();
};
</script>