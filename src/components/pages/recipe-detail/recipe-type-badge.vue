<template>
  <div
    v-if="isCMSAIOrBatch"
    :class="{
      'simple-data-tooltip': isNotRecipe,
    }"
    :data-tooltip-text="isNotRecipe && `${$t('GENERATOR.PROMPT')}: ${generatorPromptData}`"
  >
    <div
      :class="[mainSectionClass, {
        'recipe-type-badge-is-new-format': isNewFormat,
      }]"
    >
      <img v-if="isNotRecipe" alt="info" class="ai-generated-icon" src="@/assets/images/wrong-icon-cta.png" />
      <div v-if="isNotRecipe" class="ai-generated-text">
        {{ aiGeneratedText }}
      </div>
      <div v-else class="ai-generated-container">
        <img alt="ai-generated" src="@/assets/images/ai-generated-icon.png" class="ai-generated-image" />
        <div class="ai-generated-text">
          <span v-if="isCMSAI">{{ $t('AI_GENERATED') }}</span>
          <span v-else>{{ $t('BATCH_GENERATOR.BATCH_GENERATED') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";

const props = defineProps({
  generatorPromptData: {
    type: [String, null],
    required: true,
  },
  recipeData: {
    type: Object,
    required: true
  },
  recipeProvider: {
    type: String,
  },
  isNewFormat: Boolean,
});

const { $keys } = useNuxtApp();
const { t } = useI18n();

const provider = computed(() => props.recipeData?.provider || props.recipeProvider)

const isCMSAIOrBatch = computed(() => {
  return provider.value === $keys.KEY_NAMES.CMS_AI || provider.value === $keys.KEY_NAMES.CMS_AI_BATCH;
});

const isNotRecipe = computed(() => {
  return props.generatorPromptData !== $keys.KEY_NAMES.RECIPE_LAYOUT;
});

const isCMSAI = computed(() => {
  return provider.value === $keys.KEY_NAMES.CMS_AI;
});

const mainSectionClass = computed(() => {
  return [
    props.generatorPromptData === $keys.KEY_NAMES.RECIPE_LAYOUT
      ? 'recipe-ai-generated-main-section'
      : 'ai-generated-main-section',
    provider.value === $keys.KEY_NAMES.CMS_AI_BATCH
      ? 'batch-generated'
      : '',
    provider.value === $keys.KEY_NAMES.CMS_AI_BATCH && props.generatorPromptData !== $keys.KEY_NAMES.RECIPE_LAYOUT
      ? 'batch-generated-icon-height'
      : ''
  ];
});

const aiGeneratedText = computed(() => {
  return provider.value === $keys.KEY_NAMES.CMS_AI_BATCH
    ? t("BATCH_GENERATOR.BATCH_GENERATED")
    : t("AI_GENERATED");
});
</script>
