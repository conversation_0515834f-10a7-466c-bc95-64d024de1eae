<template>
  <div class="recipe-generate-main-container">
    <div class="top-section">
      <div class="heading-text">{{ $t("COMMON.ADD_GENERATED_IMAGE") }}</div>
      <div class="close-button-section">
        <button
          class="btn-reset"
          type="button"
          @click="closeRecipeImageGeneratorPopup"
        >
          <img
            alt="close"
            class="close-icon"
            src="@/assets/images/exit-gray.png"
          />
        </button>
      </div>
    </div>
    <div class="middle-section">
      <div class="selection-count-section">
        <span v-if="!isRecipeImageGenerating" class="text text-light-h3">
          {{ $t("COMMON.MAXIMUM") }}: {{ maximumRecipeImageToBeSelected }} /
          {{ $t("COMMON.SELECTED") }} {{ selectedRecipeImageCount + recipeImageListLength }}</span
        >
      </div>
      <div class="button-section">
        <button
          type="button"
          class="btn-green button"
          :class="{ disabled: isRecipeImageGenerating || selectedRecipeImageCount === 0 || addButtonEnabled }"
          @click="addRecipeImage()"
        >
          {{ $t("COMMON.ADD") }}
        </button>
      </div>
    </div>
    <div class="bottom-section">
      <recipe-image-generator
        :isImageLoading="isRecipeImageGenerating"
        :recipeImageListLength="recipeImageListLength"
        :selectedRecipeImageCount="selectedRecipeImageCount"
      ></recipe-image-generator>
    </div>
  </div>
</template>
<script setup>
import { computed } from "vue";
import { useStore } from "vuex";
import recipeImageGenerator from "./recipe-image-generator.vue";

const addButtonEnabled = ref(false)
const props = defineProps({
  isRecipeImageGenerating: {
    type: Boolean,
    default: false,
  },
  recipeImageListLength: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits([
  "closeRecipeImageGeneratorPopup",
  "addSelectedGeneratedRecipeImage",
]);

const maximumRecipeImageToBeSelected = 20;

const store = useStore();

const selectedRecipeImageCount = computed(() => {
  return store.getters["recipeDetails/getSelectedImageCount"];
});

function closeRecipeImageGeneratorPopup() {
  emit("closeRecipeImageGeneratorPopup");
}

function addRecipeImage() {
  const imageList = store.getters["recipeDetails/getSelectedRecipeImage"];
  emit("addSelectedGeneratedRecipeImage", imageList);
  addButtonEnabled.value = true;
}
</script>
