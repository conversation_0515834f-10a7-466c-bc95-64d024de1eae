<template>
  <div class="media-upload-main-container">
    <div class="top-section">
      <div class="heading-text">
        {{
          uploadMediaName === "image"
            ? $t("COMMON.ADD_EXTERNAL_IMAGE")
            : checkVideoPresent
            ? $t("COMMON.REPLACE_VIDEO")
            : $t("COMMON.ADD_VIDEO")
        }}
      </div>
      <div class="close-button-section">
        <button
          class="btn-reset"
          type="button"
          @click="closeUploadMediaPopup"
        >
          <img
            alt="close"
            class="close-icon"
            src="@/assets/images/exit-gray.png"
          />
        </button>
      </div>
    </div>
    <div class="middle-section">
      <div class="input-link-section">
        <div class="link-text">{{ $t("COMMON.LINK") }}:</div>
        <div class="input-zone">
          <input
            type="text"
            placeholder="Enter Link"
            class="input-field"
            v-model.trim="inputLink"
            autocomplete="off"
            @input="URLValidatorCheck()"
          />
          <span
            v-if="!isValidURLlink && inputLink"
            class="invalid-url-message"
            >{{ $t("COMMON.INVALID_URL") }}</span
          >
        </div>
      </div>
      <div class="add-button-section">
        <button
          class="btn-green"
          :class="{ disabled: !inputLink || !isValidURLlink }"
          @click="addMedia()"
        >
          {{ $t("COMMON.ADD") }}
        </button>
      </div>
    </div>
    <div class="bottom-section" @dragover="dragover" @drop="drop">
      <div class="inner-section">
        <div class="text-section">
          <div class="text text-title-2">
            {{ $t(dragFileText) }}
          </div>
          <div class="text text-title-2">{{ $t("COMMON.OR") }}</div>
        </div>
        <div class="upload-file-button">
          <label for="file" class="add-btn"></label>
          <button
            class="btn-green"
            type="button"
            @click="triggerFileInput"
          >
            {{ $t("COMMON.CHOOSE_FILE") }}
          </button>
          <input
            id="file"
            type="file"
            class="upload-input"
            title="Choose File"
            accept="image/*,video/*"
            ref="fileInput"
            @change="handleFileSelection"
            @click="uploadSameFileCheck($event)"
          />
        </div>
      </div>
    </div>
    <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
    />
    <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE')"
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
    />
    <sizeLimit
      v-if="isMaxVideoPopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_VIDEO_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_VIDEO')"
      :closeModal="closeModal"
      :isMaxVideoPopupVisible="isMaxVideoPopupVisible"
    />
    <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && !$nuxt.isOffline"
      :closeModal="closeModal"
      :acceptedFile="acceptedFile"
      :video="false"
      :image="true"
      :zip="false"
    />
    <invalidImageVideoPopup
      v-show="isInvalidVideoModalVisible && !$nuxt.isOffline"
      :closeModal="closeModal"
      :acceptedFile="acceptedFile"
      :fileType="$t('COMMON.INVALID_VIDEO_FILE')"
      :video="true"
      :image="false"
      :zip="false"
    />
  </div>
</template>
<script setup>
import { ref, computed,getCurrentInstance } from 'vue';
import { useRefUtils } from '@/composables/useRefUtils';
import sizeLimit from "@/components/size-limit.vue";
import invalidImageVideoPopup from "../../invalid-image-video-popup.vue";


// Props
const props = defineProps({
  uploadMediaName: {
    type: String,
    default: "",
  },
  checkVideoPresent: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(['closeUploadMediaPopup', 'uploadImageFile', 'uploadVideoFiles', 'addButtonImageURLlink', 'addButtonURLlink']);
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { getRef } = useRefUtils();
// State variables
const inputLink = ref("");
const isDragging = ref(false);
const isValidURLlink = ref(false);
const file = ref(null);
const acceptedFile = ref("");
const isUploadingImagePopup = ref(false);
const isMaxImagePopupVisible = ref(false);
const isMaxVideoPopupVisible = ref(false);
const isInvalidImageModalVisible = ref(false);
const isInvalidVideoModalVisible = ref(false);

// Utilities and Computed
const dragFileText = computed(() => {
  return props.uploadMediaName === $keys.KEY_NAMES.VIDEO
    ? "COMMON.DRAG_VIDEO_FILE"
    : "COMMON.DRAG_FILE";
});

// Methods
function closeUploadMediaPopup() {
  emit("closeUploadMediaPopup");
}

function triggerFileInput() {
  getRef("file").click();
}

function uploadSameFileCheck(event) {
  event.target.value = "";
}

function URLValidatorCheck() {
  isValidURLlink.value = isValidURL(inputLink.value);
}

function isValidURL(url) {
  const mediaExtensions = {
    image: [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"],
    video: [".mp4", ".webm", ".ogg"],
  };

  const currentMediaExtensions = mediaExtensions[props.uploadMediaName];

  if (!currentMediaExtensions) return false;

  try {
    const { hostname, pathname } = new URL(url);

    if (
      props.uploadMediaName === $keys.KEY_NAMES.VIDEO &&
      hostname.includes("youtube")
    ) {
      return true;
    }

    const isValid = currentMediaExtensions.some((extension) =>
      pathname.toLowerCase().endsWith(extension)
    );
    return isValid;
  } catch (error) {
    console.error(`Error: ${error.message}`);
    return false;
  }
}

function dragover(e) {
  e.preventDefault();
  isDragging.value = true;
}

function drop(e) {
  e.preventDefault();

  if (!isDragging.value) return;

  isDragging.value = false;

  const files = e.dataTransfer?.files;
  if (!files?.length || handleMultipleFiles(files)) return;

  handleFileSelection(e);
}

function handleMultipleFiles(files) {
  return files?.length > 1;
}

function continueImage() {
  if (file.value) {
    emit("uploadImageFile", file.value);
  }
}

function handleFileSelection(event) {
  const files = event?.target?.files || event?.dataTransfer?.files;
  if (files?.length) {
    file.value = files;
    if (props.uploadMediaName === $keys.KEY_NAMES.IMAGE) {
      uploadImageFileCheck();
    } else {
      uploadVideoFileCheck();
    }
  }
}

function uploadImageFileCheck() {
  if (file.value?.length) {
    const fileName = file.value[0].name.toLowerCase();
    const reg = /^[^\\:*?"<>|\r\n]+(\.(jpe?g|png))$/i;
    const fileSize = file.value[0].size;
    const sizeInMB = fileSize / (1024 * 1024);
    if (!fileName.match(reg)) {
      acceptedFile.value = "jpg, png";
      isInvalidImageModalVisible.value = true;
      resetFileInput();
      return;
    }

    if (sizeInMB >= 15) {
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      resetFileInput();
    } else if (sizeInMB >= 1) {
      isUploadingImagePopup.value = true;
      isMaxImagePopupVisible.value = false;
    } else {
      isUploadingImagePopup.value = false;
      emit("uploadImageFile", file.value);
    }
  }
}

function uploadVideoFileCheck() {
  if (file.value?.length) {
    const fileName = file.value[0].name.toLowerCase();
    const reg = /^[^\\:*?"<>|\r\n]+\.(mp4)$/i;
    const fileSize = file.value[0].size;
    const size = parseInt(fileSize.toFixed(0));
    if (!fileName.match(reg)) {
      acceptedFile.value = "mp4";
      isInvalidVideoModalVisible.value = true;
      file.value = null;
      return;
    }
    if (size > 1024 * 1024 * 1024) {
      isMaxVideoPopupVisible.value = true;
      return;
    } else {
      emit("uploadVideoFiles", file.value);
    }
  }
}

function resetFileInput() {
  file.value = null;
  if (getRef('fileInput')) {
    getRef('fileInput').value = "";
  }
}

function addMedia() {
  if (props.uploadMediaName === $keys.KEY_NAMES.IMAGE) {
    emit("addButtonImageURLlink", inputLink.value);
  } else {
    emit("addButtonURLlink", inputLink.value);
  }
}

function closeModal() {
  file.value = null;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  isMaxVideoPopupVisible.value = false;
  isInvalidImageModalVisible.value = false;
  isInvalidVideoModalVisible.value = false;
}

</script>


