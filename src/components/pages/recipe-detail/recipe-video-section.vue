<template>
  <div class="recipe-video-main-container">
    <div
      v-if="uploadPercentage === 0 || uploadPercentage === 100"
      class="video-inner-container"
    >
      <div v-if="!isVideoPresent" class="empty-video-section">
        <media-empty-box :isRecipePage="true" :isVideo="true"></media-empty-box>
      </div>
      <div v-if="isLinkPresent || isVideoPresent" class="video-section">
        <div v-if="recipeVariantSelectedLanguage === defaultLang" class="video-top-section">
          <button
            type="button"
            class="delete-video-button btn-reset"
            @click.stop="deleteVideo()"
            :class="{ disabled: config?.isRecipeVariant }"
          >
            <img
              class="delete-button-image"
              src="@/assets/images/deleteVideoBtn.png"
              alt="Delete"
            />
          </button>
        </div>
        <img v-if="!isVideoLoaded && !isLinkPresent && !video" :src="videoLoaderImage" alt="loader video" />
        <div v-if="(isVideoLoaded && !isLinkPresent) || video" class="video-player">
          <button
            type="button"
            class="btn-reset"
            @click="openVideoPopup()"
          >
            <img src="@/assets/images/videoPlayBtn.png" alt="player" />
          </button>
        </div>
        <video
          v-if="isVideoLoaded || video"
          :src="`${video}`"
          type="video/mp4"
          id="videoIdData"
          class="recipe-video"
          ref="myVideo"
          aria-label="video"
          title="video description"
          @loadedmetadata="setVideoDimensions"
        >
          <track
            v-if="video?.subtitles"
            :src="video?.subtitles"
            kind="subtitles"
            srclang="en"
            label="English"
          />
          <track
            v-if="video?.description"
            :src="video?.description"
            kind="description"
            srclang="en"
            label="English"
          />
        </video>
        <img
          v-if="linkURLImage && isLinkPresent"
          alt="url"
          class="display-image"
          :src="linkURLImage"
        />
        <button type="button" v-if="isLinkPresent" @click="openLinkPage()" class="video-bottom-section btn-reset">
          <div class="upper-info-text context"> {{ hostNameVideoUrl }} </div>
          <div class="lower-info-text context"> {{ $t("URL_VIDEO") }} </div>
        </button>
      </div>
    </div>
    <div
      v-if="uploadPercentage > 0 && uploadPercentage < 100"
      class="video-upload-loader"
    >
      <upload-loader
        :uploadPercentage="uploadPercentage"
        :uploadSize="uploadVideoSize"
        :loadedSize="loadedVideoSize"
      />
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import uploadLoader from "@/components/upload-loader.vue";
import mediaEmptyBox from "@/components/media-empty-box.vue";
import { useRefUtils } from '@/composables/useRefUtils';


const { getRef } = useRefUtils();

const myVideo = ref(null);

// Props
const props = defineProps({
  video: {
    type: String,
    default: "",
  },
  isVideoLoaded: {
    type: Boolean,
    default: false,
  },
  uploadPercentage: {
    type: Number,
    default: 0,
  },
  uploadVideoSize: {
    type: Number,
    default: 0,
  },
  loadedVideoSize: {
    type: Number,
    default: 0,
  },
  isVideoPresent: {
    type: Boolean,
    default: false,
  },
  isLinkPresent: {
    type: Boolean,
    default: true,
  },
  hostNameVideoUrl: {
    type: String,
    default: "",
  },
  linkURLImage: {
    type: String,
    default: "",
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  recipeVariantSelectedLanguage: {
    type: String,
    default: "",
  },
  defaultLang: {
    type: String,
    default: "",
  },
  openLinkPage: {
    type: Function,
    default: () => {},
  }
});

// Emits
const emit = defineEmits();

// Refs for static assets
const placeholderImage = ref(new URL('@/assets/images/empty-video.png', import.meta.url).href);
const videoLoaderImage = ref(new URL('@/assets/images/Eclipse-1s-200px.gif', import.meta.url).href);

// Method to open video popup
const openVideoPopup = () => {
  emit('openVideoPopup');
};

// Method to delete video
const deleteVideo = () => {
  emit('deleteVideo');
};

// Method to set video dimensions
const setVideoDimensions = () => {
  const videoElement = myVideo.value;
  const videoWidth = videoElement?.videoWidth || 0;
  const videoHeight = videoElement?.videoHeight || 0;
  emit('setVideoDimensions', { videoWidth, videoHeight });
};
</script>