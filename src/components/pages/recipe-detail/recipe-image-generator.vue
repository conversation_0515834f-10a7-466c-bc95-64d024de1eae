<template>
  <div class="recipe-image-generator-main-container">
    <div class="recipe-image-inner-section">
      <div v-if="isImageLoading" class="image-selector-loading">
        <div class="image-selector-loading-body">
          <span class="image-selector-loading-icon"></span>
          <span
            class="text-title-2 font-weight-semi-bold color-graphite-gray"
            >{{ $t("COMMON.PROCESSING_IMAGES") }}</span
          >
        </div>
      </div>
      <div class="image-selector-wrapper-main-box">
        <div class="image-selector-wrapper-inner-box" ref="scrollRef">
          <template v-for="item in imageList">
            <recipe-image-selector-box
              :isLoading="isImageLoading"
              :slideData="item"
              :recipeImageListLength="recipeImageListLength"
              :selectedRecipeImageCount="selectedRecipeImageCount"
              @onSelect="selectImage"
              @onZoomInImage="zoomInImage"
            />
          </template>
        </div>
      </div>
    </div>

    <image-zoom-popup
      v-if="zoomImageSrc"
      :zoomedImage="zoomImageSrc"
      :imageList="imageList"
      :selectedImageIndex="currentImageIndex"
      @prev-image="updateZoomedImage($keys.KEY_NAMES.PREV)"
      @next-image="updateZoomedImage($keys.KEY_NAMES.NEXT)"
      @close-image-zoom="zoomOutImage"
    ></image-zoom-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import recipeImageSelectorBox from "./recipe-image-selector-box.vue";
import imageZoomPopup from "@/components/image-zoom-popup.vue";


const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

// Props
const props = defineProps({
  isImageLoading: {
    type: Boolean,
    default: false,
  },
  recipeImageListLength: {
    type: Number,
    default: 0,
  },
  selectedRecipeImageCount: {
    type: Number,
    default: 0,
  },
});

// Store
const store = useStore();

// Component setup
const gridConfig = ref({
  boxSize: 134,
  slideXGap: 21,
  slideYGap: 23,
  rows: 2,
  itemsCount: 0,
});

const zoomImageSrc = ref(undefined);
const currentImageIndex = ref(0);

// Computed properties
const imageList = computed(() => {
  const recipeImageList = store.getters["recipeDetails/getGeneratedRecipeImage"];
  if (recipeImageList?.length && !props.isImageLoading) {
    return recipeImageList;
  }
  return Array(gridConfig.value.itemsCount).fill({});
});

// Refs
const scrollRef = ref(null);

// Methods
const updateZoomedImage = (direction) => {
  const flatImageList = imageList.value?.flat() ?? [];
  const maxIndex = flatImageList.length - 1;
  currentImageIndex.value =
    direction === $keys.KEY_NAMES.NEXT
      ? Math.min(currentImageIndex.value + 1, maxIndex)
      : Math.max(currentImageIndex.value - 1, 0);

  zoomImageSrc.value = flatImageList[currentImageIndex.value]?.key;
};

const updateGridLayout = () => {
  const containerWidth = scrollRef.value.clientWidth;
  const boxSizeWithGap = gridConfig.value.boxSize + gridConfig.value.slideXGap;
  const itemsPerRow = Math.floor(containerWidth / boxSizeWithGap);
  gridConfig.value.itemsCount = itemsPerRow * gridConfig.value.rows;
};

const selectImage = ({ key }) => {
  store.dispatch("recipeDetails/selectGeneratedRecipeImage", { key });
};

const zoomInImage = (key) => {
  const flatImageList = imageList.value.flat();
  currentImageIndex.value = flatImageList.findIndex((item) => item.key === key);
  zoomImageSrc.value = key;
};

const zoomOutImage = () => {
  zoomImageSrc.value = undefined;
};

// Lifecycle hooks
onMounted(() => {
  updateGridLayout();
  window.addEventListener("resize", updateGridLayout);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", updateGridLayout);
});
</script>