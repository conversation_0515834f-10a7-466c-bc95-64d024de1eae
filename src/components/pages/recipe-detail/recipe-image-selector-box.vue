<template>
  <div
    class="image-selector-slide"
    :class="{
      __loading: isLoading,
      __selected: slideData?.isSelected,
      'image-selector-slide-zoom': slideData?.key,
    }"
    @click.stop="zoomInImage(slideData?.key)"
  >
    <img
      v-if="slideData?.key"
      :src="slideData?.key"
      alt="recipe"
      loading="lazy"
    />
    <div v-if="isMaxImageSelected && !slideData?.isSelected" class="max-limit-reached text-light-h4">
      {{ $t("COMMON.MAX_REACHED") }}<br/> {{ $t("COMMON.DESELECT_IMAGE") }}
    </div>

    <selector-button
      v-if="slideData?.key && !slideData?.isSelected && !isMaxImageSelected"
      :label="$t('GENERATOR.IMAGE_SELECTOR_SLIDE_SELECT')"
      classes="image-selector-slide-select"
      :value="slideData?.key"
      :isCenterByAbsolute="true"
      @onSelectValue="select"
    ></selector-button>

    <selector-button
      v-if="slideData?.isSelected"
      classes="image-selected"
      :value="slideData?.key"
      :isCircularButton="true"
      :isCenterByAbsolute="true"
      @onSelectValue="select"
    ></selector-button>
  </div>
</template>

<script>
import SelectorButton from "@/components/selector-button.vue";

export default {
  name: "recipe-image-selector-slide",
  components: { SelectorButton },
  props: {
    isLoading: {
      type: Boolean,
      required: false,
      default: false,
    },
    recipeImageListLength: {
      type: Number,
      required: false,
      default: 0,
    },
    selectedRecipeImageCount: {
      type: Number,
      required: false,
      default: 0,
    },

    /**
     * @type {RecipeImageObject}
     */
    slideData: {
      type: Object,
      required: true,
    },
  },
  computed: {
    isMaxImageSelected() {
      return this.selectedRecipeImageCount + this.recipeImageListLength >= 20;
    },
  },
  methods: {
    zoomInImage(key) {
      if (!key) {
        return;
      }

      this.$emit("onZoomInImage", key);
    },
    select(key) {
      if (!key) {
        return;
      }

      this.$emit("onSelect", { key });
    },
  },
};
</script>