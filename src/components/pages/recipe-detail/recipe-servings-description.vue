<template>
  <div class="recipe-servings-description">
    <div class="servings-time-section">
      <div class="serving">
        <div class="servings-section">
          <label
            for="servings-section"
            aria-label="servings-section"
            class="servings-title main-title"
            >{{ $t("COMMON.SERVINGS") }}</label
          >
          <div class="compulsory-field-available">*</div>
          <button
            type="button"
            @click="
              toggleDropdownOff();
              disableScroll();
            "
            class="btn-reset"
          >
            <input
              type="Number"
              @input="handleServingsInput"
              v-on:blur="
                getAvailableServings(availableServings, 'Serving'),
                  removeLeadingZeros($event)
              "
              @paste="handlePaste($event, 'Serving')"
              v-model="localServings"
              @keypress="restrictSpecialCharacters($event)"
              min="0"
              max="100"
              class="input-section no-scroll"
              autocomplete="off"
              :disabled="recipeVariantSelectedLanguage !== defaultLang"
            />
          </button>
        </div>
        <div class="servings-section">
          <div class="servings-title main-title yield-title">
            <div>
              {{ $t("COMMON.AVAILABLE_SERVINGS") }}
            </div>
          </div>
          <div class="compulsory-field-available">*</div>
          <button
            type="button"
            @click="toggleDropdownOff()"
            class="btn-reset"
          >
            <input
              @input="handleAvailableServingsInput"
              v-on:blur="
                getAvailableServings(availableServings, 'availableServing')
              "
              @paste="handlePaste($event, 'availableServing')"
              v-model="localAvailableServings"
              @keypress="preventNonNumericInput($event)"
              min="0"
              max="20"
              class="input-section yield-input"
              id="available-servings"
              autocomplete="off"
              :disabled="recipeVariantSelectedLanguage !== defaultLang"
            />
          </button>
        </div>
        <div class="servings-section">
          <div class="servings-title main-title yield-title">
            <div>{{ $t("COMMON.PRICE_PER_SERVING") }}</div>
          </div>
          <button
            type="button"
            @click="
              toggleDropdownOff();
              disableScroll();
            "
            class="btn-reset"
          >
            <input
              type="Number"
              v-on:blur="updatePrice()"
              @paste="handlePaste($event, 'recipesPrice')"
              @input="saveButtonEnable()"
              @keypress="restrictNumericInput($event)"
              class="input-section price-input no-scroll"
              autocomplete="off"
              id="price-per-serving"
              min="0"
              max="1000"
              v-model="localRecipePrice"
              :disabled="recipeVariantSelectedLanguage !== defaultLang"
            />
          </button>
          <div class="currency-input-section">
            <button
              type="button"
              @click="openCurrencyDropDown()"
              class="input-section currency-dropdown btn-reset"
            >
              <span class="currency-text text-title-2 font-normal">{{
                recipeCurrency[0]?.name || ""
              }}</span>
              <img
                alt=""
                class="currency-dropdown-icon"
                src="@/assets/images/arrow-right.png"
                :class="{ rotate: isCurrencyDropdownResult }"
              />
            </button>
            <ul
              v-show="isCurrencyDropdownResult"
              class="currency-autocomplete-results"
            >
              <li
                v-for="(data, index) in recipeCurrency"
                :key="index"
                class="currency-autocomplete-result text-title-2 font-normal"
                @click="selectPrice(data)"
                :class="{ 'is-active': data.isChecked }"
              >
                <span class="currency-result-name">{{ data.name }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="cook-time">
        <div class="servings-section">
          <div class="servings-title main-title yield-title">
            <div>{{ $t("COMMON.YIELD") }}</div>
          </div>
          <button
            type="button"
            @click="toggleDropdownOff()"
            class="btn-reset"
          >
            <input
              @input="saveButtonEnable()"
              @blur="updateLocalYieldData"
              class="input-section yield-input"
              autocomplete="off"
              id="yield"
              v-model="localYieldData"
              :disabled="recipeVariantSelectedLanguage !== defaultLang"
            />
          </button>
        </div>

        <div class="servings-section time-section">
          <label
            for="time-section"
            aria-label="time-section"
            class="servings-title main-title total-time"
            >{{ $t("COMMON.SUM_TIME") }}</label
          >
          <div class="time-data">
            <div class="time-hr-min">
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="
                    updateTime();
                    totalTimeChange();
                  "
                  type="Number"
                  class="input-section no-scroll"
                  id="time-hour"
                  autocomplete="off"
                  v-model="localHour"
                  min="0"
                  max="200"
                  @blur="
                    checkTimeData(minute, 'totalTimeMinute');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'totalTimehour')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-hour"
                aria-label="time-hour"
                class="servings-value"
                >{{ $t("COMMON.HOUR") }}</label
              >
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="
                    updateTime();
                    totalTimeChange();
                  "
                  type="Number"
                  class="input-section no-scroll"
                  id="time-minute"
                  autocomplete="off"
                  v-model="localMinute"
                  min="0"
                  max="59"
                  @blur="
                    checkTimeData(minute, 'totalTimeMinute');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'totalTimeMinute')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-minute"
                aria-label="time-minute"
                class="servings-value"
                >{{ $t("COMMON.MINUTE") }}</label
              >
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="
                    updateTime();
                    totalTimeChange();
                  "
                  type="Number"
                  class="input-section"
                  id="time-second"
                  autocomplete="off"
                  v-model="localSecond"
                  min="0"
                  max="59"
                  @blur="
                    checkTimeData(second, 'totalTimeSecond');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'totalTimeSecond')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-second"
                aria-label="time-second"
                class="servings-value second-label"
                >{{ $t("COMMON.SECONDS") }}</label
              >
            </div>
          </div>
        </div>
        <div class="servings-section time-section">
          <label
            for="time-section"
            aria-label="time-section"
            class="servings-title main-title total-time"
            >{{ $t("COMMON.PREP_TIME") }}</label
          >
          <div class="time-data">
            <div class="time-hr-min">
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="updateTime()"
                  type="Number"
                  class="input-section no-scroll"
                  id="time-prep-hour"
                  autocomplete="off"
                  v-model="localPrepHour"
                  min="0"
                  max="99"
                  @blur="
                    checkTimeData(prepHour, 'prepTimeHour');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'prepTimeHour')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-prep-hour"
                aria-label="time-prep-hour"
                class="servings-value"
                >{{ $t("COMMON.HOUR") }}</label
              >
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="updateTime()"
                  type="Number"
                  class="input-section no-scroll"
                  id="time-prep-minute"
                  autocomplete="off"
                  v-model="localPrepMinute"
                  min="0"
                  max="59"
                  @blur="
                    checkTimeData(prepMinute, 'prepTimeMinute');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'prepTimeMinute')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-prep-minute"
                aria-label="time-prep-minute"
                class="servings-value"
                >{{ $t("COMMON.MINUTE") }}</label
              >
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="updateTime()"
                  type="Number"
                  class="input-section"
                  id="time-prep-second"
                  autocomplete="off"
                  v-model="localPrepSecond"
                  min="0"
                  max="59"
                  @blur="
                    checkTimeData(prepSecond, 'prepTimeSecond');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'prepTimeSecond')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-prep-second"
                aria-label="time-prep-second"
                class="servings-value second-label"
                >{{ $t("COMMON.SECONDS") }}</label
              >
            </div>
          </div>
        </div>
        <div class="servings-section time-section">
          <label
            for="time-section"
            aria-label="time-section"
            class="servings-title main-title total-time"
            >{{ $t("COMMON.COOK_TIME") }}</label
          >
          <div class="time-data">
            <div class="time-hr-min">
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="updateTime()"
                  type="Number"
                  class="input-section no-scroll"
                  id="time-cook-hour"
                  autocomplete="off"
                  v-model="localCookHour"
                  min="0"
                  max="99"
                  @blur="
                    checkTimeData(cookHour, 'cookTimeHour');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'cookTimeHour')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-cook-hour"
                aria-label="time-cook-hour"
                class="servings-value"
                >{{ $t("COMMON.HOUR") }}</label
              >
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="updateTime()"
                  type="Number"
                  class="input-section no-scroll"
                  id="time-cook-minute"
                  autocomplete="off"
                  v-model="localCookMinute"
                  min="0"
                  max="59"
                  @blur="
                    checkTimeData(cookMinute, 'cookTimeMinute');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'cookTimeMinute')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-cook-minute"
                aria-label="time-cook-minute"
                class="servings-value"
                >{{ $t("COMMON.MINUTE") }}</label
              >
              <button
                type="button"
                @click="
                  toggleDropdownOff();
                  disableScroll();
                "
                class="btn-reset"
              >
                <input
                  @input="updateTime()"
                  type="Number"
                  class="input-section"
                  id="time-cook-second"
                  autocomplete="off"
                  v-model="localCookSecond"
                  min="0"
                  max="59"
                  @blur="
                    checkTimeData(cookSecond, 'cookTimeSecond');
                    calculateTotalTime();
                  "
                  @paste="handlePaste($event, 'cookTimeSecond')"
                  @keypress="restrictSpecialCharacters($event)"
                  :disabled="recipeVariantSelectedLanguage !== defaultLang"
                />
              </button>
              <label
                for="time-cook-second"
                aria-label="time-cook-second"
                class="servings-value second-label"
                >{{ $t("COMMON.SECONDS") }}</label
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="description-section">
      <p class="description-title-header text-h3">
        {{ $t("COMMON.DESCRIPTION") }}
      </p>
      <textarea
        @click="toggleDropdownOff()"
        @input="updateDescription()"
        class="description"
        id="description"
        v-model="localDescription"
        v-on:blur="setRecipeAbstractInput()"
      ></textarea>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  servings: [String, Number],
  availableServings: [String, Array],
  recipePrice: [String, Number],
  recipeCurrency: Array,
  yieldData: String,
  description: String,
  hour: [String, Number],
  minute: [String, Number],
  second: [String, Number],
  prepHour: [String, Number],
  prepMinute: [String, Number],
  prepSecond: [String, Number],
  cookHour: [String, Number],
  cookMinute: [String, Number],
  cookSecond: [String, Number],
  toggleDropdownOff: Function,
  recipeData: Object,
  recipeVariantSelectedLanguage: String,
  defaultLang: String,
  handlePaste: Function,
  saveButtonEnable: Function,
  restrictSpecialCharacters: Function,
  disableScroll: Function,
  preventNonNumericInput: Function,
  restrictNumericInput: Function,
  validateZeroInput: Function,
  checkTimeData: Function,
  calculateTotalTime: Function,
  selectPriceCurrency: Function,
  getAvailableServings: Function,
  removeLeadingZeros: Function,
  totalTimeChange: Function,
  getRecipePrice: Function,
  setRecipeAbstractInput: Function,
});

const emit = defineEmits([
  'update:recipePrice',
  'update:servings',
  'update:availableServings',
  'update:yieldData',
  'update:time',
  'update:description',
]);

// Local state
const isCurrencyDropdownResult = ref(false);
const localServings = ref(props.servings);
const localAvailableServings = ref(props.availableServings);
const localRecipePrice = ref(props.recipePrice);
const localRecipeCurrency = ref(props.recipeCurrency);
const localYieldData = ref(props.yieldData);
const localHour = ref(props.hour);
const localMinute = ref(props.minute);
const localSecond = ref(props.second);
const localPrepHour = ref(props.prepHour);
const localPrepMinute = ref(props.prepMinute);
const localPrepSecond = ref(props.prepSecond);
const localCookHour = ref(props.cookHour);
const localCookMinute = ref(props.cookMinute);
const localCookSecond = ref(props.cookSecond);
const localDescription = ref(props.description);

// Watchers to keep props and local state in sync
watch(() => props.recipePrice, (newVal) => { localRecipePrice.value = newVal });
watch(localRecipePrice, (newVal) => emit('update:recipePrice', newVal));

watch(() => props.servings, (newVal) => { localServings.value = newVal });
watch(() => props.availableServings, (newVal) => { localAvailableServings.value = newVal });
watch(() => props.recipeCurrency, (newVal) => { localRecipeCurrency.value = newVal });
watch(() => props.yieldData, (newVal) => { localYieldData.value = newVal });
watch(() => props.hour, (newVal) => { localHour.value = newVal });
watch(() => props.minute, (newVal) => { localMinute.value = newVal });
watch(() => props.second, (newVal) => { localSecond.value = newVal });
watch(() => props.prepHour, (newVal) => { localPrepHour.value = newVal });
watch(() => props.prepMinute, (newVal) => { localPrepMinute.value = newVal });
watch(() => props.prepSecond, (newVal) => { localPrepSecond.value = newVal });
watch(() => props.cookHour, (newVal) => { localCookHour.value = newVal });
watch(() => props.cookMinute, (newVal) => { localCookMinute.value = newVal });
watch(() => props.cookSecond, (newVal) => { localCookSecond.value = newVal });
watch(() => props.description, (newVal) => { localDescription.value = newVal });

// Methods
function handleServingsInput() {
  if (localServings.value > 100) {
    localServings.value = 100;
  }
  emit('update:servings', localServings.value);
  props.saveButtonEnable();
}

function updatePrice() {
  if (localRecipePrice.value > 1000) {
    localRecipePrice.value = 1000;
  }
  if (localRecipePrice.value === 'recipesPrice' && localRecipePrice.value < 0) {
    localRecipePrice.value = '';
    return;
  }
  if (localRecipePrice.value) {
    localRecipePrice.value = parseFloat(localRecipePrice.value);
  }
  emit('update:recipePrice', localRecipePrice.value);
}

function selectPrice(data) {
  isCurrencyDropdownResult.value = false;
  props.selectPriceCurrency(data);
}

function handleAvailableServingsInput() {
  emit('update:availableServings', localAvailableServings.value);
  props.saveButtonEnable();
}

function updateTime() {
  if (localHour.value > 200) {
    localHour.value = 200;
  }
  if (localMinute.value > 59) {
    localMinute.value = 59;
  }
  if (localSecond.value > 59) {
    localSecond.value = 59;
  }
  if (localPrepHour.value > 99) {
    localPrepHour.value = 99;
  }
  if (localPrepMinute.value > 59) {
    localPrepMinute.value = 59;
  }
  if (localPrepSecond.value > 59) {
    localPrepSecond.value = 59;
  }
  if (localCookHour.value > 99) {
    localCookHour.value = 99;
  }
  if (localCookMinute.value > 59) {
    localCookMinute.value = 59;
  }
  if (localCookSecond.value > 59) {
    localCookSecond.value = 59;
  }
  emit('update:time', {
    hour: localHour.value,
    minute: localMinute.value,
    second: localSecond.value,
    prepHour: localPrepHour.value,
    prepMinute: localPrepMinute.value,
    prepSecond: localPrepSecond.value,
    cookHour: localCookHour.value,
    cookMinute: localCookMinute.value,
    cookSecond: localCookSecond.value,
  });
  props.saveButtonEnable();
  props.validateZeroInput('totalTimeHour');
}

function updateDescription() {
  emit('update:description', localDescription.value);
  props.saveButtonEnable();
}

function openCurrencyDropDown() {
  if (props.recipeVariantSelectedLanguage === props.defaultLang) {
    isCurrencyDropdownResult.value = !isCurrencyDropdownResult.value;
  }
}
function updateLocalYieldData() {
  emit('update:yieldData', localYieldData.value);
}

function handleClickOutside(event) {
  if (isCurrencyDropdownResult.value) {
    if (!document.querySelector('.currency-input-section').contains(event.target)) {
      isCurrencyDropdownResult.value = false;
    }
  }
}

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

