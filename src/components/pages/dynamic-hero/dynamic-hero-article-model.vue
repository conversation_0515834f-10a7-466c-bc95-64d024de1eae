<template>
  <Modal
    :isAddArticlesMatches="isAddArticlesMatches"
    id="addRecipeMatches"
    @close="$emit('close')"
  >
    <template #addArticlesMatches>
      <div class="select-articles-hero-modal">
        <div class="select-articles-title-section">
          <div class="select-articles-title">
            {{ $t("CONTENT_FORM.SELECT_ARTICLE_TO_FORM") }}
          </div>
          <div class="select-articles-search-box">
            <input
              type="text-hero"
              class="select-articles-search-input-box"
              placeholder="Find an article"
              autocomplete="off"
              @keypress.enter="searchArticles"
              v-model.trim="localSearchQuery"
              @input="onQueryInput"
              :class="{
                'select-articles-align-search-input-box': localSearchQuery,
              }"
            />
            <img
              alt=""
              :class="
                localSearchQuery !== ''
                  ? 'select-articles-search-icon-green'
                  : 'select-articles-search-icon-green disable-icon'
              "
              @click="searchArticles"
              src="@/assets/images/search-grey.png"
            />
            <img
              alt=""
              class="select-articles-exit-search-icon"
              v-if="isSearchExitEnable"
              @click="onSearchClear"
              src="@/assets/images/exit-search.svg?skipsvgo=true"
            />
          </div>
        </div>
        <div class="select-articles-add-table-content" ref="addTable">
          <div v-if="isTableDataLoading" class="table-image-loader-hero">
            <div class="loader-hero"></div>
          </div>
          <div
            v-if="!formattedArticles.length && !isTableDataLoading"
            class="no-articles-result"
          >
            {{ $t("COMMON.NO_RESULTS") }}
          </div>
          <div
            v-show="formattedArticles.length && !isTableDataLoading"
            class="add-article-list-section"
          >
            <div class="add-article-list-group">
              <div
                class="add-article-group-box"
                v-for="(article, groupIndex) in formattedArticles"
                :key="groupIndex"
              >
                <div
                  class="add-article-group-container"
                  v-if="article?.content?.length"
                >
                  <div class="add-article-group-text">
                    <span>{{ `${groupIndex + 1}` }}. </span
                    >{{ article?.name || "" }}
                  </div>
                </div>
                <div
                  class="add-article-list-container"
                  v-if="article?.content?.length"
                >
                  <div class="add-article-table-body">
                    <div class="add-article-list-group">
                      <div
                        v-for="(item, idx) in article.content"
                        :key="idx"
                        :class="
                          item.isAdded
                            ? 'add-article-table-tr background-color'
                            : 'add-article-table-tr'
                        "
                      >
                        <div class="add-article-body-sr-no">
                          {{ `${idx + 1}` }}
                        </div>
                        <div class="add-article-body-image">
                          <div class="add-article-image-div">
                            <img
                              alt="recipe"
                              class="add-article-image"
                              :src="item?.image || defaultImage"
                            />
                          </div>
                        </div>
                        <div class="add-article-body-isin">
                          {{ item?.uuid || "" }}
                        </div>
                        <div class="add-article-body-title">
                          {{ item?.title || "" }}
                        </div>
                        <div class="add-article-body-modified">
                          {{ item?.lastUpdate ? getTime(item.lastUpdate) : "" }}
                        </div>
                        <div class="add-article-body-status">
                          <div class="add-article-status-container">
                            <div class="add-articles-btn">
                              <button
                                type="button"
                                v-if="!item.isAdded"
                                @click="$emit('add-article', item.uuid)"
                                :class="
                                  item.isDisable
                                    ? 'disabled articles-add-btn'
                                    : 'articles-add-btn'
                                "
                              >
                                {{ $t("COMMON.SELECT") }}
                              </button>
                              <button
                                type="button"
                                v-if="item.isAdded"
                                @click="$emit('selected-article')"
                                class="articles-added-btn"
                              >
                                SELECTED
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="add-articles-done-section">
            <button
              type="button"
              class="add-articles-done-btn"
              @click="$emit('done')"
            >
              {{ $t("BUTTONS.DONE_BUTTON") }}
            </button>
          </div>
      </div>
    </template>
  </Modal>
</template>

<script setup>
const props = defineProps({
  isAddArticlesMatches: Boolean,
  searchQuery: String,
  formattedArticles: Array,
  isTableDataLoading: Boolean,
  isSearchExitEnable: Boolean,
  defaultImage: String,
  searchArticles: Function,
});
const emit = defineEmits([["update:searchQuery"]]);

const getTime = (jsonTimestamp) => {
  const timestamp = jsonTimestamp * 1000;
  const date = new Date(timestamp);
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};

const localSearchQuery = ref(props.searchQuery);

const onQueryInput = (event) => {
  emit("update:searchQuery", event.target.value);
};
const onSearchClear = () => {
  emit('clear-search');
  localSearchQuery.value = "";
};
</script>
