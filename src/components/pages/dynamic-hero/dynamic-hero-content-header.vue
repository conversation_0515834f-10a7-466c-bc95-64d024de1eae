<template>
  <div class="background-image-content-section">
    <button class="back-btn" @click="handleBackClick">
      <img
        alt="back icon"
        class="back-arrow-image"
        src="@/assets/images/back-arrow.png"
      />
      <span class="back-to-hero-list">
        {{
          isPageOverviewVisible
            ? $t("OVERVIEW.BACK_TO_OVERVIEW")
            : $t("SWITCH_PAGES.BACK_TO_DYNAMIC_HERO_LIST")
        }}
      </span>
    </button>

    <div class="head-btn">
      <button
        type="button"
        v-if="isReplaceLiveHero || isLiveHeroReplaced"
        :class="{
          'save-btn': canContinue,
          'save-btn disable': !canContinue,
        }"
        @click="handleOpenReplacePopup"
      >
        {{ $t('REPLACE') }}
      </button>

      <button
        v-else
        :class="{
          'save-btn': canContinue,
          'save-btn disable': !canContinue,
        }"
        @click="handleOpenSavePopup"
      >
        {{ isHeroLive ? $t('BUTTONS.UPDATE') : $t('BUTTONS.CONTINUE') }}
      </button>

      <button class="cancel-btn" @click="handleBackClick">
        {{ $t("BUTTONS.CANCEL_BUTTON") }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  isReplaceLiveHero: { type: Boolean, default: false },
  isPageOverviewVisible: { type: Boolean, default: null },
  isHeroLive: { type: Boolean, default: false },
  isLiveHeroReplaced: { type: Boolean, default: false },
  isCampaignModified: { type: Boolean, default: false },
  contentName: { type: String, default: "" },
  contentDescriptionText: { type: String, default: "" },
  backLabel: { type: String, default: "BACK TO DYNAMIC HERO LIST" },
  checkForTextPresence: { type: [Boolean, Number], default: false },
});

const canContinue = computed(
  () =>
    props.isCampaignModified &&
    props.contentName.trim() &&
    props.contentDescriptionText.trim() &&
    !props.checkForTextPresence
);

const emit = defineEmits(["back", "openReplace", "openSave"]);

const handleBackClick = () => emit("back");
const handleOpenReplacePopup = () => emit("openReplace");
const handleOpenSavePopup = () => emit("openSave");
</script>
