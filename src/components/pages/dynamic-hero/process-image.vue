<template>
  <div class="image-section" :class="{ disable: image }">
    <div class="image-main-div">
      <div class="image-inner-container">
        <div
          class="progress-image"
          v-show="uploadImagePercentage >= 1 && uploadImagePercentage <= 99"
        >
          <div class="progress-image-content">
            <img
              v-if="currentIcon"
              :src="currentIcon"
              alt="Progress Icon"
              class="progress-icon"
            />
            <div class="upload-text">
              <div class="upload-heading" v-if="uploadImagePercentage < 99">
                Upload is in progress
              </div>
              <div class="upload-heading text-light-h4" v-else>Uploaded</div>
              <span class="upload-media text-light-h6">
                {{ (loadedImageSize / 1024000).toFixed(1) }} of
                {{ (uploadImageSize / 1024000).toFixed(1) }} MB
              </span>
            </div>
          </div>
        </div>
      </div>
      <img
        v-if="
          image &&
          (uploadImagePercentage === 0 || uploadImagePercentage === 100)
        "
        :src="image"
        alt="Uploaded"
        class="display-image-section"
      />
      <div
        class="replace-image-tag"
        v-if="uploadImagePercentage === 0 || uploadImagePercentage === 100"
      >
        <div class="hover-image">
          <input
            type="file"
            class="upload-input"
            title="Update Picture"
            @click="uploadSameImageVideo($event)"
            @change="checkUploadedFiles"
            accept=".jpg,.png,.jpeg"
            ref="productVideo"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import iconVideoUpload0 from "@/assets/images/icon-video-upload-0.svg?skipsvgo=true";
import iconVideoUpload6 from "@/assets/images/icon-video-upload-6.svg?skipsvgo=true";
import iconVideoUpload12 from "@/assets/images/icon-video-upload-12.svg?skipsvgo=true";
import iconVideoUpload18 from "@/assets/images/icon-video-upload-18.svg?skipsvgo=true";
import iconVideoUpload25 from "@/assets/images/icon-video-upload-25.svg?skipsvgo=true";
import iconVideoUpload31 from "@/assets/images/icon-video-upload-31.svg?skipsvgo=true";
import iconVideoUpload37 from "@/assets/images/icon-video-upload-37.svg?skipsvgo=true";
import iconVideoUpload42 from "@/assets/images/icon-video-upload-42.svg?skipsvgo=true";
import iconVideoUpload50 from "@/assets/images/icon-video-upload-50.svg?skipsvgo=true";
import iconVideoUpload56 from "@/assets/images/icon-video-upload-56.svg?skipsvgo=true";
import iconVideoUpload62 from "@/assets/images/icon-video-upload-62.svg?skipsvgo=true";
import iconVideoUpload68 from "@/assets/images/icon-video-upload-68.svg?skipsvgo=true";
import iconVideoUpload75 from "@/assets/images/icon-video-upload-75.svg?skipsvgo=true";
import iconVideoUpload81 from "@/assets/images/icon-video-upload-81.svg?skipsvgo=true";
import iconVideoUpload87 from "@/assets/images/icon-video-upload-87.svg?skipsvgo=true";
import iconVideoUpload93 from "@/assets/images/icon-video-upload-93.svg?skipsvgo=true";
import iconVideoUploaded from "@/assets/images/icon-video-uploaded.svg?skipsvgo=true";

const props = defineProps({
  image: String,
  uploadImagePercentage: Number,
  loadedImageSize: Number,
  uploadImageSize: Number,
  uploadSameImageVideo: Function,
  checkUploadedFiles: Function,
});

const iconMap = [
  { range: [1, 5], src: iconVideoUpload0 },
  { range: [6, 11], src: iconVideoUpload6 },
  { range: [12, 17], src: iconVideoUpload12 },
  { range: [18, 24], src: iconVideoUpload18 },
  { range: [25, 30], src: iconVideoUpload25 },
  { range: [31, 36], src: iconVideoUpload31 },
  { range: [37, 41], src: iconVideoUpload37 },
  { range: [42, 49], src: iconVideoUpload42 },
  { range: [50, 55], src: iconVideoUpload50 },
  { range: [56, 61], src: iconVideoUpload56 },
  { range: [62, 67], src: iconVideoUpload62 },
  { range: [68, 74], src: iconVideoUpload68 },
  { range: [75, 80], src: iconVideoUpload75 },
  { range: [81, 86], src: iconVideoUpload81 },
  { range: [87, 92], src: iconVideoUpload87 },
  { range: [93, 98], src: iconVideoUpload93 },
  { range: [99, 99], src: iconVideoUploaded },
];

const currentIcon = computed(() => {
  const match = iconMap.find(
    ({ range }) =>
      props.uploadImagePercentage >= range[0] &&
      props.uploadImagePercentage <= range[1]
  );
  return match ? match.src : null;
});
</script>
