<template>
  <div class="background-image-event-section">
    <img alt="background" class="background-image" :src="imageSrc" />

    <div class="back-btn" @click="handleBack">
      <img
        alt="back"
        class="back-arrow-image"
        src="@/assets/images/back-arrow.png"
      />
      <span class="back-to-hero-list">
        {{
          isPageOverviewVisible
            ? $t("OVERVIEW.BACK_TO_OVERVIEW")
            : $t("SWITCH_PAGES.BACK_TO_DYNAMIC_HERO_LIST")
        }}
      </span>
    </div>

    <div class="head-btn">
      <button
        v-if="!isReplaceLiveHero"
        :class="{
          'save-btn': canSave,
          'disable save-btn': !canSave,
        }"
        @click="saveAction"
        @keydown="preventEnterAndSpaceKeyPress"
      >
        {{ isLiveHero ? $t('BUTTONS.UPDATE') : $t('BUTTONS.CONTINUE') }}
      </button>

      <button
        v-if="isReplaceLiveHero"
        :class="{
          'save-btn': canReplace,
          'disable save-btn': !canReplace,
        }"
        @click="replaceAction"
        @keydown="preventEnterAndSpaceKeyPress"
      >
        {{ replaceButtonText }}
      </button>

      <button type="button" class="cancel-btn" @click="handleBack">
        {{ $t("BUTTONS.CANCEL_BUTTON") }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  imageSrc: String,
  isReplaceLiveHero: Boolean,
  isPageOverviewVisible: Boolean,
  replaceButtonText: String,
  isCTALinkIsValid: Boolean,
  eventCTALinkText: String,
  uploadImagePercentage: Number,
  isCampaignModified: Boolean,
  eventName: String,
  subtext: String,
  description: String,
  eventDate: String,
  isLiveHero: Boolean,
});
const { preventEnterAndSpaceKeyPress } = useEventUtils();
const canSave = computed(
  () =>
    props.imageSrc &&
    props.eventName.trim() &&
    props.subtext.trim() &&
    props.description.trim() &&
    props.eventDate.trim() &&
    (props.isCTALinkIsValid || !props.eventCTALinkText.trim()) &&
    (props.uploadImagePercentage === 0 ||
      props.uploadImagePercentage === 100) &&
    props.isCampaignModified
);
const emit = defineEmits([
  "saveAction",
  "replaceAction",
  "backToDynamicHeroList",
]);
const canReplace = computed(() => canSave.value);

const saveAction = () => {
  emit("saveAction");
};

const replaceAction = () => {
  emit("replaceAction");
};

const handleBack = () => {
  emit("backToDynamicHeroList");
};
</script>
