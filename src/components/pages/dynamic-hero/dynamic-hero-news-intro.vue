<template>
  <div class="news-intro-container">
    <div class="news-intro-inner-container">
      <div>
        <img
          alt="news icon"
          class="news-icon"
          :src="newsIcon"
        />
      </div>
      <div class="news-heading">{{ title }}</div>
    </div>
    <div class="news-date-picker-container">
      <div class="start-date-text">{{ startDateLabel }}</div>
      <CalendarPicker
        :model-value="selectedDate"
        :start-date="selectedDate"
        :isRange="false"
        :markers="markers"
        :disabled-dates="disabledDates"
        @update:model-value="handleDateClick"
        :isHeroLive="isLiveHero"
        :isLiveHeroReplaced="isLiveHeroReplaced"
      />
    </div>
  </div>
</template>

<script setup>
import newsIcon from "@/assets/images/news-icon.png";

const props = defineProps({
  title: String,
  startDateLabel: String,
  isLiveHero: <PERSON>olean,
  isLiveHeroReplaced: Boolean,
  markers: Array,
  disabledDates: Array,
  selectedDate: [Date, Array, String, Object],
});

const emit = defineEmits([ "update:selectedDate", "date-click", "open-calendar"]);

const handleDateClick = (date) => {
  emit("update:selectedDate", date);
  emit("date-click", date);
};
</script>
