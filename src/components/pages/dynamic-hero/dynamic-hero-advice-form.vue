<template>
  <div class="advice-input">
    <div class="dynamic-hero-advice-and-publish-section">
      <div
        class="advice-input-container"
        :class="{
          'simple-data-tooltip': props.isAdviceNameFocus,
        }"
        :data-tooltip-text="props.isAdviceNameFocus && localAdviceName"
      >
        <input
          class="advice-input-text text-title-1 font-bold"
          id="adviceNameField"
          v-model.trim="localAdviceName"
          placeholder="Advice name in CMS"
          autocomplete="off"
          @mouseover="checkAdviceName()"
          @keydown="hideAdviceTip()"
          @mouseleave="hideAdviceTip()"
          @input="onInputChange()"
        />
        <span v-if="!localAdviceName" class="asterisk-input">*</span>
      </div>
      <div
        v-if="!props.isReplaceLiveHero && !props.isLiveHero"
        class="publish-button-container"
      >
        <div class="publish-btn">
          <span :class="!props.selectedDate ? 'text disabled-text' : 'text'">
            {{ $t("DYNAMIC_HERO.SCHEDULE") }}
          </span>
          <label
            class="switch"
            :class="{
              'simple-data-tooltip simple-data-tooltip-edge': !props.selectedDate,
            }"
            :data-tooltip-text="!props.selectedDate && $t('DYNAMIC_HERO.HERO_SCHEDULE_TOOLTIP')"
          >
            <input
              @click="toggleAdviceStatus"
              :disabled="!props.selectedDate"
              type="checkbox"
              :checked="props.selectedDate != 0 && props.isAdviceStatusVisible"
            />
            <span
              :class="
                props.selectedDate == ''
                  ? 'slider-round disabled-slider'
                  : 'slider-round'
              "
            ></span>
          </label>
        </div>
      </div>
    </div>
    <div class="advice-image-format-and-delete-container">
      <div class="advice-image-format-text">
        <b>Advice image:</b> jpg/ jpeg/ png format (max 15 MB)
        <span class="warning-asterisk-input">*</span>
      </div>
      <div
        v-if="!props.isReplaceLiveHero && !props.isLiveHero && props.isEditPage"
        class="delete-advice-section"
      >
        <div class="delete" @click="deleteEvent()">
          <img
            alt="delete"
            src="@/assets/images/delete-icon.png"
            width="15"
            height="16"
          />
          <span>{{ $t("DYNAMIC_HERO.DELETE_ADVICE") }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  adviceName: {
    type: String,
    default: "",
  },
  selectedDate: {
    type: [Date, Array, String, Object],
    default: null
  },
  isAdviceStatusVisible: {
    type: Boolean,
    default: false,
  },
  isReplaceLiveHero: {
    type: Boolean,
    default: false,
  },
  isLiveHero: {
    type: Boolean,
    default: false,
  },
  isAdviceNameFocus: {
    type: Boolean,
    default: false,
  },
  isEditPage: {
    type: Boolean,
    default: false,
  },
  deleteEvent: {
    type: Function,
    default: () => {},
  },
  hideAdviceTip: {
    type: Function,
    default: () => {},
  },
  checkAdviceName: {
    type: Function,
    default: () => {},
  },
});

const emit = defineEmits(["update:adviceName", "scheduleToggle"]);
const localAdviceName = ref(props.adviceName);

const onInputChange = () => {
  emit("update:adviceName", localAdviceName.value);
};

const toggleAdviceStatus = (event) => {
  emit("scheduleToggle", event.target.checked);
};
</script>
