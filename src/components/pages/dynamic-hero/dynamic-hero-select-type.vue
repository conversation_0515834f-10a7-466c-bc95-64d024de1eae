<template>
  <div class="dynamic-hero-select-type-modal font-family-averta">
    <div class="text-h2 font-size-20">{{ $t('DYNAMIC_HERO.DYNAMIC_HERO_TYPES') }}</div>

    <div class="dynamic-hero-select-type-modal-body">
      <template v-for="(item, index) in heroTypesList" :key="index">
        <label class="control-radio dynamic-hero-label">
          <span class="font-size-base color-black">{{ item.options }}</span>
          <input
            type="radio"
            :value="item.key"
            v-model="internalSelectedDynamicType"
          />
          <span class="checkmark"></span>
        </label>
      </template>
    </div>

    <div class="dynamic-hero-select-type-modal-actions">
      <button
        type="button"
        class="btn-white"
        @click="closeModal"
      >
        {{ $t('BUTTONS.CANCEL_BUTTON') }}
      </button>
      <button
        type="button"
        class="btn-green"
        @click="selectDynamicHero"
      >
        {{ $t('COMMON.NEXT') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  defaultValue: {
    type: String,
    required: true,
  },
  heroTypesList: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['close', 'callback']);

const internalSelectedDynamicType = ref(props.defaultValue);

const closeModal = () => {
  emit('close', false);
};

const selectDynamicHero = (key) => {
  emit('callback', internalSelectedDynamicType.value);
};
</script>
