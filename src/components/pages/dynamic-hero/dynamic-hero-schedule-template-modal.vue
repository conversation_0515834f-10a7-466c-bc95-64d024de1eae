<template>
  <div class="dynamic-hero-schedule-template-modal">
    <div class="text-h2 font-size-20">
      Please select a date to schedule the {{ heroName }}
    </div>
    <div class="dynamic-hero-schedule-template-modal-body">
      <CalendarPicker
        class="hero-datepicker-popup"
        v-model="datePickerValue"
        @update:model-value="handleDateClick"
        :isRange="false"
        :markers="markers"
        :disabled-dates="disabledDates"
      />
    </div>
    <div class="dynamic-hero-schedule-template-modal-actions">
      <button
        type="button"
        class="btn-white"
        @click="closeModal"
      >
        {{ $t("BUTTONS.CANCEL_BUTTON") }}
      </button>
      <button
        type="button"
        class="btn-green"
        :disabled="!datePickerValue"
        @click="scheduledHero"
      >
        {{ $t('DYNAMIC_HERO.SCHEDULE') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">

import { ref } from "vue";
import CalendarPicker from "../../calendar-picker.vue";

const props = defineProps({
  heroName: {
    type: String,
    required: true,
  },
  markers: {
    type: Array,
    required: false,
    default: () => [],
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['close', 'callback']);

const selectedDate = ref(null);
const datePickerValue = ref();

const getFormattedDate = (val) => {
  if (!val) return null;
  const date = new Date(val);
  return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
};
const handleDateClick = (val) => {
  selectedDate.value = new Date(val);
};
const closeModal = () => emit('close', false);
const scheduledHero = () => {
  emit('callback', {
    date: selectedDate.value,
    formattedDate: getFormattedDate(selectedDate.value),
  });
};
</script>
