<template>
  <div v-if="!props.isPageLoading" class="form-container-dynamic">
    <div class="recipe-variant-notes-text-container-dynamic">
      <div class="form-title-dynamic">
        <p class="form-title-header-dynamic">
          Text<span class="compulsory-field">*</span>
        </p>
      </div>

      <div v-if="!props.isLiveHero" class="preview-section-dynamic">
        <div class="text-section-prev">
          {{ $t("DYNAMIC_HERO.HERO_PREVIEW") }}
        </div>
        <label
          class="switch"
          :class="{
            'simple-data-tooltip simple-data-tooltip-edge': !isHeroPreview
          }"
          :data-tooltip-text="!isHeroPreview && $t('DYNAMIC_HERO.HERO_PREVIEW_TEXT')"
        >
          <input
            @change="handleHeroPreviewToggle"
            :checked="isHeroPreview"
            type="checkbox"
          />
          <span class="slider-round"></span>
        </label>
      </div>
    </div>

    <div class="description-section-dynamic" id="news_section_scroll">
      <textarea
        @input="updateAdviceText"
        class="description-notes-dynamic"
        id="adviceTextField"
        v-model="adviceTextInput"
        maxlength="200"
      ></textarea>
      <div v-if="adviceTextInput.length" class="description-length">
        {{ adviceTextInput.length }}/200
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  isPageLoading: {
    type: Boolean,
    default: false,
  },
  isLiveHero: {
    type: Boolean,
    default: false,
  },
  adviceText: {
    type: String,
    default: "",
  },
  isHeroPreview: {
    type: Boolean,
    default: false,
  },
  checkAdviceText: {
    type: Function,
    default: () => {},
  },
});

const emit = defineEmits([
  "update:adviceText",
  "update:isHeroPreview",
]);

const adviceTextInput = ref(props.adviceText);
const isHeroPreview = ref(props.isHeroPreview);

const updateAdviceText = (event) => {
  emit("update:adviceText", adviceTextInput.value);
  props.checkAdviceText();
};

const handleHeroPreviewToggle = () => {
  emit("update:isHeroPreview", !props.isHeroPreview);
};
</script>
