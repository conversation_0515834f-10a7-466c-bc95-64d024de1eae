<template>
  <div :class="baseClass + '-content-intro-container'">
    <div class="main-section">
      <div>
        <img
          alt="content"
          :class="baseClass + '-content-icon'"
          src="@/assets/images/content-icon.png"
        />
      </div>
      <div :class="baseClass + '-content-heading'">
        {{ heading }}
      </div>
    </div>
    <div class="news-date-picker-container">
      <div class="start-date-text">
        {{ startDateText }}
      </div>
      <CalendarPicker
        :model-value="selectedDate"
        :start-date="selectedDate"
        :isRange="false"
        :markers="markers"
        :disabled-dates="disabledDates"
        :isHeroLive="isHeroLive"
        :isLiveHeroReplaced="isLiveHeroReplaced"
        @update:model-value="handleDateClick"
      />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  heading: { type: String, required: true },
  startDateText: { type: String, required: true },
  markers: { type: Array, default: () => [] },
  isHeroLive: { type: Boolean, required: true },
  isLiveHeroReplaced: { type: Boolean, required: true },
  disabledDates: { type: Array, default: () => [] },
  baseClass: { type: String, default: "add" },
  selectedDate: {
    type: [Date, Array, String, Object],
    default: null
  }
});

const emit = defineEmits(["update:selectedDate", "date-click", "open-calendar"]);

const handleDateClick = (date) => {
  emit("update:selectedDate", date);
  emit("date-click", date);
};
</script>
