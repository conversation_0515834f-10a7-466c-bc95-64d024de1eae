<template>
  <Modal v-if="isVisible" @close="closeModal">
    <template #quizForm>
      <div class="quiz-schedule-form-modal-containerr">
        <div class="quiz-schedule-sub-container">
          <div class="quiz-schedule-top-container">
            <div class="text-h5 font-bold color-black">
              {{ $t('DYNAMIC_HERO.CONFIRM_SCHEDULE') }}
              {{
                isDynamicMasterPage ? heroData.template : heroData[0].template
              }}
            </div>
            <div @click="closeModal" class="close-icon">
              <img
                alt="close"
                class="close-icon-image"
                src="@/assets/images/exit-gray.png"
              />
            </div>
          </div>

          <div class="event-quiz-schedule-date-picker-container">
            <CalendarPicker
              class="hero-datepicker-popup"
              :model-value="datePickerValue"
              @update:model-value="handleDateClick"
              :isRange="false"
              :markers="markers"
              :disabled-dates="disabledDates"
            />
          </div>
          <div class="quiz-schedule-bottom-container">
            <div class="quiz-schedule-button-section">
              <button
                type="button"
                class="quiz-form-cancel-button"
                @click="closeModal"
              >
                {{ $t("BUTTONS.CANCEL_BUTTON") }}
              </button>
              <button
                type="button"
                :class="
                  datePickerValue || isScheduleDateSelected
                    ? 'quiz-form-schedule-button'
                    : 'quiz-form-schedule-button disable-schedule-button'
                "
                @click="PatchScheduledHero()"
              >
                {{ $t('DYNAMIC_HERO.SCHEDULE') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { ref, computed } from "vue";

// Props
const props = defineProps({
  isVisible: Boolean,
  heroData: {
    type: Object,
    required: true,
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
  PatchScheduledHero: {
    type: Function,
    required: true,
  },
  selectedDateValue: {
    type: [Date, Array, String, Object, Number],
    required: false,
    default: null,
  },
  isDynamicMasterPage: {
    type: Boolean,
    default: false,
  },
  isScheduleDateSelected: {
    type: Boolean,
    default: false,
  },
  markers: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["close", "update:selectedDateValue", "date-click", "update:selectedDate"]);

const selectedDate = ref(null);

const formattedDate = computed(() => {
  if (!selectedDate.value) return null;
  const date = new Date(selectedDate.value);
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
});

const datePickerValue = computed({
  get: () => {
    return formattedDate.value || props.selectedDateValue;
  },
  set: (newValue) => {
    if (props.selectedDateValue !== undefined) {
      emit("update:selectedDateValue", newValue);
    } else {
      selectedDate.value = new Date(newValue);
    }
  },
});
const handleDateClick = (date) => {
  emit('date-click', date);
  emit("update:selectedDate", date);
};
const closeModal = () => {
  selectedDate.value = null;
  emit("close");
};

</script>
