<template>
  <div class="background-image-advice-section">
    <img alt="" class="background-image" :src="image" />
    <div class="back-btn" @click="handleBack">
      <img
        alt="back"
        class="back-arrow-image"
        src="~/assets/images/back-arrow.png"
      />
      <span v-if="isPageOverviewVisible" class="back-to-hero-list">{{
        $t("OVERVIEW.BACK_TO_OVERVIEW")
      }}</span>
      <span v-else class="back-to-hero-list">{{
        $t("SWITCH_PAGES.BACK_TO_DYNAMIC_HERO_LIST")
      }}</span>
    </div>
    <div class="head-btn">
      <button
        v-if="props.isReplaceLiveHero"
        :class="{
          'save-btn': true,
          disable: !canReplace,
        }"
        @click="handleReplace"
        @keydown="preventEnterAndSpaceKeyPress"
      >
        {{ $t("REPLACE") }}
      </button>
      <button
        v-if="props.isLiveHero && !props.isReplaceLiveHero"
        :class="{
          'save-btn': true,
          disable: !canUpdate,
        }"
        @click="handleSave"
        @keydown="preventEnterAndSpaceKeyPress"
      >
        {{ $t("COMMON.UPDATE") }}
      </button>
      <button
        v-if="!props.isLiveHero && !props.isReplaceLiveHero"
        :class="{
          'save-btn': true,
          disable: !canContinue,
        }"
        @click="handleSave"
        @keydown="preventEnterAndSpaceKeyPress"
      >
        {{ $t("BUTTONS.CONTINUE") }}
      </button>
      <button type="button" class="cancel-btn" @click="handleBack">
        {{ $t("BUTTONS.CANCEL_BUTTON") }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  image: {
    type: String,
    required: true,
  },
  isPageOverviewVisible: {
    type: Boolean,
    default: false,
  },
  isReplaceLiveHero: {
    type: Boolean,
    default: false,
  },
  isLiveHero: {
    type: Boolean,
    default: false,
  },
  isCampaignModified: {
    type: Boolean,
    default: false,
  },
  adviceName: {
    type: String,
    default: "",
  },
  adviceText: {
    type: String,
    default: "",
  },
  uploadImagePercentage: {
    type: Number,
    default: 0,
  },
});

const { preventEnterAndSpaceKeyPress } = useEventUtils();

const emit = defineEmits(["back", "save", "replace"]);

const canReplace = computed(
  () =>
    props.isCampaignModified &&
    props.adviceName.trim() &&
    props.adviceText.trim() &&
    props.image &&
    (props.uploadImagePercentage === 0 || props.uploadImagePercentage === 100)
);
const canUpdate = computed(
  () =>
    props.isCampaignModified &&
    props.adviceName.trim() &&
    props.adviceText.trim() &&
    props.image &&
    (props.uploadImagePercentage === 0 || props.uploadImagePercentage === 100)
);
const canContinue = computed(() => canUpdate.value);

const handleBack = () => {
  emit("back");
};
const handleSave = () => {
  emit("save");
};
const handleReplace = () => {
  emit("replace");
};
</script>
