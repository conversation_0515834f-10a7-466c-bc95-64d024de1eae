<template>
  <div class="advice-intro-container">
    <div class="advice-intro-inner-container">
      <div>
        <img
          alt="advice icon"
          class="advice-icon"
          src="@/assets/images/advice-icon.png"
        />
      </div>
      <div class="advice-heading">{{ title }}</div>
    </div>
    <div class="advice-date-picker-container">
      <div class="start-date-text">{{ startDateLabel }}</div>
      <CalendarPicker
        :model-value="selectedDate"
        :start-date="selectedDate"
        :isRange="false"
        :markers="markers"
        :isHeroLive="isLiveHero"
        :isLiveHeroReplaced="isLiveHeroReplaced"
        :disabled-dates="disabledDates"
        @update:model-value="handleDateClick"
      />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  startDateLabel: {
    type: String,
    required: true,
  },
  markers: {
    type: Array,
    required: false,
  },
  selectedDate: {
    type: [Date, Array, String, Object],
    default: null
  },
  disabledDates: {
    type: Array,
    required: false,
  },
  isLiveHero: {
    type: Boolean,
    required: true,
  },
  isLiveHeroReplaced: {
    type: Boolean,
    required: true,
  }
});

const emit = defineEmits(["update:selectedDate", 'date-click', "open-calendar"]);

const handleDateClick = (date) => {
  emit('date-click', date);
  emit("update:selectedDate", date);
};
</script>
