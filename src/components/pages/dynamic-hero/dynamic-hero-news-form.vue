<template>
  <div class="news-input">
    <div class="dynamic-hero-news-and-publish-section">
      <div
        class="news-input-container"
        :class="{
          'simple-data-tooltip': hasNewsNameFocus,
        }"
        :data-tooltip-text="hasNewsNameFocus && localNewsName"
      >
        <input
          class="news-input-text text-title-1 font-bold"
          id="newsNameField"
          v-model.trim="localNewsName"
          placeholder="News name in CMS"
          autocomplete="off"
          @mouseover="checkNewsName"
          @keydown="hideNewsTip"
          @mouseleave="hideNewsTip"
          @input="onInputChange"
        />
        <span v-if="!localNewsName" class="asterisk-input">*</span>
      </div>

      <div
        v-if="!isReplaceLiveHero && !isLiveHero"
        class="publish-button-container"
      >
        <div class="publish-btn">
          <span :class="!selectedDate ? 'text disabled-text' : 'text'">
            {{ $t("DYNAMIC_HERO.SCHEDULE") }}
          </span>
          <label
            class="switch"
            :class="{
              'simple-data-tooltip simple-data-tooltip-edge': !selectedDate
            }"
            :data-tooltip-text="!selectedDate && $t('DYNAMIC_HERO.HERO_SCHEDULE_TOOLTIP')"
          >
            <input
              type="checkbox"
              :checked="props.selectedDate != 0 && props.isNewsStatus"
              :disabled="!props.selectedDate"
              @click="toggleNewsStatus"
            />
            <span
              :class="
                selectedDate === ''
                  ? 'slider-round disabled-slider'
                  : 'slider-round'
              "
            ></span>
          </label>
        </div>
      </div>
    </div>

    <div class="news-image-format-and-delete-container">
      <div class="news-image-format-text">
        <b>News image:</b> jpg/ jpeg/ png format (max 15 MB)
        <span class="warning-asterisk-input">*</span>
      </div>
      <div
        v-if="!isReplaceLiveHero && !isLiveHero && isEditPage"
        class="delete-news-section"
      >
        <div class="delete" @click="deleteEvent">
          <img
            alt="delete"
            src="@/assets/images/delete-icon.png"
            width="15"
            height="16"
          />
          <span>{{ $t('DYNAMIC_HERO.DELETE_NEWS') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  newsName: {
    type: String,
    default: "",
  },
  selectedDate: {
    type: [Date, Array, String, Object],
    default: null
  },
  isNewsStatus: {
    type: Boolean,
    default: false,
  },
  isReplaceLiveHero: {
    type: Boolean,
    default: false,
  },
  isLiveHero: {
    type: Boolean,
    default: false,
  },
  hasNewsNameFocus: {
    type: Boolean,
    default: false,
  },
  isEditPage: {
    type: Boolean,
    default: false,
  },
  deleteEvent: {
    type: Function,
    default: () => {},
  },
  hideNewsTip: {
    type: Function,
    default: () => {},
  },
  checkNewsName: {
    type: Function,
    default: () => {},
  },
});

const emit = defineEmits(["update:newsName", "scheduleToggle"]);

const localNewsName = ref(props.newsName);

const onInputChange = () => {
  emit("update:newsName", localNewsName.value);
};

const toggleNewsStatus = (event) => {
  emit("scheduleToggle", event.target.checked);
};
</script>
