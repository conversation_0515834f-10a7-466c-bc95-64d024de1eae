<template>
  <div class="background-image-news-section">
    <img alt="background" class="background-image" :src="imageSrc" />

    <div class="back-btn" @click="handleBack">
      <img
        alt="back"
        class="back-arrow-image"
        src="@/assets/images/back-arrow.png"
      />
      <span class="back-to-hero-list">
        {{
          isPageOverviewVisible
            ? $t("OVERVIEW.BACK_TO_OVERVIEW")
            : $t("SWITCH_PAGES.BACK_TO_DYNAMIC_HERO_LIST")
        }}
      </span>
    </div>

    <div class="head-btn">
      <button
        v-if="props.isReplaceLiveHero"
        :class="{
          'save-btn': canReplace,
          'disable save-btn': !canReplace,
        }"
        @click="replaceAction"
        @keydown="preventEnterAndSpaceKeyPress"
      >
        {{ replaceButtonText }}
      </button>

      <button
        v-if="!isReplaceLiveHero"
        :class="{
          'save-btn': canSave,
          'disable save-btn': !canSave,
        }"
        @click="saveAction"
        @keydown="preventEnterAndSpaceKeyPress"
      >
        {{ isLiveHero ? $t('BUTTONS.UPDATE') : $t('BUTTONS.CONTINUE') }}
      </button>

      <button type="button" class="cancel-btn" @click="handleBack">
        {{ $t("BUTTONS.CANCEL_BUTTON") }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  imageSrc: {
    type: String,
    required: true,
  },
  backText: {
    type: String,
    default: "",
  },
  replaceButtonText: {
    type: String,
    required: true,
  },
  isReplaceLiveHero: {
    type: Boolean,
    default: false,
  },
  isLiveHero: {
    type: Boolean,
    default: false,
  },
  isPageOverviewVisible: {
    type: Boolean,
    default: false,
  },
  isCampaignModified: {
    type: Boolean,
    default: false,
  },
  newsName: {
    type: String,
    default: "",
  },
  newsText: {
    type: String,
    default: "",
  },
  isCTALinkIsValid: {
    type: Boolean,
    default: false,
  },
  newsCTALinkText: {
    type: String,
    default: "",
  },
  uploadImagePercentage: {
    type: Number,
    default: 0,
  },
});
const { preventEnterAndSpaceKeyPress } = useEventUtils();

const canSave = computed(
  () =>
    props.newsName.trim() !== "" &&
    props.newsText.trim() !== "" &&
    (props.isCTALinkIsValid || !props.newsCTALinkText.trim()) &&
    props.imageSrc &&
    (props.uploadImagePercentage === 0 ||
      props.uploadImagePercentage === 100) &&
    props.isCampaignModified
);
const emit = defineEmits([
  "saveAction",
  "replaceAction",
  "backToDynamicHeroList",
]);
const canReplace = computed(() => canSave.value);

const saveAction = () => {
  emit("saveAction");
};

const replaceAction = () => {
  emit("replaceAction");
};

const handleBack = () => {
  emit("backToDynamicHeroList");
};
</script>
