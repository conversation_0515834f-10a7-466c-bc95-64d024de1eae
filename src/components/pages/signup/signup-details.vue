<template>
  <div class="auth-signup-details">
    <div v-for="detail in details" class="auth-signup-detail">
      <div class="auth-signup-detail-index">{{ detail.index }}</div>
      <p class="auth-signup-detail-title">{{ detail.title }}</p>
      <p class="auth-signup-detail-text font-weight-semi-bold">{{ detail.text }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const details = ref([
  {
    index: 1,
    title: "Enter your Prompt",
    text: "Create custom, great tasting recipe content in minutes with enterprise-class validators.",
  },
  {
    index: 2,
    title: "Generate",
    text: "Complete recipe content is generated with ingredients, step-by-step instructions and photorealistic images.",
  },
  {
    index: 3,
    title: "Export and Use",
    text: "Easily export and integrate recipes into your website or app, and make them shoppable.",
  }
]);

defineOptions({
  name: "signup-details"
});
</script>

