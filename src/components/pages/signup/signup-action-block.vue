<template>
  <div class="auth-signup-action" :class="classes">
    <button type="button" class="btn-reset auth-signup-btn" @click="signupAsync">Start Your FREE 60-Day Trial</button>
    <p class="auth-signup-action-hint font-weight-semi-bold" :class="hintClasses">No credit card required. You will not be billed.</p>
  </div>
</template>

<script setup>
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { getCurrentInstance } from 'vue';
import { useNuxtApp } from '#app';
const props = defineProps({
  classes: {
    type: String,
    required: false,
  },
  hintClasses: {
    type: String,
    required: false,
  },
});
const { setUnknownTrackerIdentityAsync } = useNonTrackUserDetails();
const { $tracker, $auth } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const signupAsync = async () => {
  await setUnknownTrackerIdentityAsync();

  $tracker.sendEvent($keys.EVENT_KEY_NAMES.CLICK_SIGN_UP, {}, { ...LOCAL_TRACKER_CONFIG });

  await $auth.loginWithRedirect("auth0", {
    params: {
      scope: "openid profile email innit_admin",
      screen_hint: "signup",
    },
  });
};
</script>

