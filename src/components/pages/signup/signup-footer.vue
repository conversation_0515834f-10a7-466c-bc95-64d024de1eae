<template>
  <div class="auth-signup-footer">
    <div class="auth-signup-footer-image-section">
      <div class="auth-signup-footer-image-section-image">
        <a href="">
          <img class="innit-image" src="@/assets/images/innit-logo.svg?skipsvgo=true" alt="innit icon">
        </a>
      </div>
      <div v-if="!isFoodLMGenerator" class="auth-signup-footer-image-section-text text-title-3">
        <a href="">Food Intelligence</a>
      </div>

    </div>
    <div class="auth-signup-footer-menu-section">
      <ul class="auth-signup-footer-menu-section-content">
        <template v-for="nav in navs">
          <li class="text-light-h3">
            <a v-if="nav.link" :href="sanitize(nav.link)">{{ nav.label }}</a>
            <p v-else>{{ nav.label }}</p>
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { sanitizeUrl } from "@braintree/sanitize-url";

const props = defineProps({
  isFoodLMGenerator: {
    type: Boolean,
    default: false,
  },
});

const navs = ref([
  {
    link: "",
    label: `©${new Date().getFullYear()} Innit International SCA`,
  },
  {
    link: "https://www.innit.com/tos",
    label: "Terms of Service",
  },
  {
    link: "https://www.innit.com/privacy",
    label: "Privacy Policy",
  },
  {
    link: "https://www.innit.com/cookie-policy",
    label: "Cookie Policy",
  },
  {
    link: "https://www.innit.com/accessibility",
    label: "Web Accessibility",
  },
]);

const sanitize = (url) => {
  if (process.client) {
    return sanitizeUrl(url);
  }
  return url;
};
</script>
