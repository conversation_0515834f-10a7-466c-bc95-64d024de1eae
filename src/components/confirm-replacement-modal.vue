<template>
    <div>
        <Modal @close="$emit('closeModal')">
            <template #selectGroupType>
                <div class="confirm-replacement-modal">
                    <div class="error-delete-hero-modal-content">
                        <div class="select-schedule-date-main-container">
                            <div class="error-cross-image-div">
                                <img class="replacement-hero-image" src="../assets/images/Sort-icon.png" alt="cross" />
                            </div>
                            <div class="select-schedule-date-hero-modal-heading">
                                <span class="unable-schedule-text">{{ replacementText }}</span>
                            </div>
                        </div>
                        <div class="replacement-hero-modal-btn-container">
                            <div class="select-schedule-date-hero-cancel-btn" @click="closeReplacement()">
                                {{ $t('BUTTONS.CANCEL_BUTTON') }}
                            </div>
                            <div class="select-schedule-date-hero-confirm-btn" @click="saveReplacement()">
                                {{ $t('BUTTONS.CONFIRM_BUTTON') }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>
  
<script>
import Modal from "@/components/Modal";
export default {
    name: "confirm-replacement-modal",
    components: {
        Modal,
    },
    data() {
        return {};
    },
    props: {
        closeModal: {
            type: Function,
        },
        replacementText: {
            type: String,
        },
        closeReplacement: {
            type: Function,
        },
        saveReplacement: {
            type: Function,
        },
    },
};
</script>