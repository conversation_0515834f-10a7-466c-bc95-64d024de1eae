<template>
    <div>
        <Modal @close="$emit('closeModal')">
            <template #deleteRecipe>
                <div class="delete-modal-container">
                    <div class="delete-image">
                        <div class="delete-image-container">
                            <img alt="" src="~/assets/images/delete.png">
                        </div>
                    </div>
                    <div class="delete-content">
                        <div class="delete-title">
                            <span class="text">{{ productInfoTitle }}</span>
                        </div>
                        <div class="delete-description-red" v-if="productionDescriptionRed">
                            {{ productionDescriptionRed }}
                        </div>
                        <div class="delete-description" v-if="productDescriptionTwo === 'ingredients?'">
                            <span v-if="availableLanguage == 1">
                                {{ productDescriptionOne }} {{ productDescriptionTwo }}
                            </span>
                            <span v-else> Deletion will apply to all variant recipes.</span>
                        </div>
                        <div v-else class="delete-description">
                            {{ productDescriptionOne }} {{ productDescriptionTwo }}
                        </div>
                        <div class="button-container">
                            <button
                                type="button"
                                class="btn-green-outline"
                                @click="closeModal"
                                data-test-id="cancel-button"
                            >
                                {{ $t('BUTTONS.CANCEL_BUTTON') }}
                            </button>
                            <button
                                type="button"
                                class="btn-red"
                                @click="deleteItem(deleteInstructionIndex, deleteIngredientIndex)"
                                data-test-id="delete-button"
                            >
                                {{ buttonText ? buttonText: 'Delete' }}
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script setup>
import Modal from "@/components/Modal";
const props = defineProps({
    closeModal: {
        type: Function,
    },
    deleteItem: {
        type: Function,
    },
    buttonText: {
        type: String,
    },
    productInfoTitle: {
        type: String,
    },
    productDescriptionOne: {
        type: String,
    },
    productionDescriptionRed: {
        type: String,
    },
    productDescriptionTwo: {
        type: String,
    },
    availableLanguage: {
        type: Number,
    },
    deleteInstructionIndex: {
        type: Number,
    },
    deleteIngredientIndex: {
        type: Number,
    },
});
</script>
