<template>
  <div>
    <Modal @close="$emit('closeModal')">
      <template #editProductMatches>
        <div class="updating-live-hero-modal">
          <div class="save-info-popup-container">
            <div class="save-image">
              <div class="save-image-container">
                <img alt="" class="save-icon" src="@/assets/images/Exit-recipe-variant.png" />
              </div>
            </div>
          </div>
          <div class="publish-content">
            <div class="publish-head">
              {{ updatingText }}
            </div>
            <div class="button-container">
              <button type="button" class="btn-green-outline" @click="closeModal">{{ $t('BUTTONS.CANCEL_BUTTON')
              }}</button>
              <button type="button" class="btn-green" @click="callConfirm">{{ $t('BUTTONS.CONFIRM_BUTTON') }}</button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import Modal from "@/components/Modal";
export default {
  name: "updating-live-hero",
  components: {
    Modal,
  },
  data() {
    return {};
  },
  props: {
    callConfirm: {
      type: Function,
    },
    closeModal: {
      type: Function,
    },
    updatingText: {
      type: String,
    },
  },
};
</script>
