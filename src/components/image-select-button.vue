<template>
  <button
    type="button"
    class="image-selected-container btn-reset"
    :class="{
      'image-selected': isSelected,
      hovered: isHovered,
    }"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <img
      class="miniature-image"
      :src="image?.url"
      alt="Recipe"
      @error="handleImageError"
    />
    <span :class="overlayClass" v-if="isImageVisible">
      <img
        class="check-image"
        src="@/assets/images/icons/check.svg?skipsvgo=true"
        alt="check"
      />
    </span>
  </button>
</template>

<script>
export default {
  name: "image-select-button",
  props: {
    image: {
      type: Object,
      required: true,
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    isMainImage: {
      type: Boolean,
      default: false,
    },
    defaultImage: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isHovered: false,
    };
  },
  computed: {
    isImageVisible() {
      return this.isMainImage || this.isHovered;
    },
    overlayClass() {
      return this.isMainImage ? "image-checked" : "hover-overlay";
    },
  },
  methods: {
    handleClick() {
      this.$emit(this.$keys.KEY_NAMES.SELECT_IMAGE, this.image);
    },
    handleMouseEnter() {
      this.isHovered = true;
      this.$emit(this.$keys.KEY_NAMES.HOVER_IMAGE, this.image);
    },
    handleMouseLeave() {
      this.isHovered = false;
      this.$emit(this.$keys.KEY_NAMES.CLEAR_HOVER);
    },
    handleImageError(event) {
      event.target.src = this.defaultImage;
    },
  },
};
</script>
