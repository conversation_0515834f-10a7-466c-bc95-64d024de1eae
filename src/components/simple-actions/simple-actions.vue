<template>
<div class="simple-actions">
  <div
    :class="{
      'simple-data-tooltip': isEditInfoTooltipShowing,
      'simple-data-tooltip simple-data-tooltip-warn': (isEditBtnDisabled && !isEditInfoTooltipShowing)
    }"
    :data-tooltip-text="isEditInfoTooltipShowing ? $t('SIMPLE_ACTIONS_EDIT_INFO_TOOLTIP') : undefined"
  >
    <button
      v-if="isEditBtnDisplayed"
      type="button"
      class="btn-icon simple-actions-btn"
      :disabled="isEditBtnDisabled"
      @click="clickEdit"
    >
      <img src="@/assets/images/edit-icon.png" alt="edit icon">
    </button>

    <div v-if="isEditBtnDisabled && !isEditInfoTooltipShowing && editBtnWarnTooltipText" class="simple-data-tooltip-content">
      <IconInfoOutlineDefault
        filled
        :fontControlled="false"
        :style="iconInfoOutlineDefaultStyle"
      />
      <span>{{ editBtnWarnTooltipText }}</span>
    </div>
  </div>

  <div
    :class="{
      'simple-data-tooltip': isDeleteInfoTooltipShowing,
      'simple-data-tooltip simple-data-tooltip-warn': (isDeleteBtnDisabled && !isDeleteInfoTooltipShowing)
    }"
    :data-tooltip-text="isDeleteInfoTooltipShowing ? $t('SIMPLE_ACTIONS_DELETE_INFO_TOOLTIP') : undefined"
  >
    <button
      v-if="isDeleteBtnDisplayed"
      type="button"
      class="btn-icon simple-actions-btn"
      :disabled="isDeleteBtnDisabled"
      @click="clickDelete"
    >
      <img src="@/assets/images/delete-icon.png" alt="delete icon">
    </button>

    <div v-if="isDeleteBtnDisabled && !isDeleteInfoTooltipShowing && deleteBtnWarnTooltipText" class="simple-data-tooltip-content">
      <IconInfoOutlineDefault
        filled
        :fontControlled="false"
        :style="iconInfoOutlineDefaultStyle"
      />
      <span>{{ deleteBtnWarnTooltipText }}</span>
    </div>
  </div>
</div>
</template>

<script setup>
import { ref } from 'vue';
import IconInfoOutlineDefault from '~/assets/images/icons/info-outline-default.svg';

const props = defineProps({
  isEditBtnDisplayed: {
    type: Boolean,
    required: false,
    default: true,
  },
  isEditBtnDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  isEditInfoTooltipShowing: {
    type: Boolean,
    required: false,
    default: false,
  },
  editBtnWarnTooltipText: {
    required: false,
  },

  isDeleteBtnDisplayed: {
    type: Boolean,
    required: false,
    default: true,
  },
  isDeleteBtnDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  isDeleteInfoTooltipShowing: {
    type: Boolean,
    required: false,
    default: false,
  },
  deleteBtnWarnTooltipText: {
    required: false,
  },
});

const emit = defineEmits(["editOnClick", "deleteOnClick"]);

const iconInfoOutlineDefaultStyle = ref({
  width: "3.5em",
  maxWidth: "16px",
});

const clickEdit = () => {
  if (!props.isEditBtnDisabled) {
    emit('editOnClick');
  }
};

const clickDelete = () => {
  if (!props.isDeleteBtnDisabled) {
    emit('deleteOnClick');
  }
};
</script>
