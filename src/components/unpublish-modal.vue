<template>
  <Modal @close="closeModal">
    <template #saveModal>
      <div class="save-recipe-variant-modal">
        <div class="save-info-popup-container">
          <div class="save-image">
            <div class="save-image-container">
              <img alt="save" class="save-icon" src="@/assets/images/Eye.svg?skipsvgo=true" />
            </div>
          </div>
        </div>
        <div class="publish-content">
          <div class="publish-head">{{ description }}</div>
          <div v-if="noteMessage" class="note-message">
            {{ noteMessage }}
          </div>
          <div class="button-container">
            <button type="button" class="btn-green-outline" @click="closeModal">{{ $t('BUTTONS.CANCEL_BUTTON')
            }}</button>
            <button type="button" class="btn-green" @click="unpublishFunction">
              <span> {{ buttonName }} </span>
            </button>
          </div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
import Modal from "@/components/Modal";
export default {
  name: "unpublish-modal",
  components: {
    Modal,
  },
  data() {
    return {};
  },
  props: {
    unpublishFunction: {
      type: Function,
    },
    closeModal: {
      type: Function,
    },
    description: {
      type: String,
      default: "",
    },
    buttonName: {
      type: String,
      default: "",
    },
    noteMessage: {
      type: String,
      default: "",
    },
  },
};
</script>
