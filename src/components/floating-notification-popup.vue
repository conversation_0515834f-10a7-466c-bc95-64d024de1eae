<template>
  <div
    v-if="isVisible"
    :class="['popup', type]"
    class="notification-popup-container"
    data-test-id="notification-popup"
  >
    <div class="notification-icon-section">
      <img
        alt="notification-icon"
        class="notification-icon"
        :src="type === $keys.KEY_NAMES.SUCCESS ? successImage : type === $keys.KEY_NAMES.ERROR ? errorImage : type === $keys.KEY_NAMES.LABEL ? labelImage : deleteImage"
      />
    </div>
    <div class="notification-content-section" :data-test-id="formattedMessage">
      <span class="text-h3">{{ message }}</span>
      <span class="text-light-h4" v-if="subMessage"> {{ subMessage }}</span>
    </div>
    <button
      type="button"
      class="close-button btn-reset"
      @click="closePopup()"
    >
      <img
        alt="cross-icon"
        class="floating-popup-cross"
        src="~/assets/images/close.svg?skipsvgo=true"
      />
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import { useDelayTimer } from '~/composables/useDelayTimer';
import { useNuxtApp } from '#app';

// images
import successImage from "@/assets/images/icons/success.svg?skipsvgo=true";
import errorImage from "@/assets/images/icons/error.svg?skipsvgo=true";
import labelImage from "@/assets/images/icons/labels.svg?skipsvgo=true";
import deleteImage from "@/assets/images/icons/deleted.svg?skipsvgo=true";

// utility define

const props = defineProps({});
const { delay } = useDelayTimer();
const isVisible = ref(false);
const message = ref("");
const subMessage = ref("");
const type = ref("");
const { $eventBus } = useNuxtApp();

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const formattedMessage = computed(() => {
  return message.value.toLowerCase().replace(/\s+/g, '-');
});

const showPopupAsync = async (data) => {
  const isDataString = typeof data === "string";
  const popupMessage = isDataString ? data : data.popupMessage;
  const popupSubMessage = data?.popupSubMessage || "";
  const popupType = data?.popupType || $keys.KEY_NAMES.SUCCESS;

  message.value = popupMessage;
  subMessage.value = popupSubMessage;
  type.value = popupType;
  isVisible.value = true;
  await delay(3000);
  isVisible.value = false;
};

const closePopup = () => {
  isVisible.value = false;
};

onMounted(() => {
  $eventBus.on("show-floating-notification", showPopupAsync);
});

onBeforeUnmount(() => {
  $eventBus.off("show-floating-notification", showPopupAsync);
});
</script>
