<template>
  <div class="languages-alert" >
    <div
      v-if="isLanguageAvailable(props.languages)"
      class="languages-alert-item simple-data-tooltip"
      :data-tooltip-text="displayTooltipLanguage(props.languages)"
    >
      <img src="@/assets/images/language-icon.png" alt="language icon" />
    </div>
    <div
      v-if="props.hasAlert"
      class="languages-alert-item simple-data-tooltip"
      :data-tooltip-text="props.alertTooltipText"
    >
      <img src="@/assets/images/red-info.svg?skipsvgo=true" alt="red info icon"  />
    </div>
  </div>
</template>

<script setup>
import { useNuxtApp } from '#app';
import { useI18n } from "vue-i18n";

const props = defineProps({
  languages: {
    type: Array,
    required: false,
    default: []
  },
  hasAlert: {
    type: Boolean,
    required: false,
    default: false,
  },
  lang: {
    type: String,
    default: "en-US",
  },
  alertTooltipText: {
    required: false,
    default: "",
  },
});

const { $keys } = useNuxtApp();
const { t } = useI18n();

const isLanguageAvailable = (data) => {
  if (data.length <= 1) {
    return false;
  }

  return data && data[1] !== $keys.LANGUAGE.FRENCH;
};

const displayTooltipLanguage = (data) => {
  const msg = data
    ?.filter((item) => item !== props.lang)
    ?.map((item) => item.split("-")[0]?.toUpperCase())
    ?.join(",");

  return t("COMMON.AVAILABLE_IN", { msg });
};
</script>
