<template>
  <div class="page-actions">
    <div class="page-actions-start">
      <NuxtLink
        v-if="!isBackDisabled"
        class="page-actions-back btn-reset font-bold color-green"
        :to="backPath"
        aria-label="Go back"
      >
        <img src="@/assets/images/back-arrow.png" alt="Go back" aria-hidden="true" />
        <span>{{ backLabel || $t('BACK_TO_ARTICLES') }}</span>
      </NuxtLink>
    </div>
    <div class="page-actions-end">
      <button
        type="button"
        class="btn-green-outline"
        :disabled="isCancelDisabled"
        @click="actionCancel"
        aria-label="Cancel"
      >
        {{ cancelLabel || $t('BUTTONS.CANCEL_BUTTON') }}
      </button>
      <button
        type="button"
        class="btn-green"
        :disabled="isContinueDisabled"
        @click="actionContinue"
        aria-label="Continue"
      >
        {{ continueLabel || $t('BUTTONS.CONTINUE') }}
      </button>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  isBackDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  backLabel: {
    type: String,
    required: false,
    default: "",
  },
  backPath: {
    type: String,
    required: false,
    default: "",
  },
  isCancelDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  cancelLabel: {
    type: String,
    required: false,
    default: "",
  },
  isContinueDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  continueLabel: {
    type: String,
    required: false,
    default: "",
  },
});

const emit = defineEmits(["actionCancel", "actionContinue"]);

const router = useRouter();

const backPath = computed(() => {
  return props.backPath || router.options?.history?.state?.back || router.options?.history?.state?.forward || "/overview";
});

const actionCancel = () => emit("actionCancel", true);
const actionContinue = () => emit("actionContinue", true);
</script>
