<template>
  <div class="block-wrapper" :class="classes">
    <h4 v-if="$slots.title" :class="['block-wrapper-title', 'display-4', 'color-gray-tundora', titleClass]">
      <slot name="title"></slot>
    </h4>

    <div
      v-if="!isLoading"
      class="block-wrapper-container"
      :class="{
        'block-wrapper-container-transparent': isTransparent
      }"
    >
      <slot></slot>
    </div>
    <loading-block v-else></loading-block>
  </div>
</template>

<script setup>
import LoadingBlock from "@/components/loading-block/loading-block.vue";

defineProps({
  classes: {
    type: String,
    required: false,
  },
  titleClass: {
    type: String,
    default: ''
  },
  isLoading: {
    type: Boolean,
    required: false,
    default: false,
  },
  isTransparent: {
    type: Boolean,
    required: false,
    default: false,
  },
});
</script>
