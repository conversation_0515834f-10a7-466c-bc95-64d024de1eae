
   <template>
    <div>
        <Modal @close="$emit('closeModal')">
            <template #deleteRecipe>
                <div class="collection-delete-modal-container">
                    <div class="collection-delete-image">
                        <div class="collection-delete-image-container">
                            <img alt="" src="@/assets/images/delete-red.png">
                        </div>
                    </div>
                    <div class="collection-delete-content">
                        <div class="collection-delete-title">
                            <span class="collection-text">{{ productInfoTitle }}</span>
                        </div>
                        <div class="collection-button-container">
                            <button
                                type="button"
                                class="btn-green-outline"
                                data-test-id="confirm-cancel-button"
                                @click="closeModal"
                            >
                                {{ $t('BUTTONS.CANCEL_BUTTON') }}
                            </button>
                            <button
                                type="button"
                                class="btn-red"
                                data-test-id="confirm-delete-button"
                                @click="deleteItem"
                            >
                                {{buttonText ? buttonText : $t('BUTTONS.DELETE_BUTTON')}}
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>
<script>
import Modal from "@/components/Modal";
export default {
    name: "collection-delete-modal",
    components: {
        Modal,
    },
    data() {
        return {};
    },
    props: {
        closeModal: {
            type: Function,
        },
        deleteItem: {
            type: Function,
        },
        buttonText: {
            type: String,
        },
        productInfoTitle: {
            type: String,
        },
    },
};
</script>
