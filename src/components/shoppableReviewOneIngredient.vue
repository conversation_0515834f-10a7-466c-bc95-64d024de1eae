<template>
  <div class="container-for-shoppable-review-for-edit-product">
    <Modal
      v-show="isReportModalVisible"
      @close="closeReportModal()"
      id="problemModal"
    >
      <template #problemModal>
        <div class="problem-modal">
          <div class="problem-modal-content">
            <div class="problem-modal-warning">Please select a problem</div>
            <div class="problem-modal-checkbox">
              <template v-for="issueType in (reportIssueTypes || [])">
                <input
                  type="radio"
                  :id="issueType.key"
                  class="issue-type"
                  :value="issueType.key"
                  v-model="selectedIssueType"
                />
                <label
                  style="
                    font-size: 16px;
                    font-weight: 400;
                    width: 182px;
                    height: 42px;
                  "
                  :for="issueType.key"
                  >&nbsp;{{ issueType.display }}</label
                >
                <br />
              </template>
            </div>
            <div class="problem-modal-input">
              <textarea
                type="text"
                class="problem-input text-light-h3"
                placeholder="Comments/notes (optional)"
                v-model="issueDescription"
              >
              </textarea>
            </div>
            <div class="problem-modal-btn-container">
              <button type="button"
                class="btn-green-outline"
                @click="closeReportModal()"
              >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </button>
              <button type="button"
                class="btn-green"
                @click="reportProductAsync()"
              >
                Send
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <cancelModal
    v-if="isConfirmModalVisible"
    :availableLang="[]"
    :isCampaignModifiedFromShoppableReview="false"
    :callConfirm="closeShoppableReview"
    :closeModal="closeModal"
    />
    <saveModal v-if="isSaveModalVisible"
      :closeModal="closeModal"
      :saveAndPublishFunction="saveShoppableReview"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
      :imageName="saveImage"
    />
    <Modal v-show="isProductConfirmationDeleted" @close="closeDeleteModal()">
      <template #deleteShoppableReviewProduct>
        <div class="delete-shoppable-review-modal">
          <div class="delete-shoppable-review-image">
            <img alt="" src="~/assets/images/delete.png" />
          </div>
          <div class="delete-shoppable-review-content">
            <div class="delete-shoppable-review-title">Remove Product?</div>
            <div class="delete-shoppable-review-description text-title-2 font-normal">
              {{ $t('REMOVE_INGREDIENT_TEXT') }}
            </div>
            <div class="delete-shoppable-review-button-container">
              <button type="button"
                class="btn-green-outline"
                @click="closeDeleteModal()"
              >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </button>
              <button type="button"
                class="btn-red"
                @click="removeProductConfirmedAsync()"
              >
                {{  $t('BUTTONS.REMOVE_BUTTON')  }}
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-show="isGotoPopupOpen" @close="closeModal">
      <template #editProductMatches>
        <div class="goto-recipe">
          <div class="goto-recipe-content">
            <div class="confirm-exit-top-section">
              <div class="confirm-exit-image">
                <div class="confirm-exit-image-container">
                  <img
                    alt=""
                    src="~/assets/images/confirm-exit.png"
                  />
                </div>
              </div>
              <div class="confirmation-description">
                {{ $t('DESCRIPTION_POPUP.EXIT_PAGE_POPUP') }}
                <span class="leave-page-warning"
                  >You will lose any unsaved changes</span
                >
              </div>
            </div>
            <div class="organizations-confirmation-button-container">
              <div
                class="btn-green-outline"
                @click="closeModal"
              >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </div>
              <div
                class="btn-green"
                @click="navigateToRecipe(), closeModal()"
              >
              {{ $t('BUTTONS.CONFIRM_BUTTON') }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-show="previewReferenceProductData" @close="closeModal">
      <template #previewReferenceIngredient>
        <div class="shoppable-preview-reference-ingredient-container">
          <div class="shoppable-preview-reference-ingredient-top-section">
            <div class="reference-product-section-name text-h2">Reference product</div>
            <div
              class="reference-product-section-close-button"
              @click="closeModal()"
            >
              <img
                class="close-icon"
                alt=""
                src="@/assets/images/exit-gray.png"
              />
            </div>
          </div>
          <div class="shoppable-preview-reference-ingredient-middle-section">
            <div class="shoppable-preview-reference-ingredient-name text-title-2 font-normal">
              Ingredient name «{{
                currentReferenceIngredientName
                  ? currentReferenceIngredientName
                  : ""
              }}»
            </div>
          </div>
          <div
            v-if="referenceIngredientProductData"
            class="shoppable-preview-reference-ingredient-bottom-section"
          >
            <div class="shoppable-preview-reference-ingredient-table-data">
              <table
                class="reference-preview-table"
                aria-label="reference-preview-table"
              >
                <thead class="reference-ingredient-table-head">
                  <tr class="reference-ingredient-table-head-row">
                    <th class="ingredient-image-heading"></th>
                    <th class="ingredient-product-id-heading">Product Id</th>
                    <th class="ingredient-product-title-heading">
                      Product Title
                    </th>
                    <th class="ingredient-product-weight-heading">WEIGHT</th>
                    <th class="ingredient-product-store-count-heading">
                      {{ $t('STORE_COUNT') }}
                    </th>
                  </tr>
                </thead>
                <tbody class="reference-ingredient-table-body">
                  <tr class="reference-ingredient-table-body-row">
                    <td class="ingredient-image">
                      <img
                        :src="
                          referenceIngredientProductData &&
                          referenceIngredientProductData.image &&
                          referenceIngredientProductData.image.url
                            ? referenceIngredientProductData.image.url
                            : defaultImage
                        "
                        alt=""
                        srcset=""
                      />
                    </td>
                    <td class="ingredient-product-id text-light-h4">
                      {{
                        referenceIngredientProductData &&
                        referenceIngredientProductData.externalId
                          ? referenceIngredientProductData.externalId
                          : ""
                      }}
                    </td>
                    <td class="ingredient-product-title text-h3">
                      <div class="product-title">
                        {{
                          setIngredientName(
                            referenceIngredientProductData &&
                              referenceIngredientProductData.brand
                              ? referenceIngredientProductData.brand
                              : "",
                            referenceIngredientProductData &&
                              referenceIngredientProductData.name
                              ? referenceIngredientProductData.name
                              : ""
                          ).name
                        }}
                      </div>
                    </td>
                    <td
                      v-if="
                        referenceIngredientProductData &&
                        referenceIngredientProductData.size &&
                        referenceIngredientProductData.size.value &&
                        referenceIngredientProductData.size.unit &&
                        referenceIngredientProductData.size.unit.abbreviation
                      "
                      class="ingredient-product-weight text-light-h3"
                    >
                      {{
                        referenceIngredientProductData.size.value +
                        " " +
                        referenceIngredientProductData.size.unit.abbreviation
                      }}
                    </td>
                    <td
                      v-else-if="
                        referenceIngredientProductData &&
                        referenceIngredientProductData.size &&
                        referenceIngredientProductData.size.value
                      "
                      class="ingredient-product-weight text-light-h3"
                    >
                      {{ referenceIngredientProductData.size.value }}
                    </td>
                    <td v-else class="ingredient-product-weight text-light-h3"></td>
                    <td class="ingredient-product-store-count text-light-h3">
                      {{
                        referenceIngredientProductData &&
                        referenceIngredientProductData.numberOfStores
                          ? referenceIngredientProductData.numberOfStores
                          : "0"
                      }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <div
                v-if="
                  referenceIngredientProductData &&
                  referenceIngredientProductData.labels &&
                  referenceIngredientProductData.labels.desirable &&
                  referenceIngredientProductData.labels.desirable.length > 0
                "
                class="reference-tags-main-container"
              >
                <div class="reference-ingredient-tags-heading text-title-2">{{ $t('TAG.TAG_TEXT')  }}:</div>
                <div class="reference-tags-list-container">
                  <div
                    v-for="(tags, index) in (referenceIngredientProductData?.labels?.desirable || [])"
                    :key="index"
                    class="ingredient-reference-tags-card"
                  >
                    <div
                      v-if="false"
                      class="ingredient-reference-tags-image"
                    ></div>
                    <div class="ingredient-reference-tags-name text-title-2 font-normal">
                      {{ tags.display }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="shoppable-preview-reference-ingredient-cancel-button">
              <button type="button" class="btn-green-outline" @click="closeModal">{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <div
      v-if="productIdCopyToClipboard"
      class="shoppable-product-copy-to-clipboard-information"
    >
      <span class="copy-to-clipboard-successfully">
        <img
          class="green-correct"
          alt=""
          src="@/assets/images/green-correct-icon.png"
        />
        Product ID copied to the clipboard
        <img class="close-icon" alt="" src="@/assets/images/exit-gray.png" />
      </span>
    </div>
    <div class="shoppable-review-popup-main-navigation">
      <div class="shoppable-review-popup-main-heading">
        <p>Shoppable Review</p>
      </div>
      <div class="close-image" @click="confirmCloseShoppableReview()">
        <img src="@/assets/images/exit-gray.png" alt="" />
      </div>
    </div>
    <div class="shoppable-review-for-edit-product-main" id="shoppable-review-for-edit-product-main-id">
      <div class="shoppable-review-for-edit-product">
        <div class="shoppable-review-popup-main">
          <div class="shoppable-review-popup-content">
            <div class="shoppable-review-popup-content-dashboard">
              <div
                class="shoppable-review-popup-content-details"
                style="display: flex"
              >
                <img
                  alt=""
                  v-if="recipeImage"
                  class="shoppable-review-popup-content-image"
                  :src="`${recipeImage}`"
                />
                <img
                  alt=""
                  v-else-if="externalImageURL"
                  @error="$event.target.src = `${defaultImage}`"
                  class="shoppable-review-popup-content-image"
                  :src="`${externalImageURL}`"
                />
                <img
                  alt=""
                  v-else
                  class="shoppable-review-popup-content-image"
                  :src="`${defaultImage}`"
                />
                <div class="about-image">
                  <p class="shoppable-review-popup-content-number">
                    {{ recipeID }}
                  </p>
                  <p class="shoppable-review-popup-title">{{ recipeName }}</p>
                  <p class="shoppable-review-popup-subtitle">
                    {{ recipeSubtitle }}
                  </p>
                  <div class="goto-recipe-link">
                    <span class="link" @click="openRedirectModal(recipeID)">
                      Go to recipe</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="ingredient && ingredient.productId"
          class="shoppable-reference-product-container"
        >
          <div class="shoppable-reference-product-text-clipboard">
            <div class="shoppable-reference-product-section">
              <u
                v-if="(referenceIngredientProductData && (referenceIngredientProductData.name || referenceIngredientProductData.brand) &&
                  referenceIngredientProductData.size) ||
                  referenceIngredientProductData.externalId
                "
                class="shoppable-underline-reference-product-text text-light-h3"
                @click="previewReferenceProduct(ingredient.name)"
                >Ref. Product&nbsp;<span id="refrenceProduct">{{
                  ingredient.productId
                }}</span></u
              >
              <u v-else class="shoppable-underline-reference-no-product-text text-light-h3"
                >Ref. Product&nbsp;<span id="refrenceProduct">{{
                  ingredient.productId
                }}</span></u
              >
              <div
                v-if="
                  (referenceIngredientProductData &&
                    (referenceIngredientProductData.name ||
                      referenceIngredientProductData.brand) &&
                    referenceIngredientProductData.size) ||
                  referenceIngredientProductData.externalId
                "
                class="shoppable-reference-product-preview-section"
              >
                <div class="shoppable-reference-product-data-section">
                  <div class="shoppable-reference-top-section">
                    <div class="shoppable-reference-product-image">
                      <img
                        :src="
                          referenceIngredientProductData &&
                          referenceIngredientProductData.image &&
                          referenceIngredientProductData.image.url
                            ? referenceIngredientProductData.image.url
                            : defaultImage
                        "
                        alt=""
                      />
                    </div>
                  </div>
                  <div class="shoppable-reference-bottom-section">
                    <div class="shoppable-reference-product-name text-h3">
                      {{
                        referenceIngredientProductData &&
                        referenceIngredientProductData.name &&
                        referenceIngredientProductData.brand
                          ? referenceIngredientProductData.brand +
                            " " +
                            referenceIngredientProductData.name
                          : ""
                      }}
                    </div>
                    <div class="shoppable-reference-product-info text-light-h4">
                      <div
                        v-if="
                          referenceIngredientProductData &&
                          referenceIngredientProductData.size &&
                          referenceIngredientProductData.size.value &&
                          referenceIngredientProductData.size.unit &&
                          referenceIngredientProductData.size.unit.abbreviation
                        "
                        class="shoppable-refrence-product-weight"
                      >
                        {{
                          referenceIngredientProductData.size.value +
                          " " +
                          referenceIngredientProductData.size.unit.abbreviation
                        }}
                      </div>
                      <div
                        v-else-if="
                          referenceIngredientProductData &&
                          referenceIngredientProductData.size &&
                          referenceIngredientProductData.size.value
                        "
                        class="shoppable-refrence-product-weight"
                      >
                        {{ referenceIngredientProductData.size.value }}
                      </div>
                      <div
                        v-else
                        class="shoppable-refrence-product-weight"
                      ></div>
                      <div class="shoppable-refrence-product-id">
                        {{
                          referenceIngredientProductData &&
                          referenceIngredientProductData.externalId
                            ? referenceIngredientProductData.externalId
                            : ""
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="shoppable-reference-product-copy-clipboard simple-data-tooltip"
              :data-tooltip-text="copyReferenceTooltip"
            >
              <div class="clipboard-image" @click="copyReferenceProduct()">
                <img
                  v-if="!copiedReferenceProductID"
                  src="~/assets/images/copy_focussed.png"
                  alt=""
                />
                <img
                  v-if="copiedReferenceProductID"
                  src="~/assets/images/copy_default.png"
                  alt=""
                />
              </div>
            </div>
          </div>
        </div>
        <div class="edit-shoppable-review-table">
          <div class="edit-shoppable-review-table-head">
            <div class="edit-shoppable-review-table-head-row text-light-h4">
              <div style="width: 23%" class="shoppable-ingredient-heading th">
                Ingredient Name
              </div>
              <div style="width: 23%" class="shoppable-notes-heading th">
                Notes
              </div>
              <div style="width: 14%" class="shoppable-quantity-heading th">
                Quantity
              </div>
              <div style="width: 15%" class="quantity-uom-heading th">Uom</div>
              <div style="width: 25%" class="shoppable-heading th">
                Shoppable
              </div>
            </div>
          </div>
          <div>
            <div class="edit-shoppable-review-table-body">
              <div class="edit-shoppable-review-table-body-row text-light-h3">
                <div
                  style="width: 23%"
                  class="edit-shoppable-ingredient-data td"
                >
                  <span
                    class="ing-tool"
                    @mouseover="checkName(ingredient.name)"
                    @mouseleave="checkNameHide()"
                    :class="{
                      'simple-data-tooltip': showIngnameTip
                    }"
                    :data-tooltip-text="showIngnameTip && ingredient?.name"
                  >
                    <input
                      :disabled="true"
                      v-model="ingredient.name"
                      @input="setCampaignModified()"
                      type="text"
                      class="filter-select-input text-light-h3"
                      autocomplete="off"
                      tabindex="0"
                      dir=""
                      autocapitalize="off"
                      placeholder="Enter Ingredient Name"
                      id="ingredientName"
                    />
                  </span>
                </div>
                <div style="width: 23%" class="edit-shoppable-notes-data td">
                  <span
                    class="ing-tool"
                    @mouseover="checkNote(ingredient.note)"
                    @mouseleave="checkNoteHide()"
                    :class="{
                      'simple-data-tooltip': isNoteTipVisible
                    }"
                    :data-tooltip-text="isNoteTipVisible && ingredientNote"
                  >
                    <input
                      :disabled="true"
                      v-model="ingredient.note"
                      id="ingredientNote"
                      @input="setCampaignModified()"
                      type="text"
                      class="filter-select-input text-light-h3"
                      autocomplete="off"
                      tabindex="0"
                      dir=""
                      autocapitalize="off"
                      placeholder="Ingredient note (optional)"
                    />
                  </span>
                </div>
                <div style="width: 14%" class="edit-shoppable-quantity-data td">
                  <input
                    :disabled="true"
                    type="text"
                    @input="setCampaignModified()"
                    id="quantity"
                    v-model="ingredient.quantity"
                    class="filter-select-input text-light-h3"
                    autocomplete="off"
                    tabindex="0"
                    dir=""
                    autocapitalize="off"
                  />
                </div>
                <div
                  id="uomDropDown"
                  style="width: 15%"
                  class="edit-quantity-uom-data td"
                >
                  <div
                    class="autocomplete-results-uom"
                    v-if="ingredient.uomAutocomplete && searchedUomText == ''"
                  >
                    <div v-for="(item, i) in (ingredientsUomList || [])" :key="i">
                      <div
                        :class="{
                          'autocomplete-result-uom': true,
                          'is-active':
                            i === ingredientUomAutocompleteArrowCounter,
                        }"
                        @click.prevent="setIngredientUomResult(item)"
                      >
                        {{ item.display }}
                      </div>
                    </div>
                  </div>
                  <div
                    class="autocomplete-results-uom"
                    v-if="ingredient.uomAutocomplete && searchedUomText !== ''"
                  >
                    <div v-for="(item, i) in (searchedUomList || [])" :key="i">
                      <div
                        :class="{
                          'autocomplete-result-uom': true,
                          'is-active':
                            i === ingredientUomAutocompleteArrowCounter,
                        }"
                        @click.prevent="setIngredientUomResult(item)"
                      >
                        {{ item.display }}
                      </div>
                    </div>
                  </div>
                  <input
                    :disabled="true"
                    id="ingredientUom"
                    v-model="ingredient.UOM"
                    type="text"
                    class="filter-select-input text-light-h3"
                    autocomplete="off"
                    tabindex="0"
                    dir=""
                    autocapitalize="off"
                  />
                </div>
                <div style="width: 25%" class="edit-shoppable-data td">
                  <img
                    alt=""
                    id="shoppableDropDownArrow"
                    @click="showShoppableDropDown()"
                    src="@/assets/images/arrow-right.png"
                    class="dropdown-shoppable-data"
                    style="display: none"
                    :class="{
                      rotate: ingredient.isShoppableDropDown,
                      'dropdown-disabled': ingredient.loading,
                    }"
                  />
                  <div
                    class="edit-autocomplete-results-shoppable"
                    v-if="ingredient.isShoppableDropDown"
                  >
                    <div v-for="item in (shoppableListData || [])" :key="item.key">
                      <div
                        :class="{
                          'autocomplete-result-shoppable': true,
                          'is-active': '',
                        }"
                        @click="selectedShoppable(item.key)"
                      >
                        {{ item.data }} {{ item.subName }}
                      </div>
                    </div>
                  </div>
                  <div
                    class="filter-select-input .text-light-h3 uom-input ingredientShoppable"
                  >
                    {{ getIngredientShoppable(ingredient) }}
                    {{ getIngredientShoppableSubName(ingredient) }}
                  </div>
                </div>
              </div>
              <div v-if="isEditProductMoreDetailsVisible" class="show-more-detail">
                <div class="line"></div>
                <div class="shoppable-admin-weight" v-if="!isAdmin">
                  <div
                    v-if="
                      (ingredient &&
                        ingredient.weightInGrams &&
                        ingredient.weightInGrams > 0) ||
                      (ingredient &&
                        ingredient.volumeInMl &&
                        ingredient.volumeInMl > 0)
                    "
                    class="ingredient-numeric-size"
                  >
                    <div class="ingredient-numeric-heading text-h3">Size:</div>
                    <div class="numeric-text">
                      <div
                        class="ing-count"
                        v-if="
                          ingredient &&
                          ingredient.weightInGrams &&
                          ingredient.weightInGrams > 0
                        "
                      >
                        <span>
                          {{
                            ingredient && ingredient.weightInGrams
                              ? ingredient.weightInGrams
                              : ""
                          }}
                        </span>
                      </div>
                      <span
                        class="ing-value"
                        v-if="
                          ingredient &&
                          ingredient.weightInGrams &&
                          ingredient.weightInGrams > 0
                        "
                        >g</span
                      >
                      <span
                        class="ing-count"
                        v-if="
                          ingredient &&
                          ingredient.volumeInMl &&
                          ingredient.volumeInMl > 0
                        "
                      >
                        <span>
                          {{
                            ingredient && ingredient.volumeInMl
                              ? ingredient.volumeInMl
                              : ""
                          }}
                        </span>
                      </span>
                      <span
                        class="ing-value"
                        v-if="
                          ingredient &&
                          ingredient.volumeInMl &&
                          ingredient.volumeInMl > 0
                        "
                        >ml</span
                      >
                      <div
                        class="close-icon"
                        :class="{
                          'remove-close-icon-disabled': true,
                        }"
                      >
                        <img alt="" src="@/assets/images/exit-search.svg?skipsvgo=true" />
                      </div>
                    </div>
                  </div>
                  <div class="ingredient-numeric-size" v-else>
                    <div class="ingredient-numeric-heading text-h3">Size:</div>
                    <div class="numeric-text">
                      <span class="ing-count">No Size.</span>
                    </div>
                  </div>
                </div>
                <div class="shoppable-review-ingredient" v-if="isAdmin">
                  <div class="shoppable-review-ingredient-keywords">
                    <span
                      class="ingredient-keyword-text text-h3"
                      v-if="
                        ingredient.keywords.length == 0 &&
                        recivedGlobalKeyword.length > 0
                      "
                      >Global Keywords:
                    </span>
                    <span
                      class="ingredient-keyword-text text-h3"
                      v-if="ingredient.keywords.length > 0"
                      >Keywords:
                    </span>
                    <span
                      class="ingredient-keyword-text text-h3"
                      v-if="
                        ingredient.keywords.length == 0 &&
                        recivedGlobalKeyword.length == 0
                      "
                      >Keywords:
                    </span>
                    <div
                      v-if="
                        ingredient.keywords.length == 0 &&
                        recivedGlobalKeyword.length == 0
                      "
                      class="ingredient-without-keyword-container"
                    >
                      <div
                        v-for="(keyword, keywordIndex) in (ingredient?.keywords || [])"
                        class="selected-ingredient-keyword"
                        :key="keywordIndex"
                      >
                        <div
                          @mouseover="showGlobalKeywordTooltip(keywordIndex)"
                          @mouseleave="hideGlobalKeywordTooltip(keywordIndex)"
                          :id="`GlobalKeyword${keywordIndex}`"
                          class="ingredient-keyword-name"
                          :class="{
                            'simple-data-tooltip': isGlobalKeywordTooltipVisible
                          }"
                          :data-tooltip-text="isGlobalKeywordTooltipVisible && keyword"
                        >
                          {{ keyword || "" }}
                        </div>
                        <div class="ingredient-keyword-remove">
                          <img
                            alt=""
                            @click="removeIngredientKeyword(keywordIndex)"
                            class="remove-ingredient-keyword-image"
                            :class="{
                              'remove-close-icon-disabled': true,
                            }"
                            src="@/assets/images/close.svg?skipsvgo=true"
                          />
                        </div>
                      </div>
                      <input
                        :disabled="true"
                        type="text"
                        :class="
                          ingredient.keywords && ingredient.keywords.length > 0
                            ? 'keywords keywordsPosition'
                            : 'keywords'
                        "
                        v-model="ingredient.keywordInput"
                        id="keywordinput"
                        autocomplete="off"
                        tabindex="0"
                        dir=""
                        autocapitalize="off"
                        :placeholder="
                          (ingredient.keywords &&
                            ingredient.keywords.length == 0) ||
                          recivedGlobalKeyword.length > 0
                            ? 'No keywords.'
                            : ''
                        "
                      />
                    </div>
                    <div
                      v-if="ingredient.keywords.length > 0"
                      class="ingredient-keyword-container"
                    >
                      <div
                        v-for="(keyword, keywordIndex) in (ingredient.keywords || [])"
                        class="selected-ingredient-keyword"
                        :key="keywordIndex"
                      >
                        <div
                          @mouseover="showGlobalKeywordTooltip(keywordIndex)"
                          @mouseleave="hideGlobalKeywordTooltip(keywordIndex)"
                          :id="`GlobalKeyword${keywordIndex}`"
                          class="ingredient-keyword-name"
                          :class="{
                            'simple-data-tooltip': isGlobalKeywordTooltipVisible
                          }"
                          :data-tooltip-text="isGlobalKeywordTooltipVisible && keyword"
                        >
                          {{ keyword || "" }}
                        </div>
                        <div class="ingredient-keyword-remove">
                          <img
                            alt=""
                            @click="removeIngredientKeyword(keywordIndex)"
                            class="remove-ingredient-keyword-image"
                            :class="{
                              'remove-close-icon-disabled': true,
                            }"
                            src="@/assets/images/close.svg?skipsvgo=true"
                          />
                        </div>
                      </div>
                      <input
                        :disabled="true"
                        type="text"
                        :class="
                          ingredient.keywords && ingredient.keywords.length > 0
                            ? 'keywords keywordsPosition'
                            : 'keywords'
                        "
                        v-model="ingredient.keywordInput"
                        id="keywordinput"
                        autocomplete="off"
                        tabindex="0"
                        dir=""
                        autocapitalize="off"
                        :placeholder="
                          ingredient.keywords && ingredient.keywords.length == 0
                            ? 'No keywords.'
                            : ''
                        "
                      />
                    </div>
                    <div
                      v-if="
                      !ingredient.keywords.length && recivedGlobalKeyword.length
                      "
                      class="ingredient-global-keyword-container"
                    >
                      <div
                        v-for="(keyword, keywordIndex) in (recivedGlobalKeyword || [])"
                        class="selected-ingredient-keyword"
                        :key="keywordIndex"
                          :class="{
                            'simple-data-tooltip simple-data-tooltip-left': isGlobalKeywordTooltipVisible
                          }"
                          :data-tooltip-text="isGlobalKeywordTooltipVisible && keyword"
                      >
                        <div
                          @mouseover="showGlobalKeywordTooltip(keywordIndex)"
                          @mouseleave="hideGlobalKeywordTooltip(keywordIndex)"
                          :id="`GlobalKeyword${keywordIndex}`"
                          class="ingredient-keyword-name"
                        >
                          {{ keyword || "" }}
                        </div>
                        <div class="ingredient-keyword-remove">
                          <img
                            alt=""
                            @click="removeIngredientKeyword(keywordIndex)"
                            class="remove-ingredient-keyword-image"
                            :class="{
                              'remove-close-icon-disabled': true,
                            }"
                            src="@/assets/images/close.svg?skipsvgo=true"
                          />
                        </div>
                      </div>
                      <input
                        :disabled="true"
                        type="text"
                        :class="
                          recivedGlobalKeyword &&
                          recivedGlobalKeyword.length > 0
                            ? 'keywords keywordsPosition'
                            : 'keywords'
                        "
                        v-model="ingredient.keywordInput"
                        id="keywordinput"
                        autocomplete="off"
                        tabindex="0"
                        dir=""
                        autocapitalize="off"
                        :placeholder="
                          (recivedGlobalKeyword &&
                            recivedGlobalKeyword.length == 0) ||
                          (ingredient.keyword &&
                            ingredient.keywords.length == 0)
                            ? 'No keywords.'
                            : ''
                        "
                      />
                    </div>
                  </div>
                  <div
                    v-if="
                      (ingredient &&
                        ingredient.weightInGrams &&
                        ingredient.weightInGrams > 0) ||
                      (ingredient &&
                        ingredient.volumeInMl &&
                        ingredient.volumeInMl > 0)
                    "
                    class="ingredient-numeric-size"
                  >
                    <div class="ingredient-numeric-heading text-h3">Size:</div>
                    <div class="numeric-text">
                      <div
                        class="ing-count"
                        v-if="
                          ingredient &&
                          ingredient.weightInGrams &&
                          ingredient.weightInGrams > 0
                        "
                      >
                        <span>
                          {{
                            ingredient && ingredient.weightInGrams
                              ? ingredient.weightInGrams
                              : ""
                          }}
                        </span>
                      </div>
                      <span
                        class="ing-value"
                        v-if="
                          ingredient &&
                          ingredient.weightInGrams &&
                          ingredient.weightInGrams > 0
                        "
                        >g</span
                      >
                      <span
                        class="ing-count"
                        v-if="
                          ingredient &&
                          ingredient.volumeInMl &&
                          ingredient.volumeInMl > 0
                        "
                      >
                        <span>
                          {{
                            ingredient && ingredient.volumeInMl
                              ? ingredient.volumeInMl
                              : ""
                          }}
                        </span>
                      </span>
                      <span
                        class="ing-value"
                        v-if="
                          ingredient &&
                          ingredient.volumeInMl &&
                          ingredient.volumeInMl > 0
                        "
                        >ml</span
                      >
                      <div
                        class="close-icon"
                        :class="{
                          'remove-close-icon-disabled': true,
                        }"
                      >
                        <img alt="" src="@/assets/images/exit-search.svg?skipsvgo=true" />
                      </div>
                    </div>
                  </div>
                  <div class="ingredient-numeric-size" v-else>
                    <div class="ingredient-numeric-heading text-h3">Size:</div>
                    <div class="numeric-text">
                      <span class="ing-count">No Size.</span>
                    </div>
                  </div>
                  <div v-if="isAdmin" class="computed-size">
                    <span class="computed-size-text text-h3">Computed size:</span>
                    <div v-if="!ingredient.loading && !ingredientsSize.weightInGrams && !ingredientsSize.volumeInMl" class="computed-size-no-data text-light-h3">
                      no data
                    </div>
                    <div v-if="!ingredient.loading && Object.keys(ingredientsSize).length > 0" class="computed-size-with-data text-light-h3">
                      <span v-if="ingredientsSize.weightInGrams">{{ ingredientsSize.weightInGrams }}</span>
                      <span v-if="ingredientsSize.weightInGrams" class="computed-unit">g</span>
                      <span v-if="ingredientsSize.weightInGrams && ingredientsSize.volumeInMl">&nbsp;/&nbsp;</span>
                      <span v-if="ingredientsSize.volumeInMl"> {{ ingredientsSize.volumeInMl }}</span>
                      <span v-if="ingredientsSize.volumeInMl" class="computed-unit">ml</span>
                    </div>
                  </div>
                </div>
              </div>
              <img
                alt=""
                @click="showEditProductDetails()"
                src="@/assets/images/arrow-down-green.png"
                class="dropdown-icon-show-more-detail"
                :class="{
                  rotate: isEditProductMoreDetailsVisible,
                }"
              />
            </div>
          </div>
        </div>
        <div
          v-if="ingredient.promotedTotalProducts == 0 && ingredient.totalProducts == 0 &&
          ingredient.products.length == 0 && displayBrandSelected == 'All' && !ingredient.loading && !ingredient.isSearchOn && ingredient.shoppableSearchQuery == ''"
          class="non-products"
        >
          <span class="text text-light-h3"> No Promoted Products. No Product Matches.</span>
        </div>
        <div
          v-else
          class="main-product-container"
          >
          <div
            class="edit-shoppable-promoted-products"
            v-if="!ingredient.loading"
            v-show="
              ingredient &&
              ingredient.campaignData &&
              ingredient.campaignData.shoppableFlag &&
              ingredient.campaignData.shoppableFlag != 'nonShoppable'
            "
          >
            <div
              v-show="
                ingredient &&
                ingredient.campaignData &&
                ingredient.campaignData.shoppableFlag &&
                ingredient.campaignData.shoppableFlag != 'nonShoppable'
              "
              id="shoppableFilterSection"
              class="tag-filter-section"
            >
              <div
                :id="`scrollTag${ingredient.id}`"
                v-if="isProductLabelFilterEnabled"
                class="tag-filter"
              >
                <div class="tag-filter-text">Tag Filter</div>
                <div class="tag-search-brand-popup">
                  <div
                    :id="`fiterdropdown${ingredient.id}`"
                    @click.stop="setFilterBrandPopupVisible(ingredient)"
                    class="tag-brand-details"
                  >
                    <span class="tag-brand-selected">{{
                      displayBrandSelected == "Gluten Free Verified" ? "Gluten Free" : displayBrandSelected == 'All' ? 'ALL' : displayBrandSelected
                    }}</span>
                    <img
                      alt=""
                      src="@/assets/images/arrow-down-red.png"
                      class="tag-brand-dropdown-icon"
                      :class="{
                        rotated: isFilterBrandPopupVisible,
                        'tag-brand-space-icon' : displayBrandSelected != 'All'
                      }"
                    />
                  </div>
                  <div v-if="displayBrandSelected != 'All'" class="tag-line"></div>
                  <div v-if="displayBrandSelected != 'All'" class="tag-exit-box-section">
                    <img
                      alt=""
                      class="tag-exit-brand-icon"
                      @click="resetSearchBrandAsync()"
                      src="@/assets/images/close-red.png"
                    />
                  </div>
                </div>
              </div>
              <!-- dropdown -->
              <div
                v-if="isFilterBrandPopupVisible"
                id="filter-brand-popup"
                class="shoppable-search-brand-main"
              >
                <div class="shoppable-brand-search-bar">
                  <div class="shoppable-search-bar-content">
                    <img
                      alt=""
                      @click="searchBrandAsync()"
                      class="shoppable-search-icon-grey-image"
                      src="@/assets/images/search-grey.png"
                    />
                    <input
                      autocomplete="off"
                      v-model.trim="brandSearchQuery"
                      type="text"
                      class="shoppable-search-bar-text text-title-2 font-normal"
                      placeholder="Search by Tags"
                      @input="searchBrandAsync()"
                      @keyup.down="dietAutocompleteArrowDown()"
                      @keyup.enter="searchBrandAsync()"
                      @keyup.up="dietAutocompleteArrowUp()"
                      :class="{
                        'shoppable-align-search-input-box': brandSearchQuery,
                      }"
                    />
                    <img
                      alt="close"
                      id="ingredient-search-brand-reset-query-icon"
                      class="shoppable-exit-search-icon"
                      @click="resetSearchQuery()"
                      src="@/assets/images/exit-gray.png"
                    />
                  </div>
                </div>
                <div
                  ref="dropdown"
                  class="shoppable-search-brand-data-main"
                  :id="`button`"
                >
                  <div v-if="isFilterBrandLoading" class="shoppable-image-loader">
                    <div class="shoppable-loader"></div>
                  </div>
                  <div
                    id="addTable"
                    v-if="!isFilterBrandLoading && filteredFilterList"
                    class="shoppable-brand-details-checkbox"
                  >
                    <div
                      v-for="(info, index) in (filteredFilterList || [])"
                      :key="`searchListData${index}`"
                      class="shoppable-brand-data"
                      :class="{
                        'shoppable-brand-data-add-background': info.isChecked,
                        'is-active': index === dietAutocompleteArrowCounter,
                      }"
                      :id="`selected${index}`"
                      ref="optionItems"
                      @click="toggleCheckbox(info)"
                    >
                      <span
                        v-if="info.isChecked"
                        class="filter-checked-box"
                      ></span>
                      <span
                        v-if="!info.isChecked"
                        class="filter-without-checked-box"
                      ></span>
                      <div class="shoppable-search-list-data">
                        <div class="search-shoppable-sort-name text-title-2 font-normal">
                          {{ info.display === $t('SHOPPABLE_REVIEW.GLUTEN_FREE_VERIFIED') ? $t('SHOPPABLE_REVIEW.GLUTEN_FREE') : info.display }}
                        </div>
                      </div>
                    </div>
                    <div
                      ref="addTable"
                      v-if="
                        !isFilterBrandLoading &&
                        filteredFilterList.length == 0 &&
                        !brandSearchQuery
                      "
                      class="shoppable-brand-details-checkbox"
                    >
                      <div
                        v-for="(info, index) in (filterList || [])"
                        :key="`searchListData${index}`"
                        class="shoppable-brand-data"
                        :class="{
                          'shoppable-brand-data-add-background': info.isChecked,
                          'is-active': index === dietAutocompleteArrowCounter,
                        }"
                        :id="`selected${index}`"
                        ref="optionItems"
                        @click="toggleCheckbox(info)"
                      >
                        <span
                          v-if="info.isChecked"
                          class="filter-checked-box"
                        ></span>
                        <span
                          v-if="!info.isChecked"
                          class="filter-without-checked-box"
                        ></span>
                        <div class="shoppable-search-list-data">
                          <div class="search-shoppable-sort-name">
                            {{ info.display == "Gluten Free Verified" ? "Gluten Free" : info.display }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="no-result-for-shoppable"
                  v-if="
                    !isFilterBrandLoading &&
                    filteredFilterList.length == 0 &&
                    brandSearchQuery.length > 0
                  "
                >
                  {{ $t('COMMON.NO_RESULTS') }}
                </div>
                <div class="shoppable-apply-btn-container">
                  <div class="shoppable-filter-save-btn">
                    <button
                      type="button"
                      class="shoppable-filter-btn text-h3"
                      :class="!isToggleModified ? 'disabled-button' : ''"
                      @click="applyFilterAsync()"
                    >
                    {{ $t('BUTTONS.APPLY_BUTTON') }}
                    </button>
                  </div>
                </div>
              </div>
              <div
                class="no-result-for-shoppable"
                v-if="searchListData.length == 0 && !isFilterBrandLoading"
              >
                {{ $t('COMMON.NO_RESULTS') }}
              </div>
              <div
                v-if="isProductSortEnabled"
                class="sort-by-section-main-container-popup"
              >
              </div>
            </div>
            <div
              v-show="
                ingredient &&
                ingredient.campaignData &&
                ingredient.campaignData.shoppableFlag &&
                ingredient.campaignData.shoppableFlag != 'nonShoppable'
              "
              class="show-only-promoted-products text-h3">
              <span class="toggle-top" :class="ingredient.promotedTotalProducts === 0  ? 'disable-toggle':''"> {{ $t('SHOW_ONLY_PROMOTED_PRODUCTS') }} </span>
              <label
                :class="[
                  'switch',
                  { 'simple-data-tooltip simple-data-tooltip-edge': ingredient?.promotedTotalProducts === 0 }
                ]"
                :data-tooltip-text="ingredient?.promotedTotalProducts === 0 ? $t('COMMON.PROMOTE_RECIPE_WARNING') : null"
              >
                <input
                  type="checkbox"
                  @click="toggleOnlyPromoted"
                  :disabled="ingredient?.promotedTotalProducts === 0"
                  :checked="ingredient?.campaignData?.onlyPromoted || false"
                />
                <span
                  :class="[
                    'slider-round',
                    { 'inactive-button': ingredient?.promotedTotalProducts === 0 }
                  ]"
                ></span>
              </label>
            </div>
          </div>
          <div v-show="getIngredientShoppable(ingredient) == 'Not Shoppable'" class="not-shoppable-main-box-container">
            <div class="not-shoppable-main-box-content text-light-h3">
              <span>This ingredient is not shoppable. Make shoppable to view products.</span>
            </div>
          </div>
          <div v-if="ingredient &&
            ingredient.campaignData &&
            ingredient.campaignData.shoppableFlag &&
            ingredient.campaignData.shoppableFlag != 'nonShoppable'" class="promoted-text-heading"
            >
              <div class="promoted-products" id="promoted-products-id">
                {{
                  ingredient?.promotedTotalProducts
                    ? ingredient.promotedTotalProducts
                    : ingredient?.promotedResult?.length ? ingredient.promotedResult.length : "No"
                }}
                <span v-if="ingredient?.promotedTotalProducts == 1">Promoted Product</span>
                <span v-else>Promoted Products</span>
              </div>
              <div
                v-if="ingredient.promotedTotalProducts == 0 && ingredient.promotedResult.length == 0 &&
                  !isPromoteLoadingPopupVisible &&
                  ingredient &&
                  ingredient.campaignData &&
                  ingredient.campaignData.shoppableFlag &&
                  ingredient.campaignData.shoppableFlag != 'nonShoppable'"
                class="tooltip-zero-promoted-message"
                >
                  <div
                    class="tooltip-icon simple-data-tooltip"
                    :data-tooltip-text="clickOnPromoteTooltip"
                    >
                    <img alt="info" src="@/assets/images/informationSymbol.png"/>
                  </div>
              </div>
            </div>
          <div
            class="Shoppable-popup-loader-section"
            v-show="isPromoteLoadingPopupVisible"
          >
            <div class="content">
              <div class="loading">
                <div class="input-loading">
                  <div class="loader-image"></div>
                </div>
                <div class="loading-text">
                  <p>{{ $t('LOADER.LOADING') }}</p>
                </div>
              </div>
            </div>
          </div>
          <div v-if="ingredient.promotedTotalProducts == 0 && !isPromoteLoadingPopupVisible && ingredient &&
              ingredient.campaignData &&
              ingredient.campaignData.shoppableFlag &&
              ingredient.campaignData.shoppableFlag != 'nonShoppable'"  class="zero-promoted-product-line"></div>
          <div
            class="shop-preview-popup product-section"
            v-if="!isPromoteLoadingPopupVisible"
            v-show="
              ingredient &&
              ingredient.campaignData &&
              ingredient.campaignData.shoppableFlag &&
              ingredient.campaignData.shoppableFlag != 'nonShoppable'
            "
          >
            <draggable
              v-if="ingredient?.promotedResult"
              :list="ingredient.promotedResult"
              class="list-group"
              :scroll-sensitivity="200"
              :force-fallback="true"
              ghost-class="hidden-list"
              @start="drag = true"
              @end="drag = false"
              @update="updateArrayAsync(ingredient)"
            >
              <div
                class="slider-main-section-for-pagination"
                v-for="(slide, index) in (ingredient.promotedResult || [])"
                :key="index"
              >
                <div class="card-container-for-shoppable" :class="getSameShoppableFilter(slide) ? 'disable-card' : ''">
                  <div :class="!isAdmin ? 'decrease-height-shop-preview-card' : ''" class="shop-preview-card card-container-promoted">
                    <div class="shop-preview-card-navigation">
                      <div
                        class="qty recipe-name-tooltip-qty simple-data-tooltip"
                        @click="setProductForReport(slide)"
                        :data-tooltip-text="clickToReportTooltip"
                        :class="{
                          'simple-data-tooltip-left': index === 0,
                        }"
                      >
                        {{
                          slide.shoppable && slide.shoppable.quantity
                            ? slide.shoppable.quantity + " "
                            : ""
                        }}qty
                      </div>
                      <span class="shop-preview-card-span-image"
                        ><img
                          class="shop-preview-card-image"
                          :src="
                            slide.image &&
                            slide.image.sizes &&
                            slide.image.sizes[300]
                              ? slide.image.url
                              : ''
                          "
                          alt=""
                      /></span>
                      <div
                        :id="`changePosition${slide.id}`"
                        class="promoted-product-counting-container"
                      >
                        <span
                          v-if="!slide.changePositionPopupOpened"
                          @click.stop="
                            openPromotedCountingBox(
                              slide,
                              ingredient,
                              checkCount + index + 1
                            )
                          "
                          class="promoted-product-counting"
                        >
                          {{ checkCount + index + 1 }}
                        </span>
                        <span
                          v-if="slide.changePositionPopupOpened"
                          @click="closeAllPromotedCountingBox()"
                          class="promoted-product-counting"
                        >
                          {{ checkCount + index + 1 }}
                        </span>
                        <div
                          v-if="slide.changePositionPopupOpened"
                          class="autocomplete-results-promoted"
                        >
                          <div
                            v-for="number in (ingredient.setPromotedCountingList || [])"
                            :key="`positionCounting${number.position}${index}${currentPagePromoted}`"
                          >
                            <div
                              :class="{
                                'autocomplete-result-promoted': true,
                                'is-active': '',
                              }"
                              @click="selectedPositionAsync(slide, number.position)"
                              v-if="number.position != checkCount + index + 1"
                            >
                              <span>{{ number.position }}</span>
                            </div>
                            <div
                              class="disable-current-position"
                              @click="selectedPositionAsync(slide, number.position)"
                              v-if="number.position == checkCount + index + 1"
                            >
                              <span>{{ number.position }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <span style="width: 23px; height: 17px"></span>
                    </div>
                    <div class="promoted-recipe-logo">
                      <span class="promoted-recipe-text">Promoted</span>
                    </div>
                    <div
                      v-if="slide.name || slide.brand"
                      class="recipe-name-tooltip"
                      :class="{
                        'simple-data-tooltip-left': index === 0,
                      }"
                      v-tooltip-if-overflow="setIngredientName(slide.brand, slide.name).name"
                    >
                    <span class="recipe-title-text simple-data-tooltip-text">
                      {{ setIngredientName(slide.brand, slide.name).name }}</span>
                    </div>
                    <div class="shop-preview-ingredient-price-info-container">
                      <div
                        v-if="
                          slide.inventory &&
                          slide.inventory[0] &&
                          slide.inventory[0].price
                        "
                        class="shop-preview-card-price"
                      >
                        <span>
                          $&nbsp;{{
                            slide.inventory &&
                            slide.inventory[0] &&
                            slide.inventory[0].price
                              ? slide.inventory[0].price
                              : ""
                          }}
                        </span>
                      </div>
                      <div
                        v-if="isInfoIconVisible"
                        class="shop-preview-info-icon-section"
                      >
                        <span
                          @mouseover="
                            showInfoTip(
                              slide,
                              index,
                              slide &&
                                slide.labels.desirable &&
                                slide.labels.desirable.length > 0
                                ? slide.labels.desirable
                                : ''
                            )
                          "
                          @mouseleave="hideInfoTip(slide)"
                          @click="openProductTag(slide, ingredient, true)"
                          class="info-icon-image"
                        >
                          <img alt="info" src="@/assets/images/info-icon-blue.svg?skipsvgo=true" />
                          <span
                            :style="styleForTooltip"
                            :id="`product${slide.id}`"
                            class="info-tooltip"
                          >
                            <div
                              v-show="tagText.length > 0"
                              class="info-tooltip-text-intro"
                            >
                              <div class="info-text-heading">{{ $t('TAG.TAG_TEXT')  }}:</div>
                              <div class="info-text-content">
                                <div v-if="tagText" class="info-tag-content">
                                  {{ tagText }}
                                </div>
                              </div>
                            </div>
                            <div class="info-availability-text">
                              *availability to buy in
                              <span class="info-count">{{
                                slide.numberOfStores
                              }}</span>
                              stores.
                            </div>
                          </span>
                        </span>
                      </div>
                    </div>
                    <div class="shop-preview-card-quantity">
                      <span
                        v-if="
                          slide.size &&
                          slide.size.value &&
                          slide.size.unit &&
                          (slide.size.unit.abbreviation ||
                            slide.size.unit.display)
                        "
                      >
                        {{ slide.size.value }}
                        {{
                          slide.size.unit.abbreviation
                            ? slide.size.unit.abbreviation
                            : slide.size.unit.display.toLowerCase()
                        }}
                      </span>
                    </div>
                    <div v-if="isAdmin" class="shop-preview-card-size">
                      <span v-if="slide.size.weightInGrams">{{slide.size.weightInGrams}}g</span>
                      <span class="parallel-icon" v-if="slide.size.weightInGrams && slide.size.volumeInMl">|</span>
                      <span v-if="slide.size.volumeInMl">{{slide.size.volumeInMl}}ml</span>
                    </div>
                    <div class="shop-preview-card-number">
                      {{ getProductId(slide) }}
                    </div>
                    <div class="shop-preview-card-btn-container">
                      <button type="button"
                        class="btn-green-outline"
                        @click="unPromoteProductAsync(slide, ingredient)"
                        @keydown="preventEnterAndSpaceKeyPress($event)"
                      >
                        {{ $t('COMMON.UNPROMOTE') }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
             </draggable>
            <div
              v-show="ingredient?.promotedResult?.length"
              class="empty-card-for-shoppable"
              v-for="(recipe, index) in Array.from({ length: Math.max(0, numProductMatches - (ingredient?.promotedResult?.length || 0)) })"
              :key="`empty${index}`"
            ></div>
          </div>
          <paginate
            id="pagination-block"
            v-show="
              ingredient &&
              ingredient.campaignData &&
              ingredient.campaignData.shoppableFlag &&
              ingredient.campaignData.shoppableFlag != 'nonShoppable'
            "
            v-if="
              (ingredient && ingredient.promotedTotalProducts
                ? ingredient.promotedTotalProducts
                : ingredient.promotedResult && ingredient.promotedResult.length ? ingredient.promotedResult.length : 0) > numProductMatches &&
              !isUnpromoteLoadingPopupVisible &&
              !isPromoteLoadingPopupVisible
            "
            v-model="currentPagePromoted"
            :total-rows="
              ingredient && ingredient.promotedTotalProducts
                ? ingredient.promotedTotalProducts
                : ingredient.promotedResult && ingredient.promotedResult.length ? ingredient.promotedResult.length : 0
            "
            :page-range="pageRangeOneIngredient"
            :per-page="numProductMatches"
            :page-count="
              Math.ceil(
                (ingredient && ingredient.promotedTotalProducts
                  ? ingredient.promotedTotalProducts
                  : ingredient.promotedResult && ingredient.promotedResult.length ? ingredient.promotedResult.length : 0) / numProductMatches
              )
            "
            prev-text="<"
            next-text=">"
            :prev-class="'prev-promoted'"
            :next-class="'next-promoted'"
            :click-handler="pageChangePromotedAsync"
            :container-class="'pagination-unpromoted'"
            :page-class="'page-item-unpromoted'"
            :page-link-class="'page-link-unpromoted'"
            :disabled-class="'disabled-pagination'"
            :active-class="'active-promoted'"
            :margin-pages="marginPages"
          >
          </paginate>
          <div class="edit-product-matches-section">
            <div
              class="product-matches-title-search"
              v-show="
                ingredient &&
                ingredient.campaignData &&
                ingredient.campaignData.shoppableFlag &&
                ingredient.campaignData.shoppableFlag != 'nonShoppable'
              "
            >
              <div
                v-show="ingredient.totalProducts != 0"
                class="product-matches-title dynamic-title" id="product-matches-title-id">
                {{
                  isUnpromoteLoadingPopupVisible
                    ? "..."
                    : (currentPageUnPromoted ? currentPageUnPromoted - 1 : 0) * numProductMatches + (ingredient?.products?.length ?? 0)
                }}/{{ ingredient?.totalProducts ? ingredient.totalProducts : 0 }}
                Product Matches
                <div v-if="ingredient?.campaignData?.onlyPromoted" class="not-applicable">(not applicable)</div>
              </div>
              <div
                v-show="ingredient.totalProducts == 0 && ingredient.products.length == 0"
                class="product-matches-title"
                :class="ingredient.promotedTotalProducts != 0 || ingredient.shoppableSearchQuery != '' ? ' dynamic-no-product-title' : ''"
                >
                No Product Matches
                <div v-if="displayBrandSelected !== 'All'" class="tag-filter-zero-product-tooltip-section">
                  <div
                    class="tooltip-icon simple-data-tooltip"
                    :data-tooltip-text="zeroMatchesFilterTooltip"
                  >
                    <img alt="info" src="@/assets/images/informationSymbol.png"/>
                  </div>
                </div>
              </div>
            </div>
            <div v-show="ingredient.totalProducts == 0 && ingredient.products.length == 0" class="zero-product-matches-line"></div>
            <div
              class="product-matches-title-search"
              v-show="
                ingredient &&
                ingredient.campaignData &&
                ingredient.campaignData.shoppableFlag &&
                ingredient.campaignData.shoppableFlag == 'nonShoppable'
              "
            >
              <div class="product-matches-title">Product Matches
              </div>
            </div>
            <div
              v-show="
                ingredient &&
                ingredient.campaignData &&
                ingredient.campaignData.shoppableFlag &&
                ingredient.campaignData.shoppableFlag != 'nonShoppable' &&
                ((!ingredient.isSearchOn && ingredient.totalProducts != 0 && ingredient.shoppableSearchQuery == '') || (ingredient.shoppableSearchQuery != ''))
              "
              id="shoppableFilterSection"
              class="shoppable-filter-section"
              :class="isProductSortEnabled ? '' : 'filter-section-no-sort'"
            >
              <div
                v-if="isProductSortEnabled"
                class="sort-by-section-main-container-popup"
              >
                <div class="sort-by-section-main-popup">
                  <div class="sort-by-heading-popup text-h3">{{ $t('SORT_BY') }}</div>
                  <div
                    class="sort-by-result-main-popup"
                    @click="openSortByDropdown()"
                  >
                    <div class="sort-by-result-text-popup text-title-2 font-normal">
                      <span v-if="sortByDataName == ''">Default</span>
                      <span v-if="sortByDataName != ''">{{
                        sortByDataName
                      }}</span>
                      <div class="sort-by-arrow-icon-popup">
                        <img
                          alt=""
                          src="@/assets/images/arrow-right.png"
                          :class="
                            isSortByDropdownOpen
                              ? 'sort-by-dropdown-icon-open-popup'
                              : 'sort-by-dropdown-icon-close-popup'
                          "
                        />
                      </div>
                    </div>
                    <div
                      v-if="isSortByDropdownOpen"
                      class="sort-by-dropdown-result-popup"
                    >
                      <div
                        v-for="(data, index) in (productSortOptionsData || [])"
                        :key="index"
                        class="sort-by-result-main-container-popup"
                        @click="selectSortBy(data)"
                        :class="{
                          'is-active': data && data.display == sortByDataName,
                        }"
                      >
                        <div class="sort-by-result-content-popup">
                          <span class="sort-by-result-content-text-popup text-title-2 font-normal">
                            {{ data.display }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="product-matches-search">
                <div class="search-box-shoppable-review">
                  <input
                    autocomplete="off"
                    type="text"
                    class="shoppable-review-input-box"
                    placeholder="Search by"
                    v-model="ingredient.shoppableSearchQuery"
                    :disabled="ingredient.loading"
                    @keyup.enter="ingredient.loading || !ingredient.shoppableSearchQuery  ?  '' : searchProductMatchesAsync(ingredient)"
                    @input="checkSearchProductMatches(ingredient)"
                    :class="{
                      'align-search-input-box': ingredient.shoppableSearchQuery,
                    }"
                  />
                  <div>
                    <button
                     class="btn-reset exit-shoppable-review"
                     type="button"
                     @click="resetShoppableProductsAsync()"
                     v-if="ingredient.isSearchOn"
                     >
                     <img
                      alt="cross-icon"
                      src="@/assets/images/exit-gray.png"
                    />
                    </button>
                    <button class="btn-reset shoppable-review-icon-green-edit-product-image"
                     type="button"
                     @click="ingredient.loading || !ingredient.shoppableSearchQuery  ?  '' : searchProductMatchesAsync(ingredient)"
                     >
                      <img
                      alt="search-icon"
                      src="@/assets/images/search-icon-green.png"
                    />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="Shoppable-popup-loader-section"
              v-show="isUnpromoteLoadingPopupVisible"
            >
              <div class="content">
                <div class="loading">
                  <div class="input-loading">
                    <div class="loader-image"></div>
                  </div>
                  <div class="loading-text">
                    <p>{{ $t('LOADER.LOADING') }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product-matches-box-container"
              v-show="!isUnpromoteLoadingPopupVisible"
            >
              <div
                v-if="ingredient.errorLoading"
                class="loading-product-matches-shoppable"
              >
                Error Loading Product Matches
              </div>
              <div
                v-else-if="ingredient.loading"
                class="loading-product-matches-shoppable"
              >
                Loading Product Matches
              </div>
              <div
                v-else-if="
                  ingredient.products &&
                  ingredient.products.length == 0 &&
                  ingredient.isSearchOn
                "
                class="no-product-matches-shoppable"
              >
                {{ $t('COMMON.NO_RESULTS') }}
              </div>
              <div
                v-else
                :class="
                  ingredient &&
                  ingredient.campaignData &&
                  ingredient.campaignData.shoppableFlag &&
                  ingredient.campaignData.shoppableFlag == 'nonShoppable'
                    ? 'shop-preview-popup product-disable'
                    : 'shop-preview-popup product'
                "
              >
                <div
                  class="slider-main-section-for-pagination"
                  v-for="(slide, index) in (ingredient.products || [])"
                  :key="index"
                >
                  <div class="card-container-for-shoppable">
                    <div :class="!isAdmin ? 'decrease-height-shop-preview-card' : ''" class="shop-preview-card">
                      <div class="shop-preview-card-navigation">
                        <div
                          class="qty recipe-name-tooltip-qty simple-data-tooltip"
                          @click="setProductForReport(slide)"
                          :data-tooltip-text="clickToReportTooltip"
                          :class="{
                            'simple-data-tooltip-left': index === 0,
                          }"
                        >
                          {{
                            slide.shoppable && slide.shoppable.quantity
                              ? slide.shoppable.quantity + " "
                              : ""
                          }}qty
                        </div>
                        <span class="shop-preview-card-span-image"
                          ><img
                            class="shop-preview-card-image"
                            :src="
                              slide.image &&
                              slide.image.sizes &&
                              slide.image.sizes[300]
                                ? slide.image.url
                                : ''
                            "
                            alt=""
                        /></span>
                        <div
                          :id="`changePosition${slide.id}`"
                          class="promoted-product-counting-container"
                        >
                          <span
                            v-if="slide.changePositionPopupOpened"
                            @click="closeAllPromotedCountingBox()"
                            class="promoted-product-counting"
                          >
                            {{ index + 1 }}
                          </span>
                        </div>
                        <span style="width: 23px; height: 17px"></span>
                        <span class="menu">
                          <div @click="setProductForRemove(slide)">
                            <img
                              class="edit-btn"
                              src="~/assets/images/delete-icon.png"
                              alt=""
                            />
                          </div>
                        </span>
                      </div>
                      <div
                        class="recipe-name-tooltip"
                        v-if="slide.name || slide.brand"
                        :class="{
                          'simple-data-tooltip': setIngredientName(slide.brand, slide.name).nameLength >= 40, 'left': index === 0,
                        }"
                        :data-tooltip-text="setIngredientName(slide.brand, slide.name).nameLength >= 40 && setIngredientName(slide.brand, slide.name).name"
                      >
                        <span class="recipe-title-text">
                          {{ setIngredientName(slide.brand, slide.name).name }}
                        </span>
                      </div>
                      <div class="shop-preview-ingredient-price-info-container">
                        <div
                          v-if="
                            slide.inventory &&
                            slide.inventory[0] &&
                            slide.inventory[0].price
                          "
                          class="shop-preview-card-price"
                        >
                          <span>
                            $&nbsp;{{
                              slide.inventory &&
                              slide.inventory[0] &&
                              slide.inventory[0].price
                                ? slide.inventory[0].price
                                : ""
                            }}
                          </span>
                        </div>
                        <div
                          v-if="isInfoIconVisible"
                          class="shop-preview-info-icon-section"
                        >
                          <span
                            @mouseover="
                              showInfoTip(
                                slide,
                                index,
                                slide &&
                                  slide.labels.desirable &&
                                  slide.labels.desirable.length > 0
                                  ? slide.labels.desirable
                                  : ''
                              )
                            "
                            @mouseleave="hideInfoTip(slide)"
                            @click="openProductTag(slide, ingredient, false)"
                            class="info-icon-image"
                          >
                            <img
                              alt=""
                              src="@/assets/images/informationSymbol.png"
                            />
                            <span
                              :style="styleForTooltip"
                              :id="`product${slide.id}`"
                              class="info-tooltip"
                            >
                              <div
                                v-show="tagText.length > 0"
                                class="info-tooltip-text-intro"
                              >
                                <div class="info-text-heading">{{ $t('TAG.TAG_TEXT')  }}:</div>
                                <div class="info-text-content">
                                  <div v-if="tagText" class="info-tag-content">
                                    {{ tagText }}
                                  </div>
                                </div>
                              </div>
                              <div class="info-availability-text">
                                *availability to buy in
                                <span class="info-count">{{
                                  slide.numberOfStores
                                }}</span>
                                stores.
                              </div>
                            </span>
                          </span>
                        </div>
                      </div>
                      <div class="shop-preview-card-quantity">
                        <span
                          v-if="
                            slide.size &&
                            slide.size.value &&
                            slide.size.unit &&
                            (slide.size.unit.abbreviation ||
                              slide.size.unit.display)
                          "
                        >
                          {{ slide.size.value }}
                          {{
                            slide.size.unit.abbreviation
                              ? slide.size.unit.abbreviation
                              : slide.size.unit.display.toLowerCase()
                          }}
                        </span>
                      </div>
                      <div v-if="isAdmin" class="shop-preview-card-size">
                        <span v-if="slide.size.weightInGrams">{{slide.size.weightInGrams}}g</span>
                        <span class="parallel-icon" v-if="slide.size.weightInGrams && slide.size.volumeInMl">|</span>
                        <span v-if="slide.size.volumeInMl">{{slide.size.volumeInMl}}ml</span>
                      </div>
                      <div class="shop-preview-card-number">
                        {{ getProductId(slide) }}
                      </div>
                      <div class="shop-preview-card-btn-container">
                        <button type="button"
                          class="btn-green-outline"
                          @click="promoteProductAsync(slide)"
                          @keydown="preventEnterAndSpaceKeyPress($event)"
                        >
                          {{ $t('COMMON.PROMOTE') }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-show="ingredient?.products?.length"
                  class="empty-card-for-shoppable"
                  v-for="(recipe, index) in Array.from({ length: Math.max(0, numProductMatches - (ingredient?.products?.length || 0)) })"
                  :key="`empty${index}`"
                ></div>

              </div>
            </div>
          </div>
          <paginate
            id="pagination-block"
            v-show="
              ingredient &&
              ingredient.campaignData &&
              ingredient.campaignData.shoppableFlag &&
              ingredient.campaignData.shoppableFlag != 'nonShoppable'
            "
            v-if="
              (ingredient && ingredient.totalProducts
                ? ingredient.totalProducts
                : 0) > numProductMatches &&
              !isUnpromoteLoadingPopupVisible &&
              ingredient.products.length !== 0
            "
            v-model="currentPageUnPromoted"
            :total-rows="
              ingredient && ingredient.totalProducts
                ? ingredient.totalProducts
                : 0
            "
            :page-range="pageRangeOneIngredient"
            :per-page="numProductMatches"
            :page-count="
              Math.ceil(
                (ingredient && ingredient.totalProducts
                  ? ingredient.totalProducts
                  : 0) / numProductMatches
              )
            "
            prev-text="<"
            next-text=">"
            :prev-class="'prev-unpromoted'"
            :next-class="'next-unpromoted'"
            :click-handler="pageChangeUnPromotedAsync"
            :container-class="'pagination-unpromoted'"
            :page-class="'page-item-unpromoted'"
            :page-link-class="'page-link-unpromoted'"
            :disabled-class="'disabled-pagination'"
            :active-class="'active-unpromoted'"
            :margin-pages="marginPages"
          >
          </paginate>
          </div>
      </div>
    </div>
    <div class="shoppable-review-for-edit-product-button-container">
      <button type="button"
        @click="confirmCloseShoppableReview()"
        @keydown="preventEnterAndSpaceKeyPress($event)"
        class="btn-green-outline"
      >Cancel</button>
      <button type="button"
        @click="confirmSaveShoppableReview()"
        @keydown="preventEnterAndSpaceKeyPress($event)"
        :class="
          isCampaignModified
            ? 'btn-green'
            : 'disabled-button btn-green'
        "
      >Save</button>
    </div>
    <savingModal v-show="isIngredientSaving" :status="'saving'" />
    <Modal v-if="isProductTagOpen" @close="closeModal">
      <template #previewReferenceIngredient>
        <div class="shoppable-ingredient-product-tag-container">
          <div class="shoppable-ingredient-reference-top-section">
            <div class="product-tag-section-heading">Product details</div>
            <div class="product-tag-section-close-button" @click="closeModal()">
              <img
                class="close-icon"
                alt=""
                src="@/assets/images/exit-gray.png"
              />
            </div>
          </div>
          <div class="product-reference-ingredient-middle-section">
            <div class="product-reference-ingredient-name">
              Ingredient name «{{
                currentTagReferenceIngredientName
                  ? currentTagReferenceIngredientName
                  : ""
              }}»
            </div>
          </div>
          <div class="product-reference-ingredient-bottom-section">
            <div
              id="product-reference-ingredient-table-data"
              class="product-reference-ingredient-table-data"
            >
              <table
                class="reference-product-table"
                aria-label="reference-product-table"
              >
                <thead class="reference-product-ingredient-table-head">
                  <tr class="reference-product-ingredient-table-head-row">
                    <th class="reference-ingredient-product-image-heading"></th>
                    <th class="reference-ingredient-product-id-heading">
                      {{ $t('PRODUCT_ID') }}
                    </th>
                    <th class="reference-ingredient-product-title-heading">
                      {{ $t('PRODUCT_TITLE') }}
                    </th>
                    <th class="reference-ingredient-product-weight-heading">
                      {{ $t('WEIGHT') }}
                    </th>
                    <th class="reference-ingredient-product-quantity-heading">
                      {{ $t('QUANTITY') }}
                    </th>
                    <th
                      class="reference-ingredient-product-store-count-heading"
                    >
                      {{ $t('STORE_COUNT') }}
                    </th>
                  </tr>
                </thead>
                <tbody class="reference-product-ingredient-table-body">
                  <tr class="reference-product-ingredient-table-body-row">
                    <td class="ingredient-product-image">
                      <div>
                        <img
                          :src="
                            tagData.image &&
                            tagData.image.sizes &&
                            tagData.image.sizes[500]
                              ? tagData.image.url
                              : ''
                          "
                          alt=""
                        />
                      </div>
                      <div
                        v-if="showPromotedIcon"
                        class="ingredient-promoted-recipe-logo"
                      >
                        <span class="promoted-recipe-text">Promoted</span>
                      </div>
                    </td>
                    <td class="ingredient-product-id text-light-h4">
                      <div>
                        {{ getProductId(tagData) }}
                      </div>
                    </td>
                    <td class="ingredient-product-title text-h3">
                      <div
                        v-if="tagData.name || tagData.brand"
                        class="product-title"
                      >
                        {{ setTagName(tagData.brand, tagData.name) }}
                      </div>
                    </td>
                    <td class="ingredient-product-weight text-light-h3">
                      <div
                        v-if="
                          tagData.size &&
                          tagData.size.value &&
                          tagData.size.unit &&
                          (tagData.size.unit.abbreviation ||
                            tagData.size.unit.display)
                        "
                      >
                        {{ tagData.size.value }}
                        {{
                          tagData.size.unit.abbreviation
                            ? tagData.size.unit.abbreviation
                            : tagData.size.unit.display.toLowerCase()
                        }}
                      </div>
                    </td>
                    <td class="ingredient-product-quantity text-light-h3">
                      <div v-if="tagData.shoppable.quantity">
                        {{
                          tagData.shoppable.quantity
                            ? tagData.shoppable.quantity
                            : ""
                        }}
                      </div>
                    </td>
                    <td class="ingredient-product-store-count text-light-h3">
                      {{
                        tagData.numberOfStores ? tagData.numberOfStores : "0"
                      }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="shoppable-tag-and-nutrition-main-container">
                <div class="shoppable-ingredient-reference-tags-main-container">
                  <div
                    v-if="tagData.labels && tagData.labels.desirable.length > 0"
                    class="shoppable-ingredient-tags-heading text-title-2"
                  >
                    {{ $t('TAG.TAG_TEXT')  }}:
                  </div>
                  <div
                    v-if="tagData.labels && tagData.labels.desirable.length > 0"
                    class="shoppable-ingredient-reference-tags-list-container"
                  >
                    <div
                      v-for="(tag, index) in (tagData.labels.desirable || [])"
                      :key="index"
                      class="shoppable-ingredient-reference-tags-card"
                    >
                      <div
                        v-if="false"
                        class="shoppable-ingredient-reference-tags-image"
                      ></div>
                      <div
                        v-if="tag.display"
                        class="shoppable-ingredient-reference-tags-name text-title-2 font-normal"
                      >
                        {{ tag.display ? tag.display : "" }}
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="
                    nutritionListTableColumnOne.length > 0 ||
                    nutritionListTableColumnTwo.length > 0
                  "
                  class="shoppable-ingredient-product-details-nutrition-container"
                >
                  <div class="nutrition-section-heading text-title-2">
                    Nutrition {{ showNutritionServing }}:
                  </div>
                  <div class="nutrition-table-container">
                    <div class="nutrition-table-section">
                      <div class="nutrition-section-area">
                        <div
                          v-for="(item, index) in (nutritionListTableColumnOne || [])"
                          :key="index"
                          class="nutrition-content-row"
                        >
                          <div
                            :class="
                              item.subNutrient
                                ? 'nutrition-name-heading nutrition-sub-name-heading'
                                : 'nutrition-name-heading'
                            "
                            :id="item.bold ? 'bold-text' : ''"
                          >
                            {{ item.name.display }}
                          </div>
                          <div class="nutrition-unit-heading">
                            {{
                              item && item.amount && item.amount.value
                                ? item.amount.value
                                : "0"
                            }}
                            {{
                              item &&
                              item.amount &&
                              item.amount.unit &&
                              item.amount.unit.abbreviation
                                ? item.amount.unit.abbreviation
                                : ""
                            }}
                          </div>
                          <div class="nutrition-precentage-unit-heading">
                            <span
                              v-if="
                                item &&
                                item.dvp &&
                                (item.dvp.value == 0 || item.dvp.value)
                              "
                            >
                              {{
                                item &&
                                item.dvp &&
                                item.dvp.value &&
                                (item.dvp.value == 0 || item.dvp.value)
                                  ? item.dvp.value
                                  : 0
                              }}%
                            </span>
                          </div>
                          <hr
                            :class="
                              !item.subNutrient
                                ? 'line-break'
                                : 'line-break-sub'
                            "
                          />
                        </div>
                      </div>
                    </div>
                    <div class="nutrition-table-section">
                      <div class="nutrition-section-area">
                        <div
                          v-for="(item, index) in (nutritionListTableColumnTwo || [])"
                          :key="index"
                          class="nutrition-content-row"
                        >
                          <div
                            :class="
                              item.subNutrient
                                ? 'nutrition-name-heading nutrition-sub-name-heading'
                                : 'nutrition-name-heading'
                            "
                            :id="item.bold ? 'bold-text' : ''"
                          >
                            {{ item.name.display }}
                          </div>
                          <div class="nutrition-unit-heading">
                            {{
                              item && item.amount && item.amount.value
                                ? item.amount.value
                                : "0"
                            }}
                            {{
                              item &&
                              item.amount &&
                              item.amount.unit &&
                              item.amount.unit.abbreviation
                                ? item.amount.unit.abbreviation
                                : ""
                            }}
                          </div>
                          <div class="nutrition-precentage-unit-heading">
                            <span
                              v-if="
                                item &&
                                item.dvp &&
                                (item.dvp.value == 0 || item.dvp.value)
                              "
                            >
                              {{
                                item &&
                                item.dvp &&
                                item.dvp.value &&
                                (item.dvp.value == 0 || item.dvp.value)
                                  ? item.dvp.value
                                  : 0
                              }}%
                            </span>
                          </div>
                          <hr
                            :class="
                              !item.subNutrient
                                ? 'line-break'
                                : 'line-break-sub'
                            "
                          />
                        </div>
                      </div>
                      <div class="nutrition-notes-section">
                        <span class="nutrition-notes-text text-light-h4"
                          >* Percent Daily Values based on a 2,000 calorie diet.
                          Your daily value may be higher or lower depending on
                          your calorie needs.
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="shoppable-product-reference-ingredient-cancel-button">
              <div class="btn-green-outline" @click="closeModal">{{ $t('BUTTONS.CANCEL_BUTTON') }}</div>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import savingModal from "@/components/saving-modal";
import cancelModal from "@/components/cancel-modal";
import saveModal from "@/components/save-modal.vue";
import Modal from "@/components/Modal";
import IngredientService from "@/services/IngredientService";


// composables
import { useRefUtils } from "@/composables/useRefUtils";
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useDelayTimer } from "@/composables/useDelayTimer";
import { useEventUtils } from "@/composables/useEventUtils";
import { useProjectLang } from "@/composables/useProjectLang";
import { useIngredientStore } from "@/stores/ingredient";

// images
import defaultImage from "@/assets/images/default_recipe_image.png";
import saveImage from "@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";

// utility
import { useStore } from "vuex";
import { useNuxtApp } from "#app";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";

// utility functions declare
const { getIngredientReferenceProductDataAsync, getIngredientReferenceProduct } = useIngredientStore();
const { readyProject } = useProjectLang();
const store = useStore();
const { triggerLoading } = useCommonUtils();
const { $eventBus } = useNuxtApp();
const { getRef } = useRefUtils();
const { delay } = useDelayTimer();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const { preventEnterAndSpaceKeyPress } = useEventUtils()

const props = defineProps({
  checkProductTagPopup: {
    type: Function,
  },
  recipeIngredient: {
    type: Object,
  },
  recipeName: {
    type: String,
  },
  recipeSubtitle: {
    type: String,
  },
  recipeID: {
    type: String,
  },
  recipeImage: {
    type: String,
  },
  externalImageURL: {
    type: String,
  },
  recipeProvider: {
    type: String,
  },
  ingredientCampaign: {
    type: Object,
  },
  ingredientNames: {
    type: Array,
  },
  globalKeywords: {
    type: Array,
  },
  isAdminCheck: {
    type: Boolean,
  },
});


const project = ref({});
const isSaveClicked = ref(false);
const isSearchActive = ref(false);
const isProductPromote = ref(false);
const isProductUnpromote = ref(false);
const popupText = ref("");
const notApplicable = ref("");
const isProductDelete = ref(false);
const isPromoteLoadingPopupVisible = ref(false);
const isUnpromoteLoadingPopupVisible = ref(false);
const isCampaignModified = ref(false);
const isProductConfirmationDeleted = ref(false);
const isReportModalVisible = ref(false);
const isEditProductMoreDetailsVisible = ref(false);
const isIngredientSaving = ref(false);
const isPromotedLoadingPopup = ref(false);
const ingredientNote = ref("");
const isAdmin = ref(false);
const isNoteTipVisible = ref(false);
const ingredientName = ref("");
const showIngnameTip = ref("");
const isGotoPopupOpen = ref(false);
const redirectRecipeID = ref("");
const sortByDataName = ref("");
const isSortByDropdownOpen = ref(false);

const shoppableListData = ref([
  { key: "shoppable", data: "Shoppable", subName: "(not pantry)" },
  { key: "shoppablePantry", data: "Shoppable", subName: "(pantry)" },
  { key: "nonShoppable", data: "Not Shoppable" },
]);

const productQuantityList = ref([
  { value: 5, data: "5" },
  { value: 10, data: "10" },
  { value: 15, data: "15" },
  { value: 20, data: "20" },
]);

const ingredientUomAutocompleteArrowCounter = ref(0);
const numProductMatches = ref(5);
const pageRangeOneIngredient = ref(4);
const currentPagePromoted = ref(1);
const currentPageUnPromoted = ref(1);
const marginPages = ref(0);
const fromPromotedProductMatches = ref(0);
const fromProductMatches = ref(0);
const ingredient = ref({});
const reportIssueTypes = ref([
  { key: "incorrectQuantity", display: "Wrong quantity" },
  { key: "other", display: "Other" },
]);
const copyReferenceTooltip = 'Copy Product ID to the clipboard';
const isGlobalKeywordTooltipVisible = ref(false);
const clickToReportTooltip = 'Click to report a quantity issue.';
const clickOnPromoteTooltip = 'To promote products, click on “Promote” in Product Matches.';
const zeroMatchesFilterTooltip = 'Selected filter(s) have 0 results. Remove the filter(s) to view all product matches.';
const selectedIssueType = ref("incorrectQuantity");
const issueDescription = ref("");
const recivedGlobalKeyword = ref([]);
const currentIndexOfPromotedIng = ref(null);
const productIdCopyToClipboard = ref(false);
const previewReferenceProductData = ref(false);
const copiedReferenceProductID = ref(false);
const isSaveModalVisible = ref(false);
const isConfirmModalVisible = ref(false);
const referenceIngredientProductData = ref({});
const currentReferenceIngredientName = ref("");
const styleForTooltip = ref({
  visibility: "hidden",
});

const isProductTagOpen = ref(false);
const tagText = ref("");
const tagData = ref({});
const showPromotedIcon = ref(false);
const currentTagReferenceIngredientName = ref("");
const shoppableNutritionData = ref([]);
const nutritionListTableColumnOne = ref([]);
const nutritionListTableColumnTwo = ref([]);
const tagNutrition = ref([]);
const showNutritionServing = ref("");
const isInfoIconVisible = ref(false);
const isFilterBrandPopupVisible = ref(false);
let count = ref(0);
const filterList = ref([]);
const displayBrandSelected = ref("");
const isBrandCrossIconVisible = ref(false);
const brandSearchQuery = ref("");
const isFilterBrandLoading = ref(false);
const searchListData = ref(0);
const localArray = ref([]);
const isToggleModified = ref(false);
const dietAutocompleteArrowCounter = ref(0);
const isProductLabelFilterEnabled = ref(false);
const selectedFliterItems = ref([]);
const isProductSortEnabled = ref(false);
const productSortOptionsData = ref([]);
const sortByDataKey = ref("");
const filteredFilterList = ref([]);
const hasLabelsKeys = ref([]);
const ingredientsSize = ref({});
const tempLabel = ref([]);
const lang = ref("");
const ingredientsUomList = ref([]);
const currentProduct = ref([]);
const promotedResultPopupRef = ref(null);
const searchedUomText = ref("");

// Computed properties
const checkCount = computed(() => {
  return (currentPagePromoted.value - 1) * numProductMatches.value;
});

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (!isProjectReady) return;

    displayBrandSelected.value = count.value == 0 ? "All" : count.value;
    isBrandCrossIconVisible.value = count.value !== 0;
    project.value = store.getters["userData/getProject"];

    displayBrandSelected.value = "All";
    lang.value = store.getters["userData/getDefaultLang"];
    setRecipePageSize();
    ingredient.value = {
      ...props.recipeIngredient,
      loading: true,
      campaignData: getDefaultCampaignData(),
      originalCampaignData: getDefaultCampaignData(),
      uomAutocomplete: false,
      isShoppableDropDown: false,
      shoppableSearchQuery: "",
      isSearchOn: false,
      products: [],
      totalProducts: 0,
      promotedTotalProducts: 0,
      promotedResult: [],
      setPromotedCountingList: []
    };
    recivedGlobalKeyword.value = props.globalKeywords;
    isAdmin.value = props.isAdminCheck;

    document.addEventListener("click", handleClickOutside);
    document.addEventListener("keyup", handleESCClickOutside);

    getShoppableReviewConfigDataResult();

    if (ingredient?.value?.productId) {
      await getIngredientReferenceProductAsync(ingredient.value.productId);
    }

    await getRecipeUnitConfig();
    await resetShoppableCampaignAndProductsAsync();
    filterList.value[0].isChecked = true;

    $eventBus.on("productpromote", isProductPromotedAsync);
    $eventBus.on("productunpromote", isProductUnpromotedAsync);
    $eventBus.on("productdelete", isProductDeletedAsync);
  });
});

const getSameShoppableFilter = (slide) => {
  let filter = true;
  if (hasLabelsKeys.value && hasLabelsKeys.value.length > 0 && displayBrandSelected.value !== "All") {
    let tempLabel = [];
    slide.labels.desirable.forEach((ing) => {
      tempLabel.push(ing.key);
    });
    if (arraysAreEqual(hasLabelsKeys.value, tempLabel)) {
      filter = false;
    }
  } else if (slide.labels.desirable.length === 0 && hasLabelsKeys.value && hasLabelsKeys.value.length > 0) {
    filter = false;
  } else {
    filter = false;
  }
  return filter;
};

const arraysAreEqual = (arr1, arr2) => {
  return arr1.every(value => arr2.includes(value));
};

const getShoppableReviewConfigDataResult = async () => {
  const params = {
    lang: lang.value
  }
  try {
    await store.dispatch("shoppableReview/getShoppableReviewConfigDataAsync", {
      params
    });
    const response = store.getters['shoppableReview/getShoppableReviewConfig'];
    if (response) {
      isInfoIconVisible.value = response?.productInfoEnabled ?? false;
      isProductLabelFilterEnabled.value = response?.productLabelFilterEnabled ?? false;
      let productLabelFilterOptions = response?.productLabelFilterOptions ?? [];
      let sortedFilterOptions = productLabelFilterOptions.sort((optionA, optionB) =>
        optionA.display.localeCompare(optionB.display)
      );
      sortedFilterOptions.forEach((data) => {
        data["isChecked"] = false;
      });
      filterList.value = [
        { display: "All", isChecked: false },
        ...sortedFilterOptions,
      ];
      shoppableNutritionData.value = response?.productNutrients ?? [];
      isProductSortEnabled.value = response?.productSortEnabled ?? false;
      productSortOptionsData.value = response?.productSortOptions ?? [];
    }
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getShoppableReviewConfigDataResultAsync:", error);
  }
};

const scrollToSelected = (refs) => {
  nextTick(() => {
    const optionItems = refs.optionItems;
    if (optionItems && optionItems.length > 0) {
      const selectedItem = optionItems[dietAutocompleteArrowCounter.value];
      if (selectedItem) {
        const dropdown = refs.dropdown;
        const dropdownRect = dropdown.getBoundingClientRect();
        const selectedItemRect = selectedItem.getBoundingClientRect();
        if (
          selectedItemRect.top < dropdownRect.top ||
          selectedItemRect.bottom > dropdownRect.bottom
        ) {
          const scrollTop = selectedItem.offsetTop - dropdown.offsetTop;
          dropdown.scrollTop = scrollTop - 44;
        }
      }
    }
  });
};

const searchBrandAsync = async () => {
  if (brandSearchQuery.value.length) {
    isFilterBrandLoading.value = true;
  }
  await delayTimerShoppableAsync();
  dietAutocompleteArrowCounter.value = -1;
  const brandSearchQueryLower = brandSearchQuery.value.toLowerCase();
  filteredFilterList.value = filterList.value.filter((filterItem) =>
    filterItem?.display.toLowerCase().includes(brandSearchQueryLower)
  );
  isFilterBrandLoading.value = false;
  const iconEl = getRef("ingredient-search-brand-reset-query-icon");
  if (iconEl) {
    iconEl.style.display = !brandSearchQuery.value.length ? "none" : "block";
  }
};

const resetSearchQuery = () => {
  brandSearchQuery.value = "";
  filteredFilterList.value = [];
  const iconEl = getRef("ingredient-search-brand-reset-query-icon");
  if (iconEl) {
    iconEl.style.display = "none";
  }
  dietAutocompleteArrowCounter.value = -1;
};

const toggleCheckbox = (info) => {
  if (info.display !== "All") {
    filterList.value.forEach((item, index) => {
      if (index === 0) item.isChecked = false;
      else if (item.display === info.display) item.isChecked = !item.isChecked;
    });
  } else if (info.display === "All") {
    filterList.value.forEach((item, index) => {
      if (index === 0) item.isChecked = true;
      else item.isChecked = false;
    });
  }
  count.value = 0;
  filterList.value.forEach((item) => {
    if (!item.isChecked) count.value++;
  });
  if (count.value === filterList.value.length) {
    filterList.value[0].isChecked = true;
  }
  isToggleModified.value = true;
};

const setFilterBrandPopupVisible = async (ingredient) => {
  let element = getRef("scrollTag" + ingredient.id);
  let headerOffset = 70;
  let elementPosition = element?.offsetTop || 0;
  let offsetPosition = elementPosition - headerOffset;
  document.documentElement.scrollTop = offsetPosition;
  document.body.scrollTop = offsetPosition;
  dietAutocompleteArrowCounter.value = -1;
  await nextTick();
  setTimeout(() => {
    let divElement = getRef("shoppableFilterSection");
    if (divElement) {
      divElement.scrollIntoView({ behavior: "smooth" });
    }
  }, 100);

  isFilterBrandPopupVisible.value = !isFilterBrandPopupVisible.value;
  filterList.value.forEach((item) => {
    item.isChecked = false;
  });
  selectedFliterItems.value.forEach((selectedItem) => {
    const item = filterList.value.find(
      (item) => item.display === selectedItem.display
    );
    if (item) {
      item.isChecked = true;
    }
  });

  if (selectedFliterItems.value.length === 0) {
    filterList.value[0].isChecked = true;
  }

  if (hasLabelsKeys.value.length > 0) {
    hasLabelsKeys.value.forEach((key) => {
      filterList.value.forEach((item) => {
        if (item?.key === key) {
          item.isChecked = true;
        }
      });
    });
  }

  isToggleModified.value = false;
  brandSearchQuery.value = "";
  filteredFilterList.value = [];
};
const applyFilterAsync = async () => {
  isBrandCrossIconVisible.value = true;
  let count = 0;

  // Handle "All" selection and reset search
  filterList.value.forEach((item, index) => {
    if (index === 0 && item?.isChecked) {
      displayBrandSelected.value = "All";
      resetSearchBrandAsync();
    }
  });

  // Count checked items and update displayBrandSelected accordingly
  filterList.value.forEach((item) => {
    if (item?.isChecked) count++;
  });
  if (count > 1) {
    displayBrandSelected.value = count;
  } else if (count === 1) {
    filterList.value.forEach((item) => {
      if (item?.isChecked) {
        displayBrandSelected.value = item.display;
      }
    });
  }

  isFilterBrandPopupVisible.value = false;

  // Smooth scroll to shoppable section
  const divElement = getRef("shoppableFilterSection");
  if (divElement) {
    setTimeout(() => {
      divElement?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  }

  // Update selected filter items
  selectedFliterItems.value = filterList.value.filter((item) => item?.isChecked);

  // Handle labels keys and visibility of brand cross icon
  if (selectedFliterItems.value.length > 0 && selectedFliterItems.value[0].display !== "All") {
    hasLabelsKeys.value = [];
    selectedFliterItems.value.forEach((item) => {
      if (item?.key) {
        hasLabelsKeys.value.push(item.key);
        isBrandCrossIconVisible.value = true;
      }
    });
  }

  if (selectedFliterItems.value[0]?.display === "All" && hasLabelsKeys.value.length === 0) {
    hasLabelsKeys.value = [];
    isBrandCrossIconVisible.value = false;
  } else {
    isBrandCrossIconVisible.value = true;
  }

  // Loading and product fetching
  isUnpromoteLoadingPopupVisible.value = true;
  isPromotedLoadingPopup.value = true;
  fromProductMatches.value = 0;
  currentPageUnPromoted.value = 1;
  const result = await getShoppableProductsAsync(ingredient.value);

  // Update ingredient product data
  isUnpromoteLoadingPopupVisible.value = false;
  isPromoteLoadingPopupVisible.value = false;
  ingredient.value.promotedResult = result?.promotedResult ? [...result.promotedResult] : [];
  ingredient.value.promotedTotalProducts = result?.promotedTotalProducts || 0;
  ingredient.value.products = result?.products ? [...result.products] : [];
  ingredient.value.totalProducts = result?.totalProducts || 0;

  setCampaignModified();
};

const resetSearchBrandAsync = async () => {
  displayBrandSelected.value = "All";
  hasLabelsKeys.value = [];
  isBrandCrossIconVisible.value = false;
  brandSearchQuery.value = "";
  isFilterBrandPopupVisible.value = false;

  filterList.value.forEach((item, index) => {
    item.isChecked = index === 0;
  });

  selectedFliterItems.value = [];
  isUnpromoteLoadingPopupVisible.value = true;
  fromProductMatches.value = 0;
  currentPageUnPromoted.value = 1;

  const result = await getShoppableProductsAsync(ingredient.value);

  isUnpromoteLoadingPopupVisible.value = false;
  ingredient.value.promotedResult = result?.promotedResult ? [...result.promotedResult] : [];
  ingredient.value.promotedTotalProducts = result?.promotedTotalProducts || 0;
  ingredient.value.products = result?.products ? [...result.products] : [];
  ingredient.value.totalProducts = result?.totalProducts || 0;

  setCampaignModified();
};

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    selectedIssueType.value = "incorrectQuantity";
    issueDescription.value = "";
    closeModal();
  }
};

const openSortByDropdown = () => {
  if (sortByDataName.value === "") {
    sortByDataName.value = "Default";
  }
  isSortByDropdownOpen.value = !isSortByDropdownOpen.value;
};

const selectSortBy = (data) => {
  sortByDataName.value = data.display;
  sortByDataKey.value = data.key;
  isUnpromoteLoadingPopupVisible.value = true;
  getProductMatchesAsync();
  currentPageUnPromoted.value = 1;
  pageChangeUnPromotedAsync(currentPageUnPromoted.value);
};

const setTagName = (brand, name) => {
  const brandName = brand || "";
  const tagName = name || "";
  if (tagName !== "" && tagName.startsWith(brandName)) {
    return tagName;
  } else {
    return String(brandName) + String(tagName);
  }
};

const openProductTag = (data, ing, isPromoted) => {
  currentTagReferenceIngredientName.value = ing && ing.name ? ing.name : "";
  showPromotedIcon.value = isPromoted;
  isProductTagOpen.value = true;
  nutritionListTableColumnOne.value = [];
  nutritionListTableColumnTwo.value = [];

  if (data) {
    tagData.value = { ...data };
  }
  if (data.nutrients) {
    tagNutrition.value = [];
    tagNutrition.value = data.nutrients;

    if (tagNutrition.value?.perServing?.length > 0) {
      showNutritionServing.value = "per serving";
      tagNutrition.value.perServing.forEach((item) => {
        shoppableNutritionData.value.forEach((data) => {
          if (item.name.key === data.key) {
            item.bold = data.bold;
            item.column = data.column;
            item.subNutrient = data.subNutrient;
          }
        });
      });

      tagNutrition.value.perServing.forEach((item) => {
        if (item.column === "1") {
          nutritionListTableColumnOne.value.push(item);
        }
        if (item.column === "2") {
          nutritionListTableColumnTwo.value.push(item);
        }
      });
    }
  }
  props.checkProductTagPopup(isProductTagOpen.value);
};

const previewReferenceProduct = (ing) => {
  currentReferenceIngredientName.value = ing;
  previewReferenceProductData.value = true;
};

const copyReferenceProduct = () => {
  const copyElement = getRef("refrenceProduct");
  navigator.clipboard.writeText(copyElement.innerHTML);
  productIdCopyToClipboard.value = true;
  copiedReferenceProductID.value = true;

  setTimeout(() => {
    productIdCopyToClipboard.value = false;
  }, 1000);

  setTimeout(() => {
    copiedReferenceProductID.value = false;
  }, 1000);
};

const getIngredientReferenceProductAsync = async (productID) => {
  if (productID) {
    try {
      await getIngredientReferenceProductDataAsync(productID, lang.value);
      const response = await getIngredientReferenceProduct.value;
      if (response?.externalId?.toString() === productID.toString()) {
        referenceIngredientProductData.value = response;
      }
    }
    catch (e) {
      console.error(e);
    }
  }
};

const showInfoTip = (data, index, tag) => {
  const temp = tag ? tag.map(item => item.display) : [];
  tagText.value = temp.join(", ");

  const element = getRef("product" + data.id);
  if (element) {
    element.style.visibility = "visible";
    if ((index + 1) % numProductMatches.value === 0) {
      element.classList.remove("info-tooltip");
      element.classList.add("info-tooltip-right");
    }
  }
};

const hideInfoTip = (data) => {
  const element = getRef("product" + data.id);
  if (element) {
    element.style.visibility = "hidden";
  }
};

const setRecipePageSize = () => {
  if (window.innerWidth > 1550 && window.innerWidth <= 1625) {
    numProductMatches.value = 6;
  } else if (window.innerWidth > 1625 && window.innerWidth <= 1800) {
    numProductMatches.value = 7;
  } else if (window.innerWidth > 1800 && window.innerWidth <= 2100) {
    numProductMatches.value = 8;
  } else if (window.innerWidth > 2100 && window.innerWidth <= 2400) {
    numProductMatches.value = 9;
  } else if (window.innerWidth > 2400) {
    numProductMatches.value = 10;
  } else {
    numProductMatches.value = 5; // Default case
  }
};

const pageChangePromotedAsync = async (event = currentPagePromoted.value) => {
  isPromoteLoadingPopupVisible.value = true;
  fromPromotedProductMatches.value = (event - 1) * numProductMatches.value;

  const result = await getShoppableProductsAsync(ingredient.value);
  isPromoteLoadingPopupVisible.value = false;

  ingredient.value.promotedResult = result?.promotedResult || [];
  ingredient.value.promotedTotalProducts = result?.promotedTotalProducts || 0;
  ingredient.value.products = result?.products || [];
  ingredient.value.totalProducts = result?.totalProducts || 0;
};

const pageChangeUnPromotedAsync = async (event = currentPageUnPromoted.value) => {
  isUnpromoteLoadingPopupVisible.value = true;
  fromProductMatches.value = (event - 1) * numProductMatches.value;

  const result = await getShoppableProductsAsync(ingredient.value);
  isUnpromoteLoadingPopupVisible.value = false;

  ingredient.value.promotedResult = result?.promotedResult || [];
  ingredient.value.promotedTotalProducts = result?.promotedTotalProducts || 0;
  ingredient.value.products = result?.products || [];
  ingredient.value.totalProducts = result?.totalProducts || 0;
};

const showGlobalKeywordTooltip = (index) => {
  const element = getRef("GlobalKeyword" + index);
  if (element && element.scrollWidth > element.clientWidth) {
    isGlobalKeywordTooltipVisible.value = true;
  }
};

const hideGlobalKeywordTooltip = (index) => {
  const element = getRef("GlobalKeyword" + index);
  if (element && element.scrollWidth > element.clientWidth) {
    isGlobalKeywordTooltipVisible.value = true;
  }
};

const navigateToRecipe = () => {
  triggerLoading('goToRecipe');
  router.push({
    path: "recipe-detail",
    query: {
      isin: redirectRecipeID.value,
      from: 1,
      fromPage: "edit-ingredient",
    },
  });
};

const openRedirectModal = (id) => {
  isGotoPopupOpen.value = true;
  redirectRecipeID.value = id;
  props.checkProductTagPopup(isGotoPopupOpen.value);
};

const isProductPromotedAsync = async () => {
  isProductPromote.value = true;
  popupText.value = "Product Promoted";
  await delayTimerShoppableAsync();
  isProductPromote.value = false;
  popupText.value = "";
};

const isProductUnpromotedAsync = async () => {
  isProductUnpromote.value = true;
  popupText.value = "Product Unpromoted";
  await delayTimerShoppableAsync();
  isProductUnpromote.value = false;
  popupText.value = "";
};

const isProductDeletedAsync = async () => {
  isProductDelete.value = true;
  popupText.value = "Removed";
  await delayTimerShoppableAsync();
  isProductDelete.value = false;
  popupText.value = "";
};


const delayTimerShoppableAsync = async() => {
  await delay(1000);
};

const closeModal = () => {
  isProductPromote.value = false;
  isProductUnpromote.value = false;
  isProductDelete.value = false;
  isSaveModalVisible.value = false;
  isConfirmModalVisible.value = false;
  isGotoPopupOpen.value = false;
  productIdCopyToClipboard.value = false;
  previewReferenceProductData.value = false;
  isProductTagOpen.value = false;
  showPromotedIcon.value = false;
  isProductConfirmationDeleted.value = false;
  isReportModalVisible.value = false;
  isToggleModified.value = false;
  props.checkProductTagPopup(false);
};

const checkNote = (value) => {
  ingredientNote.value = value;
  const note = getRef("ingredientNote");
  if (note.scrollWidth > note.clientWidth) {
    isNoteTipVisible.value = true;
  }
};

const checkNoteHide = () => {
  isNoteTipVisible.value = false;
};

const checkName = (value) => {
  ingredientName.value = value;
  const name = getRef("ingredientName");
  if (name.scrollWidth > name.clientWidth) {
    showIngnameTip.value = true;
  }
};

const checkNameHide = () => {
  showIngnameTip.value = false;
};

const setIngredientName = (brand, name) => {
  const ingBrand = brand.toLowerCase();
  const ingName = name.toLowerCase();
  if (ingName.startsWith(ingBrand)) {
    return { name: name, nameLength: name.length };
  } else {
    return {
      name: `${brand} ${name}`,
      nameLength: `${brand} ${name}`.length,
    };
  }
};

const setCampaignModified = () => {
  isCampaignModified.value = true;
};

const closeReportModal = () => {
  isReportModalVisible.value = false;
  selectedIssueType.value = "incorrectQuantity";
  issueDescription.value = "";
};

const closeDeleteModal = () => {
  isProductConfirmationDeleted.value = false;
  props.checkProductTagPopup(false);
};

const removeIngredientKeyword = (keywordIndex) => {
  ingredient.value.keywords.splice(keywordIndex, 1);
  setCampaignModified();
  resetShoppableProductsAsync();
};

const getRecipeUnitConfig = async () => {
  const params = {
    lang: lang.value
  }
  try {
    await store.dispatch("ingredient/getRecipeUnitConfigAsync", {
      params,
    });
    const response = store.getters['ingredient/getRecipeUnit'];
    if (response?.units) {
      ingredientsUomList.value = response.units
        .filter(unit => unit.display != null && unit.display !== '')
        .sort((a, b) => a.display.localeCompare(b.display));
    }
  } catch(e) {
    console.error(e);
    throw e;
  }
};
const setIngredientUomResult = (result) => {
  setCampaignModified();
  ingredient.value.UOM = result.display;
  searchedUomText.value = "";
  ingredient.value.uomAutocomplete = false;

  if (ingredient.value && !ingredient.value.UOM) {
    ingredientUomAutocompleteArrowCounter.value = -1;
  }

  resetShoppableProductsAsync();
};

const toggleOnlyPromoted = () => {
  setCampaignModified();
  ingredient.value.campaignData.onlyPromoted = !ingredient.value.campaignData.onlyPromoted;
  notApplicable.value = ingredient.value.campaignData.onlyPromoted;
};

const checkSearchProductMatches = (ingredient) => {
  if (!ingredient.shoppableSearchQuery && ingredient.isSearchOn) {
    resetShoppableProductsAsync();
  }
};

const searchProductMatchesAsync = async (ingredient) => {
  isUnpromoteLoadingPopupVisible.value = true;
  isSearchActive.value = true;

  if (!ingredient.shoppableSearchQuery) {
    await resetShoppableProductsAsync();
  }

  fromProductMatches.value = 0;
  currentPageUnPromoted.value = 1;
  ingredient.products.splice(0, ingredient.products.length);
  ingredient.isSearchOn = true;

  let result = await getShoppableProductsAsync(ingredient);
  isUnpromoteLoadingPopupVisible.value = false;

  ingredient.promotedResult = result?.promotedResult ? [...result.promotedResult] : [];
  ingredient.promotedTotalProducts = result?.promotedTotalProducts || 0;
  ingredient.products = result?.products ? [...result.products] : [];
  ingredient.totalProducts = result?.totalProducts || 0;
  ingredient.loading = false;
};

const selectedShoppable = (shoppable) => {
  setCampaignModified();
  if (ingredient.value.campaignData) {
    ingredient.value.campaignData.shoppableFlag = shoppable;
  }
  ingredient.value.isShoppableDropDown = false;
};

const getIngredientShoppable = () => {
  if (ingredient.value.campaignData?.shoppableFlag) {
    for (const shoppableData of shoppableListData.value) {
      if (shoppableData.key === ingredient.value.campaignData.shoppableFlag) {
        return shoppableData.data;
      }
    }
  }
  return "";
};
const getIngredientShoppableSubName = () => {
  if (ingredient.value.campaignData?.shoppableFlag) {
    for (const shoppableData of shoppableListData.value) {
      if (shoppableData.key === ingredient.value.campaignData.shoppableFlag) {
        return shoppableData.subName;
      }
    }
  }
  return "";
};

const getProductId = (product) => {
  const projectIds = ['e_heb', 'heb_sandbox', 'central_market', 'joev'];
  return projectIds.includes(project.value.id)
    ? product.externalId || product.gtin || ""
    : product.gtin;
};

const removeProduct = (product) => {
  setCampaignModified();
  const { includedProducts, filteredProducts } = ingredient.value.campaignData;

  const index = includedProducts.indexOf(product.gtin);
  if (index > -1) {
    includedProducts.splice(index, 1);
  } else {
    filteredProducts.push(product.gtin);
  }
};

const unPromoteProductAsync = async (product, ingredient) => {
  $eventBus.emit('productunpromote');
  scrollToElement("product-matches-title-id", 0);
  isPromoteLoadingPopupVisible.value = true;
  isUnpromoteLoadingPopupVisible.value = true;
  setCampaignModified();

  if (ingredient.promotedResult.length <= 1 && currentPagePromoted.value > 1) {
    fromPromotedProductMatches.value -= numProductMatches.value;
    currentPagePromoted.value -= 1;
  }

  const index = ingredient.campaignData.promotedProducts.indexOf(product.gtin);
  if (index > -1) {
    ingredient.campaignData.promotedProducts.splice(index, 1);
  }

  // Fetch updated shoppable products
  const result = await getShoppableProductsAsync(ingredient);
  isPromoteLoadingPopupVisible.value = false;
  isUnpromoteLoadingPopupVisible.value = false;

  ingredient.promotedResult = result?.promotedResult ? [...result.promotedResult] : [];
  ingredient.promotedTotalProducts = result?.promotedTotalProducts || 0;
  ingredient.products = result?.products ? [...result.products] : [];
  ingredient.totalProducts = result?.totalProducts || 0;

  if (ingredient.isSearchOn && ingredient.shoppableSearchQuery !== "" && ingredient.name !== "") {
    await resetShoppableProductsAsync();
  }
};
const promoteProductAsync = async (product) => {
  $eventBus.emit('productpromote');
  scrollToElement("promoted-products-id", 140);
  setCampaignModified();
  isPromoteLoadingPopupVisible.value = true;
  isUnpromoteLoadingPopupVisible.value = true;

  if (ingredient.value.products.length <= 1 && currentPageUnPromoted.value > 1) {
    fromProductMatches.value -= numProductMatches.value;
    currentPageUnPromoted.value -= 1;
  }

  ingredient.value.campaignData.promotedProducts.unshift(product.gtin);
  const result = await getShoppableProductsAsync(ingredient.value);
  isPromoteLoadingPopupVisible.value = false;
  isUnpromoteLoadingPopupVisible.value = false;

  ingredient.value.promotedResult = result?.promotedResult ? [...result.promotedResult] : [];
  ingredient.value.promotedTotalProducts = result?.promotedTotalProducts ? result.promotedTotalProducts : 0;
  ingredient.value.products = result?.products ? [...result.products] : [];
  ingredient.value.totalProducts = result?.totalProducts || 0;
};

const scrollToElement = (refName, headerOffset) => {
  let element = getRef(refName);
  if (!element) return;
  let scrollContainer = element.closest("#shoppable-review-for-edit-product-main-id");

  if (scrollContainer) {
    let elementPosition = element.offsetTop;
    let offsetPosition = elementPosition - headerOffset;
    scrollContainer.scrollTo({
      top: offsetPosition
    });
  }
};

const resetShoppableCampaignAndProductsAsync = async () => {
  ingredient.value.originalCampaignData = getDefaultCampaignData();

  if (ingredientNameMatches()) {
    Object.assign(ingredient.value.originalCampaignData, props.ingredientCampaign);
  }

  ingredient.value.campaignData = getDefaultCampaignData();

  let campaigns;
  try {
    const params = {
      ingredients: [ingredient.value.name].join('|')
    }
    campaigns = await store.dispatch("ingredient/getIngredientsCampaignDataAsync", { params });
  } catch (e) {
    if (!(e.response && e.response.status === 404)) {
      console.error(e);
      throw e;
    }
  }

  if (campaigns) {
    const recipeCampaign = campaigns.find(campaign => campaign.data?.recipe === props.recipeID);

    if (recipeCampaign) {
      ingredient.value.originalCampaignData = recipeCampaign.data;
      ingredient.value.originalCampaignData.version = recipeCampaign.version;
      ingredient.value.originalCampaignData.identifier = recipeCampaign.identifier;

      const hasLabels = recipeCampaign.data.hasLabels || [];

      if (hasLabels.length > 0) {
        filterList.value.forEach(item => {
          if (hasLabels.includes(item?.key)) {
            hasLabelsKeys.value.push(item?.key);
          }
        });
        selectedFliterItems.value.push(...hasLabelsKeys.value);

        hasLabelsKeys.value.forEach(key => {
          filterList.value.forEach(item => {
            if (item?.key === key) {
              item.isChecked = true;
              count.value++;

              if (count.value > 1) {
                displayBrandSelected.value = count.value;
              } else if (count.value === 1) {
                filterList.value.forEach(item => {
                  if (item?.isChecked) {
                    displayBrandSelected.value = item.display;
                  }
                });
              }
            }
          });
        });
      }
    }
  }

  copyCampaignData();
  await resetShoppableProductsAsync();
};
const ingredientNameMatches = () => {
  let cleaned = ingredient.value.name
    .trim()
    .replace(/\s+/g, ' ')
    .toLowerCase();

  return props.ingredientNames.some(name => name.toLowerCase() === cleaned);
};

const getDefaultCampaignData = () => ({
  version: null,
  identifier: null,
  recipe: null,
  promotedProducts: [],
  includedProducts: [],
  filteredProducts: [],
  onlyPromoted: false,
  hasLabels: [],
  onlyIncluded: false,
  shoppableFlag: "shoppable",
});

const copyCampaignData = () => {
  const originalData = ingredient.value.originalCampaignData;

  ingredient.value.campaignData.version = originalData.version;
  ingredient.value.campaignData.identifier = originalData.identifier;
  ingredient.value.campaignData.recipe = originalData.recipe;
  ingredient.value.campaignData.promotedProducts.push(...originalData.promotedProducts);
  ingredient.value.campaignData.filteredProducts.push(...originalData.filteredProducts);
  ingredient.value.campaignData.includedProducts.push(...originalData.includedProducts);
  ingredient.value.campaignData.onlyPromoted = originalData.onlyPromoted;
  ingredient.value.campaignData.hasLabels = originalData.hasLabels;
  ingredient.value.campaignData.onlyIncluded = originalData.onlyIncluded;
  ingredient.value.campaignData.shoppableFlag = originalData.shoppableFlag;
};

const resetShoppableProductsAsync = async () => {
  isUnpromoteLoadingPopupVisible.value = true;
  isPromoteLoadingPopupVisible.value = true;
  fromProductMatches.value = 0;
  currentPageUnPromoted.value = 1;
  ingredient.value.shoppableSearchQuery = "";
  ingredient.value.isSearchOn = false;
  ingredient.value.products = [];
  const result = await getShoppableProductsAsync(ingredient.value);
  isUnpromoteLoadingPopupVisible.value = false;
  isPromoteLoadingPopupVisible.value = false;

  ingredient.value.promotedResult = result?.promotedResult ? [...result.promotedResult] : [];
  ingredient.value.promotedTotalProducts = result?.promotedTotalProducts || 0;
  ingredient.value.products = result?.products ? [...result.products] : [];
  ingredient.value.totalProducts = result?.totalProducts || 0;
  ingredient.value.loading = false; // Ensure this is set according to your loading logic
};

const getShoppableProductsAsync = async (ingredient) => {
  try {
    let result, promotedResult;

    if (ingredient.isSearchOn) {
      result = await getProductMatchesAsync(ingredient, 0, ingredient.shoppableSearchQuery);
    } else {
      result = await getProductMatchesAsync(ingredient, 0, null);
    }

    promotedResult = await getPromotedProductMatchesAsync(ingredient);

    return {
      promotedResult: promotedResult?.products ? [...promotedResult.products] : [],
      promotedTotalProducts: promotedResult?.totalProducts ?? 0,
      products: result?.products ? [...result.products] : [],
      totalProducts: result?.totalProducts ?? 0,
    };
  } catch (error) {
    console.error(error);
    ingredient.errorLoading = true;
    return {
      products: [],
      totalProducts: 0,
      promotedResult: [],
      promotedTotalProducts: 0,
    };
  }
};

// Get unit key based on ingredient's UOM
const getUnitKey = () => {
  if (ingredient.value.UOM) {
    const unit = ingredientsUomList.value.find((uom) => uom.display === ingredient.value.UOM);
    if (!unit) {
      throw new Error(`Unit ${ingredient.value.UOM} not found`);
    }
    return unit.key;
  }
  return null;
};

const getPromotedProductMatchesAsync = async (ingredient) => {
  isPromotedLoadingPopup.value = true;
  const unitKey = getUnitKey();
  const productList = [];
  let totalProducts = 0;
  const promotedProducts = ingredient.campaignData.promotedProducts;

  if (promotedProducts?.length > 0) {
    const payload = {
      recipeIsin: props.recipeID,
      name: ingredient.name,
      quantity: Number(ingredient.quantity),
      unit: unitKey,
      keywords: ingredient.keywords.length ? ingredient.keywords : props.globalKeywords || [],
      promotedGtins: promotedProducts,
      weightInGrams: (ingredient.weightInGrams && ingredient.weightInGrams !== "0") ? ingredient.weightInGrams : null,
      volumeInMl: (ingredient.volumeInMl && ingredient.volumeInMl !== "0") ? ingredient.volumeInMl : null,
    };
    const params = {
      lang: lang.value,
      from: fromPromotedProductMatches.value ?? 0,
      size: numProductMatches.value,
    };

    try {
      await store.dispatch("shoppableReview/getIngredientPromotedProductsAsync", { params, payload });
      const response = store.getters['shoppableReview/getIngredientPromotedProducts'];

      ingredient.errorLoading = false;

      if (
        response &&
        response.ingredient === ingredient.name &&
        response.products?.results
      ) {
        const products = response.products.results;

        promotedProducts.forEach((gtin) => {
          products.forEach((product) => {
            if (product.gtin === gtin && !productList.find((item) => item.gtin === product.gtin)) {
              product.id = `${ingredient.id}_${product.gtin}`;
              product.changePositionPopupOpened = false;
              product.menuDropDown = false;
              productList.push(product);
            }
          });
        });

        totalProducts += response.products.total;
      }
    } catch (error) {
      console.error(error);
      ingredient.errorLoading = true;
    }
  }

  isPromotedLoadingPopup.value = false;

  return {
    products: productList,
    totalProducts: totalProducts,
  };
};

const getProductMatchesAsync = async (ingredient, skip, searchQuery) => {
  const unitKey = getUnitKey();
  let products = [];
  let totalProducts = 0;

  const payload = {
    recipeIsin: props.recipeID,
    name: ingredient.name,
    quantity: Number(ingredient.quantity),
    unit: unitKey,
    keywords: ingredient.keywords.length ? ingredient.keywords : props.globalKeywords || [],
    promotedGtins: ingredient.campaignData.promotedProducts,
    includedGtins: ingredient.campaignData.includedProducts,
    filteredGtins: ingredient.campaignData.filteredProducts,
    onlyIncluded: ingredient.campaignData.onlyIncluded,
    filterTerm: searchQuery,
    weightInGrams: (ingredient.weightInGrams && ingredient.weightInGrams !== "0") ? ingredient.weightInGrams : null,
    volumeInMl: (ingredient.volumeInMl && ingredient.volumeInMl !== "0") ? ingredient.volumeInMl : null,
    sort: sortByDataKey.value,
    hasLabels: hasLabelsKeys.value,
  };
  const params = {
    lang: lang.value,
    from: fromProductMatches.value ?? 0,
    size: numProductMatches.value,
  };

  try {
    await store.dispatch("shoppableReview/getIngredientProductMatchesAsync", { params, payload });
    const response = store.getters['shoppableReview/getIngredientProductMatches'];

    ingredientsSize.value = response.size || {};
    ingredient.errorLoading = false;

    if (
      response &&
      response.ingredient === ingredient.name &&
      response.products?.results
    ) {
      products = response.products.results;

      products.forEach((product) => {
        product.id = `${ingredient.id}_${product.gtin}`;
        product.changePositionPopupOpened = false;
        product.menuDropDown = false;
      });
      totalProducts += response.products.total;
    }
  } catch (error) {
    console.error(error);
    ingredient.errorLoading = true;
  }

  return {
    products,
    totalProducts,
  };
};

const openPromotedCountingBox = (product, ingredientData, index) => {
  currentIndexOfPromotedIng.value = index;

  ingredientData.setPromotedCountingList = Array.from(
    { length: ingredientData.promotedTotalProducts },
    (_, i) => ({
      position: i + 1,
    })
  );

  currentProduct.value = product;
  closeAllPromotedCountingBox();

  // Open the current product's position popup
  product.changePositionPopupOpened = true;
};

// Close all promoted counting boxes
const closeAllPromotedCountingBox = () => {
  ingredient.value.promotedResult.forEach((product) => {
    product.changePositionPopupOpened = false;
  });
};

const selectedPositionAsync = async (product, position) => {
  setCampaignModified();

  // Update promoted products
  ingredient.value.campaignData.promotedProducts.splice(currentIndexOfPromotedIng.value - 1, 1);
  ingredient.value.campaignData.promotedProducts.splice(position - 1, 0, product.gtin);

  isPromoteLoadingPopupVisible.value = true;

  // Fetch updated shoppable products
  const result = await getShoppableProductsAsync(ingredient.value);
  isPromoteLoadingPopupVisible.value = false;

  // Update ingredient promoted results
  ingredient.value.promotedResult = result?.promotedResult || [];
  ingredient.value.promotedTotalProducts = result?.promotedTotalProducts || 0;

  closeAllPromotedCountingBox();
};

// Toggle the display of the product dropdown menu
const displayOptionShoppableReview = (product) => {
  currentProduct.value = product;
  product.menuDropDown = !product.menuDropDown;
  closeProductsDropDown();
};

// Close all product dropdowns
const closeProductsDropDown = () => {
  ingredient.value.products.forEach((product) => {
    product.menuDropDown = false;
  });
};

// Set product for reporting
const setProductForReport = (product) => {
  product.menuDropDown = false;
  currentProduct.value = product;
  isReportModalVisible.value = true;
  props.checkProductTagPopup(isReportModalVisible.value);
};

// Set product for removal
const setProductForRemove = (product) => {
  product.menuDropDown = false;
  currentProduct.value = product;
  isProductConfirmationDeleted.value = true;
  props.checkProductTagPopup(isProductConfirmationDeleted.value);
};

// Async method to confirm product removal
const removeProductConfirmedAsync = async () => {
  if (ingredient.value.products.length <= 1 && currentPageUnPromoted.value > 1) {
    fromProductMatches.value -= numProductMatches.value;
    currentPageUnPromoted.value -= 1;
  }

  removeProduct(currentProduct.value);
  isProductConfirmationDeleted.value = false;
  isUnpromoteLoadingPopupVisible.value = true;

  // Emit product delete event
  $eventBus.emit("productdelete");

  // Fetch updated shoppable products
  const result = await getShoppableProductsAsync(ingredient.value);
  isUnpromoteLoadingPopupVisible.value = false;

  // Update ingredient products
  ingredient.value.products = result?.products || [];
  ingredient.value.totalProducts = result?.totalProducts || 0;
};

const reportProductAsync = async () => {
  const payload = {
    isin: props.recipeID,
    ingredient: ingredient.value.name,
    gtin: currentProduct.value.gtin,
    type: selectedIssueType.value,
    description: issueDescription.value,
  };

  try {
    await store.dispatch(
      "shoppableReview/postProductSuggestionIssueAsync",
      { payload }
    );
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    isReportModalVisible.value = false;
    selectedIssueType.value = "incorrectQuantity";
    issueDescription.value = "";
  }
};

const showShoppableDropDown = () => {
  ingredient.value.isShoppableDropDown = !ingredient.value.isShoppableDropDown;
};

const showEditProductDetails = () => {
  isEditProductMoreDetailsVisible.value = !isEditProductMoreDetailsVisible.value;
};

const confirmCloseShoppableReview = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
    props.checkProductTagPopup(isConfirmModalVisible.value);
  } else {
    closeShoppableReview();
  }
};

const closeShoppableReview = () => {
  document.removeEventListener("click", handleClickOutside);
  $eventBus.emit("closeShoppableReview");
};

const confirmSaveShoppableReview = () => {
  isSaveModalVisible.value = true;
  props.checkProductTagPopup(isSaveModalVisible.value);
};

const saveShoppableReview = () => {
  if (isSaveClicked.value) return;

  isSaveClicked.value = true;
  isIngredientSaving.value = true;
  document.removeEventListener("keyup", handleESCClickOutside);
  document.removeEventListener("click", handleClickOutside);

  ingredient.value.campaignData.hasLabels = hasLabelsKeys.value;

  const cleanIngredient = {
    name: ingredient.value.name,
    quantity: ingredient.value.quantity,
    UOM: ingredient.value.UOM,
    rawText: ingredient.value.rawText,
    foodItem: ingredient.value.foodItem,
    uomAutocomplete: false,
    group: ingredient.value.group,
    excludeFromNutrition: ingredient.value.excludeFromNutrition,
    level: ingredient.value.level,
    modifier: ingredient.value.modifier,
    note: ingredient.value.note,
    keywords: ingredient.value.keywords,
    keywordInput: "",
    campaignData: ingredient.value.campaignData,
    hasOverridingCampaign: campaignHasChanges(),
    productId: ingredient.value.productId,
    hasLabels: hasLabelsKeys.value || [],
  };

  $eventBus.emit("saveShoppableReview", cleanIngredient);
};

const campaignHasChanges = () => {
  const originalCampaignData = ingredient.value.originalCampaignData;
  const currentCampaignData = ingredient.value.campaignData;

  if (originalCampaignData.promotedProducts.length !== currentCampaignData.promotedProducts.length) {
    return true;
  }

  for (let i = 0; i < originalCampaignData.promotedProducts.length; i++) {
    if (originalCampaignData.promotedProducts[i] !== currentCampaignData.promotedProducts[i]) {
      return true;
    }
  }

  if (originalCampaignData.hasLabels !== currentCampaignData.hasLabels ||
      originalCampaignData.filteredProducts.length !== currentCampaignData.filteredProducts.length ||
      originalCampaignData.includedProducts.length !== currentCampaignData.includedProducts.length ||
      originalCampaignData.onlyPromoted !== currentCampaignData.onlyPromoted ||
      originalCampaignData.shoppableFlag !== currentCampaignData.shoppableFlag) {
    return true;
  }

  for (const gtin of originalCampaignData.filteredProducts) {
    if (!currentCampaignData.filteredProducts.includes(gtin)) {
      return true;
    }
  }

  for (const gtin of originalCampaignData.includedProducts) {
    if (!currentCampaignData.includedProducts.includes(gtin)) {
      return true;
    }
  }

  return false;
};

const handleClickOutside = (event) => {
  handleDropdownClose(event);
  handleFilterBrandPopup(event);
  handleUomAutocomplete(event);
  handleShoppableDropdown(event);
  handleProductMenuDropdown(event);
  handlePromotedResultPopup(event);
};

const isElementClicked = (selector, event) => {
  const element = document.querySelector(selector);
  return element && element.contains(event.target);
};

const handleDropdownClose = (event) => {
  if (isSortByDropdownOpen.value && !isElementClicked(".sort-by-result-main-popup", event)) {
    isSortByDropdownOpen.value = false;
  }
};

const handleFilterBrandPopup = (event) => {
  if (isFilterBrandPopupVisible.value) {
    const brandDetailsClicked = isElementClicked(".shoppable-brand-details", event);
    const searchBrandClicked = isElementClicked(".shoppable-search-brand-main", event);
    if (!brandDetailsClicked && searchBrandClicked) {
      isFilterBrandPopupVisible.value = true;
    } else if (!brandDetailsClicked) {
      isFilterBrandPopupVisible.value = false;
    }
  }
};

const handleUomAutocomplete = (event) => {
  if (ingredient.value.uomAutocomplete && !isElementClicked(`#uomDropDown`, event)) {
    ingredient.value.uomAutocomplete = false;
  }
};

const handleShoppableDropdown = (event) => {
  if (ingredient.value.isShoppableDropDown && !isElementClicked(`#shoppableDropDownArrow`, event)) {
    ingredient.value.isShoppableDropDown = false;
  }
};

const handleProductMenuDropdown = (event) => {
  ingredient?.value?.products?.forEach((product) => {
    if (product.menuDropDown && !isElementClicked(`#openMenu${currentProduct.value.id}`, event)) {
      product.menuDropDown = false;
    }
  });
};

const handlePromotedResultPopup = (event) => {
  if (!promotedResultPopupRef.value?.contains(event.target)) {
    ingredient.value.promotedResult.forEach((product) => {
      if (product?.changePositionPopupOpened && !isElementClicked(`#changePosition${currentProduct.value.id}`, event)) {
        product.changePositionPopupOpened = false;
      }
    });
  }
};

const updateArrayAsync = async () => {
  setCampaignModified();
  const preArray = ingredient.value.promotedResult.map(data => data.gtin);
  const newArray = [...ingredient.value.campaignData.promotedProducts];

  newArray.splice(fromPromotedProductMatches.value, preArray.length, ...preArray);
  ingredient.value.campaignData.promotedProducts = newArray;

  isPromoteLoadingPopupVisible.value = true;
  try {
    const result = await getShoppableProductsAsync(ingredient.value);
    ingredient.value.promotedResult = result?.promotedResult || [];
    ingredient.value.promotedTotalProducts = result?.promotedTotalProducts || 0;
    ingredient.value.products = result?.products || [];
    ingredient.value.totalProducts = result?.totalProducts || 0;
  } catch (error) {
    console.error("Failed to fetch shoppable products:", error);
    // Optionally handle errors here (e.g., show a notification)
  } finally {
    isPromoteLoadingPopupVisible.value = false;
  }
};
</script>
