<template>
    <div>
        <Modal @close="$emit('closeModal')">
            <template #recipeVariantModal>
                <div class="add-recipe-variant-modal">
                    <div class="recipe-variant-image">
                        <img alt="" src="@/assets/images/circular-add-icon.png" />
                    </div>
                    <div class="recipe-variant-content">
                        <div class="recipe-variant-title">Add {{ addItem }}?</div>
                        <div class="recipe-variant-desc">
                            {{ $t('VARIANT_POPUP') }}
                        </div>
                        <div class="recipe-variant-button-container">
                            <button type="button" class="btn-green-outline" @click="closeAddConfirmModal()">
                                {{ $t('BUTTONS.CANCEL_BUTTON') }}
                            </button>
                            <button type="button" class="btn-green" @click="openAddPopUp()">
                                {{ $t('BUTTONS.CONFIRM_BUTTON') }}
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import Modal from "@/components/Modal";
export default {
    name: "add-ingredient-group-step-modal",
    components: {
        Modal,
    },
    data() {
        return {

        };
    },
    props: {
        closeModal: {
            type: Function,
        },
        closeAddConfirmModal: {
            type: Function,
        },
        openAddPopUp: {
            type: Function,
        },
        addItem: {
            type: String,
        },
    },
};
</script>
