<template>
  <div class="getting-started-main-container">
    <div class="getting-started-left-container">
      <div
        class="getting-started-left-section"
        v-for="(item, index) in videoData"
        :key="index"
      >
        <div class="radio-button-container">
          <div class="getting-started-radio-button">
            <input
              type="radio"
              @click="showGettingStartedAsync(item)"
              :value="item.value"
              :checked="item.isChecked"
              name="radio"
              :data-test-id="`getting-started-video-${index}`"
            />
            <span class="checkmark"></span>
          </div>
        </div>
        <div class="title-and-time-section">
          <div class="getting-started-title-container">
            <button
              type="button"
              class="getting-started-title btn-reset"
              @click="showGettingStartedAsync(item)"
            >
              {{ item.name }}
            </button>
          </div>
          <div class="getting-started-total-time text-light-h3">
            {{
              item.duration ? parseDurationString(item.duration) : item.duration
            }}
          </div>
        </div>
      </div>
    </div>
    <div class="getting-started-right-section">
      <div class="getting-started-video-container">
        <div
          class="getting-started-video-player"
          id="getting-started-video-player"
        >
          <video
            v-for="(video, index) in videoData"
            :key="index"
            v-show="video?.isChecked"
            id="getting-started-video-container-popup"
            type="video/mp4"
            @canplaythrough="checkVideoLoaded"
            @dblclick="onFullScreen"
            :src="video.link"
          >
            <track
              v-if="video.subtitles"
              :src="video.subtitles"
              kind="subtitles"
              srclang="en"
              label="English"
            />
            <track
              v-if="video.description"
              :src="video.description"
              kind="description"
              srclang="en"
              label="English"
            />
          </video>
          <button type="button" id="getting-started-play-pause">
            <img
              alt="Play"
              class="getting-started-play-video-icon-image"
              id="getting-started-play-video-icon-image"
              :src="videoPlayButton"
            />
          </button>
        </div>
        <div v-show="isVideoLoaded" class="getting-started-seek-container">
          <span class="getting-started-seek">
            <input
              type="range"
              ref="seekBar"
              id="getting-started-seek-bar"
              class="getting-started-bar"
              value="0"
              @input="onSeekBarInput"
            />
          </span>
        </div>
        <div v-show="isVideoLoaded" id="getting-started-video-controls">
          <button
            type="button"
            @click="onFullScreen($event)"
            class="getting-started-full-screen-icon"
          >
            <span class="getting-started-oval">
              <img
                alt="Full Screen"
                class="getting-started-full-screen-image"
                :src="fullScreenIcon"
              />
            </span>
          </button>
          <button type="button" @click="toggleMute" id="getting-started-mute">
            <span class="getting-started-oval">
              <img
                :alt="hasVideoMuted ? 'Mute' : 'Unmute'"
                id="mute"
                :src="hasVideoMuted ? mutedImage : muteNowImage"
              />
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

  <script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance  } from "vue";
import { useI18n } from "vue-i18n";

// composables
import { useTimeUtils } from "~/composables/useTimeUtils";
import { useRefUtils } from "~/composables/useRefUtils";
import { useDelayTimer } from "~/composables/useDelayTimer";
import { useCommonUtils } from "~/composables/useCommonUtils";

// images
import fullScreenIcon from "~/assets/images/full-screen-icon.png";
import videoPlayButton from "~/assets/images/video-play-button.svg?skipsvgo=true";
import mutedImage from "~/assets/images/muted.png";
import muteNowImage from "~/assets/images/mutenow.png";

const { t } = useI18n();
const { getRef } = useRefUtils();
const { setOpacity } = useCommonUtils();
const { parseDurationString } = useTimeUtils();
const { delay } = useDelayTimer();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

// video state
const isFullScreenDisplay = ref(true);
const hasVideoMuted = ref(true);
const isVideoLoaded = ref(false);
const playButton = ref('');
const video = ref(null);
const isPlayingBeforeSeek = ref(false);
const seekBar = ref(null);
const isPlaying = ref(false); // for tracking play/pause state

// Props
const props = defineProps({
  videoData: {
    type: Array,
    default: () => [],
  },
  checkEvent: {
    type: Function,
    default: () => {},
  },
});

onMounted(() => {
    eventVideoLoaded();
});

onBeforeUnmount(function () {
  if (playButton.value) {
    playButton.value.removeEventListener("click", playButtonEvent);
  }
  if (seekBar.value) {
    seekBar.value.removeEventListener("mousedown", seekBarOnMouseDown);
    seekBar.value.removeEventListener("mouseup", seekBarOnMouseUp);
    seekBar.value.removeEventListener("change", seekBarListener);
  }
  if (video.value) {
    video.value.removeEventListener("timeupdate", videoTimeUpdate);
    video.value.removeEventListener("canplaythrough", checkVideoLoaded);
  }
  document.removeEventListener("fullscreenchange", fullScreenToggle);
});
const eventVideoLoaded = () => {
  video.value = getRef("getting-started-video-container-popup");
  playButton.value = getRef("getting-started-video-player");
  seekBar.value = getRef("getting-started-seek-bar");
  const playIcon = getRef("getting-started-play-pause");
  if (video.value) {
    initializeVideo();
  }
  if (playButton.value) {
    playButton.value.addEventListener("click", playButtonEvent);
  }
  if (seekBar.value) {
    initializeSeekBar();
  }
  if (playIcon) {
    playIcon.style.opacity = "1";
  }
  document.addEventListener("fullscreenchange", fullScreenToggle);
};
const initializeVideo = () => {
  if (video.value) {
    video.value.pause();
    video.value.addEventListener("canplaythrough", checkVideoLoaded);
    video.value.addEventListener("timeupdate", videoTimeUpdate);
    video.value.currentTime = 0;
    video.value.muted = true;
  }
};
const initializeSeekBar = () => {
  if (seekBar.value) {
    seekBar.value.addEventListener("change", seekBarListener);
    seekBar.value.addEventListener("click", seekBarClickListener);
    seekBar.value.addEventListener("mousedown", seekBarOnMouseDown);
    seekBar.value.addEventListener("mouseup", seekBarOnMouseUp);
    seekBar.value.value = 0;
  }
};
const seekBarClickListener = (event) => {
  if (video.value) {
    video.value.pause();
    setOpacity("getting-started-play-pause", "1");
    const offsetX = event.offsetX;
    const seekBarWidth = seekBar.value.offsetWidth;
    if (seekBarWidth === 0) return;
    const newTime = video.value.duration * (offsetX / seekBarWidth);
    if (Number.isFinite(newTime)) {
      video.value.currentTime = newTime;
    }
    restorePlayStateAfterSeek();
  }
};
const checkVideoLoaded = () => {
  isVideoLoaded.value = true;
};
const toggleMute = () => {
  hasVideoMuted.value = !hasVideoMuted.value;
  if (video.value) {
    video.value.muted = hasVideoMuted.value;
  }
};
const seekBarOnMouseUp = () => {
  restorePlayStateAfterSeek();
};
const seekBarOnMouseDown = () => {
  isPlayingBeforeSeek.value = !video.value?.paused;
  video.value.pause();
};
const restorePlayStateAfterSeek = () => {
  if (isPlayingBeforeSeek.value) {
    video.value.play();
    setOpacity("getting-started-play-pause", "0");
  } else {
    setOpacity("getting-started-play-pause", "1");
  }
};
const videoTimeUpdate = () => {
  timeValue.value = (100 / video.value?.duration) * video.value?.currentTime;
  if (seekBar.value) {
    seekBar.value.value = timeValue.value;
  }
};
const seekBarListener = () => {
  time.value = video.value?.duration * (seekBar.value?.value / 100);
  if (video.value) {
    video.value.currentTime = time.value;
  }
  setOpacity("getting-started-play-pause", "0");
};
const fullScreenToggle = () => {
  isFullScreenDisplay.value = !!document.fullscreenElement;
  if (!isFullScreenDisplay.value) {
    const videoElement = getRef("getting-started-video-container-popup");
    videoElement.pause();
    setOpacity("getting-started-play-pause", "1");
  }
};
const playButtonEvent = () => {
  if (!isFullScreenDisplay.value) {
    if (video.value?.paused) {
      video.value.play();
      setOpacity("getting-started-play-pause", "0");
    } else {
      video.value?.pause();
      setOpacity("getting-started-play-pause", "1");
    }
  }
};
const onFullScreen = () => {
  const mainVideo = getRef("getting-started-video-container-popup");
  if (mainVideo) {
    requestFullscreen(mainVideo);
  }
};
const requestFullscreen = (element) => {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  }
};
const showGettingStartedAsync = async (selectedData) => {
  isVideoLoaded.value = false;
  setOpacity("getting-started-play-pause", "1");
  props.checkEvent($keys.EVENT_KEY_NAMES.CLICK_GETTING_STARTED, { videoName: selectedData?.name ?? "" });

  props.videoData.forEach((item) => {
    item.isChecked = selectedData.uuid === item.uuid;
  });

  await delay(500);
  eventVideoLoaded();
};
</script>
