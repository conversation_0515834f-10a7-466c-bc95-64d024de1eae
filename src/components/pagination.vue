<template>
  <div class="pagination-section">
    <paginate
      v-if="(props.listTotal > props.sizePerPage) && props.list.length"
      id="pagination-block"
      v-model="localCurrentPage"
      :total-rows="props.listTotal"
      :page-range="props.pageRange"
      :per-page="props.sizePerPage"
      :page-count="Math.ceil(props.listTotal / props.sizePerPage)"
      first-button-text="<<"
      prev-text="<"
      next-text=">"
      last-button-text=">>"
      :prev-class="'prev'"
      :next-class="'next'"
      :first-last-button="true"
      :click-handler="pageChange"
      :container-class="'pagination'"
      :page-class="'page-item'"
      :page-link-class="'page-link'"
      :disabled-class="'disabled-pagination'"
      :active-class="'active'"
      :margin-pages="props.marginPages">
    </paginate>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  listTotal: {
    type: Number,
    required: true
  },
  sizePerPage: {
    type: Number,
    required: true
  },
  pageRange: {
    type: Number,
    default: 6
  },
  marginPages: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
});
const emit = defineEmits(["pageChange"]);

const localCurrentPage = ref(props.currentPage);

const pageChange = (newPage) => {
  emit("pageChange", newPage);
};

watch(() => props.currentPage, (newPage) => {
  localCurrentPage.value = newPage;
});
</script>
