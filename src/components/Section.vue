<template>
  <section ref="section" class="section">
    <div class="shell">
      <header v-if="$slots.title" class="section-head">
        <h6 class="section-title">
          <slot name="title" />
        </h6>
        <!-- /.section-title -->
      </header>
      <!-- /.section-head -->

      <div>
        <h6 class="section-title section-subtitle">
          <slot name="subTitle" />
        </h6>
        <!-- /.section-title -->
      </div>

      <div v-if="$slots.score">
        <h6 class="section-title">
          <slot name="score" />
        </h6>
        <!-- /.section-title -->
      </div>

      <div v-if="$slots.content" class="section-body">
        <slot name="content" />
      </div>
      <!-- /.section-body -->
    </div>
    <!-- /.shell -->
  </section>
  <!-- /.section -->
</template>

<script setup>
</script>
