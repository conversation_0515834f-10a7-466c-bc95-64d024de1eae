<template>
  <div class="invite-popup invite-popup-container" data-test-id="invite-users-popup" >
    <div class="invite-popup-title">
      <span class="text-h2 color-black">{{ $t('USERS.INVITE_USER') }}</span>
    </div>
    <div class="invite-popup-body">
      <div class="invite-popup-form">
        <div class="invite-popup-input-box">
          <input
            data-test-id="invite-users-field"
            @input="validateEmail"
            v-model.trim="localEmail"
            class="invite-popup-input-email text-title-2 font-normal"
            type="text"
            :placeholder="t('COMMON.EMAIL_PLACEHOLDER')"
            :disabled="hasInviteSent"
          />
        </div>
        <div
          v-if="hasWarning || emailExists"
          class="text-title-2 font-weight-semi-bold color-red"
        >
          <span>{{ hasWarning ? $t('USERS.INVITE_USER_SUBTITLE') : $t('USERS.EMAIL_ALREADY_EXISTS') }}</span>
        </div>
        <div
          v-if="hasInviteSent"
          data-test-id="invite-success-message"
          class="text-title-2 font-weight-semi-bold color-black"
        >
          <span>{{ $t('USERS.INVITE_USER_SUCCESS') }}</span>
        </div>
      </div>

      <div class="invite-popup-actions">
        <button
          v-if="!hasInviteSent"
          type="button"
          class="btn-green-outline"
          @click="closeModal"
        >
          {{ $t('BUTTONS.CANCEL_BUTTON') }}
        </button>
        <button
          v-if="!hasInviteSent"
          type="button"
          class="btn-green"
          @click="submitInvite"
          :disabled="!localEmail || hasWarning || emailExists"
          data-test-id="invite-users-button"
        >
          {{ $t('USERS.INVITE') }}
        </button>

        <button
          v-if="hasInviteSent"
          type="button"
          class="btn-green"
          @click="closeModal"
        >
          {{ $t('BUTTONS.CLOSE_BUTTON') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useI18n } from 'vue-i18n';

const props = defineProps({
  email: String,
  hasInviteSent: Boolean,
  emails: Array,
});

const { t } = useI18n();
const { isEmptyOrWhitespace } = useCommonUtils();

const emit = defineEmits(['post-invite', "close", "callback"]);

const localEmail = ref(props.email);
const hasWarning = ref(false);
const emailExists = ref(false);

watch(
  () => props.email,
  (newVal) => localEmail.value = newVal,
);

const validateEmail = () => {
  if (isEmptyOrWhitespace(localEmail.value)) {
    hasWarning.value = false;
    emailExists.value = false;
    return;
  }
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  hasWarning.value = !emailRegex.test(localEmail.value);
  checkIfEmailExists(localEmail.value);
};

const checkIfEmailExists = (email) => {
  const emailList = props.emails.map((user) => user.email);
  emailExists.value = emailList.includes(email);
};

const submitInvite = () => {
  if (!hasWarning.value && !emailExists.value) {
    emit("callback", localEmail.value);
  }
};

const closeModal = () => {
  emit("close", false);
};
</script>
