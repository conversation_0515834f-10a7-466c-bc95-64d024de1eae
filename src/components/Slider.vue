<template>
  <div class="slider-container">
    <div v-swiper="options">
      <div class="swiper-wrapper">
        <div
          class="swiper-slide"
          v-for="(slide, index) in slides"
          :key="id(slide)"
        >
          <slot :slide="slide" :index="index"></slot>
        </div>
      </div>
    </div>
    <div
      :id="nextId"
      class="slider-button-next"
      :class="slides.length <= 5 ? 'hide-arrows' : ''"
    ></div>
    <div
      :id="prevId"
      class="slider-button-prev"
      :class="slides.length <= 5 ? 'hide-arrows' : ''"
    ></div>
  </div>
</template>

<script>
export default {
  name: "slider-components",
  props: {
    options: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    slides: {
      type: Array,
      required: true,
      default: () => [],
    },
    id: {
      type: Function,
      default: (slide) => slide.id,
    },
  },
  computed: {
    navigationOptions() {
      return this.options.navigation;
    },
    nextId() {
      return this.navigationOptions.nextEl.replace("#", "");
    },
    prevId() {
      return this.navigationOptions.prevEl.replace("#", "");
    },
  },
};
</script>
