<template>
  <div>
    <Modal @close="closeModal">
      <template #editProductMatches>
        <div class="invalid-file-type-modal">
          <div class="invalid-file-type-modal-content">
            <div v-if="video" class="invalid-file-header">Invalid video file format</div>
            <div v-if="image" class="invalid-file-header">Invalid Image file format</div>
            <div v-if="zip" class="invalid-file-header">Invalid zip file format</div>
            <div class="invalid-file-text">Accepted file formats:{{ acceptedFile }}</div>
            <div class="invalid-file-type-button-container">
              <button type="button" class="btn-green" @click="closeModal()">
                Okay
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>
<script>
import Modal from "@/components/Modal";
export default {
  name: "invalid-image-video-popup",
  components: {
    Modal,
  },
  data() {
    return {
    };
  },
  props: {
    acceptedFile: {
      type: String,
    },
    closeModal: {
      type: Function,
      default: false
    },
    video: {
      type: Boolean,
      default: false
    },
    zip: {
      type: Boolean,
      default: false
    },
    image: {
      type: <PERSON>olean,
      default: false
    }
  },
};
</script>
