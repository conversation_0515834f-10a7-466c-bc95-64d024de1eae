<template>
  <div>
    <Modal @close="$emit('closeModal')">
      <template #nutrition>
        <div class="unable-to-publish-article">
          <div class="popup">
            <div class="cross-image-div">
              <img
                class="cross-image"
                src="../assets/images/Group 2.png"
                alt="cross"
              />
            </div>
            <div class="text-heading">
              <span v-if="text">{{ text }}</span>
            </div>
          </div>
          <div v-if="required" class="error-description">
            <p>Please fill in all Required data.</p>
          </div>
          <div class="btn-div">
            <button type="button" class="btn-red" @click="closeModal()">
              Ok
            </button>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import Modal from "@/components/Modal";
export default {
  name: "unable-to-content-modal",
  components: {
    Modal,
  },
  props: {
    closeModal: {
      type: Function,
    },
    text: {
      type: String,
    },
    required: {
      type: Boolean,
      default: true,
    },
  },
};
</script>
