<template>
    <div>
        <Modal @close="props.closeModal()">
            <template #categoryVariantName>
                <div class="add-variant-name-container">
                    <div class="top-section-recipe-variant">
                        <img alt="" class="back" src="@/assets/images/arrow-gray.png" @click="backToRoute()" />
                        <span class="add-variant-text"> Add a Variant {{ props.typeName }} name</span>
                        <img alt="" class="close-icon" src="@/assets/images/exit-gray.png" @click="props.closeModal" />
                    </div>
                    <div class="middle-section-recipe-variant">
                        <span class="variant-category-select">
                            <p v-if="props.addVariantSelectedLanguage == 'es-US'">Spanish</p>
                            <p v-if="props.addVariantSelectedLanguage == 'fr-FR'">French</p>
                            &nbsp;Variant {{ props.typeName }}&nbsp;
                            <span v-if="props.itemName">«</span>
                            <p class="item-name" v-if="props.itemName">
                                {{ props.itemName.trim() }}
                            </p>
                            <span v-if="props.itemName">»</span>
                        </span>
                        <input type="text" placeholder="Enter Name" class="variant-name-input" v-model="variantName" />
                    </div>
                    <div class="bottom-section-recipe-variant">
                        <button
                            type="button"
                            class="add-button"
                            :class="{'disabled-button': !variantName.trim()}"
                            :disabled="!variantName.trim()"
                            @click="variantName && addVariant(variantName)"
                            @keydown="preventEnterAndSpaceKeyPress($event)">
                            {{ $t('COMMON.ADD') }}
                        </button>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script setup>
import Modal from "@/components/Modal";
import { ref } from 'vue';
import { useCommonUtils } from "../composables/useCommonUtils.js";

const props = defineProps({
  closeModal: {
    type: Function,
  },
  addVariantSelectedLanguage: {
    type: String,
  },
  itemName: {
    type: String,
  },
  typeName: {
    type: String,
  },
});

const emit = defineEmits(["backToRoute", "addConfirmVariant", "preventEnterAndSpaceKeyPress"]);

const { triggerLoading } = useCommonUtils();

const variantName = ref("");

const backToRoute = () => {
  emit('backToRoute');
};
const addVariant = (variantName) => {
  emit('addConfirmVariant', variantName);
};
const preventEnterAndSpaceKeyPress = (event) => {
  triggerLoading('preventEnterAndSpaceKeyPress', event);
};
</script>
