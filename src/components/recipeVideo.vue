<template>
  <div>
    <Modal class="video-modal" @close="closeModal" id="videoModal">
      <template #videoModal>
        <div class="video-popup-main">
          <div class="close-image" @click="closeModal">
            <img alt="" src="@/assets/images/exit-gray.png" />
          </div>
          <div class="video-title text-h2">
            <span> {{ $t('BUTTONS.PREVIEW_BUTTON') }} </span>
          </div>
          <img alt="video" v-show="!isVideoLoaded" class="video-loader" src="~/assets/images/Pulse-1s-200px.gif" />
          <div v-show="isVideoLoaded" class="video-player" id="video-player">
            <video v-show="isVideoLoaded" id="video-container-popup" :src="`${videoLink}`" type="video/mp4"
              @canplaythrough="checkVideoLoaded" @dblclick="onFullScreen">
              <track v-if="isVideoLoaded?.description" :src="isVideoLoaded?.description" kind="description" srclang="en" label="English"/>
              <track v-if="isVideoLoaded?.subtitles" :src="isVideoLoaded?.subtitles" kind="subtitles" srclang="en" label="English" />
              </video>
            <button type="button" id="play-pause">
              <img alt="video" class="play-video-icon-image" id="play-video-icon-image" src="~/assets/images/playlogo.png" />
            </button>
          </div>
          <div v-show="isVideoLoaded" class="seek-container">
            <span class="seek">
              <input type="range" id="seek-bar" class="sk" value="0" />
            </span>
          </div>
          <div v-show="isVideoLoaded" id="video-controls">
            <button type="button" @click="onFullScreen($event)" class="full-screen-icon">
              <span class="oval">
                <img alt="" class="full-screen-image" id="" src="~/assets/images/full-screen-icon.png" />
              </span>
            </button>
            <button type="button" @click="onMute()" id="mute">
              <span class="oval">
                <img alt="" v-if="isVideoMuted" class="" id="muteimg" src="~/assets/images/muted.png" />
                <img alt="" v-if="!isVideoMuted" id="muteimg" src="~/assets/images/mutenow.png" />
              </span>
            </button>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import Modal from '@/components/Modal';
import { useRefUtils } from '@/composables/useRefUtils'; // Adjust if this is a Nuxt 3 composable

const isFullScreen = ref(false);
const isVideoMuted = ref(false);
const isVideoLoaded = ref(false);
const video = ref(null);
const playButton = ref(null);
const seekBar = ref(null);
const time = ref('');
const value = ref('');
const playPause = ref(null); // For play-pause button opacity

const props = defineProps({
  videoLink: {
    type: String,
    default: '',
  },
  closeModal: {
    type: Function,
  },
});

// Using refUtils from the mixin (assuming it's converted to a composable for Nuxt 3)
const { getRef } = useRefUtils();

// Mounted lifecycle hook (replaces the `mounted` option)
onMounted(() => {
  video.value = getRef('video-container-popup');
  playButton.value = getRef('video-player');
  seekBar.value = getRef('seek-bar');
  playPause.value = getRef('play-pause');
  
  playButton.value.addEventListener('click', playbutton);
  seekBar.value.addEventListener('change', seekBarListner);
  seekBar.value.addEventListener('click', seekBarClickListner);
  video.value.addEventListener('timeupdate', videoTimeUpdate);
  seekBar.value.addEventListener('mousedown', seekBarOnMouseDown);
  seekBar.value.addEventListener('mouseup', seekBarOnMouseUp);
  document.addEventListener('fullscreenchange', fullScreenToggle);
  
  seekBar.value.value = 0;
  video.value.currentTime = 0;
  playPause.value.style.opacity = '1';
});

// Before unmount lifecycle hook (replaces the `beforeDestroy` option)
onBeforeUnmount(() => {
  playButton.value.removeEventListener('click', playbutton);
  seekBar.value.removeEventListener('change', seekBarListner);
  video.value.removeEventListener('timeupdate', videoTimeUpdate);
  seekBar.value.removeEventListener('mousedown', seekBarOnMouseDown);
  seekBar.value.removeEventListener('mouseup', seekBarOnMouseUp);
  document.removeEventListener('fullscreenchange', fullScreenToggle);
});

// Method implementations (replacing `methods` option)
function seekBarClickListner(event) {
  video.value.pause();
  playPause.value.style.opacity = '1';
  const offsetX = event.offsetX;
  const seekBarWidth = seekBar.value.offsetWidth;
  
  if (seekBarWidth !== 0) {
    const newTime = video.value.duration * (offsetX / seekBarWidth);
    if (!isNaN(newTime) && isFinite(newTime)) {
      video.value.currentTime = newTime;
      playPause.value.style.opacity = '0';
      video.value.play();
    } else {
      playPause.value.style.opacity = '0';
      video.value.play();
    }
  }
}

function checkVideoLoaded() {
  isVideoLoaded.value = true;
}

function onMute() {
  isVideoMuted.value = !isVideoMuted.value;
  video.value.muted = isVideoMuted.value;
}

function seekBarOnMouseUp() {
  video.value.play();
}

function seekBarOnMouseDown() {
  video.value.pause();
}

function videoTimeUpdate() {
  value.value = (100 / video.value.duration) * video.value.currentTime;
  seekBar.value.value = value.value;
}

function seekBarListner() {
  time.value = video.value.duration * (seekBar.value.value / 100);
  video.value.currentTime = time.value;
  playPause.value.style.opacity = '0';
}

function fullScreenToggle() {
  if (document.fullscreenElement) {
    isFullScreen.value = true;
  } else {
    isFullScreen.value = false;
    video.value.pause();
    playPause.value.style.opacity = '1';
  }
}

function playbutton(event) {
  if (!isFullScreen.value) {
    if (video.value.paused) {
      video.value.play();
      playPause.value.style.opacity = '0';
    } else {
      video.value.pause();
      playPause.value.style.opacity = '1';
    }
  }
}

function onFullScreen() {
  if (video.value) {
    requestFullscreen(video.value);
  }
}

function requestFullscreen(element) {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  }
}
</script>

<style scoped>
.video-loader {
  mix-blend-mode: hard-light;
  margin: 0px 200px;
  margin-bottom: 70px;
}

.video-section {
  text-align: center;
}

video::-webkit-media-controls-mute-button,
video::-webkit-media-controls-volume-slider {
  display: none !important;
}

video::-webkit-media-controls-volume-slider {
  display: none !important;
}

.full-screen-image {
  width: 20px;
  height: 20px;
}

.full-screen-icon {
  position: absolute;
  bottom: 88px;
  right: 105px;
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

.play-button-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  pointer-events: none;
}

#circle-play-b {
  cursor: pointer;
  pointer-events: auto;
}

#play-pause,
#step-play-pause {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  background: transparent;
}

.seek {
  position: absolute;
  top: -35px;
  left: 6px;
  width: 93%;
  accent-color: white;
  border: none;
}

#seek-bar,
#step-seek-bar {
  width: 100%;
  height: 5px;
}

#mute,
#step-mute {
  position: absolute;
  background: none;
  bottom: 89px;
  right: 52px;
  color: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
}

#mute img,
#step-mute img {
  border-radius: 45%;
  width: 20px;
  height: 20px;
  background: transparent;
  z-index: 999;
}

input[type="range"]::-moz-range-progress {
  background-color: white;
}

input[type="range"]::-moz-range-track {
  background-color: #a8abb0;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 3px;
  width: 7px;
  border-radius: 0px;
  cursor: pointer;
  margin-top: -3px;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="range"] {
    background-color: #a8abb0;
  }

  input[type="range"]::-webkit-slider-runnable-track {
    height: 10px;
    -webkit-appearance: none;
    color: #fff;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 3px;
    width: 7px;
    border-radius: 0px;
    margin-bottom: 8px;
  }
}

input[type="range"]:focus {
  outline: none;
}

.oval {
  outline: inherit;
  background-color: rgba(0, 0, 0, 0.4);
  padding: 10px;
  border-radius: 50%;
  margin-bottom: 2px;
}

#video-player,
#step-video-player {
  position: relative;
  cursor: pointer;
}

.seek-container {
  position: relative;
  text-align: center;
  margin: 0px 10px;
  padding: opx 10px;
  width: calc(100% - 20px);
  background-color: #a8abb0;
}
</style>