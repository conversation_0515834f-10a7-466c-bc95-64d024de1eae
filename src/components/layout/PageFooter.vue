<template>
  <div class="page-footer">
    <NuxtLink
      :to="!isCampaignModified ? 'terms-of-service' : ''"
      event=""
      class="terms-of-service text-light-h3"
      @click.native="selectRoute('terms-of-service')"
      no-prefetch
    >
      {{ $t('TERMS_OF_SERVICE') }}
    </NuxtLink>
    <NuxtLink
      :to="!isCampaignModified ? 'privacy-policy' : ''"
      event=""
      class="privacy-policy text-light-h3"
      @click.native="selectRoute('privacy-policy')"
      no-prefetch
    >
      {{ $t('PRIVACY_POLICY') }}
    </NuxtLink>
    <div class="footer-text text-light-h3">
      {{ $t("INNIT_SCA", { year: currentYear }) }}
    </div>

    <Teleport to="body">
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :isCampaignModifiedFromShoppableReview="false"
        :callConfirm="switchRoute"
        :closeModal="closeModal"
      />
    </Teleport>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useNuxtApp } from '#app'; // Import useNuxtApp
import cancelModal from "@/components/cancel-modal";

const { $eventBus, $keys } = useNuxtApp(); // Accessing keys from nuxtApp
const { routeToPage, triggerLoading } = useCommonUtils();

const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const selectedRoute = ref('');
const currentYear = ref(new Date().getFullYear());

const selectRoute = (data) => {
  selectedRoute.value = data;
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);
  } else {
    switchRoute();
  }
};

const closeModal = () => {
  isConfirmModalVisible.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
};

const switchRoute = () => {
  if (selectedRoute.value) {
    routeToPage(selectedRoute.value); // Implement this method as per your routing logic
  }
  closeModal();
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
};

const handleEscapeKey = (event) => {
  if (event.key === $keys.KEY_NAMES.ESCAPE && isConfirmModalVisible.value) {
    closeModal();
  }
};

onMounted(() => {
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleEscapeKey);

  $eventBus.on($keys.KEY_NAMES.CAMPAIGN_MODIFIED, (data) => {
    isCampaignModified.value = data;
  });
});

onBeforeUnmount(() => {
  $eventBus.off($keys.KEY_NAMES.CAMPAIGN_MODIFIED);
});
</script>
