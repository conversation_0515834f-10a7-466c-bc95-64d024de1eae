<template>
  <header class="header">
    <div class="header-block-search">
      <searchBar
        v-if="isSearchBarEnabled"
        searchContext="global"
      />
    </div>

    <div class="header-block-actions">
      <project-switcher
        v-if="isAuthenticated"
        :isCampaignModified="isCampaignModified"
      />

      <profile-menu
        v-if="isAuthenticated"
        :is-route-loading="isRouteLoading"
      />
    </div>

    <floating-notification-popup />
  </header>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useNuxtApp } from '#app';
import { useRoute } from 'vue-router';
import floatingNotificationPopup from "../floating-notification-popup.vue";
import searchBar from "@/components/search-bar/search-bar.vue";
import { useI18n } from 'vue-i18n';
import { useAuth0 } from '@auth0/auth0-vue';
import { useCommonUtils } from "~/composables/useCommonUtils";
import ProjectSwitcher from "../project-switcher.vue";
import ProfileMenu from "../profile-menu.vue";

const $emit = defineEmits();

const { t } = useI18n();
const { isAuthenticated } = useAuth0();
const { $eventBus, $keys } = useNuxtApp();
const route = useRoute();
const { triggerLoading } = useCommonUtils();
const { isAdmin, readyProject } = useProjectLang();

const isInnit = ref(false);
const isRouteLoading = ref(false);
const isCampaignModified = ref(false);
const isAdminCheck = ref(false);

const validRoutesForSearchBar = new Set(["recipes", "category", "cat-group", "tags", "ingredients", "iq-users"]);
const isSearchBarEnabled = computed(() => validRoutesForSearchBar.has(route.name));

async function showErrorMessageAsync() {
  $eventBus.emit('show-floating-notification', t('COULD_NOT_SAVE_EDITS'), t('OPERATION_FAILED_MESSAGE'), $keys.KEY_NAMES.ERROR);
}
async function applyChangesToastAsync() {
  $eventBus.emit('show-floating-notification', t('COMMON.CHANGES_APPLIED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function calendarErrorAsync() {
  $eventBus.emit('show-floating-notification', t('COMMON.ERROR'), t('PLEASE_SELECT_DATES_BEFORE_DISABLED'), $keys.KEY_NAMES.ERROR);
}
async function collectionPublishToastAsync(isCollectionPublished) {
  const message = isCollectionPublished ? t('TEXT_POPUP.COLLECTION_FORM_IS_PUBLISHED') : t('TEXT_POPUP.COLLECTION_FORM_IS_SAVED');
  $eventBus.emit('show-floating-notification', message, '', $keys.KEY_NAMES.SUCCESS);
}
async function productAddedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.PRODUCT_ADDED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function productPromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.PRODUCT_PROMOTED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function productUnpromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.PRODUCT_UNPROMOTED'), '', $keys.KEY_NAMES.DELETED);
}
async function recipePromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.RECIPE_PROMOTED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function recipeUnpromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.RECIPE_UNPROMOTED'), '', $keys.KEY_NAMES.DELETED);
}
async function recipeDeletedAsync(data) {
  let message = t('TEXT_POPUP.DELETED');
  if (data) {
    message = `Deleted ${data} group`;
  }
  $eventBus.emit("show-floating-notification", { popupMessage: message, popupType: $keys.KEY_NAMES.ERROR });
}
async function newRecipeDeletedAsync(data) {
  let message = t('TEXT_POPUP.DELETED');
  if (data) {
    message = `Deleted ${data}`;
  }
  $eventBus.emit("show-floating-notification", { popupMessage: message, popupType: $keys.KEY_NAMES.ERROR });
}
function iqUsersNotification(data) {
  const info = data?.color === $keys.KEY_NAMES.RED ? "error" : "success";
  $eventBus.emit('show-floating-notification', data?.message, '', info);
}
function newToastFloatNotification() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEW_LABEL_TEXT"), '', $keys.KEY_NAMES.LABELS);
}
function bannerRescheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_RESCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}
function liveBannerRescheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_DATE_CHANGED"), '', $keys.KEY_NAMES.SUCCESS);
}
async function recipeRemovedAsync(data) {
  let message = t('TEXT_POPUP.REMOVED');
  if ((data === $keys.KEY_NAMES.DIET || data === $keys.KEY_NAMES.TAG) || (data === $keys.KEY_NAMES.DIETS || data === $keys.KEY_NAMES.TAGS)) {
    message = `Removed ${data}`;
  }
  $eventBus.emit("show-floating-notification", { popupMessage: message, popupType: $keys.KEY_NAMES.ERROR });
}
async function recipeSavedAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}
async function recipeScheduledAsync(count) {
  const popupText = count > 1 ? t("TEXT_POPUP.RECIPES_SCHEDULED") : t("TEXT_POPUP.RECIPE_SCHEDULED");
  $eventBus.emit('show-floating-notification', popupText, '', $keys.KEY_NAMES.SUCCESS);
}
async function recipePublishedAsync(count) {
  const popupText = count > 1 ? t("TEXT_POPUP.RECIPES_PUBLISHED") : t("TEXT_POPUP.RECIPE_PUBLISHED");
  $eventBus.emit('show-floating-notification', popupText, '', $keys.KEY_NAMES.SUCCESS);
}
async function categoryNameChangedAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.CATEGORY_NAME_CHANGED"), '', $keys.KEY_NAMES.SUCCESS);
}
async function showIngredientAddedPopupAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ADDED"), '', $keys.KEY_NAMES.SUCCESS);
}
function videoUploaded() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.UPLOADED"), '', $keys.KEY_NAMES.SUCCESS);
}

async function copyToClipboardAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.COPY_TO_CLIPBOARD"), '', $keys.KEY_NAMES.SUCCESS);
}

function articleUpdated() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

function articleSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}
function articlePublished() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_PUBLISHED"), '', $keys.KEY_NAMES.SUCCESS);
}

function articleUnpublished() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_UNPUBLISHED"), '', $keys.KEY_NAMES.SUCCESS);
}

function draftSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.DRAFT_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function isPublishedData() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.PUBLISHED"), '', $keys.KEY_NAMES.SUCCESS);
}
function articleCategoryCreated() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEW_CATEGORY_ADDED"), '', $keys.KEY_NAMES.SUCCESS);
}

function contentSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.CONTENT_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function newsSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEWS_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function adviceSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ADVICE_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}
function newScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEWS_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function adviceScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ADVICE_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function contentLive() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.LIVE_HERO_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

function contentScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.CONTENT_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}
function quizSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.QUIZ_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function quizScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.QUIZ_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function eventSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.EVENT_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function eventScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.EVENT_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.HERO_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}
function bannerFormUpdated() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

function bannerFormScheduled() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function bannerFormUnscheduled() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_UNSCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function bannerFormSaved() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroUnscheduled() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.HERO_UNSCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroReplaced() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.HERO_REPLACED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroChange() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.LIVE_HERO_CHANGED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroUpdate() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.LIVE_HERO_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

async function recipeImageUploadFailedAsync() {
    const subMessage = t('COMMON.PLEASE_TRY_AGAIN_LATER') + t("COMMON.OR_CONTACT_US") + t("COMMON.CMS_SUPPORT");
    $eventBus.emit('show-floating-notification', t('COMMON.IMAGE_UPLOAD_ISSUE'), subMessage, $keys.KEY_NAMES.ERROR);
}

function somethingWentWrong() {
    $eventBus.emit('show-floating-notification', t('COMMON.SOMETHING_WENT_WRONG'), t('COMMON.PLEASE_TRY_AGAIN_LATER'), $keys.KEY_NAMES.ERROR);
}

function videoNameExist() {
  $eventBus.emit("show-floating-notification", {
    popupMessage: t("COMMON.ERROR"),
    popupSubMessage: t("VIDEO_ALREADY_EXISTS"),
    popupType: $keys.KEY_NAMES.ERROR
  });
}
function errorOccurred() {
  $eventBus.emit("show-floating-notification", {
    popupMessage: t("COMMON.ERROR"),
    popupSubMessage: t("COMMON.SOMETHING_WENT_WRONG"),
    popupType: $keys.KEY_NAMES.ERROR
  });
}
function videoUnexpectedError() {
    $eventBus.emit('show-floating-notification', t('COMMON.SOMETHING_WENT_WRONG'), t('COMMON.PLEASE_TRY_AGAIN_LATER'), $keys.KEY_NAMES.ERROR);
}

async function recipeImageGenerationFailedAsync() {
    const subMessage = t('COMMON.PLEASE_TRY_AGAIN_LATER') + t("COMMON.OR_CONTACT_US") + t("COMMON.CMS_SUPPORT");
    $eventBus.emit('show-floating-notification', t('COMMON.IMAGE_GENERATION_FAILED'), subMessage, $keys.KEY_NAMES.ERROR);
}

function closeModal() {
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
}

async function innit() {
  if (isAuthenticated.value) {
    isInnit.value = true;

    const eventHandlers = {
      campaignModified: (data) => (isCampaignModified.value = data),
      routeloading: (data) => (isRouteLoading.value = data),
      iqUsersNotification,
      error_for_409: showErrorMessageAsync,
      calendarError: calendarErrorAsync,
      [ $keys.KEY_NAMES.DELETED_SUCCESS]: recipeDeletedAsync,
      [ $keys.KEY_NAMES.NEW_DELETED_SUCCESS]: newRecipeDeletedAsync,
      [ $keys.KEY_NAMES.NEW_SUCCESS]: newToastFloatNotification,
      [ $keys.KEY_NAMES.PRODUCT_PROMOTED]: productPromotedAsync,
      [ $keys.KEY_NAMES.PRODUCT_ADDED]: productAddedAsync,
      [ $keys.KEY_NAMES.RECIPE_PROMOTED]: recipePromotedAsync,
      [ $keys.KEY_NAMES.PRODUCT_UNPROMOTED]: productUnpromotedAsync,
      [ $keys.KEY_NAMES.RECIPE_UNPROMOTED]: recipeUnpromotedAsync,
      [ $keys.KEY_NAMES.DELETED]: recipeRemovedAsync,
      [ $keys.KEY_NAMES.APPLY]: applyChangesToastAsync,
      [ $keys.KEY_NAMES.SAVED_SUCCESS]: recipeSavedAsync,
      [ $keys.KEY_NAMES.RECIPE_PUBLISHED]: recipePublishedAsync,
      [ $keys.KEY_NAMES.SCHEDULE_SUCCESS]: recipeScheduledAsync,
      [ $keys.KEY_NAMES.ARTICLE_NAME_CHANGED]: categoryNameChangedAsync,
      [ $keys.KEY_NAMES.INGREDIENT_ADDED]: showIngredientAddedPopupAsync,
      [ $keys.KEY_NAMES.ARTICLE_SUCCESS]: articleSaved,
      [ $keys.KEY_NAMES.ARTICLE_UPDATED]: articleUpdated,
      [ $keys.KEY_NAMES.DRAFT_SAVED]: draftSaved,
      [ $keys.KEY_NAMES.ARTICLE_PUBLISHED_SUCCESS]: articlePublished,
      [ $keys.KEY_NAMES.PUBLISHED_DATE]: isPublishedData,
      [ $keys.KEY_NAMES.ARTICLE_CREATED]: articleCategoryCreated,
      [ $keys.KEY_NAMES.CLOSE_FLOATING_POPUP]: closeModal,
      [ $keys.KEY_NAMES.ARTICLE_UNPUBLISHED_SUCCESS]: articleUnpublished,
      contentSaved,
      contentScheduled,
      contentLive,
      newsSaved,
      AdviceSaved: adviceSaved,
      newScheduled,
      AdviceScheduled: adviceScheduled,
      quizSaved,
      quizScheduled,
      eventSaved,
      eventScheduled,
      collectionPublishToastAsync,
      heroScheduled,
      heroUnscheduled,
      heroReplaced,
      heroChange,
      heroUpdate,
      [ $keys.KEY_NAMES.ARTICLE_WRONG]: somethingWentWrong,
      [ $keys.KEY_NAMES.VIDEO_NAME_EXIST]: videoNameExist,
      [ $keys.KEY_NAMES.IMAGE_GENERATION_FAILED]: recipeImageGenerationFailedAsync,
      [ $keys.KEY_NAMES.IMAGE_UPLOAD_ISSUE]: recipeImageUploadFailedAsync,
      [ $keys.KEY_NAMES.VIDEO_UNEXPECTED_ERROR]: videoUnexpectedError,
      [ $keys.KEY_NAMES.CATEGORY_WRONG_CLOSED]: somethingWentWrong,
      [ $keys.KEY_NAMES.VIDEO_UPLOADED]: videoUploaded,
      copyToClipboard: copyToClipboardAsync,
      bannerFormUpdated,
      bannerFormScheduled,
      bannerFormUnscheduled,
      bannerFormSaved,
      somethingWentWrong,
      errorOccurred,
    };

    Object.entries(eventHandlers).forEach(([event, handler]) => {
      $eventBus.on(event, handler);
    });
  }
};

watch(
  () => isAuthenticated.value,
  (val) => {
    if (val && !isInnit.value) {
      innit().catch((error) => {
        console.error("[IQ][PageHeader] Error during initialization:", error);
      });
    }
  }
);

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isAdminCheck.value = await isAdmin.value;
    }
  });
  innit().catch();
});

const cleanupEventListeners = () => {
  $eventBus.off($keys.KEY_NAMES.ARTICLE_NAME_CHANGED);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_SUCCESS);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_UPDATED);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_PUBLISHED_SUCCESS);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_CREATED);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_UNPUBLISHED_SUCCESS);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_WRONG);
  $eventBus.off($keys.KEY_NAMES.CATEGORY_WRONG_CLOSED);
  $eventBus.off($keys.KEY_NAMES.VIDEO_UPLOADED);
  $eventBus.off($keys.KEY_NAMES.VIDEO_NAME_EXIST);
  $eventBus.off($keys.KEY_NAMES.VIDEO_UNEXPECTED_ERROR);
  $eventBus.off($keys.KEY_NAMES.SOMETHING_WENT_WRONG);
  $eventBus.off($keys.KEY_NAMES.IQ_USERS_NOTIFICATION);
  $eventBus.off($keys.KEY_NAMES.IMAGE_GENERATION_FAILED);
  $eventBus.off($keys.KEY_NAMES.PRODUCT_ADDED);
  $eventBus.off($keys.KEY_NAMES.IMAGE_UPLOAD_ISSUE);
};
onBeforeUnmount(() => {
  cleanupEventListeners();
});
</script>
