<template>
  <button
    type="button"
    class="button-with-switcher btn-reset"
    :class="{
      'button-with-switcher-checked': isChecked,
    }"
    :disabled="isDisabled"
    @click="clickAction"
  >
    <span class="font-size-base font-bold color-black" :class="{ 'color-green': greenLabel }">{{ label }}</span>
    <span class="button-with-switcher-slider-round"></span>
  </button>
</template>

<script setup>

const props = defineProps({
  label: {
    type: String,
    required: true,
    default: "",
  },
  isChecked: {
    type: Boolean,
    required: true,
    default: false,
  },
  isDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  greenLabel: {
    type: Boolean,
    required: false,
    default: false,
  }
});

const emit = defineEmits(["action"]);

const clickAction = () => emit("action", true);
</script>
