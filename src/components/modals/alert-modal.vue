<template>
  <div class="alert-modal" :class="alertType">
    <div class="alert-modal-title font-size-18 font-bold">{{ title }}</div>
    <div v-if="text" class="alert-modal-text font-size-14 font-normal color-charcoal-light">{{ text }}</div>
    <button
      type="button"
      class="btn btn-green"
      @click="okAction"
    >
      {{ btnLabel }}
    </button>
  </div>
</template>

<script setup>

const props = defineProps({
  title: {
    type: String,
    required: true,
    default: "",
  },
  text: {
    type: String,
    required: false,
    default: "",
  },
  btnLabel: {
    type: String,
    required: false,
    default: "Okay",
  },
  alertType: {
    type: String,
    required: false,
    default: "alert-modal-error", // alert-modal-error | alert-modal-warn | alert-modal-info
  }
});

const emit = defineEmits(["close"]);

const okAction = () => emit("close", false);
</script>
