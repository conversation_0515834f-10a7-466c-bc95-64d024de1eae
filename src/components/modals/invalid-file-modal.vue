<template>
  <div class="invalid-file-modal">
    <div class="invalid-file-modal-header font-bold color-red">{{ headerText }}</div>
    <div class="invalid-file-modal-description">
      <span>Accepted file formats:</span> <span>{{ acceptedFile }}</span>
    </div>
    <button
      type="button"
      class="btn-green"
      @click="closeModal()"
    >
      Okay
    </button>
  </div>
</template>

<script setup>
const props = defineProps({
  acceptedFile: {
    type: String,
  },
  video: {
    type: Boolean,
    default: false
  },
  zip: {
    type: Boolean,
    default: false
  },
  image: {
    type: Boolean,
    default: false
  },
});

const emit = defineEmits(["close"]);

const headerText = computed(() => {
  if (props.video) {
    return "Invalid video file format";
  } else if (props.zip) {
    return "Invalid zip file format";
  } else if (props.image) {
    return "Invalid Image file format";
  } else {
    return "Invalid file format";
  }
});

const closeModal = () => emit("close", false);

</script>
