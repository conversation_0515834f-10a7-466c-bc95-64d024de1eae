<template>
  <div class="process-modal" :class="modalType">
    <div class="process-modal-loader spin-animation"></div>
    <div class="process-modal-description text-title-2 font-normal">
      <span>{{ descriptionText }}</span>
    </div>
  </div>
</template>

<script setup>
import { PROCESS_MODAL_TYPE } from "../../models/process-modal.model.js";
import { useI18n } from "vue-i18n";

const props = defineProps({
  modalType: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: false,
  },
});

const { t } = useI18n();

const descriptionText = computed(() => {
  if (props.description) {
    return props.description;
  }

  switch (props.modalType) {
    case PROCESS_MODAL_TYPE.SAVING:
      return t("LOADER.SAVING_MESSAGE");
    case PROCESS_MODAL_TYPE.PUBLISHING:
      return t("LOADER.PUBLISHING_MESSAGE");
    case PROCESS_MODAL_TYPE.UNPUBLISHING:
      return t("LOADER.UNPUBLISHING_MESSAGE");
    case PROCESS_MODAL_TYPE.SCHEDULING:
      return t("LOADER.SCHEDULING_MESSAGE");
    case PROCESS_MODAL_TYPE.UNSCHEDULING:
      return t("LOADER.UNSCHEDULING_MESSAGE");
    case PROCESS_MODAL_TYPE.DELETING:
      return t("COMMON.DELETE_PLEASE_WAIT");
  }
});
</script>
