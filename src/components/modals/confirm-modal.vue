<template>
  <div
    class="confirm-modal"
    :class="modalType"
    :style="{
      width: width,
    }"
  >
    <div class="confirm-modal-icon">
      <img v-if="image" alt="modal icon" :src="image" loading="lazy">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.SAVE" alt="modal icon" src="@/assets/images/confirm-modal/published.png" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.EDIT" alt="modal icon" src="@/assets/images/confirm-modal/published.png" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.UNPUBLISH" alt="modal icon" src="@/assets/images/confirm-modal/unpublished.svg?skipsvgo=true" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.EXIT" alt="modal icon" src="@/assets/images/confirm-modal/exit.png" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.UPDATE_LIVE_BANNER" alt="modal icon" src="@/assets/images/confirm-modal/exit.png" class="w-100 rotate-270" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.DELETE" alt="modal icon" src="@/assets/images/confirm-modal/delete.png" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.UNABLE" alt="modal icon" src="@/assets/images/confirm-modal/unable.png" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.CONTINUE_LOAD" alt="modal icon" src="@/assets/images/confirm-modal/upload.png" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.DRAFT" alt="modal icon" src="@/assets/images/quiz-form.png" class="w-100" loading="eager">
      <img v-else-if="modalType === CONFIRM_MODAL_TYPE.BANNER_TYPE_REPLACE" alt="modal icon" src="@/assets/images/Sort-icon.png" class="w-100 rotate-90" loading="eager">
    </div>
    <div class="confirm-modal-content">
      <div class="confirm-modal-title font-size-20 font-bold color-black">{{ title }}</div>
      <div v-if="descriptionRed" class="confirm-modal-description-red font-size-base font-normal color-red-d">{{ descriptionRed }}</div>
      <div v-if="description" class="confirm-modal-description font-size-base font-normal color-grey">{{ description }}</div>
      <div class="confirm-modal-action">
        <button
          v-if="!hideCancelBtn || modalType !== CONFIRM_MODAL_TYPE.UNABLE"
          type="button"
          class="btn-green-outline"
          @click="cancelAction()"
          data-test-id="cancel-button"
        >
          {{ cancelBtnLabelString }}
        </button>
        <button
          v-if="!hideConfirmBtn"
          type="button"
          :class="{
            'btn-green': modalType !== CONFIRM_MODAL_TYPE.DELETE && modalType !== CONFIRM_MODAL_TYPE.UNABLE,
            'btn-red': modalType === CONFIRM_MODAL_TYPE.DELETE || modalType === CONFIRM_MODAL_TYPE.UNABLE,
          }"
          @click="confirmAction()"
          data-test-id="delete-button"
        >
          {{ confirmBtnLabelString }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { CONFIRM_MODAL_TYPE } from "../../models/confirm-modal.model.js";
import { useI18n } from "vue-i18n";

const props = defineProps({
  modalType: {
    type: String,
    required: false,
    default: CONFIRM_MODAL_TYPE.SAVE,
  },
  width: {
    type: String,
    required: false,
  },
  image: {
    type: String,
    required: false,
  },
  title: {
    type: String,
    required: true,
  },
  descriptionRed: {
    type: String,
    required: false,
  },
  description: {
    type: String,
    required: false,
  },
  confirmBtnLabel: {
    type: String,
    required: false,
    default: "",
  },
  hideConfirmBtn: {
    type: Boolean,
    required: false,
    default: false,
  },
  cancelBtnLabel: {
    type: String,
    required: false,
  },
  hideCancelBtn: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const emit = defineEmits(["callback", "close"]);

const { t } = useI18n();

const confirmBtnLabelString = computed(() => {
  if (props.confirmBtnLabel) {
    return props.confirmBtnLabel;
  }

  switch (props.modalType) {
    case CONFIRM_MODAL_TYPE.SAVE:
      return t("BUTTONS.SAVE_BUTTON");
    case CONFIRM_MODAL_TYPE.EDIT:
      return t("BUTTONS.EDIT_BUTTON");
    case CONFIRM_MODAL_TYPE.DELETE:
      return t("BUTTONS.DELETE_BUTTON");
    case CONFIRM_MODAL_TYPE.UNABLE:
      return "Ok";
    case CONFIRM_MODAL_TYPE.CONTINUE_LOAD:
      return "Continue";
    case CONFIRM_MODAL_TYPE.UPDATE_LIVE_BANNER:
      return "Confirm";
    default:
      return t("BUTTONS.CONFIRM_BUTTON");
  }
});
const cancelBtnLabelString = computed(() => props.cancelBtnLabel || t("BUTTONS.CANCEL_BUTTON"));

const cancelAction = () => emit("close", false);
const confirmAction = () => emit("close", true);
</script>
