<template>
  <section
    class="content-wrapper"
    :class="[wrapperClasses, {
      '__loading-body': isBodyLoading
    }]"
    data-test-id="content-wrapper"
  >
    <h1
      v-if="$slots.title && !$slots.head"
      class="content-wrapper-title display-1 color-green-dark"
      data-test-id="content-wrapper-title"
    >
      <slot name="title"></slot>
    </h1>

    <div
      v-if="$slots.head"
      class="content-wrapper-head"
      data-test-id="content-wrapper-head"
    >
      <div class="content-wrapper-head-title" data-test-id="content-wrapper-head-title">
        <h1 class="content-wrapper-title display-1 color-green-dark">
          <slot name="title"></slot>
        </h1>
      </div>
      <div class="content-wrapper-head-actions" data-test-id="content-wrapper-head-actions">
        <slot name="head"></slot>
      </div>
    </div>

    <div
      v-if="!isBodyLoading"
      class="content-wrapper-body"
      :class="bodyClasses"
      data-test-id="content-wrapper-body"
    >
      <slot></slot>
    </div>

    <loading-block v-if="isBodyLoading"></loading-block>
  </section>
</template>

<script setup>
import LoadingBlock from "@/components/loading-block/loading-block.vue";

const props = defineProps({
  title: {
    type: String,
    required: false,
  },
  wrapperClasses: {
    type: String,
    required: false,
  },
  bodyClasses: {
    type: String,
    required: false,
  },
  isBodyLoading: {
    type: Boolean,
    required: false,
    default: false,
  },
});
</script>
