<template>
  <div
    ref="profileMenuElRef"
    class="profile-menu font-family-averta"
  >
    <button
      type="button"
      class="btn-reset profile-menu-button font-size-20 font-weight-semi-bold color-white"
      :style="{
        backgroundColor: userProfileColor
      }"
      @click="toggleDropdown"
      :disabled="isRouteLoading"
    >
      {{ userNameFirstChar }}
    </button>

    <Teleport to="body">
      <div
        v-if="isOpen"
        ref="profileMenuDropdownElRef"
        class="profile-menu-dropdown font-family-averta"
        :style="dropdownStyles"
        v-on-click-outside="onClickOutsideHandler"
      >
        <div class="profile-menu-dropdown-item w-100 font-size-15 font-normal color-charcoal-light text-overflow">
          <img src="@/assets/images/postcard-icon.png" alt="postcard" />
          <span>{{ user.name }}</span>
        </div>
        <hr>
        <button
          type="button"
          class="profile-menu-dropdown-item profile-menu-dropdown-action btn-reset w-100 font-size-15 font-normal color-charcoal-light"
          @click="userLogout"
        >
          <img src="@/assets/images/logout-icon.png" alt="logout" />
          <span>Logout</span>
        </button>
      </div>
    </Teleport>

  </div>
</template>

<script setup>
import { useAuth0 } from "@auth0/auth0-vue";
import { useStore } from "vuex";
import { computed, ref } from "vue";
import { vOnClickOutside } from '@vueuse/components';
import ConfirmModal from "./modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import { useContext } from "../composables/useContext.js";
import { STORAGE_KEY } from "../сonstants/storage-key.js";

const props = defineProps({
  isRouteLoading: {
    type: Boolean,
  },
});

const { $keys } = useNuxtApp();
const { isAuthenticated, user } = useAuth0();
const { appLogout } = useContext();
const store = useStore();
const { triggerLoading } = useCommonUtils();
const { openModal, closeModal } = useBaseModal({
  "ProfileMenuConfirmModal": {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.EXIT,
      title: "Are you sure you want to logout ?",
    },
  },
});

const profileMenuElRef = ref();
const profileMenuDropdownElRef = ref();
const isOpen = ref(false);

const dropdownStyles = reactive({
  position: 'fixed',
  top: '0px',
  left: '0px',
});

const userNameFirstChar = computed(() => user.value?.name?.[0]);
const userProfileColor = computed(() => {
  const projectUsers = store.getters["userData/getProject"];
  const userId = user.value.sub;
  return projectUsers?.users?.find((user) => user.id === userId)?.profileColor ?? "lightgreen";
});

const onClickOutsideHandler = [() => closeDropdown(), { ignore: [profileMenuElRef] }];

const setOpen = (val) => isOpen.value = val;
const openDropdown = async () => {
  setOpen(true);
  await nextTick();
  setDropdownPosition();
};
const closeDropdown = () => setOpen(false);
const toggleDropdown = () => !isOpen.value ? openDropdown() : closeDropdown();

const setDropdownPosition = () => {
  const triggerEl = profileMenuElRef.value;
  const dropdownEl = profileMenuDropdownElRef.value;
  if (!triggerEl || !dropdownEl) return;

  const rect = triggerEl.getBoundingClientRect();

  dropdownStyles.top = `${Math.max(rect.bottom + 5, 0)}px`;
  dropdownStyles.left = `${rect.right - 290}px`;
};

const userLogout = () => {
  closeDropdown();
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);

  openModal({
    name: "ProfileMenuConfirmModal",
    onClose: async (response) => {
      if (response) {
        localStorage.removeItem(STORAGE_KEY.USER_DATA_PROJECT);
        triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
        await appLogout();
      } else {
        triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
      }
    },
  });
};

useEventListener('resize', setDropdownPosition);
useEventListener('scroll', setDropdownPosition);
onMounted(() => {
  setDropdownPosition();
});
</script>
