<template>
  <Teleport to="body">
    <div
      v-if="isOpen"
      class="global-modal-overlay"
      :aria-hidden="!isOpen"
    >
      <div
        :id="name"
        ref="globalModalWrapperElRef"
        class="global-modal-wrapper"
        :class="modalWrapperClass"
        :style="modalWrapperStyles"
        role="dialog"
        aria-modal="true"
        :aria-labelledby="name + '-label'"
        :data-modal-component="name"
        :data-skip-click-outside="skipClickOutside"
        :data-skip-escape-click="skipEscapeClick"
      >
        <button
          v-if="!hideCloseBtn"
          type="button"
          class="global-modal-btn-close btn-reset"
          @click="emitClose()"
          aria-label="Close modal"
        >
          <img src="@/assets/images/exit-gray.png" alt="exit icon" />
        </button>

        <slot />
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { onClickOutside } from '@vueuse/core';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  modalWrapperClass: {
    type: String,
    required: false,
  },
  hideCloseBtn: {
    type: Boolean,
    required: false,
    default: true,
  },
  skipClickOutside: {
    type: Boolean,
    required: false,
    default: false,
  },
  skipEscapeClick: {
    type: Boolean,
    required: false,
    default: false,
  },
  width: {
    type: String,
    required: false,
  },
  maxWidth: {
    type: String,
    required: false,
  },
  height: {
    type: String,
    required: false,
  },
  maxHeight: {
    type: String,
    required: false,
  },
});

const emit = defineEmits(["close"]);

const globalModalWrapperElRef = ref();

const modalWrapperStyles = computed(() => ({
  width: props.width || "",
  height: props.height || "",
  maxWidth: props.maxWidth || "",
  maxHeight: props.maxHeight || "",
}));

const emitClose = () => emit("close", false);

// Ensure click outside only works when modal is open
watchEffect((onCleanup) => {
  if (props.isOpen && !props.skipClickOutside) {
    const stop = onClickOutside(globalModalWrapperElRef, emitClose);
    onCleanup(() => stop());
  }
});

// Handle Escape key only when modal is open
watchEffect((onCleanup) => {
  if (props.isOpen && !props.skipEscapeClick) {
    const stop = onKeyStroke("Escape", (e) => {
      e.preventDefault();
      emitClose();
    });
    onCleanup(() => stop());
  }
});
</script>
