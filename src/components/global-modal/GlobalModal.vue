<template>
  <component
    v-for="modal in renderModals(modalsState)"
    :is="modal"
    :key="modal?.props?.name"
  />
</template>

<script setup>
import { renderModals, useBaseModal } from "../../composables/useBaseModal.js";

/**
 * Global modal state handler
 * This should be in a parent component (e.g., Layout.vue or App.vue)
 */
const { modalsState } = useBaseModal(); // Ensure modals are properly registered
</script>
