<template>
  <div class="image-box">
    <img
      v-if="image && !isUploading"
      :src="`${image}`"
      class="image-box-image"
      alt="uploaded"
      loading="eager"
    >

    <div v-if="isUploading" class="image-box-progress">
      <img
        alt="Progress Icon"
        class="progress-icon"
        :src="currentIcon"
        loading="eager"
      />

      <div class="image-box-progress-text text-light-h4">
        <p v-if="uploadImagePercentage >= 1 && uploadImagePercentage <= 98" class="color-white">Upload is in progress</p>
        <p v-else class="color-white">Uploaded</p>
        <p class="upload-media text-light-h6 color-white">
          {{ (loadedImageSize / 1024000).toFixed(1) }} of {{ (uploadImageSize / 1024000).toFixed(1) }}MB
        </p>
      </div>
    </div>

    <div v-if="!isOnlyImage && !isUploading" class="image-box-hover cursor-pointer">
      <input
        ref="imageBoxInputFileElRef"
        type="file"
        class="image-box-upload-input"
        title="Update Picture"
        @click="cleanEventTargetValue($event)"
        @change="checkUploadedFiles"
        :accept="acceptedFileTypes"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import InvalidFileModal from "./modals/invalid-file-modal.vue";
import iconVideoUpload0 from "@/assets/images/icon-video-upload-0.svg?skipsvgo=true";
import iconVideoUpload6 from "@/assets/images/icon-video-upload-6.svg?skipsvgo=true";
import iconVideoUpload12 from "@/assets/images/icon-video-upload-12.svg?skipsvgo=true";
import iconVideoUpload18 from "@/assets/images/icon-video-upload-18.svg?skipsvgo=true";
import iconVideoUpload25 from "@/assets/images/icon-video-upload-25.svg?skipsvgo=true";
import iconVideoUpload31 from "@/assets/images/icon-video-upload-31.svg?skipsvgo=true";
import iconVideoUpload37 from "@/assets/images/icon-video-upload-37.svg?skipsvgo=true";
import iconVideoUpload42 from "@/assets/images/icon-video-upload-42.svg?skipsvgo=true";
import iconVideoUpload50 from "@/assets/images/icon-video-upload-50.svg?skipsvgo=true";
import iconVideoUpload56 from "@/assets/images/icon-video-upload-56.svg?skipsvgo=true";
import iconVideoUpload62 from "@/assets/images/icon-video-upload-62.svg?skipsvgo=true";
import iconVideoUpload68 from "@/assets/images/icon-video-upload-68.svg?skipsvgo=true";
import iconVideoUpload75 from "@/assets/images/icon-video-upload-75.svg?skipsvgo=true";
import iconVideoUpload81 from "@/assets/images/icon-video-upload-81.svg?skipsvgo=true";
import iconVideoUpload87 from "@/assets/images/icon-video-upload-87.svg?skipsvgo=true";
import iconVideoUpload93 from "@/assets/images/icon-video-upload-93.svg?skipsvgo=true";
import iconVideoUploaded from "@/assets/images/icon-video-uploaded.svg?skipsvgo=true";
import { cleanEventTargetValue } from "../utils/clean-event-target-value.js";

const props = defineProps({
  imageSrc: String,
  isOnlyImage: Boolean,
  acceptedFileTypes: {
    type: String,
    default: "image/jpg, image/jpeg, image/png"
  },
  loadedImageSize: {
    type: Number,
    default: 0,
  },
  uploadImagePercentage: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["uploadedFile"]);

const { openModal } = useBaseModal({
  "invalidFileModal": InvalidFileModal,
});

const imageBoxInputFileElRef = ref();
const uploadImagePercentage = ref(0);
const uploadImageSize = ref(0);
const uploadedImage = ref();
const iconMap = ref([
  { range: [0, 5], src: iconVideoUpload0 },
  { range: [6, 11], src: iconVideoUpload6 },
  { range: [12, 17], src: iconVideoUpload12 },
  { range: [18, 24], src: iconVideoUpload18 },
  { range: [25, 30], src: iconVideoUpload25 },
  { range: [31, 36], src: iconVideoUpload31 },
  { range: [37, 41], src: iconVideoUpload37 },
  { range: [42, 49], src: iconVideoUpload42 },
  { range: [50, 55], src: iconVideoUpload50 },
  { range: [56, 61], src: iconVideoUpload56 },
  { range: [62, 67], src: iconVideoUpload62 },
  { range: [68, 74], src: iconVideoUpload68 },
  { range: [75, 80], src: iconVideoUpload75 },
  { range: [81, 86], src: iconVideoUpload81 },
  { range: [87, 92], src: iconVideoUpload87 },
  { range: [93, 98], src: iconVideoUpload93 },
  { range: [99, 99], src: iconVideoUploaded },
]);

const image = computed(() => props.imageSrc);
const isUploading = computed(() => (props.uploadImagePercentage > 0 && props.uploadImagePercentage < 100));
const currentIcon = computed(() => {
  const match = iconMap.value.find(
    ({ range }) => props.uploadImagePercentage >= range[0] && props.uploadImagePercentage <= range[1]
  );
  return match ? match.src : null;
});

const isValidFileType = (file) => props.acceptedFileTypes?.split(",").map((item) => item.trim()).includes(file?.type);

const checkUploadedFiles = (event) => {
  const file = event?.target?.files?.[0] || event?.srcElement?.files?.[0];
  if (isValidFileType(file)) {
    uploadImageSize.value = file.size;
    emit("uploadedFile", file);
  } else {
    openModal({
      name: "invalidFileModal",
      props: {
        acceptedFile: props.acceptedFileTypes?.match(/(?:image\/|\.)(\w+)/g)?.map(ext => ext.replace(/^image\/|\./, '')).join(", "),
        image: true,
      }
    });
  }
};
</script>
