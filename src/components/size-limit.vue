<template>
    <div>
        <Modal v-show="isUploadingImagePopup" @close="$emit('closeModal')">
            <template #editProductMatches>
                <div class="uploading-image-confirmation-modal">
                    <div class="uploading-image-confirmation-modal-content">
                        <div class="upload-image-top-section">
                            <div class="upload-image">
                                <div class="confirm-exit-image-container">
                                    <img alt="upload" src="@/assets/images/upload-image.png" />
                                </div>
                            </div>
                            <div class="uploading-image-description">
                                {{ maxFileSize }}
                                <div data-test-id="popup-description" class="uploading-image-description-subtitle">
                                    {{ optimalImageSize }}
                                </div>
                            </div>
                        </div>
                        <div class="uploading-image-confirmation-button-container">
                            <div data-test-id="popup-cancel-button" class="btn-green-outline" @click="closeModal">
                                {{ $t('BUTTONS.CANCEL_BUTTON') }}
                            </div>
                            <div class="btn-green" @click="continueImage(), closeModal()">
                                Continue
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
        <Modal v-show="isMaxImagePopupVisible || isMaxImagePopupVisiblezip || isMaxVideoPopupVisible" @close="closeModal">
            <template #editProductMatches>
                <div class="max-image-upload-modal">
                    <div class="max-image-upload-modal-content">
                        <div class="max-image-description">
                            {{ imageSizeAlert }}
                            <div class="max-image-description-subtitle">
                                {{ fileSizeAlert }}
                            </div>
                        </div>
                        <div class="max-image-confirmation-button-container">
                            <div class="max-image-confirmation-cancel-btn" @click="closeModal">
                                OKAY
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>
<script>
import Modal from "@/components/Modal";
export default {
    name: "size-limit",
    components: {
        Modal,
    },
    data() {
        return {

        };
    },
    props: {
        continueImage: {
            type: Function,
        },
        closeModal: {
            type: Function,
        },
        isMaxImagePopupVisible: {
            type: Boolean,
            default: false,
        },
        isUploadingImagePopup: {
            type: Boolean,
            default: false,
        },
        isMaxImagePopupVisiblezip: {
            type: Boolean,
            default: false,
        },
        isMaxVideoPopupVisible: {
            type: Boolean,
            default: false,
        },
        maxFileSize: {
            type: String,
        },
        optimalImageSize: {
            type: String,
        },
        imageSizeAlert: {
            type: String,
        },
        fileSizeAlert: {
            type: String,
        },
    },
};
</script>
