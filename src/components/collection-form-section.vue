<template>
  <div class="form-content-section">
    <div class="form-content-inner-section">
      <div class="content">
        <div class="collection-name">
          Collection Name
          <span class="compulsory-collection-name-field">*</span>
        </div>
        <div class="publish-section">
          <div
            class="publish-btn"
            :class="{ 'simple-data-tooltip simple-data-tooltip-edge': isPublished }"
            :data-tooltip-text="isPublished ? t('UNPUBLISH_PUBLISHED_COLLECTION') : ''"
          >
            <span class="text" :class="{ 'inactive-button': isPublished }"> {{ t('COMMON.PUBLISH') }} </span>
            <button type="button" class="toggle-section" :class="{ 'disabled-button': isPublished }" @click="handleClickPublish">
              <label class="switch">
                <input
                  type="checkbox"
                  :checked="localCollectionState"
                  v-model="localCollectionState"
                  @change="updateCollectionState($event.target.checked)"
                />
                <span class="slider-round"></span>
              </label>
          </button>
          </div>
        </div>
        <div class="input-delete-container">
          <div class="input-box text-title-2 font-normal">
            <input
                @input="updateCollectionName($event.target.value)"
                :placeholder= "placeholder"
                type="text"
                autocomplete="off"
                maxlength="30"
                v-model="localCollectionName"
            />
            <div class="collection-name-count text-title-2 font-normal" v-if="localCollectionName">
              {{ localCollectionName.length }}/30
            </div>
          </div>
          <div
            v-if="!isNewCollection"
            class="delete-section"
            :class="{
              'simple-data-tooltip simple-data-tooltip-edge': isPublished,
            }"
            :data-tooltip-text="isPublished && t('COLLECTION.TO_DELETE_PUBLISHED_COLLECTION')"
          >
            <div
              :class="localCollectionState ? 'delete disable-publish' : 'delete'"
            >
              <button class="delete-collection-button text-h3 font-weight-black" type="button" @click="displayDeleteCollection()">
                <img alt="delete icon" src="@/assets/images/delete-red.png" />
                <span>{{ t('COLLECTION.DELETE_COLLECTION') }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <unableToContentModal v-if="isUnableToPublish" :text="'Unable to publish'" :closeModal="closeModal" />
    </div>
  </div>
</template>
<script setup>
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n'; // If you are using vue-i18n
import unableToContentModal from "@/components/unable-to-content-modal.vue";

// Define props and convert to ref
const props = defineProps({
  collectionName: {
    type: String,
    default: "",
  },
  enableContinueButton: Function,
  isCollectionPublished: {
    type: Boolean,
    default: false,
  },
  isNewCollection: {
    type: Boolean,
    default: false,
  },
  checkCollectionNameText: Function,
  isCollectionState: {
    type: String,
    default: false,
  },
  displayDeleteCollection: Function,
  setCampaignModified: Function,
});

// Convert props to ref
const collectionName = ref(props.collectionName);
const enableContinueButton = ref(props.enableContinueButton);
const isCollectionPublished = ref(props.isCollectionPublished);
const isNewCollection = ref(props.isNewCollection);
const checkCollectionNameText = ref(props.checkCollectionNameText);
const displayDeleteCollection = ref(props.displayDeleteCollection);
const setCampaignModified = ref(props.setCampaignModified);

// Local state (previously data)
const isUnableToPublish = ref(false);
const localCollectionName = ref(props.collectionName);
const localCollectionState = ref(props.isCollectionPublished);

// Emit event
const emit = defineEmits(['update:collectionName', 'update:isCollectionPublished']);

// Watchers
watch(() => props.collectionName, (updatedValue) => {
  localCollectionName.value = updatedValue;
});

watch(() => props.isCollectionPublished, (updatedValue) => {
  localCollectionState.value = updatedValue;
});

// Computed properties
const { t } = useI18n(); // Assuming you are using vue-i18n for translation
const placeholder = computed(() => t('ENTER_NAME_BASED_ON_TAGS'));
const isPublished = computed(() => props.isCollectionState === t("COLLECTION.PUBLISHED"));

// Methods
const updateCollectionName = (updatedName) => {
  localCollectionName.value = updatedName;
  emit("update:collectionName", updatedName);
  checkCollectionNameText.value(updatedName);
  setCampaignModified.value();
};

const closeModal = () => {
  isUnableToPublish.value = false;
};

const updateCollectionState = (updatedState) => {
  setCampaignModified.value();
  localCollectionState.value = updatedState;
  emit("update:isCollectionPublished", updatedState);
};

const handleClickPublish = (event) => {
  isUnableToPublish.value = false;
  if ((isPublished.value || !enableContinueButton.value()) &&
      !localCollectionState.value || isPublished.value) {
    if (!isPublished.value) {
      isUnableToPublish.value = true;
    }
    event.preventDefault();
  }
};
</script>
