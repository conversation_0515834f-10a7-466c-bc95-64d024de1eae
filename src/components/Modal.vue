<template>
  <div class="modal-backdrop">
    <div class="modal">
      <slot name="header"></slot>
      <slot v-if="props.isAddArticlesMatches" name="addArticlesMatches"></slot>

      <section v-if="!props.isAddArticlesMatches" class="modal-body">
        <slot name="deleteRecipe"></slot>
        <slot name="deleteVideoModal"></slot>
        <slot name="recipeStepErrorModal"></slot>
        <slot name="nutrition"></slot>
        <slot name="productMatches"></slot>
        <slot name="noProductMatches"></slot>
        <slot name="editProductMatches"></slot>
        <slot name="isAddCategoriesGroupModal"></slot>
        <slot name="newCategoryPopUp"></slot>
        <slot name="isUpdateCategoryModal"></slot>
        <slot name="createTag"></slot>
        <slot name="addRecipeMatches"></slot>
        <slot name="createCtaegoryGroup"></slot>
        <slot name="importRecipe"> </slot>
        <slot name="videoModal"> </slot>
        <slot name="addIngredientsFromRecipe"> </slot>
        <slot name="addRecipeToTag"> </slot>
        <slot name="deleteRecipeTagModal"> </slot>
        <slot name="deleteTagModal"> </slot>
        <slot name="addProductModal"> </slot>
        <slot name="problemModal"> </slot>
        <slot name="shoppablePageLeaveModal"> </slot>
        <slot name="deleteShoppableReviewProduct"> </slot>
        <slot name="shoppableReviewForEditProduct"> </slot>
        <slot name="recipeVariant"> </slot>
        <slot name="editVariantTagName"> </slot>
        <slot name="recipeVariantModal"> </slot>
        <slot name="categoryVariantName"></slot>
        <slot name="editIngredientsName"> </slot>
        <slot name="selectGroupType"></slot>
        <slot name="filterPublishpopup"></slot>
        <slot name="articlePreview"></slot>
        <slot name="deleteArticles"></slot>
        <slot name="unpubishArticles"></slot>
        <slot name="saveArticles"></slot>
        <slot name="previewReferenceIngredient"></slot>
        <slot name="quizForm"></slot>
        <slot name="BannerForm"></slot>
        <slot name="saveModal"></slot>
      </section>

      <footer class="modal-footer" v-if="props.isDeleteRecipe">
        <slot name="footer">
          <button type="button" class="btn-green-outline" @click="close">
            {{ $t('BUTTONS.CANCEL_BUTTON') }}
          </button>
        </slot>
      </footer>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  slide: {
    type: Object,
    required: false,
  },
  isDeleteRecipe: {
    type: Boolean,
    required: false,
  },
  isAddArticlesMatches: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(["close", "delete"]);

const close = () => {
  emit("close");
};
const deleteRecipe = () => {
  emit("delete");
};
</script>
