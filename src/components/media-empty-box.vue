<template>
  <div v-if="isRecipePage" class="media-empty-box-main">
    <div class="media-empty-box-container">
      <div v-if="isVideo" class="media-empty-box-section">
        <img
          :src="uploadVideoImage"
          class="media-empty-box"
          alt="upload"
        />
      </div>
      <div v-else class="media-empty-box-section">
        <img
          :src="uploadImage"
          class="media-empty-box"
          alt="upload"
        />
        <div class="content">
          {{ isActionNeeded ? $t("MEDIA_EMPTY_BOX.MINIATURE_IMAGE") : $t("MEDIA_EMPTY_BOX.MAIN_IMAGE") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import uploadImage from '@/assets/images/icons/upload-image-icon.svg?skipsvgo=true';
import uploadVideoImage from '@/assets/images/icons/upload-video-icon.svg?skipsvgo=true';

const props = defineProps({
  isActionNeeded: {
    type: Boolean,
    default: false,
  },
  isRecipePage: {
    type: Boolean,
    default: false,
  },
  isVideo: {
    type: <PERSON>olean,
    default: false,
  },
});
</script>

