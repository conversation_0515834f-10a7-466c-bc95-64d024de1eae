<template>
  <div class="page-top-block">
    <div v-if="hasBackgroundImage" class="page-top-block-background">
      <img alt="background" class="background-image" :src="`${backgroundImage}`" />
    </div>
    <div class="page-top-block-body">
      <div
        class="page-top-block-actions"
        :class="{
          'page-top-block-actions-with-margin': !hint && !hintBlack,
        }"
      >
        <page-actions
          :is-back-disabled="pageActionsIsBackDisabled"
          :back-label="pageActionsBackLabel"
          :back-path="pageActionsBackPath"
          :is-cancel-disabled="pageActionsIsCancelDisabled"
          :cancel-label="pageActionsCancelLabel"
          :is-continue-disabled="pageActionsIsContinueDisabled"
          :continue-label="pageActionsContinueLabel"
          @actionCancel="pageActionsCancel"
          @actionContinue="pageActionsContinue"
        />
      </div>
      <div v-if="hint" class="page-top-block-hint font-normal color-white">{{ hint }}</div>
      <div v-if="hintBlack" class="page-top-block-hint font-normal color-black">{{ hintBlack }}</div>
      <simple-content-wrapper>
        <slot></slot>
      </simple-content-wrapper>
    </div>

  </div>
</template>

<script setup>

import SimpleContentWrapper from "./simple-content-wrapper.vue";
import PageActions from "./page-actions.vue";

const props = defineProps({
  hasBackgroundImage: {
    type: Boolean,
    required: false,
    default: true,
  },
  hint: {
    type: String,
    required: false,
    default: "",
  },
  hintBlack: {
    type: String,
    required: false,
    default: "",
  },
  backgroundImage: {
    type: String,
    required: false,
    default: "",
  },

  pageActionsIsBackDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  pageActionsBackLabel: {
    type: String,
    required: false,
    default: "",
  },
  pageActionsBackPath: {
    type: String,
    required: false,
    default: "",
  },
  pageActionsIsCancelDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  pageActionsCancelLabel: {
    type: String,
    required: false,
    default: "",
  },
  pageActionsIsContinueDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  pageActionsContinueLabel: {
    type: String,
    required: false,
    default: "",
  },
});

const emit = defineEmits(["pageActionsCancel", "pageActionsContinue"]);

const pageActionsCancel = () => emit("pageActionsCancel", true);
const pageActionsContinue = () => emit("pageActionsContinue", true);

</script>
