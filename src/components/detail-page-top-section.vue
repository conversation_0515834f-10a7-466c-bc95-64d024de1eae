<template>
  <div class="collection-intro">
    <button type="button" @click="backToCollectionBtn()" class="back-section">
      <img alt="back-icon" class="back-arrow-image" src="@/assets/images/back-arrow.png" />
      <span class="back-to-collection-list">{{ t('COMMON.BACK_TO_MASTER_LIST') }}</span>
    </button>
    <div class="top-section">
      <div class="collection-heading" data-test-id="collection-form-heading">{{ t('COLLECTION.COLLECTION_FORM') }}</div>
      <div class="button-container">
        <button type="button" @click="backToCollectionBtn()" class="btn-green-outline">
          {{ t('BUTTONS.CANCEL_BUTTON')}}
        </button>
        <div
          class="save-button-container"
          :class="{
            'simple-data-tooltip simple-data-tooltip-edge': !enableContinueButton(),
          }"
          :data-tooltip-text="!enableContinueButton() && t('PLEASE_FILL_REQUIRED_FIELDS')"
        >
          <button type="button" @click="displaySavePopup()" :class="enableContinueButton() && isCampaignModified
            ? 'btn-green'
            : 'disabled-button btn-green'
          ">
          <span v-if="isCollectionPublished" class="text">Publish</span>
          <span v-else class="text">Continue</span>
        </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useI18n } from "vue-i18n";

// Define props
const props = defineProps({
  enableContinueButton: Function,
  isCollectionPublished: {
    type: Boolean,
    default: false,
  },
  selectedTags: {
    type: Array,
    default: () => [],
  },
  isCampaignModified: {
    type: Boolean,
    default: false,
  },
});

const { t } = useI18n();
const emit = defineEmits(['backToCollection', 'displaySavePopup']);

// Methods
const backToCollectionBtn = () => {
  emit('backToCollection');
};

const displaySavePopup = () => {
  emit('displaySavePopup');
};
</script>
