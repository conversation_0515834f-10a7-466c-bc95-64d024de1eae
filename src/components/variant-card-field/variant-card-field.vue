<template>
  <div class="variant-card-field">
    <div v-if="props.prefixLabel" class="variant-card-field-prefix">{{ props.prefixLabel }}</div>

    <input
      ref="inputElRef"
      v-model.trim="model"
      :id="props.inputId"
      type="text"
      class="variant-card-field-input"
      autocomplete="off"
      tabindex="0"
      dir=""
      :placeholder="props.inputPlaceholder"
      :readonly="true"
      @blur="inputBlur"
      @input="inputChange"
    >

    <div class="variant-card-field-actions">
      <button
        type="button"
        class="btn-reset variant-card-field-action"
        @click="editAction"
      >
        <img src="@/assets/images/edit-icon.png" alt="edit icon" width="16" height="16" />
      </button>
      <div
        :class="{
          'simple-data-tooltip': props.isDeleteActionDisabled && props.deleteActionTooltipText
        }"
        :data-tooltip-text="props.deleteActionTooltipText"
      >
        <button
          type="button"
          class="btn-reset variant-card-field-action variant-card-field-action-delete"
          :disabled="props.isDeleteActionDisabled"
          @click="deleteAction"
        >
          <img src="@/assets/images/delete-icon.png" alt="delete icon" width="16" height="16" />
        </button>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref } from "vue";

const props = defineProps({
  prefixLabel: {
    type: String,
    required: false,
  },

  inputId: {
    type: String,
    required: false,
  },
  inputPlaceholder: {
    type: String,
    required: false,
  },

  isDeleteActionDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  deleteActionTooltipText: {
    type: String,
    required: false,
  },
});

const emit = defineEmits(["editAction", "deleteAction", "inputChange"]);

const model = defineModel();

const inputElRef = ref(null);

const changeInputReadonlyAttr = (val) => {
  if (!inputElRef.value) {
    return;
  }
  inputElRef.value.readOnly = val;
};

const inputBlur = () => changeInputReadonlyAttr(true);

const editAction = () => {
  changeInputReadonlyAttr(false);
  inputElRef?.value?.focus()
  emit("editAction", true);
};

const deleteAction = () => {
  emit("deleteAction", true);
};

const inputChange = () => emit("inputChange", model.value);

</script>
