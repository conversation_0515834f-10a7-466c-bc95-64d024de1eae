<template>
  <div>
    <Modal @close="$emit('closeModal')">
      <template #deleteRecipe>
        <div class="delete-loader-section">
          <div class="delete-content">
            <div class="loading-popup">
              <div class="delete-loading">
                <div class="loader-image"></div>
              </div>
              <div class="loading-text text-title-2 font-normal">
                <p>{{ $t('COMMON.DELETE_PLEASE_WAIT') }}</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
/**
 * @deprecated We should use useBaseModal({ "deletingModal": { component: ProcessModal } })!
 */
</script>
