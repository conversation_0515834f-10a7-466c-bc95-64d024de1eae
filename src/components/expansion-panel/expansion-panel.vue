<template>
  <div
    class="expansion-panel bg-white"
    :class="[props.classes, {
      'expansion-panel-expanded': expandedRef,
    }]"
  >
    <div class="expansion-panel-head">
      <div v-if="props.withDrag" class="expansion-panel-drag">
        <img alt="drag vertically icon" src="@/assets/images/drag-vertically.svg?skipsvgo=true" loading="lazy" />
      </div>
      <div class="expansion-panel-head-content">
        <slot name="head"></slot>
      </div>
      <div v-if="!props.hideCollapsedBtn" class="expansion-panel-head-action">
        <button
          type="button"
          class="btn"
          :style="actionStyle"
          @click="togglePanel()"
        >
          <span v-if="props.collapsedBtnViewLabel || props.collapsedBtnHideLabel">
            {{ !expandedRef ? props.collapsedBtnViewLabel : props.collapsedBtnHideLabel }}
          </span>
          <img alt="arrow down icon" src="@/assets/images/arrow-down-green.png" loading="lazy" />
        </button>
      </div>
    </div>
    <div class="expansion-panel-body">
      <div>
        <slot name="body"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted } from "vue";

  const props = defineProps({
    expanded: {
      type: Boolean,
      required: false,
      default: false,
    },
    classes: {
      type: String,
      required: false,
    },
    withDrag: {
      type: Boolean,
      required: false,
      default: true,
    },
    hideCollapsedBtn: {
      type: Boolean,
      required: false,
      default: false,
    },
    collapsedBtnViewLabel: {
      type: String,
      required: false,
    },
    collapsedBtnHideLabel: {
      type: String,
      required: false,
    },
  });

  const emit = defineEmits(["update"]);

  const expandedRef = ref(props.expanded);
  const panelIdRef = ref(Date.now().toString());

  const actionStyle = computed(() => {
    const viewTextLength = props.collapsedBtnViewLabel?.length || 0;
    const hideTextLength = props.collapsedBtnHideLabel?.length || 0;
    const minTextWidth = viewTextLength > hideTextLength ? `${viewTextLength * 9}px` : `${hideTextLength * 9}px`;
    return {
      width: `calc(21px + ${minTextWidth})`,
    };
  });

  const emitUpdate = () => {
    if (props.hideCollapsedBtn) {
      return;
    }
    emit("update", {
      id: panelIdRef.value,
      expanded: expandedRef.value,
    });
  };

  const togglePanel = () => {
    expandedRef.value = !expandedRef.value;
    emitUpdate();
  };

  watch(() => props.expanded, (value) => {
    if (props.hideCollapsedBtn) {
      return;
    }

    expandedRef.value = value;
    emitUpdate();
  });

  watch(() => props.hideCollapsedBtn, (value) => {
    if (!props.hideCollapsedBtn) {
      return;
    }

    expandedRef.value = false;
  });

  onMounted(() => {
    emitUpdate();
  });
</script>
