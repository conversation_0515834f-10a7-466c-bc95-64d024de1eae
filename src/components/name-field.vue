<template>
  <div
    class="name-field w-100"
    :class="{
      'simple-data-tooltip': !isFocused && isOverflowing && name
    }"
    :data-tooltip-text="name"
  >
    <div
      v-if="isRequired && !name"
      class="name-field-required-mark font-weight-black color-red"
      :style="requiredMarkStyle"
    > * </div>
    <input
      ref="inputElRef"
      type="text"
      :id="inputId"
      class="name-field-input w-100 font-bold"
      v-model.trim="name"
      :placeholder="inputPlaceholder"
      autocomplete="off"
      @mouseover="checkOverflow"
      @focus="() => isFocused = true"
      @blur="() => isFocused = false"
      @input="$emit('input', $event)"
    >
  </div>
</template>

<script setup>
import { getPlaceholderWidth } from "../utils/get-placeholder-width.js";

const props = defineProps({
  inputId: String,
  inputPlaceholder: String,
  isRequired: {
    type: Boolean,
    required: false,
    default: true,
  },
});

const emit = defineEmits(['input']);
const name = defineModel("name");

const inputElRef = ref(null);
const isFocused = ref(false);
const isOverflowing = ref(false);

const requiredMarkStyle = computed(() => {
  const el = inputElRef.value;
  return {
    left: `${getPlaceholderWidth(el)}px`,
  }
});

const checkOverflow = (event) => {
  if (event?.target) {
    isOverflowing.value = event?.target.scrollWidth > (event?.target.clientWidth + 1);
  }
};
</script>
