<template>
  <div
    class="form-container steps-section"
    id="stepsSection"
    :class="{
      'disable-recipe-detail-content': isReadOnlyProvider(recipeData.provider),
    }"
  >
    <div class="recipe-step">
      <div class="recipe-variant-step-form-text-container">
        <div class="recipe-variant-flag-section">
          <img
            alt="us-flag"
            v-if="isEnglishLanguage"
            class="recipe-flag"
            src="@/assets/images/us-flag.png"
          />
          <img
            alt="france-flag"
            v-if="isFrenchLanguage"
            class="recipe-flag"
            src="@/assets/images/france-flag.png"
          />
          <img
            alt="spain-flag"
            v-if="isSpanishLanguage"
            class="recipe-flag"
            src="@/assets/images/spain-flag.png"
          />
        </div>
        <div class="step-header">
          <p class="recipe-step-header">{{ $t("COMMON.RECIPE_STEPS") }}</p>
          <span class="compulsory-field">*</span>
        </div>
      </div>
      <div class="instruction-minimize-view">
        <button
          type="button"
          class="recipe-step-form-dropdown btn-reset"
          v-if="tasksData?.length"
          @click="recipeDropDown()"
        >
          <span>{{
            isRecipeStepDropDown
              ? $t("COMMON.COLLAPSE_ALL")
              : $t("COMMON.EXPAND_ALL")
          }}</span>
          <img
            alt="arrow-down-icon"
            :style="isRecipeStepDropDown ? transformIcon : ''"
            src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
            class="dropdown-icon"
          />
        </button>
      </div>
    </div>
    <div class="description-section">
      <draggable
         v-if="props.tasksData?.length"
        :list="props.tasksData"
        :scroll-sensitivity="200"
        :force-fallback="true"
        class="instructions-row-data"
        ghost-class="instruction-hidden-list"
        @start="stepDragStart"
        @end="stepDragEnd"
        handle=".instruction-drag-icon"
        @change="handleDrag"
      >
        <div
          v-for="(step, stepIndex) in props.tasksData"
          :key="stepIndex"
          :id="`stepCount${stepIndex}`"
          class="description"
          :class="{
            'description-fims':
            isReadOnlyProvider(recipeData.provider) ||
            recipeVariantSelectedLanguage !== defaultLang,
          }"
        >
          <div class="instruction-drag-icon">
            <img alt="drag-icon" src="@/assets/images/drag-vertically.svg?skipsvgo=true" />
          </div>
          <div
            class="empty-instruction"
            v-if="shouldShowEmptyInstruction(step)"
          >
            {{ $t("COMMON.CLICK_ON_EDIT_ICON") }}
          </div>
          <div class="instruction-container">
            <div
              v-if="shouldShowMedia(step)"
              :class="{
                'instruction-gallery':
                  !step[recipeVariantSelectedLanguage]?.media?.image,
                'instruction-gallery disable':
                  step[recipeVariantSelectedLanguage]?.media?.image,
              }"
            >
              <img
                v-if="step[recipeVariantSelectedLanguage]?.media?.image"
                alt="step"
                :src="step[recipeVariantSelectedLanguage]?.media?.image"
                class="instruction-media"
                loading="lazy"
              />
              <video
                v-if="step[recipeVariantSelectedLanguage]?.media?.video?.[0]?.url"
                :src="step[recipeVariantSelectedLanguage]?.media?.video?.[0]?.url"
                class="instruction-media"
                type="video/mp4"
                aria-label="Instruction Video"
                title="Video description"
                preload="auto"
              >
                <track
                  v-if="videoUrl?.description"
                  :src="videoUrl?.description"
                  kind="description"
                  srclang="en"
                  label="English"
                />
                <track
                  v-if="videoUrl?.subtitles"
                  :src="videoUrl?.subtitles"
                  kind="subtitles"
                  srclang="en"
                  label="English"
                />
              </video>
              <button
                @click="
                  openRecipeStepVideoPopup(
                    step[recipeVariantSelectedLanguage]?.media?.video?.[0]?.url
                  )
                "
                type="button"
                class="btn-reset"
              >
                <img
                  v-if="
                    step[recipeVariantSelectedLanguage]?.media?.video?.[0]?.url
                  "
                  alt="play-btn"
                  class="play-video-icon-image"
                  src="@/assets/images/videoPlayBtn.png"
                />
              </button>
            </div>
            <div class="instruction-content">
              <span
                class="step-count-number"
                v-if="step[recipeVariantSelectedLanguage]?.title?.trim()"
              >
                {{ $t("COMMON.STEP") }} {{ stepIndex + 1 }}
              </span>
              <div
                v-if="step[recipeVariantSelectedLanguage]?.title?.trim()"
                class="instruction-id"
              >
                <span>{{
                  step[recipeVariantSelectedLanguage]?.title ?? ""
                }}</span>
              </div>

              <div class="recipe-form-drop-down">
                <div v-if="isStepVisible(step)" class="instruction-full-view">
                  <button
                    class="recipe-step-form-dropdown btn-reset"
                    type="button"
                    @click="recipeStepFormDropDown(stepIndex, step.isChecked)"
                  >
                    <span>{{ $t("COMMON.VIEW_MORE") }}</span>
                    <img
                      alt="drop-icon"
                      src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                      class="dropdown-icon"
                    />
                  </button>
                </div>
              </div>
            </div>
            <div
              class="buttons-section"
              :class="{
                'hide-button': isReadOnlyProvider(recipeData.provider),
              }"
            >
              <button
                class="edit btn-reset"
                type="button"
                @click="editRecipeStep(stepIndex)"
              >
                <img alt="edit-icon" src="@/assets/images/edit-icon.png" />
              </button>
              <button
                class="delete btn-reset"
                type="button"
                :class="{
                  'hide-button': recipeVariantSelectedLanguage !== defaultLang,
                }"
                @click="
                  deleteModalVisible('recipeStepModal', stepIndex, null, null)
                "
              >
                <img alt="delete-icon" src="@/assets/images/delete-icon.png" />
              </button>
            </div>
          </div>
          <div
            class="instruction-drop-section"
            v-if="shouldShowInstructionDropSection(step)"
          >
            <div
              class="instruction-description"
              v-if="hasInstructionsWithoutIngredients(step)"
            >
              <div class="instruction">
                <span class="instruction-text">Instructions</span>
                <span
                  v-for="(instruction, index) in step[
                    recipeVariantSelectedLanguage
                  ]?.instructions"
                  :key="index"
                >
                  {{ instruction.text || "" }}
                </span>
              </div>
            </div>
            <div
              v-if="hasIngredientsAndInstructions(step)"
              class="instruction-ingredients-section"
            >
              <div class="instruction-description">
                <div class="instruction">
                  <span class="instruction-text">{{
                    $t("COMMON.INSTRUCTIONS")
                  }}</span>
                  <span
                    v-for="(instruction, index) in step[
                      recipeVariantSelectedLanguage
                    ]?.instructions"
                    :key="index"
                  >
                    {{ instruction.text }}
                  </span>
                </div>
              </div>
              <div
                class="ingredients-section"
                v-if="step[recipeVariantSelectedLanguage]?.ingredients?.length"
              >
                <div class="ingredients">
                  <p class="ingredients-header">
                    {{ $t("COMMON.STEP_INGREDIENTS") }}
                  </p>
                </div>
                <div class="ingredients-available">
                  <div class="ingredients-space">
                    <div
                      v-for="(ingredient, index) in step[
                        recipeVariantSelectedLanguage
                      ].ingredients"
                      :key="index"
                      class="ingredients-and-value"
                    >
                      <span class="circle-pointer"></span>
                      <div class="data">
                        <span class="value" v-if="isIngredientValid(ingredient)"
                          >{{
                            ingredient.quantityMirror ||
                            ingredient?.amount?.value
                          }}
                          {{
                            ingredient?.UOMMirror || ingredient?.amount?.unit
                          }}
                        </span>
                        <span class="ingredients">
                          {{ ingredient?.nameMirror || ingredient?.name
                          }}<i v-if="ingredient.modifier !== ''" class="comma"
                            >,
                          </i>
                        </span>
                        <span class="modifier">
                          {{
                            ingredient && ingredient.modifier
                              ? ingredient.modifier
                              : ""
                          }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="step.isChecked" class="instruction-minimize-view">
            <button
              class="recipe-step-form-dropdown btn-reset"
              type="button"
              @click="recipeStepFormDropDown(stepIndex, step.isChecked)"
            >
              <span>{{ $t("COMMON.VIEW_LESS") }}</span>
              <img
                alt="arrow-icon"
                src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                class="dropdown-icon"
              />
            </button>
          </div>
        </div>
      </draggable>
      <div
        class="add-step-button-container"
        :class="
          isReadOnlyProvider(recipeData.provider) ||
          recipeVariantSelectedLanguage === defaultLang
            ? ''
            : 'hide-block'
        "
      >
        <div
          class="add-step-button-section"
          v-if="!isReadOnlyProvider(recipeData.provider)"
        >
          <button
            type="button"
            class="btn-green-outline"
            @click="addStepConfirm()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            :disabled="isReadOnlyProvider(recipeData.provider)"
          >
            {{ $t("COMMON.ADD_STEP") }}
          </button>
        </div>
        <span
          class="add-step-text"
          :class="
            isReadOnlyProvider(recipeData.provider) ||
            recipeVariantSelectedLanguage !== defaultLang ||
            availableLang.length < 2
              ? 'hide-add-text'
              : ''
          "
        >
          {{ $t("COMMON.ADDING_AND_SORTING_MESSAGE") }}</span
        >
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance } from 'vue';
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const props = defineProps({
  recipeDropDown: Function,
  handleDrag: Function,
  stepDragStart: Function,
  stepDragEnd: Function,
  recipeVariantSelectedLanguage: String,
  tasksData: Array,
  defaultLang: String,
  availableLang: Array,
  videoUrl: String,
  openRecipeStepVideoPopup: Function,
  recipeData: Object,
  isRecipeStepDropDown: Boolean,
  recipeStepFormDropDown: Function,
  editRecipeStep: Function,
  deleteModalVisible: Function,
  addStepConfirm: Function,
  transformIcon: Object,
});

const { preventEnterAndSpaceKeyPress } = useEventUtils();

const isEnglishLanguage = computed(() => {
  return props.recipeVariantSelectedLanguage === $keys.LANGUAGE.ENGLISH;
});

const isFrenchLanguage = computed(() => {
  return props.recipeVariantSelectedLanguage === $keys.LANGUAGE.FRENCH;
});

const isSpanishLanguage = computed(() => {
  return props.recipeVariantSelectedLanguage === $keys.LANGUAGE.SPANISH;
});

const shouldShowEmptyInstruction = computed(() => (step) => {
  const langData = step[props.recipeVariantSelectedLanguage] || {};
  return langData.instructions?.length === 0 && !langData.title?.trim();
});

const shouldShowMedia = computed(() => (step) => {
  const langData = step[props.recipeVariantSelectedLanguage] || {};
  return (
    langData.title?.length &&
    (langData.media?.image || langData.media?.video?.[0]?.url)
  );
});

const shouldShowInstructionDropSection = computed(() => (step) => {
  const langData = step[props.recipeVariantSelectedLanguage] || {};
  return (
    step.isChecked &&
    (langData.instructions?.length || langData.ingredients?.length)
  );
});

const hasInstructionsWithoutIngredients = computed(() => (step) => {
  const langData = step[props.recipeVariantSelectedLanguage] || {};
  return (
    langData.ingredients?.length === 0 &&
    langData.instructions?.length > 0
  );
});

const hasIngredientsAndInstructions = computed(() => (step) => {
  const langData = step[props.recipeVariantSelectedLanguage] || {};
  return (
    langData.ingredients?.length > 0 && langData.instructions?.length > 0
  );
});

const isIngredientValid = computed(() => (ingredient) => {
  return (
    (ingredient?.quantityMirror !== 0 && ingredient?.UOMMirror) ||
    (ingredient?.amount?.value !== 0 && ingredient?.amount?.unit)
  );
});

const isStepVisible = computed(() => (step) => {
  return (
    !step.isChecked &&
    (step[props.recipeVariantSelectedLanguage]?.instructions?.length ||
      step[props.recipeVariantSelectedLanguage]?.ingredients?.length)
  );
});
</script>
