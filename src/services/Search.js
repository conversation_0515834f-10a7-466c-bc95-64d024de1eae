import * as Clients from "./clients.js";
/**
 * @module service/search
 */

export default {
  /**
   * Represents the filters used for categorizing and grouping recipes.
   * @type {ApiResponse_getEditSearch}
   * @property {Array.<Filter>} filters - An array of filter objects. Each filter can be of different types such as categories or diets.
   *
   */
  /**
   * Represents a filter object used for categorizing recipes.
   * @typedef ApiResponse_getEditSearch
   * @property {string} type - The type of filter (e.g., categories, diets).
   * @property {string} name - The name of the filter.
   * @property {Array.<FilterValue>} values - An array of values associated with the filter.
   * 	@property {string} values.name - The name of the filter value.
   * 	@property {string|number} values.id - The identifier for the filter value.
   * 	@property {string} values.image - The URL to an image representing the filter value.
   * 	@property {number} values.totalRecipes - The total number of recipes associated with this filter value.
   * @property {boolean} [dropDown] - Optional property indicating if the filter should be displayed as a dropdown.
   */

  /**
   * Fetches the edit search data from the Flite client.
   *
   * This function constructs the appropriate endpoint URL and headers, then makes a GET request to fetch the edit search data.
   * It uses the project ID for authorization and expects the result to be in the data property of the response.
   *
   * @param {Object} project - The project object containing the project details.
   * @param {Object} store - The Vuex store object used for state management.
   * @param {Object} auth - The authentication object containing the user's auth details.
   * @returns {Promise<Object>} A promise that resolves to the edit search data.
   */
  async getEditSearch(project, store, auth) {
    const endpoint = store.getters['config/getClientEndpoint']("flite", "getEditSearch");
    const headers = { "X-Innit-ProjectId": project.id };

    const fliteClient = await Clients.getFliteClient(store, auth);
    const result = await fliteClient.get(endpoint, { headers });
    return result.data;
  },

  /**
   * Sends a POST request to update the edit search data on the Flite client.
   *
   * This function constructs the appropriate endpoint URL, headers, and parameters,
   * then makes a POST request to send the payload data to update the edit search information.
   * It uses the project ID for authorization and includes the user information in the request parameters.
   *
   * @param {Object} project - The project object containing the project details.
   * @param {Object} payload - The payload object containing the data to be sent in the POST request.
   * @param {Object} store - The Vuex store object used for state management.
   * @param {Object} user - The user object containing the user's details.
   * @param {Object} auth - The authentication object containing the user's auth details.
   * @returns {Promise<Object>} A promise that resolves to the response data.
   */
  async postEditSearch(project, payload, store, user, auth) {
    const endpoint = store.getters['config/getClientEndpoint']("flite", "getEditSearch");
    const params = { user };
    const headers = { "X-Innit-ProjectId": project.id };

    const fliteClient = await Clients.getFliteClient(store, auth);
    const result = await fliteClient.post(endpoint, payload, { params, headers });
    return result.data;
  }
}