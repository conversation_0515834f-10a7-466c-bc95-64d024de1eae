import { fetchEventSource } from '@microsoft/fetch-event-source';
import queryString from 'query-string';
import { TEXT_GENERATION_MODEL } from "@/models/recipe-generator.model";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useConfig } from "../composables/useConfig.js";

/**
 * @module service/AIService
*/
const generatorToken = useConfig().config.value.GENERATOR.TOKEN;
const projectId = useConfig().config.value.GENERATOR.PROJECT_ID;
export default {
    /**
     *
     * Generates a recipe asynchronously using server-sent events (SSE).
     *
     * Fetch the client configuration from the store. Sends a POST request to '/api/recipes/-/contents/-/generations?stream=true'
     * with provided payload and handles events using specified callbacks.
     *
     * @async
     * @param {Object} store - Vuex store object.
     * @param {Object} auth - Authentication object.
     * @param {Object} payload - Payload object containing data for recipe generation.
     * @param {Function} onmessage - Callback function for SSE onmessage event.
     * @param {Function} onclose - Callback function for SSE onclose event.
     * @param {Function} onerror - Callback function for SSE onerror event.
     * @param {AbortController} ctrl - AbortController instance for cancelling the request.
     */
    async generateRecipe(store, auth, payload, onmessage, onclose, onerror, ctrl) {
        const clientConf = store.getters['config/getClientConfig']("fais");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        await fetchEventSource(clientConf.host + '/api/recipes/-/contents/-/generations?stream=true', {
            method: 'POST',
            headers: {
                'Accept': 'text/event-stream',
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
            },
            body: JSON.stringify(payload),
            onmessage: onmessage,
            onclose: onclose,
            onerror: onerror,
            signal: ctrl.signal,
            openWhenHidden: true,
        });
    },
    /**
     * Runs validators asynchronously using server-sent events (SSE).
     *
     * Fetch the client configuration from the store. Sends a POST request to '/api/recipes/-/contents/-/validations?stream=true'
     * with provided payload and handles events using specified callbacks.
     * @async
     * @param {Object} store - Vuex store object.
     * @param {Object} auth - Authentication object.
     * @param {Object} payload - Payload object containing data for running validators.
     * @param {Function} onmessage - Callback function for SSE onmessage event.
     * @param {Function} onclose - Callback function for SSE onclose event.
     * @param {Function} onerror - Callback function for SSE onerror event.
     * @param {AbortController} ctrl - AbortController instance for cancelling the request.
     */
    async runValidators(store, auth, payload, onmessage, onclose, onerror, ctrl) {
        const clientConf = store.getters['config/getClientConfig']("fais");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        await fetchEventSource(clientConf.host + '/api/recipes/-/contents/-/validations?stream=true', {
            method: 'POST',
            headers: {
                'Accept': 'text/event-stream',
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
            },
            body: JSON.stringify(payload),
            onmessage: onmessage,
            onclose: onclose,
            onerror: onerror,
            signal: ctrl.signal,
            openWhenHidden: true,
        });
    },
    /**
     * Generates a recipe review asynchronously using server-sent events (SSE).
     *
     * Fetch the client configuration from the store. Sends a POST request to '/api/recipes/-/reviews/-/generations?stream=true'
     * with provided payload and handles events using specified callbacks.
     * @async
     * @param {Object} store - Vuex store object.
     * @param {Object} auth - Authentication object.
     * @param {Object} payload - Payload object containing data for recipe review generation.
     * @param {Function} onmessage - Callback function for SSE onmessage event.
     * @param {Function} onclose - Callback function for SSE onclose event.
     * @param {Function} onerror - Callback function for SSE onerror event.
     * @param {AbortController} ctrl - AbortController instance for cancelling the request.
     */
    async generateRecipeReview(store, auth, payload, onmessage, onclose, onerror, ctrl) {
        const clientConf = store.getters['config/getClientConfig']("fais");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        await fetchEventSource(clientConf.host + '/api/recipes/-/reviews/-/generations?stream=true', {
            method: 'POST',
            headers: {
                'Accept': 'text/event-stream',
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
            },
            body: JSON.stringify(payload),
            onmessage: onmessage,
            onclose: onclose,
            onerror: onerror,
            signal: ctrl.signal,
            openWhenHidden: true,
        });
    },
    /**
     * Modifies a recipe asynchronously using server-sent events (SSE).
     *
     * Fetch the client configuration from the store. Sends a POST request to '/api/recipes/-/contents/-/modifications?stream=true'
     * with provided payload and handles events using specified callbacks.
     * @async
     * @param {Object} store - Vuex store object.
     * @param {Object} auth - Authentication object.
     * @param {Object} payload - Payload object containing data for recipe modification.
     * @param {Function} onmessage - Callback function for SSE onmessage event.
     * @param {Function} onclose - Callback function for SSE onclose event.
     * @param {Function} onerror - Callback function for SSE onerror event.
     * @param {AbortController} ctrl - AbortController instance for cancelling the request.
     */
    async modifyRecipe(store, auth, payload, onmessage, onclose, onerror, ctrl) {
        const clientConf = store.getters['config/getClientConfig']("fais");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        await fetchEventSource(clientConf.host + '/api/recipes/-/contents/-/modifications?stream=true', {
            method: 'POST',
            headers: {
                'Accept': 'text/event-stream',
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
            },
            body: JSON.stringify(payload),
            onmessage: onmessage,
            onclose: onclose,
            onerror: onerror,
            signal: ctrl.signal,
            openWhenHidden: true,
        });
    },
    /**
     * Retrieves images asynchronously.
     *
     * Fetch the client configuration from the store. Sends a POST request to '/api/images/-/contents/-/generations'
     * with provided payload and returns a promise resolving to the JSON response containing images.
     * Throws an error if the request fails.
     * @async
     * @param {Object} store - Vuex store object.
     * @param {Object} auth - Authentication object.
     * @param {Object} payload - Payload object containing data for image retrieval.
     * @param {AbortController} ctrl - AbortController instance for cancelling the request.
     * @returns {Promise<Object>} A promise that resolves to the JSON response containing images.
     * @throws {Error} Throws an error if the request fails.
     */
    async getImages(store, auth, payload, ctrl) {
        const clientConf = store.getters['config/getClientConfig']("fais");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        const response = await fetch(clientConf.host + '/api/images/-/contents/-/generations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
            },
            body: JSON.stringify(payload),
            signal: ctrl.signal,
        });
        if (!response.ok) {
            throw new Error("Image generation failed with status: " + response.status);
        }
        return response.json();
    },
    /**
     * Retrieves validators asynchronously.
     *
     * Fetch the client configuration from the store. Sends a GET request to '/api/recipes/-/contents/-/validations' with query parameters,
     * and returns a promise resolving to the JSON response containing validators.
     * Throws an error if the request fails.
     * @async
     * @param {Object} store - Vuex store object.
     * @param {Object} auth - Authentication object.
     * @param {string} input - Input parameter for validators retrieval.
     * @param {AbortController} ctrl - AbortController instance for cancelling the request.
     * @returns {Promise<Object>} A promise that resolves to the JSON response containing validators.
     * @throws {Error} Throws an error if the request fails.
     */
    async getValidators(store, auth, input, ctrl) {
        const clientConf = store.getters['config/getClientConfig']("fais");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        const params = {
            input: input
        };
        const response = await fetch(clientConf.host + '/api/recipes/-/contents/-/validations?' + queryString.stringify(params), {
            headers: {
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
            },
            signal: ctrl.signal,
        })
        if (!response.ok) {
            throw new Error("Failed getting validators with status: " + response.status);
        }
        return response.json();
    },
/**
     * Fetch the client configuration from the store.
     * Retrieves a stream asynchronously based on the specified stream type.
     * @async
     * @param {Object} store - Vuex store object.
     * @param {Object} auth - Authentication object.
     * @param {Object} payload - Payload object containing data for the stream.
     * @param {Function} onopen - Callback function for SSE onopen event.
     * @param {Function} onmessage - Callback function for SSE onmessage event.
     * @param {Function} onclose - Callback function for SSE onclose event.
     * @param {Function} onerror - Callback function for SSE onerror event.
     * @param {AbortController} ctrl - AbortController instance for cancelling the request.
     * @param {string} streamType - Type of the stream to retrieve (generations, imageGeneration, contentGeneration).
     */
    async getGenerationStreamAsync(store, auth, payload, onopen, onmessage, onclose, onerror, ctrl, streamType)  {
        const config = useConfig().config.value;
        const clientConf = store.getters['config/getClientConfig']("fais");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        const paths = {
            "generations": "/api/recipes/-/generations?stream=true",
            "imageGeneration": "/api/recipes/-/generations/-/steps/image_generation",
            "contentGeneration": "/api/recipes/-/generations/-/steps/content_generation",
        };
        const url = `${clientConf.host}${paths[streamType]}`;
        const models = store.getters["recipeGeneration/getImageGenerationModelsForStreamPayload"];
        const body = {
            ...payload,
            config: {
                generation: {
                    text: {
                        model: {
                            id: TEXT_GENERATION_MODEL.GPT_4O,
                        },
                    },
                    image: {
                        models,
                    },
                },
            },
        };
        await fetchEventSource(url, {
            method: 'POST',
            headers: {
                'Accept': 'text/event-stream',
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
                [config.HEADERS.X_INNIT_PROJECT_ID]: isFoodLMGenerator ? projectId : store.getters["userData/getProject"]?.id || config.DEFAULT_PROJECT_ID,
            },
            body: JSON.stringify(body),
            onopen,
            onmessage,
            onclose,
            onerror,
            signal: ctrl.signal,
            openWhenHidden: true,
        });
    },

    async postGenerationDataAsync(store, auth, lang, payload, isFoodLMGenerator) {
        const config = useConfig().config.value;
        const clientConf = store.getters['config/getClientConfig']("flite");
        const url = new URL(clientConf.host + '/v1/recipes/generator/data');
        url.searchParams.append('lang', lang);

        await fetch(url.toString(), {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
                [config.HEADERS.X_INNIT_PROJECT_ID]: isFoodLMGenerator ? projectId : store.getters["userData/getProject"]?.id || config.DEFAULT_PROJECT_ID,
            },
            body: JSON.stringify(payload),
        });
    },

    async putGenerationDataAsync(store, auth, lang, payload, uuid) {
        const config = useConfig().config.value;
        const clientConf = store.getters['config/getClientConfig']("flite");
        const isFoodLMGenerator = store.getters['recipeGeneration/getFoodLMGenerator'];
        const url = new URL(`${clientConf.host}/v1/recipes/generator/data/${uuid}`);
        url.searchParams.append('lang', lang);

        await fetch(url.toString(), {
            method: 'PUT',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': isFoodLMGenerator ? generatorToken : useInnitAuthStore().authorizationToken.value,
                [config.HEADERS.X_INNIT_PROJECT_ID]: isFoodLMGenerator ? projectId : store.getters["userData/getProject"]?.id || config.DEFAULT_PROJECT_ID,
            },
            body: JSON.stringify(payload),
        });
    },
}
