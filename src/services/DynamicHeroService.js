import * as Clients from './clients.js'
/**
 * @module service/DynamicHeroService
*/
export default {
    getDynamicHeroData(project, store, auth, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getDynamicHeroList");
        let params = {};
        params.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    postDynamicHeroData(project, payload, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getDynamicHeroList");
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    postDynamicEventData(project, store, auth, lang,payload) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getDynamicHeroList");
        payload.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    postDynamicContentData(project, store, auth, lang,payload) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getDynamicHeroList");
        payload.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    getEditDynamicHeroData(project, lang, uuid, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getDynamicHeroList");
        endpoint = endpoint + `/${uuid}`;
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Deletes dynamic hero data from the server based on its UUID.
 *
 * This function constructs the endpoint URL for deleting a specific dynamic hero identified by its UUID.
 * It makes a DELETE request to remove the dynamic hero using Vuex store and authentication details.
 *
 * @param {Object} project - The project object containing the project details.
 * @param {Object} store - The Vuex store object used for state management.
 * @param {Object} auth - The authentication object containing the user's auth details.
 * @param {string} uuid - The UUID of the dynamic hero to delete.
 * @returns {Promise<Object>} A promise that resolves to the data returned by the server after deletion.
 */
    deleteDynamicHeroData(project, store, auth, uuid) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getDynamicHeroList");
        endpoint = endpoint + `/${uuid}`;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).delete(
            endpoint, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Updates existing dynamic hero data on the server using PATCH method.
 *
 * This function constructs the endpoint URL for updating an existing dynamic hero identified by its UUID.
 * It makes a PATCH request to modify the dynamic hero data using Vuex store and authentication details.
 *
 * @param {Object} project - The project object containing the project details.
 * @param {string} uuid - The UUID of the dynamic hero to update.
 * @param {Object} payload - The data payload containing updated information for the dynamic hero.
 * @param {Object} store - The Vuex store object used for state management.
 * @param {Object} auth - The authentication object containing the user's auth details.
 * @returns {Promise<Object>} A promise that resolves to the data returned by the server after updating.
 */
    patchDynamicHeroData(project, uuid, payload, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getDynamicHeroList");
        endpoint = endpoint + `/${uuid}`;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).patch(
            endpoint, payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
}