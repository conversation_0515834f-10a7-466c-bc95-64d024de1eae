const _SIMPLE_MEMORY_STORAGE = new Map();

/**
 * Provides a simple memory storage mechanism using a Map.
 *
 * @returns {{
 *  set: (key: any, value: any) => Map<any, any>,
 *  get: (key: any) => any,
 *  has: (key: any) => boolean,
 *  delete: (key: any) => boolean
 * }}
 */
export const simpleMemoryStorage = () => ({
  /**
   * Sets a value in the storage.
   *
   * @param {any} key - The key to identify the value.
   * @param {any} value - The value to be stored.
   * @returns {Map<any, any>} The Map object with the updated value.
   */
  set: (key, value) => _SIMPLE_MEMORY_STORAGE.set(key, value),

  /**
   * Checks if a key exists in the storage.
   *
   * @param {any} key - The key to check for existence.
   * @returns {boolean} True if the key exists, otherwise false.
   */
  has: (key) => _SIMPLE_MEMORY_STORAGE.has(key),

  /**
   * Gets a value from the storage.
   *
   * @param {any} key - The key to identify the value.
   * @returns {any} The value associated with the key, or undefined if the key does not exist.
   */
  get: (key) => _SIMPLE_MEMORY_STORAGE.get(key),

  /**
   * Deletes a value from the storage.
   *
   * @param {any} key - The key to identify the value.
   * @returns {boolean} True if the value was deleted, otherwise false.
   */
  delete: (key) => _SIMPLE_MEMORY_STORAGE.delete(key),
});
