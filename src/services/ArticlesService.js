import * as Clients from './clients.js'
import axios from "axios";

/**
 * @module service/ArticleService
*/

export default {
    getArticleData(project, store, auth, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        let params = {};
        params.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Creates a new article category in the Flite API.
 * This method sends a POST request to the Flite API endpoint designed for creating
 * new article categories. It includes the project ID, authentication credentials,
 * and the payload containing data for the new article category.
 * @function
 * @param {Object} project The project object containing the project ID.
 * @param {Object} store Vuex store object providing access to application state.
 * @param {Object} auth Authentication credenti
 * als or token required for API access.
 * @param {Object} payload The payload containing data for the new article category.
 * @returns {Promise<Object>} A promise that resolves with the created article category data from the API.
 * @throws {Error} Throws an error if the API request fails or returns an error response.
 */
    postArticleCategory(project, store, auth, payload) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleCategoriesData")
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(endpoint,
            payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Updates an article category with the specified UUID using PATCH method.
 * 
 * This function sends a PATCH request to update an article category identified by the UUID.
 * It utilizes Vuex store getters to obtain client configuration and endpoint details,
 * along with authentication headers.
 * 
 * @param {object} project - The project object containing project details.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @param {object} payload - The payload containing the updated article category data.
 * @param {string} uuid - The UUID of the article category to be updated.
 * @returns {Promise<object>} A promise resolving to the response data after updating the article category.
 */
    patchArticleCategory(project, store, auth, payload, uuid) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleCategoriesData");
        endpoint = endpoint + `/${uuid}`;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).patch(
            endpoint, payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Deletes an article category identified by UUID.
 * 
 * This function sends a DELETE request to remove an article category specified by its UUID.
 * It utilizes Vuex store getters to obtain client configuration and endpoint details,
 * along with authentication headers.
 * 
 * @param {object} project - The project object containing project details.
 * @param {string} lang - The language code indicating the language of the article category.
 * @param {string} uuid - The UUID of the article category to be deleted.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data after deleting the article category.
 */
    deleteArticleCategoriesData(project, lang, uuid, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleCategoriesData");
        endpoint = endpoint + `/${uuid}`
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).delete(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Posts article data to the server.
 * 
 * This function sends a POST request to submit article data to the server.
 * It uses Vuex store getters to fetch client configuration and endpoint details,
 * along with authentication headers for API authorization.
 * 
 * @param {object} project - The project object containing project details.
 * @param {object} payload - The payload object containing article data to be posted.
 * @param {string} lang - The language code indicating the language of the article.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data after posting the article.
 */
    postArticlesData(project, payload, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    getEditArticlesData(project, lang, uuid, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        endpoint = endpoint + `/${uuid}`;
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    getArticleUuid(project, store, auth, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        endpoint = endpoint + `/uuid`
        let params = {};
        params.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Patches article data on the server.
 * 
 * This function sends a PATCH request to update article data on the server.
 * It utilizes Vuex store getters to fetch client configuration and endpoint details,
 * along with authentication headers for API authorization.
 * 
 * @param {object} project - The project object containing project details.
 * @param {object} payload - The payload object containing updated article data.
 * @param {string} lang - The language code indicating the language of the article.
 * @param {string} uuid - The UUID of the article to be updated.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data after patching the article.
 */
    patchArticlesData(project, payload, lang, uuid, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        endpoint = endpoint + `/${uuid}`
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).patch(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    deletetArticlesData(project, lang, uuid, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        endpoint = endpoint + `/${uuid}`;
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).delete(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves article categories data from the server.
 * 
 * This function sends a GET request to fetch article categories data from the server.
 * It utilizes Vuex store getters to fetch client configuration and endpoint details,
 * along with authentication headers for API authorization.
 * 
 * @param {object} project - The project object containing project details.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @param {string} lang - The language code indicating the language of the article categories.
 * @param {string} uuid - (Optional) The UUID of the specific article category to retrieve.
 * @returns {Promise<object>} A promise resolving to the response data containing article categories.
 */
    getArticleCateogoriesData(project, store, auth, lang, uuid) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleCategoriesData");
        let params = {};
        params.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
/**
 * Updates the order of article categories on the server.
 * 
 * This function sends a PATCH request to update the order of article categories
 * on the server. It utilizes Vuex store getters to fetch client configuration
 * and endpoint details, along with authentication headers for API authorization.
 * 
 * @param {object} project - The project object containing project details.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @param {object} payload - The payload containing the updated order of article categories.
 * @param {string} lang - The language code indicating the language of the article categories.
 * @returns {Promise<object>} A promise resolving to the response data confirming the update.
 */
    patchCategoriesOrder(project, store, auth, payload, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleCategoriesData");
        endpoint = endpoint + '/order';
        let params = {};
        params.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).patch(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Updates the order of articles in the Flite API.
 * This method sends a PATCH request to the Flite API endpoint responsible for
 * updating the order of articles. It includes the project ID, payload containing
 * article order data, and optional language parameter for localization.
 * @function
 * @param {Object} project The project object containing the project ID.
 * @param {Object} payload The payload containing data to update article order.
 * @param {string} lang Optional language code for localization.
 * @param {Object} store Vuex store object providing access to application state.
 * @param {Object} auth Authentication credentials or token required for API access.
 * @returns {Promise<Object>} A promise that resolves with the updated article order data from the API.
 * @throws {Error} Throws an error if the API request fails or returns an error response.
 */
    patchArticleorder(project, payload, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        endpoint = endpoint + '/order'
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).patch(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves a pre-signed URL for downloading an image from the Flite API.
 * This method makes a GET request to the Flite API endpoint that generates
 * a pre-signed URL for downloading an image identified by the UUID and additional parameters.
 * It includes project ID for authorization and retrieves the pre-signed URL from the response.
 * @function
 * @param {Object} project The project object containing the project ID.
 * @param {string} uuid The UUID (Universal Unique Identifier) identifying the image.
 * @param {Object} params Additional parameters to be included in the request URL.
 * @param {Object} store Vuex store object providing access to application state.
 * @param {Object} auth Authentication credentials or token required for API access.
 * @returns {Promise<string>} A promise that resolves with the pre-signed URL for downloading the image.
 * @throws {Error} Throws an error if the API request fails or returns an error response.
 */
    getPreSignedImageUrl(project, uuid, params, store, auth) {
        const clientConf = store.getters['config/getClientConfig']("flite");
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getPreSignedUrlImage");
        let headers = Clients.getAuthorizationHeader(auth);
        headers["X-Innit-ProjectId"] = project.id;
        return axios.get(
            endpoint.replace("{isin}", uuid),
            {
                baseURL: clientConf.host,
                headers: headers,
                params: params
            }
        ).then(response => {
            return response.data
        });
    },
    upload(url, file) {
        return axios.put(url, file, { headers: { 'Content-Type': file.type, 'x-amz-acl': 'public-read' } }).then(result => {
            return result
        })
    },
    /**
     * Uploads a ZIP file containing article data to the Flite API.
     * This method sends a POST request to the Flite API endpoint designed for uploading
     * article ZIP files. It includes the project ID, authentication credentials, and 
     * optional language parameter for localization. Upon successful upload, it returns
     * the response data from the API.
     * 
     * @param {Object} project The project object containing the project ID.
     * @param {Object} store Vuex store object providing access to application state.
     * @param {Object} auth Authentication credentials or token required for API access.
     * @param {string} uuid The UUID (Universal Unique Identifier) identifying the article.
     * @param {Blob|Buffer|Stream} zip The ZIP file data to upload.
     * @param {string} [lang] Optional language code for localization.
     * @returns {Promise<Object>} A promise that resolves with the upload status or data returned from the API.
     * @throws {Error} Throws an error if the API request fails or returns an error response.
     */
    uploadArticleZipFile(project, store, auth, uuid, zip, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "articleData");
        endpoint = endpoint + `/${uuid}` + `/zip`;
        let params = {};
        params.lang = lang;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, zip, { params: params, timeout: 900000, headers: headers }
        ).then(result => {
            return result.data
        })

    }
}