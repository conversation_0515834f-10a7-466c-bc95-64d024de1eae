import * as Clients from './clients.js'
import axios from 'axios'

/**
 * @module service/OrganisationsService
 */
export default {
  /**
   * Represents a paginated response.
   *
   * @typedef {Object} ApiResponse_getSearchOrganizations
   * @property {number} from - The starting index of the results.
   * @property {number} size - The number of results per page.
   * @property {number} total - The total number of results available.
   * @property {Array} results - The array containing the actual results.
   */

  /**
   * Fetches a paginated list of organizations based on search criteria.
   * This function constructs the request parameters and headers, then
   * makes a GET request to the appropriate endpoint to retrieve the data.
   *
   * @param {Object} project - The project object containing the project details.
   * @param {number} from - The starting index for the search results.
   * @param {number} size - The number of search results to retrieve.
   * @param {string} lang - The language code for the search results.
   * @param {Object} store - The Vuex store instance used to retrieve configurations and clients.
   * @param {Object} auth - The authentication object used for making authenticated requests.
   * @returns {Promise<Object>} A promise that resolves to the data containing the search results.
   * @type {ApiResponse_getSearchOrganizations}
   */
    getSearchOrganizations(project, from, size, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getSearchOrganizations");
        let params = { lang: lang };
        params.from = from;
        params.size = size;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
        /**
 * Uploads a file to a specified URL using a PUT request.
 * This function sends a PUT request to the provided URL to upload
 * the specified file with the necessary headers for content type and access control.
 *
 * @param {string} url - The URL where the file will be uploaded.
 * @param {File} file - The file to be uploaded.
 * @returns {Promise<Object>} A promise resolving to the result of the upload operation.
 */
    upload(url, file) {
        return axios.put(url, file, { headers: { 'Content-Type': file.type, 'x-amz-acl': 'public-read' } }).then(result => {
            return result
        })
    },
  /**
   * Requests new ISINs for a specified entity type.
   * This function constructs the request parameters and headers, and makes a POST request
   * to retrieve new ISINs based on the specified entity type.
   *
   * @param {Object} project - The project object containing the project details.
   * @param {string} type - The type of entity for which new ISINs are being requested.
   * @param {string} lang - The language code for the request.
   * @param {string} user - The user identifier making the request.
   * @param {Object} store - The Vuex store instance used to retrieve configurations and clients.
   * @param {Object} auth - The authentication object used for making authenticated requests.
   * @returns {Promise<Object>} A promise that resolves to the data containing the new ISINs.
   */
  async getNewIsins(project, type, lang, user, store, auth) {
    let endpoint = store.getters['config/getClientEndpoint']("flite", "getNewIsins");
    let params = { lang: lang, entity: type, user: user };
    let headers = { "X-Innit-ProjectId": project.id };
    
    try {
        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.post(endpoint, {}, { params: params, headers: headers });
        return result.data;
    } catch (error) {
        console.error('Error fetching new ISINs:', error);
        throw error;
    }
},
  /**
   * Retrieves diet information for recipes based on a search query.
   * This function constructs the request parameters and headers, and makes a GET request
   * to fetch diet information for recipes matching the search query.
   *
   * @param {Object} project - The project object containing the project details.
   * @param {string} query - The search query to filter recipes.
   * @param {boolean} recipeCount - Whether to include the total count of recipes in the response.
   * @param {string} lang - The language code for the request.
   * @param {Object} store - The Vuex store instance used to retrieve configurations and clients.
   * @param {Object} auth - The authentication object used for making authenticated requests.
   * @returns {Promise<Object>} A promise that resolves to the data containing the diet information for the recipes.
   */
    getRecipeDiets(project, query, recipeCount, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeDiets");
        let params = { lang: lang };
        params.q = query;
        params.includeTotals = recipeCount;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    }
}