import * as Clients from './clients.js';

export default {
    async getMultipleTagData({ id }, lang, store, auth, isins) {
        const endpoint = store.getters['config/getClientEndpoint']("flite", "getTagMultiData");
        const params = { lang, isins };
        const headers = { "X-Innit-ProjectId": id };

        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.get(endpoint, { params, headers });
        return result.data;
    },

    async getCategoryStatistics({ id }, store, auth, lang, sectionType, isins) {
        const endpoint = store.getters['config/getClientEndpoint']("icl", "getCategoryStatistics");
        const [language, country] = lang.split('-');
        const params = { type: sectionType, country, lang: language, isins: isins.join(',') };
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { params, headers });
        return result.data;
    },

    async getCategoryForCategoryGroupList({ id }, store, auth, from, size, sectionType, isins) {
        const endpoint = store.getters['config/getClientEndpoint']("icl", "saveTag");
        const params = { type: sectionType, from, size, isins: isins.join(',') };
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { params, headers });
        return result.data;
    },

    async patchCategory({ id }, store, auth, payload, isin) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "saveTag");
        endpoint += `/${isin}`;
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.patch(endpoint, payload, { headers });
        return result.data;
    },

    async getEditCategoryGroupList({ id }, store, auth, isin, sectionType) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "saveTag");
        endpoint += `/${isin}`;
        const params = { type: sectionType };
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { params, headers });
        return result.data;
    },

    async deleteCategoryList({ id }, store, auth, isin) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "saveTag");
        endpoint += `/${isin}`;
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.delete(endpoint, { headers });
        return result.data;
    },

    async getOperationStatus({ id }, operationId, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getOperationStatus").replace('{opId}', operationId);
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { headers });
        return result.data;
    },

    async postCategoryOrCategoryGroup({ id }, payload, store, auth, lang) {
        const endpoint = store.getters['config/getClientEndpoint']("icl", "saveTag");
        const [language, country] = lang.split('-');
        const params = { country, lang: language };
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.post(endpoint, payload, { headers });
        return result.data;
    },

    async postCategoryRecipe({ id }, payload, store, auth) {
        const endpoint = store.getters['config/getClientEndpoint']("flite", "postAssociateOperation");
        const headers = { "X-Innit-ProjectId": id };

        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.post(endpoint, payload, { headers });
        return result.data;
    },

    async getCategoriesNames({ id }, isin, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getCategoriesNames");
        endpoint += `/${isin}/categories`;
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { headers });
        return result.data;
    },

    async deleteLanguageVariant({ id }, store, auth, lang, isin) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "saveTag");
        endpoint += `/${isin}`;
        const params = { langs: lang.join(',') };
        const headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.delete(endpoint, { params, headers });
        return result.data;
    },

    async getTagData({ id }, tagAlert, lang, store, auth, isin) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getTagData");
        const params = { lang, includeAlerts: tagAlert };
        endpoint += `/${isin}`;
        const headers = { "X-Innit-ProjectId": id };

        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.get(endpoint, { params, headers });
        return result.data;
    },

    async getTagAssociations({ id } , isin, from, size, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getTagAssociations").replace('{isin}', isin);
        const params = { lang, from, size };
        const headers = { "X-Innit-ProjectId": id };

        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.get(endpoint, { params, headers });
        return result.data;
    },

    async getRecipeAllergens({ id }, query, lang, store, auth) {
        const endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeAllergens");
        const params = { lang, q: query };
        const headers = { "X-Innit-ProjectId": id };

        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.get(endpoint, { params, headers });
        return result.data;
    },
};
