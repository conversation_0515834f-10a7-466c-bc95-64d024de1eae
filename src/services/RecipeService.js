import * as Clients from './clients.js'
import axios from 'axios'
import JSONbig from "json-bigint";

/**
 * @module service/RecipeService
*/

export default {

/**
 * Fetches serving scale data based on a payload and language.
 *
 * This function sends a POST request to retrieve serving scale data from the backend API using the FSS client.
 * It includes the payload, language, and project details in the request parameters and headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {object} payload - The payload containing data required for the request.
 * @param {string} lang - The language code representing the desired country for localization.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the fetched serving scale data from the API.
 */
    getServingScale(project, payload, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("fss", "getServingScale");
        let params = { country: lang.split('-')[1] };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFSSClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Fetches recipe data based on the provided ISIN (International Securities Identification Number).
 *
 * This function sends a GET request to retrieve recipe data from the backend API using the ICL client.
 * It includes the ISIN, language, and project details in the request parameters and headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe to fetch.
 * @param {string} lang - The language code representing the desired country for localization.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the fetched recipe data from the API.
 */
    getRecipe(project, isin, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getRecipe");
        endpoint = endpoint + `/${isin}`;
        let params = { country: lang.split('-')[1] };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getICLClient(store, auth).get(
            endpoint, { params: params, headers: headers, transformResponse: data => JSONbig.parse(data) }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Fetches a recipe slug based on the provided ISIN (International Securities Identification Number), language, title, and subtitle.
 *
 * This function sends a GET request to retrieve the recipe slug from the backend API using the Flite client.
 * It includes the ISIN, language, title, subtitle, and project details in the request parameters and headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe to fetch the slug for.
 * @param {string} lang - The language code representing the desired language for localization.
 * @param {string} title - The title of the recipe for slug generation.
 * @param {string} subtitle - The subtitle of the recipe for slug generation.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the fetched recipe slug data from the API.
 */
    async getRecipeSlug({ id }, isin, lang, title, subtitle, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeSlug");
        endpoint = endpoint.replace("{isin}", isin);
        let params = {};
        params.lang = lang;
        params.title = title;
        params.subtitle = subtitle;

        let headers = { "X-Innit-ProjectId": id }
        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.get(endpoint, { params, headers });
        return result.data;
    },
    /**
 * Checks if a recipe slug already exists in the system.
 *
 * This function sends a GET request to check the existence of a recipe slug using the ICL client.
 * It includes the slug and project details in the request parameters and headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @param {string} slug - The slug to check for existence in the system.
 * @returns {Promise<object>} A promise resolving to the response data indicating whether the slug exists or not.
 */
    async checkSlugExist({ id }, store, auth, slug) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getRecipe");
        let params = {};
        params.slug = slug;
        let headers = { "X-Innit-ProjectId": id };
        return Clients.getICLClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        });
    },
        /**
 * Fetches a preview of a recipe from ICL API for a specific ISIN.
 * This function posts a request to the ICL API endpoint to retrieve
 * preview data for the given ISIN under the specified project.
 *
 * @param {Object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @returns {Promise<Object>} A promise resolving to the preview data of the recipe.
 */
    postPreviewRecipe(project, isin, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getRecipe");
        endpoint = endpoint + `/${isin}/_preview`;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getICLClient(store, auth).post(
            endpoint, {}, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves recipe categories based on the provided query parameters.
 *
 * This function sends a GET request to fetch recipe categories using the ICL client.
 * It includes query parameters such as language, query string, pagination details, and project ID.
 *
 * @param {object} project - The project object containing project details.
 * @param {string} query - The query string used to filter categories.
 * @param {string} lang - The language code in the format 'language-country', e.g., 'en-US'.
 * @param {number} from - The index of the first category to retrieve (pagination offset).
 * @param {number} size - The number of categories to retrieve per page (pagination limit).
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data containing recipe categories.
 */
    async getRecipeCategories({ id }, query, lang, from, size, store, auth) {
        const langString = typeof lang === 'string' ? lang : 'en-US'; // Fallback to 'en-US' if lang is not a string
        const endpoint = store.getters['config/getClientEndpoint']("icl", "getRecipeCategories");
        
        const params = {
            country: langString.split('-')[1] || 'US',
            lang: langString.split('-')[0] || 'en',
            q: query,
            from: from,
            size: size,
            type: 'category'
        };
    
        const headers = { "X-Innit-ProjectId": id };
    
        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { params, headers });
    
        return result.data;
    },        
    /**
 * Retrieves recipe tags based on the provided query parameters.
 *
 * This function sends a GET request to fetch recipe tags using the ICL client.
 * It includes query parameters such as language, query string, pagination details, and project ID.
 *
 * @param {object} project - The project object containing project details.
 * @param {string} query - The query string used to filter tags.
 * @param {string} lang - The language code in the format 'language-country', e.g., 'en-US'.
 * @param {number} from - The index of the first tag to retrieve (pagination offset).
 * @param {number} size - The number of tags to retrieve per page (pagination limit).
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data containing recipe tags.
 */
    async getRecipeTags({ id }, query, lang, from, size, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getRecipeTags");
        const params = {
            country: lang.split('-')[1],
            lang: lang.split('-')[0],
            q: query,
            type: 'tag',
            from,
            size,
            sort: 'lastMod'
        };
        const headers = { "X-Innit-ProjectId": id };
        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { params, headers });
        return result.data;
    },
    /**
 * Retrieves recipe unit configuration data based on the specified language.
 *
 * This function sends a GET request to fetch recipe unit configuration using the Flite client.
 * It includes the language parameter and project ID for authentication and identification.
 *
 * @param {object} project - The project object containing project details.
 * @param {string} lang - The language code used to fetch unit configuration data.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data containing recipe unit configuration.
 */
    async getRecipeUnitConfig({ id }, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeUnitConfig");
        const params = { lang };
        const headers = { "X-Innit-ProjectId": id };
        const fliteClient = await Clients.getFliteClient(store, auth);
        const result = await fliteClient.get(endpoint, { params, headers });
        return result.data;
    },    
    /**
 * Retrieves schedule data for all specified recipes based on the provided language and project.
 *
 * This function sends a GET request to fetch recipe schedule data using the Flite client.
 * It includes the language parameter, list of ISINs, and project ID for authentication and identification.
 *
 * @param {object} project - The project object containing project details.
 * @param {Array<string>} isins - An array of ISINs (International Securities Identification Numbers) representing recipes to retrieve schedules for.
 * @param {string} lang - The language code used to fetch schedule data.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data containing recipe schedule information.
 */
    getAllRecipeSchedule(project,isins, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeSchedule");
        let headers = { "X-Innit-ProjectId": project.id }
        let params = {
            lang: lang,
            isins: isins ? isins.join(',') : null,
            excludingState: 'unpublished',
        };
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Posts a request to schedule recipes using Flite API.
 * This function sends a POST request to the Flite API endpoint
 * to schedule recipes based on the provided payload and language.
 * @function
 * @param {Object} project - The project object containing project details.
 * @param {Object} payload - The payload containing data to schedule recipes.
 * @param {string} lang - The language code for scheduling (e.g., 'en', 'fr').
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @returns {Promise<Object>} A promise resolving to the result data from scheduling.
 */
    postScheduleRecipes( project,payload, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeSchedule");
        let headers = { "X-Innit-ProjectId": project.id }
        let params = { lang: lang };
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves schedule data for a specific recipe identified by its ISIN and language.
 *
 * This function sends a GET request to fetch the schedule data for a recipe using the Flite client.
 * It includes the ISIN (International Securities Identification Number), language parameter,
 * and project ID for authentication and identification.
 *
 * @param {object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe to retrieve schedule data for.
 * @param {string} lang - The language code used to fetch schedule data.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data containing the schedule information for the specified recipe.
 */
    getRecipeSchedule(project,isin, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeSchedule");
        endpoint = endpoint + `/${isin}`;
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params , headers: headers}
        ).then(result => {
            return result.data
        })
    },
    patchScheduleRecipes( project,payload, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeSchedule");
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).patch(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Deletes a scheduled recipe using Flite API.
 * This function sends a DELETE request to the Flite API endpoint
 * to delete the scheduled recipe identified by the given ISIN and language.
 *
 * @param {Object} project - The project object containing project details.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @param {string} lang - The language code associated with the scheduled recipe (e.g., 'en', 'fr').
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe to delete.
 * @returns {Promise<Object>} A promise resolving to the result data after deleting the scheduled recipe.
 */
    deleteScheduleRecipe(project,store, auth, lang, isin) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getRecipeSchedule");
        endpoint = endpoint + `/${isin}`;
        let params = {
            langs: lang
        };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).delete(
            endpoint, { params: params , headers: headers}
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves a pre-signed URL for uploading an image associated with a specific ISIN (International Securities Identification Number).
 *
 * This function sends a GET request to fetch a pre-signed URL for uploading an image related to the given ISIN using Axios.
 * It uses the client configuration and endpoint getters from the Vuex store, along with authentication headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the image.
 * @param {object} params - Additional parameters to be included in the request (e.g., image file details).
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data containing the pre-signed URL for image upload.
 */
    getPreSignedUrlForImage(project, isin, params, store, auth) {
        const clientConf = store.getters['config/getClientConfig']("icl");
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getPreSignedUrl");
        let headers = Clients.getAuthorizationHeader(auth);
        headers["X-Innit-ProjectId"] = project.id;
        return axios.get(
            endpoint.replace("{isin}", isin),
            {
                baseURL: clientConf.host,
                headers: headers,
                params: params
            }
        ).then(response => {
            return response.data
        });
    },
    /**
 * Uploads a file to a specified URL using a PUT request.
 * This function sends a PUT request to the provided URL to upload
 * the specified file with the necessary headers.
 *
 * @param {string} url - The URL where the file will be uploaded.
 * @param {File} file - The file to be uploaded.
 * @returns {Promise<Object>} A promise resolving to the result of the upload operation.
 */
    upload(url, file) {
        return axios.put(url, file, { headers: { 'Content-Type': file.type, 'x-amz-acl': 'public-read' } }).then(result => {
            return result
        })
    },
    /**
 * Saves a recipe using the specified payload and transaction ID.
 *
 * This function sends a POST request to save a recipe using the payload data and transaction ID.
 * It utilizes the client configuration and endpoint getters from the Vuex store, along with authentication headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {object} payload - The payload data containing recipe information to be saved.
 * @param {string} txnId - The transaction ID associated with the save operation.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the response data confirming the save operation.
 */
    async saveRecipe({ id }, payload, txnId, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "saveRecipe");
        let params = {};
        params.txnId = txnId;
        let headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.post(endpoint, payload, { params, headers });
        return result.data;
    },
    /**
 * Posts raw text ingredient data to FSS API.
 * This function sends a POST request to the FSS API endpoint
 * to submit raw text ingredient data for processing.
 *
 * @param {Object} project - The project object containing project details.
 * @param {Object} payload - The payload containing raw text ingredient data.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @param {string} lang - The language code used to determine the country parameter (e.g., 'en-US').
 * @returns {Promise<Object>} A promise resolving to the result data after posting the ingredient raw text.
 */
    async postIngredientRawText({ id }, payload, store, auth, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("fss", "postIngredientRawText");
        let params = {};
        params.country = lang.split('-')[1];

        let headers = { "X-Innit-ProjectId": id }
        const fssClient = await Clients.getFSSClient(store, auth);
        const result = await fssClient.post(endpoint, payload, { headers, params });
        return result.data;
    },
    saveTag(project, payload, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "saveTag");
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getICLClient(store, auth).post(
            endpoint, payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Deletes a recipe using Flite API.
 * This function sends a DELETE request to the Flite API endpoint
 * to delete the recipe identified by the given ISIN.
 * @function
 * @param {Object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe to delete.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @returns {Promise<Object>} A promise resolving to the result data after deleting the recipe.
 */
    deleteRecipe(project, isin, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "deleteRecipe");
        endpoint = endpoint.replace("{isin}", isin);
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).delete(
            endpoint, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Publishes a recipe using ICL API.
 * This function sends a POST request to the ICL API endpoint
 * to publish the recipe identified by the given ISIN under the specified project.
 * @function
 * @param {Object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe to publish.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @returns {Promise<Object>} A promise resolving to the result data after publishing the recipe.
 */
    async publishRecipeAsync({ id }, isin, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "publishRecipe");
        endpoint = endpoint.replace("{isin}", isin);
        let headers = { "X-Innit-ProjectId": id }
        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.post(endpoint, {}, { headers });
        return result.data;
    },

    /**
 * Unpublishes a recipe using ICL API.
 * This function sends a POST request to the ICL API endpoint
 * to unpublish the recipe identified by the given ISIN under the specified project.
 *
 * @param {Object} project - The project object containing project details.
 * @param {string} isin - The ISIN (International Securities Identification Number) of the recipe to unpublish.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @returns {Promise<Object>} A promise resolving to the result data after unpublishing the recipe.
 */
    async unPublishRecipeAsync({ id }, isin, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "unPublishRecipe");
        endpoint = endpoint.replace("{isin}", isin);
        let headers = { "X-Innit-ProjectId": id };

        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.post(endpoint, {}, { headers });
        return result.data;
    },
    async getRecipeForCategories( { id }, query, isin, from, size, excludIsins, includeIsins, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getRecipeList");
        let params = { country: lang.split('-')[1] };
        params.q = query;
        params.excludingIsins = excludIsins.join(',');
        params.groupsIncludingIsins = includeIsins.join(',');
        params.groups = isin;
        params.from = from;
        params.size = size;
        params.sort = 'lastMod';
        let headers = { "X-Innit-ProjectId": id }
        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { params: params, headers: headers });
        return result;
    },
    async getRecipeForCategoriesPopUp({ id }, query, groupISIN, from, size, lang, store, auth) {
        const endpoint = store.getters['config/getClientEndpoint']("icl", "getRecipeList");
        const params = {
            country: lang.split('-')[1],
            q: query,
            excludingGroups: groupISIN,
            from,
            size,
            sort: 'lastMod'
        };
        const headers = { "X-Innit-ProjectId": id };
    
        // You should ensure that the client creation logic does not have circular references.
        const iclClient = await Clients.getICLClient(store, auth);
        const result = await iclClient.get(endpoint, { params, headers });
        return result.data;  // Return the actual data
    },
    /**
 * Posts a request to parse recipe ingredients using FCS API.
 * This function sends a POST request to the FCS API endpoint
 * to parse recipe ingredients based on the provided payload.
 *
 * @param {Object} project - The project object containing project details.
 * @param {Object} payload - The payload containing recipe ingredient data to parse.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @returns {Promise<Object>} A promise resolving to the result data after parsing the recipe ingredients.
 */
    async postRecipeIngredientParse({ id }, payload, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("fcs", "postIngredientParse");
        let headers = { "X-Innit-ProjectId": id };

        const fcsClient = await Clients.getFCSService(store, auth);
        const result = await fcsClient.post(endpoint, payload, { headers });
        return result.data;
    },

    getNutritionalData(project, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getNutritionalData");
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Calculates the Daily Value Percentages (DVP) for nutritional values based on the provided payload and language.
 *
 * This function sends a POST request to calculate nutritional DVP using the provided payload and language.
 * It uses Vuex store getters to retrieve client configuration and endpoint information,
 * along with authentication headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {object} payload - The payload containing data necessary for nutritional DVP calculation.
 * @param {string} lang - The language code (e.g., "en-US") used to specify language for the request.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @returns {Promise<object>} A promise resolving to the calculated Daily Value Percentages (DVP) for nutritional values.
 */
    calculateNutritionalDVP(project, payload, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "calculateNutritionalDVP");
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves reference product data for a specific ingredient code and language.
 *
 * This function sends a GET request to retrieve reference product data for a specific ingredient
 * using the provided code and language.
 * It utilizes Vuex store getters to fetch getFliteClientgetFliteClientclient configuration and endpoint details,
 * along with authentication headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @param {string} code - The code identifying the ingredient reference product to retrieve.
 * @param {string} lang - The language code (e.g., "en-US") used to specify language for the request.
 * @returns {Promise<object>} A promise resolving to the reference product data for the specified ingredient code.
 */
    getIngredientReferenceProductData(project, store, auth, code, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getIngredientReferenceProduct");
        endpoint = endpoint + `${code}`;
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves reference product data for multiple ingredient codes and a specified language.
 *
 * This function sends a GET request to retrieve reference product data for multiple ingredient
 * codes using the provided codes array and language.
 * It utilizes Vuex store getters to fetch client configuration and endpoint details,
 * along with authentication headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @param {array} code - An array of ingredient codes identifying the reference products to retrieve.
 * @param {string} lang - The language code (e.g., "en-US") used to specify language for the request.
 * @returns {Promise<object>} A promise resolving to the reference product data for the specified ingredient codes.
 */
    getIngredientReferenceMultipleProductData(project, store, auth, code, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getIngredientReferenceProduct");
        let params = { lang: lang };
        params.codes = code.join(",");
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Retrieves shoppable review configuration data for a specified language.
 *
 * This function sends a GET request to retrieve shoppable review configuration data
 * using the provided language code.
 * It utilizes Vuex store getters to fetch client configuration and endpoint details,
 * along with authentication headers.
 *
 * @param {object} project - The project object containing project details.
 * @param {object} store - The Vuex store object for accessing state and getters.
 * @param {object} auth - The authentication object for API authentication credentials.
 * @param {string} lang - The language code (e.g., "en-US") used to specify language for the request.
 * @returns {Promise<object>} A promise resolving to the shoppable review configuration data.
 */
    getShoppableReviewConfigData(project, store, auth, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getShoppableReviewConfig");
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Posts a simple recipe using Flite API.
 * This function sends a POST request to the Flite API endpoint
 * to submit a simple recipe based on the provided payload, language, and provider.
 *
 * @param {Object} project - The project object containing project details.
 * @param {Object} payload - The payload containing the recipe data to be submitted.
 * @param {string} provider - The provider identifier for the recipe.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @param {string} lang - The language code for the recipe (e.g., 'en', 'fr').
 * @returns {Promise<Object>} A promise resolving to the result data after posting the simple recipe.
 */
    postSimpleRecipe(project, payload, provider, store, auth, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "postSimpleRecipe");
        let params = {
            lang: lang,
            provider: provider
        };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
    /**
 * Uploads an image from a URL using Flite API.
 * This function sends a POST request to the Flite API endpoint
 * to upload an image based on the provided payload and language.
 *
 * @param {Object} project - The project object containing project details.
 * @param {Object} payload - The payload containing the image URL and other related data.
 * @param {Object} store - Vuex store instance for managing application state.
 * @param {Object} auth - Authentication details for making authorized requests.
 * @param {string} lang - The language code for the upload context (e.g., 'en', 'fr').
 * @returns {Promise<Object>} A promise resolving to the result data after uploading the image.
 */
    uploadImageFromUrl(project, payload, store, auth, lang) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "uploadImageFromUrl");
        let params = { lang: lang };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
}

