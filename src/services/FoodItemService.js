import * as Clients from "./clients.js";

/**
 * @module service/FoodItemService
 */
export default {

     /**
   * Fetches product data from the FSS client using GTINs and language information.
   *
   * This function constructs the appropriate endpoint URL, headers, and parameters,
   * then makes a GET request to fetch product data. It uses the project ID for authorization,
   * joins the GTINs into a comma-separated string, and extracts the country code from the language.
   *
   * @param {Object} project - The project object containing the project details.
   * @param {Array.<string>} gtins - An array of GTINs (Global Trade Item Numbers) to fetch product data for.
   * @param {string} lang - The language string in the format "en-US" to extract the country code from.
   * @param {Object} store - The Vuex store object used for state management.
   * @param {Object} auth - The authentication object containing the user's auth details.
   * @returns {Promise<Object>} A promise that resolves to the product data.
   */
    getProducts(project, gtins, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("fss", "getProducts");
        let params = {};
        params.gtins = gtins.join(',');
        params.country = lang.split('-')[1];
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFSSClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },

  /**
   * Fetches all food item data using ISINs and language information.
   *
   * This function constructs the appropriate endpoint URL, headers, and parameters,
   * then makes a GET request to fetch food item data. It uses the project ID for authorization,
   * joins the ISINs into a comma-separated string, and extracts the country code from the language.
   *
   * @param {Object} project - The project object containing the project details.
   * @param {Array.<string>} isins - An array of ISINs (International Securities Identification Numbers) to fetch food item data for.
   * @param {string} lang - The language string in the format "en-US" to extract the country code from.
   * @param {Object} store - The Vuex store object used for state management.
   * @param {Object} auth - The authentication object containing the user's auth details.
   * @returns {Promise<Object>} A promise that resolves to the food item data.
   */
    getAllFoodItemIsin(project, isins, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("fss", "getAllFoodItemIsin");
        let params = { country: lang.split('-')[1] };
        params.isins = isins.join(",");
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFSSClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },

}

