/**
 * @module service/local-storage-service
*/

/**
 * Sets an item to localStorage using a specified key.
 *
 * This function checks if the code is running on the server-side (e.g., in a Node.js environment)
 * and returns early without performing any actions because localStorage is not available in server-side environments.
 * On the client-side, it converts the provided value to JSON format and stores it in localStorage under the specified key.
 *
 * @param {string} key - The key under which to store the item in localStorage.
 * @param {any} value - The value to be stored in localStorage. This can be any JSON-serializable data.
 */
export function setItemToLocalStorage(key, value) {
  if (process.server) {
    return;
  }

  localStorage.setItem(key, JSON.stringify(value));
}

/**
 * Retrieves an item from localStorage by its key.
 *
 * This function checks if the code is running on the server-side (e.g., in a Node.js environment)
 * and returns null because localStorage is not available in server-side environments.
 * On the client-side, it attempts to retrieve and parse the item from localStorage.
 * If parsing fails, it logs a warning message and returns null.
 *
 * @param {string} key - The key of the item to retrieve from localStorage.
 * @returns {any} The parsed JSON data from localStorage, or null if retrieval or parsing fails.
 */
export function getItemFromLocalStorage(key) {
  if (process.server) {
    return null;
  }

  try {
    return JSON.parse(localStorage.getItem(key));
  } catch (e) {
    console.warn('Cannot parse data from local storage!', e);
    return localStorage.getItem(key) || null;
  }
}

/**
 * Removes an item from localStorage based on the specified key.
 *
 * This function checks if the code is running on the server-side (e.g., in a Node.js environment)
 * and returns early without performing any actions because localStorage is not available in server-side environments.
 * On the client-side, it removes the item from localStorage associated with the provided key.
 *
 * @param {string} key - The key of the item to remove from localStorage.
 */
export function removeItemFromLocalStorage(key) {
  if (process.server) {
    return;
  }

  localStorage.removeItem(key);
}
