import * as Clients from './clients.js'
import qs from 'query-string'
/**
 * @module service/IngredientService
 */
export default {
    getIngredientsCampaign(project, names, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("fmp", "getIngredientsCampaign");
        let params = {};
        params.ingredients = names.join('|');
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFMPClient(store, auth).get(
            endpoint, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
  /**
   * Fetches product matches for ingredients based on a provided payload from a remote API endpoint.
   * This function sends a POST request to retrieve product matches based on specified criteria,
   * including language localization, pagination parameters, and project identification.
   * @param {Object} project - The project object containing an id for identification.
   * @param {Object} payload - The payload object containing ingredients or criteria for product matching.
   * @param {number} from - The index representing the starting position of the results.
   * @param {number} size - The number of results to fetch per page.
   * @param {string} lang - The language code for localization of responses (e.g., "en-US").
   * @param {Object} store - Vuex store object providing access to getters and dispatch methods.
   * @param {Object} auth - Authentication object containing credentials for API access.
   * @returns {Promise<Object>} A promise resolving to the fetched product match data from the API.
   */
    getIngredientProductMatches(project, payload, from, size, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getIngredientProducts");
        let params = {
            lang: lang,
            from: from,
            size: size
        };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        });
    },
  /**
   * Retrieves brand distribution information for ingredients based on a provided payload from a remote API endpoint.
   * This function sends a POST request to fetch brand distribution data based on specified criteria,
   * including language localization and project identification.
   * @param {Object} project - The project object containing an id for identification.
   * @param {Object} payload - The payload object containing ingredients or criteria for brand distribution.
   * @param {string} lang - The language code for localization of responses (e.g., "en-US").
   * @param {Object} store - Vuex store object providing access to getters and dispatch methods.
   * @param {Object} auth - Authentication object containing credentials for API access.
   * @returns {Promise<Object>} A promise resolving to the fetched brand distribution data from the API.
   */
  /**
   * Retrieves already added products matching ingredients based on a provided payload from a remote API endpoint.
   * This function sends a POST request to fetch products that have already been matched to ingredients,
   * based on specified criteria including language localization and project identification.
   * @param {Object} project - The project object containing an id for identification.
   * @param {Object} payload - The payload object containing ingredients or criteria for matched products.
   * @param {string} lang - The language code for localization of responses (e.g., "en-US").
   * @param {Object} store - Vuex store object providing access to getters and dispatch methods.
   * @param {Object} auth - Authentication object containing credentials for API access.
   * @returns {Promise<Object>} A promise resolving to the fetched matched product data from the API.
   */
    getIngredientPromotedProducts(project, payload, from, size, lang, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getIngredientPromotedProducts");
        let params = {
            lang: lang,
            from: from,
            size: size
        };
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(
            endpoint, payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        });
    },

  /**
   * Saves ingredient campaign data by sending a POST request to a remote API endpoint.
   * This function posts ingredient campaign data payload to initiate or update campaigns,
   * applying them to recipe campaigns based on provided user details and project identification.
   * @param {Object} project - The project object containing an id for identification.
   * @param {Object} payload - The payload object containing ingredient campaign data to be saved.
   * @param {Object} store - Vuex store object providing access to getters and dispatch methods.
   * @param {Object} user - User object containing details associated with the campaign.
   * @param {Object} auth - Authentication object containing credentials for API access.
   * @returns {Promise<Object>} A promise resolving to the saved ingredient campaign data response from the API.
   */
    saveIngredientCampaignData(project, payload, store, user, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "postIngredientCampaign");
        let params = {};
        params.applyToRecipeCampaigns = true;
        params.user = user;
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(endpoint,
            payload, { params: params, headers: headers }
        ).then(result => {
            return result.data
        })
    },
  /**
   * Posts a request to update the name of an ingredient via a POST operation to a remote API endpoint.
   * This function handles the process of sending a payload to modify the name of an ingredient,
   * utilizing project-specific configurations and authentication credentials for secure API access.
   * @param {Object} project - The project object containing an id for identification.
   * @param {Object} payload - The payload object containing the data for updating the ingredient name.
   * @param {Object} store - Vuex store object providing access to getters and dispatch methods.
   * @param {Object} auth - Authentication object containing credentials for API access.
   * @returns {Promise<Object>} A promise resolving to the response data from the API after updating the ingredient name.
   */
    postUpdateIngredientName(project, payload, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "postUpdateOperation")
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(endpoint,
            payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
  /**
   * Retrieves the current status of an operation identified by operationId via a GET request to a remote API endpoint.
   * This function fetches the status data of a specific operation using project-specific configurations and authentication credentials,
   * ensuring secure access to operational information and providing real-time updates on its progress.
   * @param {Object} project - The project object containing an id for identification.
   * @param {string} operationId - The identifier for the operation whose status is being retrieved.
   * @param {Object} store - Vuex store object providing access to getters and dispatch methods.
   * @param {Object} auth - Authentication object containing credentials for API access.
   * @returns {Promise<Object>} A promise resolving to the status data of the operation retrieved from the API.
   */
    getOperationStatus(project, operationId, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "getOperationStatus");
        endpoint = endpoint.replace('{opId}', operationId);
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getICLClient(store, auth).get(
            endpoint, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    postProductSuggestionIssue(project, payload, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("icl", "postProductSuggestionIssue")
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getICLClient(store, auth).post(endpoint,
            payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
    getIngredientsKeyword(project, data, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "getingredientKeywords");
        let params = {};
        if(data){
            params.ingredients=data.map((name) => `${name}`);
        }
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).get(
            endpoint,{
                params: params,
                headers: headers,
                paramsSerializer: params => {
                    return qs.stringify(params, { arrayFormat: "repeat" })
                }
            }
        ).then((result) => {
        return result.data;
      });
    },
    postIngredientsKeyword(project, payload, store, auth) {
        let endpoint = store.getters['config/getClientEndpoint']("flite", "postingredientKeywords")
        let headers = { "X-Innit-ProjectId": project.id }
        return Clients.getFliteClient(store, auth).post(endpoint,
            payload, { headers: headers }
        ).then(result => {
            return result.data
        })
    },
}
