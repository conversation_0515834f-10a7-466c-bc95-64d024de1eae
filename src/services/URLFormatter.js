import utils from "@/mixins/utils";

/**
 * @module service/URLFormatter
*/
export default {
    mixins: [utils],
    /**
 * Formats a URL template by replacing placeholders with corresponding values from a replace map.
 *
 * This function takes a URL template and a replace map, then replaces all placeholders in the template
 * with their corresponding values from the replace map. Placeholders are expected to be in the format `{key}`.
 *
 * @param {string} template - The URL template containing placeholders in the format `{key}`.
 * @param {Object} [replaceMap={}] - An object containing key-value pairs where the key is the placeholder in the template, 
 *                                    and the value is the string to replace the placeholder with.
 * @returns {string} The formatted URL with all placeholders replaced by their corresponding values from the replace map.
 */
    urlFormat(template, replaceMap = {}) {
        let url = template;
        try {
            for (const key in replaceMap) {
                const regexp = new RegExp('\\{' + key + '\\}', 'gi');
                url = url.replace(regexp, replaceMap[key]);
            }
            return url;
        } catch (e) {
            utils.methods.printConsole("Failed to format url");
            return ""
        }
    }
};
