export const AUTH_SCOPE = "openid profile email innit_admin";

export const getCreateAuthOptions = ({ domain, clientId }) => {
  return {
    domain,
    clientId,
    audience: "https://api.innit.com",
    responseType: "code",
    codeChallengeMethod: "S256",
    grantType: "authorization_code",
    cacheLocation: 'localstorage',
    authorizationParams: {
      redirect_uri: window.location.origin,
    },
  }
};

export const ACCESS_TOKEN_SILENTLY_OPTIONS = {
  authorizationParams: {
    audience: "https://api.innit.com/",
    scope: AUTH_SCOPE,
  },
  detailedResponse: true,
  timeoutInSeconds: 10,
};
