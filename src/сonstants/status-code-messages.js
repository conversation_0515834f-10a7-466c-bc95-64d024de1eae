export const STATUS_CODE_MESSAGES = {
  400: "There was a problem with your request. Please try again.",
  401: "You are not authorized to access this resource.",
  402: "Payment is required to proceed.",
  403: "Access to this resource is forbidden.",
  404: "The resource you are looking for could not be found.",
  405: "The requested method is not allowed.",
  406: "The requested resource is not acceptable.",
  407: "Please authenticate with the proxy to access this resource.",
  408: "The request timed out. Please try again.",
  409: "There is a conflict with the current state of the resource.",
  410: "The resource you are looking for is no longer available.",
  411: "The request requires a valid content length.",
  412: "The request did not meet the server's preconditions.",
  413: "The request payload is too large.",
  414: "The requested URI is too long.",
  415: "The media type of the request is not supported.",
  416: "The requested range is not satisfiable.",
  417: "The server could not meet the expectations given in the request.",
  418: "I'm a teapot.", // Fun Easter egg from RFC 2324
  421: "The request was misdirected.",
  422: "The server could not process the request.",
  423: "The resource is locked.",
  424: "The request failed due to a previous failure.",
  425: "The request is too early.",
  426: "Please upgrade to a newer protocol to proceed.",
  428: "The request requires preconditions to be met.",
  429: "You are making too many requests. Please slow down.",
  431: "The request headers are too large.",
  451: "The resource is unavailable due to legal reasons.",
  500: "An internal server error occurred. Please try again later.",
  501: "The server does not support the functionality required to fulfill the request.",
  502: "The server received an invalid response from the upstream server.",
  503: "The service is currently unavailable. Please try again later.",
  504: "The server timed out waiting for a response from the upstream server.",
  505: "The HTTP version used in the request is not supported.",
  506: "There was a negotiation error. Please try again.",
  507: "The server is out of storage space.",
  508: "The server detected an infinite loop while processing the request.",
  510: "Further extensions are required to fulfill the request.",
  511: "Network authentication is required to access this resource."
};
