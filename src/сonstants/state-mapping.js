import { BADGE_TYPE } from "@/components/badge/badge-type";
import { KEYS } from "@/сonstants/keys";
import publishedIcon from '@/assets/images/published-icon.png';
import unpublishedIcon from '@/assets/images/unpublished-icon.png';
import updatingIcon from '@/assets/images/updating-icon.png';
import greenArrowPublishedIcon from '@/assets/images/green-arrow-published.png';
import calendarIcon from '@/assets/images/calendar.png';
import timerSmallIcon from '@/assets/images/timer-small.png';

export const STATE_MAPPING = {
  [KEYS.STATE.PUBLISHED]: {
    tKey: "STATE.PUBLISHED",
    badgeType: BADGE_TYPE.LIGHT_GREEN,
    icon: publishedIcon,
  },
  [KEYS.STATE.UNPUBLISHED]: {
    tKey: "STATE.UNPUBLISHED",
    badgeType: BADGE_TYPE.SILVER,
    icon: unpublishedIcon,
  },
  [KEYS.STATE.PUBLISHING]: {
    tKey: "STATE.PUBLISHING",
    badgeType: BADGE_TYPE.BLUE,
    icon: updatingIcon,
  },
  [KEYS.STATE.UNPUBLISHING]: {
    tKey: "STATE.UNPUBLISHING",
    badgeType: BADGE_TYPE.BLUE,
    icon: updatingIcon,
  },
  [KEYS.STATE.FAILED]: {
    tKey: "STATE.FAILED",
    badgeType: BADGE_TYPE.YELLOW,
    icon: "",
  },
  [KEYS.STATE.DRAFT]: {
    tKey: "STATE.DRAFT",
    badgeType: BADGE_TYPE.SILVER,
    icon: unpublishedIcon,
  },
  [KEYS.KEY_NAMES.LIVE]: {
    tKey: "STATE.LIVE",
    badgeType: BADGE_TYPE.LIGHT_GREEN,
    icon: greenArrowPublishedIcon,
  },
  [KEYS.KEY_NAMES.SCHEDULED]: {
    tKey: "STATE.SCHEDULED",
    badgeType: BADGE_TYPE.ORANGE,
    icon: calendarIcon,
  },
  [KEYS.KEY_NAMES.EXPIRED]: {
    tKey: "STATE.EXPIRED",
    badgeType: BADGE_TYPE.RED,
    icon: timerSmallIcon,
  }
}
