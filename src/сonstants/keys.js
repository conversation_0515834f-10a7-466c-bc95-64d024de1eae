export const KEYS = {
  EVENT_KEY_NAMES: {
    VIEW_LOGIN: "View login",
    CLICK_SIGN_IN: "Click sign in",
    VIEW_CREATE_PROJECT: "View create project",
    CLICK_CREATE_PROJECT: "Click create project",
    VIEW_OVERVIEW: "View overview",
    CLICK_QUICK_LINK: "Click quick link",
    CLICK_RECIPES: "Click recipes",
    CLIC<PERSON>_GENERATOR: "Click generator",
    CLICK_BATCH_GENERATOR: "Click batch generator",
    CLICK_IMPORTER: "Click Importer",
    CLICK_CATEGORIES: "Click categories",
    CATEGORY_TYPE: "category",
    SORT_BY: "lastMod",
    CLICK_TAGS: "Click tags",
    CLICK_FOOD_ITEMS: "Click food items",
    CLICK_EXPORT: "Click export",
    CLICK_USERS: "Click users",
    CL<PERSON>K_SETTINGS: "Click settings",
    CLICK_HELP: "Click help",
    CLICK_GETTING_STARTED: "Click getting started",
    VIEW_RECIPES: "View recipes",
    VIEW_GENERATOR: "View generator",
    VIEW_IMPORTER: "View importer",
    VIEW_CATEGORIES: "View categories",
    VIEW_TAGS: "View tags",
    VIEW_FOOD_ITEMS: "View food items",
    CLICK_IMPORT_SUBMIT: "Click import submit",
    VIEW_IMPORT_ERROR: "View import error",
    VIEW_IMPORT_SUCCESS: "View import success",
    VIEW_EXPORT: "View export",
    CLICK_EXPORT_SUBMIT: "Click export submit",
    VIEW_EXPORT_ERROR: "View export error",
    VIEW_EXPORT_SUCCESS: "View export success",
    VIEW_USERS: "View users",
    CLICK_USER_PERMISSION: "Click user permission",
    CLICK_INVITE_USER: "Click invite user",
    VIEW_INVITE_USER: "View invite user",
    CLICK_INVITE_USER_SUBMIT: "Click invite user submit",
    CLICK_INVITE_USER_ERROR: "Click invite user error",
    VIEW_SETTINGS: "View settings",
    CLICK_RENAME_PROJECT: "Click rename project",
    VIEW_RENAME_PROJECT_ERROR: "View rename project error",
    CLICK_DELETE_PROJECT: "Click delete project",
    VIEW_DELETE_PROJECT_ERROR: "View delete project error",
    CLICK_GENERATOR_SUBMIT: "Click generator submit",
    VIEW_GENERATOR_SUCCESS: "View generator success",
    CLICK_GENERATOR_SAVE: "Click generator save",
    VIEW_GENERATOR_SAVE_ERROR: "View generator save error",
    VIEW_GENERATOR_ERROR: "View generator error",
    VIEW_EDIT_RECIPE: "View edit recipe",
    CLICK_EDIT_RECIPE_SAVE: "Click edit recipe save",
    EDIT_RECIPE_ERROR: "Edit recipe error",
    CLICK_ADD_TAG: "Click add tag",
    CLICK_REMOVE_TAG: "Click remove tag",
    VIEW_TAG_ERROR: "View tag error",
    VIEW_HELP: "View help",
    CLICK_OVERVIEW: "Click overview",
    CLICK_COLLECTIONS: "Click collections",
    VIEW_COLLECTIONS: "View collections",
    CLICK_CATEGORY_GROUP: "Click category group",
    VIEW_CATEGORY_GROUP: "View category group",
    CLICK_SEARCH_FILTER: "Click search filter",
    VIEW_SEARCH_FILTER: "View search filter",
    CLICK_ORGANIZATION: "Click organization",
    VIEW_ORGANIZATION: "View organization",
    CLICK_SIGN_UP: "Click sign up",
    VIEW_LANDING: "View landing",
    VIEW_FOODLM_GENERATOR: "View foodlm generator",
    EXCEPTIONS_GENERATOR_ON_ERROR: "View recipe generation error",
    EXCEPTIONS_GENERATOR_ON_ABORT: "View recipe generation abort",
    EXCEPTIONS_GENERATOR_MODIFY_ON_ERROR: "View recipe modify error",
    EXCEPTIONS_GENERATOR_MODIFY_ON_ABORT: "View recipe modify abort",
    EXCEPTIONS_GENERATOR_IMAGES_REFRESH_ON_ERROR: "View recipe refresh error",
    EXCEPTIONS_GENERATOR_IMAGES_REFRESH_ON_ABORT: "View recipe refresh abort",
    VIEW_ACCESS_DENIED: "View access denied",
    OVERVIEW: "overview",
    EXPORT: "export",
    IMPORTER: "importer",
    HELP: "help",
    ROUTE_PAGE: "routePage",
  },

  KEY_NAMES: {
    PROJECT_CHANGED: "projectChanged",
    CAMPAIGN_MODIFIED: "campaignModified",
    GENERATION_DATA_UPDATED: "recipeGenerationDataModified",
    SAVED_SUCCESS: "savedSuccess",
    PUBLISH_DATA: "isPublishedData",
    DELETED_SUCCESS: "deletedSuccess",
    NEW_SUCCESS: "newSuccess",
    ALL_RECIPE_STATUS: "recipeStatusAll",
    RECIPE: "recipe",
    JPEG: "jpeg",
    KEYUP: "keyup",
    INPUT: "input",
    SCROLL: "scroll",
    NEW_DELETED_SUCCESS: "newDeletedSuccess",
    ROUTE_LOADING: "routeloading",
    CURRENT_LINK: "current-link",
    CLICK: "click",
    ERROR_IN: "error in",
    MISSING_REQUIRED_PARAMETERS: "Missing required parameters",
    UPDATE_RECIPE_LIST: "updateRecipeList",
    FETCH_RECIPE_DETAILS: "fetchRecipeDetails",
    FETCH_CATEGORY_DATA: "fetchCategoryData",
    RECIPE_COUNT_UPDATE: "recipeCountUpdate",
    PRODUCT_PROMOTED: "productPromoted",
    RECIPE_PROMOTED: "recipepromoted",
    PRODUCT_UNPROMOTED: "productUnpromoted",
    RECIPE_UNPROMOTED: "recipeUnpromoted",
    YOUTUBE: "youtube",
    LOADER_PROGRESS: "loaderProgress",
    CLOSE_HISTORY_DROPDOWN: "closeHistoryDropdown",
    SLUG_ALREADY_EXIST: "slugExist",
    COLLECTION_PUBLISH_TOAST: "collectionPublishToastAsync",
    PUBLISH_FAILED: "publishFailed",
    UNPUBLISH_FAILED: "unpublishFailed",
    SOURCE_ID: "210030",
    PUBLISHING: "publishing",
    OVERRIDE: "override",
    HIDDEN: "hidden",
    FIMS: "fims",
    APPLY: "apply",
    DONE: "done",
    ALL_TEXT: "all_text",
    TEXT_BUTTON: "text_button",
    SCHEDULED: "scheduled",
    SCHEDULE: "schedule",
    SHOPPABLE: "shoppable",
    END: "end",
    START: "start",
    ESCAPE: "Escape",
    DRAFT: "draft",
    EXPIRED : "expired",
    LIVE: "live",
    FAILED: "failed",
    SOMETHING_WENT_WRONG: "somethingWentWrong",
    ERROR_OCCURRED: "errorOccurred",
    DELETED: "deleted",
    VIDEO_UPLOADED: "videoUploaded",
    VIDEO_UNEXPECTED_ERROR: "videoUnexpectedError",
    ARTICLE_DATA: "articlesData",
    ARTICLE_NAME_CHANGED: "categoryNameChanged",
    ARTICLE_WRONG: "articleWrong",
    ARTICLE_PUBLISH: "articlePublish",
    ARTICLE_PUBLISHED_SUCCESS: "articlePublishedSuccess",
    ARTICLE_UNPUBLISHED_SUCCESS: "articleUnPublishedSuccess",
    CATEGORY_WRONG_CLOSED: "articleWrongClose",
    CLOSE_FLOATING_POPUP: "closeFloatingPopup",
    ARTICLE_SUCCESS: "articleSuccess",
    ARTICLE_CREATED: "articleCreated",
    ARTICLE_UPDATED: "articleUpdated",
    SCHEDULE_SUCCESS: "scheduledSucess",
    RECIPE_PUBLISHED: "recipePublished",
    INGREDIENT_ADDED: "ingredientsAdded",
    DRAFT_SAVED: "draftSaved",
    PUBLISHED_DATE: "isPublishedData",
    VIDEO_NAME_EXIST: "videoNameExist",
    IQ_USERS_NOTIFICATION: "iqUsersNotification",
    CTA_NAVIGATE_ARTICLE: "ctaNavigateArticles",
    ARTICLE_MAIN: "articleMain",
    MOUSEOVER: "mouseover",
    UNKNOWN: "unknown",
    CMS_AI: "cmsAI",
    CMS_AI_BATCH: "cmsAiBatch",
    IMAGE: "image",
    VIDEO: "video",
    AI_GENERATED: "aiGenerated",
    MANUAL_UPLOAD: "manualUpload",
    EXTERNAL_LINK: "externalLink",
    HISTORY: "history",
    ALL: "all",
    PUBLISHED: "published",
    UNPUBLISHED: "unpublished",
    IMAGE_GENERATION_FAILED: "imageGenerationFailed",
    IMAGE_UPLOAD_ISSUE: "Image Upload failed",
    SCHEDULING: "scheduling",
    SAVING: "saving",
    TAG: "tag",
    TAGS: "tags",
    DIET: "diet",
    DIETS: "diets",
    RED: "red",
    SUCCESS: "success",
    ERROR: "error",
    LABELS: "labels",
    USD: "usd",
    PER_SERVING: "perServing",
    PER_100G: "per100g",
    RECIPE_LAYOUT: "RecipeLayout",
    GENERATOR: "generator",
    SELECT_IMAGE: "selectImage",
    HOVER_IMAGE: "hoverImage",
    CLEAR_HOVER: "clearHover",
    NEXT: "next",
    PREV: "prev",
    LPN: "lpn",
  },
  STATE: {
    PUBLISHED: "published",
    UNPUBLISHED: "unpublished",
    PUBLISHING: "publishing",
    UNPUBLISHING: "unpublishing",
    FAILED: "failed",
    DRAFT: "draft",
  },
  BATCH_GENERATOR_KEYS:{
    ADD_PROMPT: "add-prompt",
    CREATE_NEW_BATCH: "createNewBatch",
    STATUS_ADDED: "Added",
    STATUS_DONE: "Generated",
    STATUS_FAILED: "Failed",
    STATUS_RUNNING: "Generating...",
    STATUS_PENDING: "Pending",
    STATUS_DONE_LOWER: "done",
    STATUS_FAILED_LOWER: "failed",
    STATUS_RUNNING_LOWER: "running",
    STATUS_PENDING_LOWER: "pending",
    STATUS_DONE_CLASS: "status-done",
    STATUS_FAILED_CLASS: "status-failed",
    STATUS_RUNNING_CLASS: "status-running",
    STATUS_PENDING_CLASS: "status-pending",
    STATUS_DEFAULT_CLASS: "status-default",
    RECIPE_DETAIL: "recipe-detail",
    IQ_BATCH_GENERATOR: "iq-batch-generator",
    EMPTY: "",
    PROMPT_LABEL: "Prompt",
    STATUS_LABEL: "Status",
    RECIPE_TO_REVIEW_LABEL: "Recipe to review",
    ADD_PROMPT_EVENT: "add-prompt",
    ERROR_FETCHING_BATCH_PROMPTS: "Error fetching batch prompts:",
    ERROR_POST_BATCH_PROMPTS: "Error posting batch prompts",
    LIMIT: "Limit",
    ERROR_IN_POST_BATCH_ASYNC: "Error in postBatchPromptsAsync",
    MISSING_LANG_PARAMS: "Missing lang param",
    MISSING_OR_EMPTY_PROMPTS: "Missing or empty prompts",
    ERROR_IN_GET_BATCH_RECIPES_ASYNC: "Error in getBatchRecipesAsync",
    ERROR_MISSING_PARAMETERS: "Missing required parameters: {missingParams}",
    ERROR_UNEXPECTED_RESPONSE_FORMAT: "Unexpected response format",
    ERROR_GET_BATCH_PROMPTS: "Error in getBatchPromptsAsync",
    ERROR_MISSING_BATCH_PROMPT_PARAMETERS: "Missing batch prompt payload parameters",
    PROMPT: "prompt",
    ERROR_IN_POST_HIDDEN_BATCH_ASYNC: "Error Posting hidden batch",
    ERROR_GET_HIDDEN_BATCH: "Error in Getting hidden batch",
    ERROR_DELETE_BATCH_HIDDEN_ITEMS: "Eror in Deleting hidden batch"
  },
  RECENT_ACTIVITIES: {
    BATCH_GENERATED_RECIPES: "Batch Generated Recipes",
    RECIPES_EXPORTED: "Recipes Exported",
    BATCH_GENERATED: "batchGenerateRecipes",
    GENERATED_RECIPES: "generateRecipe",
    ADD_RECIPES: "addRecipe",
    EXPORT_RECIPES: "exportRecipes",
    REFRESH_ACTIVITY: "refreshActivity",
    BATCH_GENERATOR_CARD_POSITION:"1",
    GENERATED_RECIPES_CARD_POSITION:"2",
    ADD_RECIPE_CARD_POSITION:"3",
    EXPORT_RECIPES_CARD_POSTITION:"4",
    BATCH_TYPE:"batchPrompt"
  },

  LANGUAGE: {
    ENGLISH: "en-US",
    SPANISH: "es-US",
    FRENCH: "fr-FR",
    ENGLISH_LANGUAGE: "English",
    SPANISH_LANGUAGE: "Spanish",
    FRENCH_LANGUAGE: "French",
  },
  ZENDESK_WIDGET_EVENTS : {
    ON: "webWidget:on",
    CLOSE: "close",
    WIDGET: "webWidget",
    HIDE: "hide"
  },
  SHOPPABLE_FLAG: {
    SHOPPABLE: "shoppable",
    SHOPPABLE_PANTRY: "shoppablePantry",
    NON_SHOPPABLE: "nonShoppable",
  },
};
