export const termsPolicyData = {
  heading: "Definitions and legal references",
  definitions: [
    {
      title: "This Application (or this Application)",
      description: "The property that enables the provision of the Service.",
    },
    {
      title: "Agreement",
      description:
        "Any legally binding or contractual relationship between the Owner and the User, governed by these Terms.",
    },
    {
      title: "Business User",
      description: "Any User that does not qualify as a Consumer.",
    },
    {
      title: "European (or Europe)",
      description:
        "Applies where a User, regardless of nationality, is in the EU.",
    },
    {
      title: "Example withdrawal form",
      description: `
              Addressed to:<br>
              Innit Privacy, 2345 Yale Street, 1st Floor, Palo Alto, CA, 94306, United States of America<br>
              mailto:<EMAIL><br><br>
        
              I/We hereby give notice that I/we withdraw from my/our contract of sale of the following goods/for the provision of the following service:<br>
              <em>______________________________________________________</em>
              (insert a description of the goods/services that are subject to the respective withdrawal)<br><br>
        
              • Ordered on: <em>______________________</em> (insert the date)<br>
              • Received on: <em>______________________</em> (insert the date)<br>
              • Name of consumer(s): <em>______________________</em><br>
              • Address of consumer(s): <em>______________________</em><br>
              • Date: <em>______________________</em><br><br>
        
              (sign if this form is notified on paper)
            `,
    },
    {
      title: "Owner (or We)",
      description:
        "Indicates the natural person(s) or legal entity that provides this Application and/or the Service to Users.",
    },
    {
      title: "Product",
      description:
        "A good or service available for purchase through this Application, such as e.g. physical goods, digital files, software, booking services etc.<br><br>The sale of Products may be part of the Service.",
    },
    {
      title: "Service",
      description:
        "The service provided by this Application as described in these Terms and on this Application.",
    },
    {
      title: "Terms",
      description:
        "All provisions applicable to the use of this Application and/or the Service as described in this document, including any other related documents or agreements, and as updated from time to time.",
    },
    {
      title: "User (or You)",
      description:
        "Indicates any natural person or legal entity using this Application.",
    },
    {
      title: "Consumer",
      description:
        "Consumer is any User qualifying as such under applicable law.",
    },
  ],
};
