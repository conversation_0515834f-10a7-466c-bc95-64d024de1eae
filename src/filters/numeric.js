export function formatDouble(value, precision = 2) {
    return parseFloat(Math.round(parseFloat(value) * Math.pow(10, precision)) / Math.pow(10, precision)).toFixed(precision);
}

export function formatInteger(value) {
    if (value % 1 != 0) {
        return value;
    } else {
        return parseInt(value);
    }
}

export function displayNaN(value, fallbackValue = "-") {
    if (isNaN(value)) {
        return fallbackValue;
    } else {
        return value;
    }
}