<template>
  <section class="auth-login-container">
    <div class="auth-login-logo">
      <img
        src="@/assets/images/innit.png"
        alt="Innit logo"
      />
    </div>

    <h1 class="display-2 font-weight-black color-green">Redirecting</h1>

    <p class="text-h3 font-normal color-black">
      If you are not automatically redirected within 10 seconds,
      <nuxt-link
        v-if="pathForLink"
        :to="{ path: pathForLink }"
        class="color-green"
      >click here</nuxt-link>
      <button
        v-if="!pathForLink"
        type="button"
        class="btn-reset auth-text-btn color-green"
        @click="loginAsync"
      >click here</button>
    </p>
  </section>
</template>

<script setup>
import { useInnitAuth } from "../composables/useInnitAuth.js";

definePageMeta({
  layout: 'none',
  auth: false,
});
import updateProtocol from "@/mixins/updateProtocol";
import { useAuth0 } from '@auth0/auth0-vue';
import { useStore } from 'vuex';
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useProjectLang } from "@/composables/useProjectLang";

defineOptions({
  name: "LoginPage",
  mixins: [updateProtocol],
});

const pathForLink = ref("/overview");

const auth0 = useAuth0();
const auth = useInnitAuth();
const store = useStore();
const router = useRouter();
const projectLang = useProjectLang();

const loginAsync = async () => {
  await auth0.loginWithRedirect();
};

const getProjectsAsync = async (store) => {
  if (!store.getters["userData/getProject"]) {
    await store.dispatch('userData/fetchProjectsAsync', { isAdmin: projectLang.isAdmin, isHotRefresh: true });
  }
  return store.getters["userData/getProject"];
};

const checkProjectStatus = async () => {
  if (auth0.isLoading.value) {
    return;
  }

  if (auth0.isAuthenticated.value) {
    await auth.checkTokenSilentlyAsync();
    const project = await getProjectsAsync(store);
    const path = !project?.id ? "/create-project" : "/overview";
    await router.push({ path });
    return;
  }

  await loginAsync();
};

watch(() => auth0.isLoading.value, async () => {
  await checkProjectStatus()
});

onMounted(async () => {
  await checkProjectStatus();
});
</script>
