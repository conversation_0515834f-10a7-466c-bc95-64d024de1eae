<template>
    <content-wrapper :is-body-loading="isPageLoading" class="padding-zero">
      <div class="background-image-add-organizations" v-show="!isPageLoading">
        <img alt="" class="background-image" :src="`${image}`" />
        <div @click="confirmBack()" class="back-btn">
          <img
            alt=""
            class="back-arrow-image"
            src="~/assets/images/back-arrow.png"
          />
          <span class="back-to-organizations text-title-2">{{
            $t('ORGANIZATION.BACK_TO_ORGANIZATIONS')
          }}</span>
        </div>
        <div class="head-btn">
          <button type="button"
            @click="saveOrganizations()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            :class="
              organizationsName.trim() !== '' &&
              isCampaignModified &&
              !isImageUploading &&
              (uploadImagePercentage === 0 || uploadImagePercentage === 100)
                ? 'btn-green'
                : 'disabled-button btn-green'
            "
          >
            {{ $t('BUTTONS.SAVE_BUTTON') }}
          </button>
          <button type="button"
            @click="confirmBack()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            class="btn-green-outline"
          >
            {{ $t('BUTTONS.CANCEL_BUTTON') }}
          </button>
        </div>
        <div Class="edit-organization-isin text-title-2 font-normal">ISIN: {{ isinOrganizations }}</div>
      </div>
      <div class="input-organizatons-section" v-show="!isPageLoading">
        <div class="organizatons-input-section">
          <div class="organizatons-left-section">
            <div
              :style="{ backgroundImage: image ? `url('${image}')` : '' }"
              class="image-section"
              id="categoryImage"
            >
              <div
                @click="deleteImagePopup()"
                v-show="
                  image &&
                  (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                "
                class="organizations-delete-icon"
              >
                <img
                  alt=""
                  class="delete-organizations-icon-image"
                  src="@/assets/images/deleteVideoBtn.png"
                />
              </div>
              <div class="image-main-div" id="recipeVideo">
                <div class="image-inner-container">
                  <div
                    class="progress-image"
                    v-show="
                      uploadImagePercentage >= 1 && uploadImagePercentage <= 99
                    "
                  >
                    <div class="progress-image-content">
                      <div
                        v-show="
                          uploadImagePercentage >= 1 &&
                          uploadImagePercentage <= 5
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 6 &&
                          uploadImagePercentage <= 11
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 12 &&
                          uploadImagePercentage <= 17
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 18 &&
                          uploadImagePercentage <= 24
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 25 &&
                          uploadImagePercentage <= 30
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 31 &&
                          uploadImagePercentage <= 36
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 37 &&
                          uploadImagePercentage <= 41
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 42 &&
                          uploadImagePercentage <= 49
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 50 &&
                          uploadImagePercentage <= 55
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 56 &&
                          uploadImagePercentage <= 61
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 62 &&
                          uploadImagePercentage <= 67
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 68 &&
                          uploadImagePercentage <= 74
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 75 &&
                          uploadImagePercentage <= 80
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 81 &&
                          uploadImagePercentage <= 86
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 87 &&
                          uploadImagePercentage <= 92
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true"
                        />
                      </div>
                      <div
                        v-show="
                          uploadImagePercentage >= 93 &&
                          uploadImagePercentage <= 98
                        "
                      >
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true"
                        />
                      </div>
                      <div v-show="uploadImagePercentage == 99">
                        <img
                          alt=""
                          class="progress-icon"
                          src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true"
                        />
                      </div>
                      <div class="upload-text">
                        <div
                          class="upload-heading text-light-h4"
                          v-if="
                            uploadImagePercentage >= 1 &&
                            uploadImagePercentage <= 98
                          "
                        >
                          Upload is in progress
                        </div>
                        <div class="upload-heading text-light-h4" v-else>Uploaded</div>
                        <span class="upload-media text-light-h6"
                          >{{ (loadedImageSize / 1024000).toFixed(1) }} of
                          {{ (uploadImageSize / 1024000).toFixed(1) }}
                          MB</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
                <img
                  alt=""
                  v-if="
                    image &&
                    (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                  "
                  class="display-image-section"
                  :src="`${image}`"
                />
                <div
                  class="replace-image-tag text-light-h4"
                  v-if="
                    uploadImagePercentage == 0 || uploadImagePercentage == 100
                  "
                >
                  <div class="hover-image">
                    <input
                      type="file"
                      class="upload-input"
                      title="Update Picture"
                      @click="uploadSameImageVideo($event)"
                      @change="checkUploadedFiles"
                      accept=".jpg,.png,.jpeg"
                      ref="productVideo"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="organizations-detail">
              <div
                class="organizations-text-section"
                :class="{
                  'simple-data-tooltip': isOrganizationNameFocused,
                }"
                :data-tooltip-text="isOrganizationNameFocused && organizationsName"
              >
                <input
                  id="titleName"
                  type="text"
                  class="organizatons-title text-title-1"
                  autocomplete="off"
                  @input="campaignModified(), hideEditOrganizationNameTip()"
                  @mouseover="checkEditOrganizationName()"
                  @mouseleave="hideEditOrganizationNameTip()"
                  @keydown="hideEditOrganizationNameTip()"
                  placeholder="Name your organization"
                  v-model="organizationsName"
                  maxlength="50"
                />
                <span v-if="organizationsName == ''" class="compulsory-field">
                </span>
              </div>
              <div class="organizatons-image-details">
                <div class="organizatons-bottom-section">
                  <span class="font-bold">Image (optional): </span>
                  <span class="font-normal">jpg,png format (max 1 MB)</span>
                </div>
              </div>
            </div>
          </div>
          <div class="delete-btn">
            <div
              @click="deletOrganization()"
              class="delete-btn-content text-h3"
            >
              <img
                alt=""
                class="image"
                src="~/assets/images/delete-icon.png"
              />
              <span class="text">
                {{ $t('ORGANIZATION.DELETE_ORGANIZATION') }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <deleteModal
        v-if="isDeleteModalVisible"
        :closeModal="closeModal"
        :productInfoTitle="'Delete Organization?'"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
        :productDescriptionTwo="'organization?'"
        :deleteItem="deleteConfirmOrganization"
        :availableLanguage="0"
      />
      <deleteModal
        v-if="isDeleteImageModal"
        :closeModal="closeModal"
        :productInfoTitle="'Delete Image?'"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
        :productDescriptionTwo="'image?'"
        :deleteItem="deleteImageData"
        :availableLanguage="0"
      />
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :isCampaignModifiedFromShoppableReview="false"
        :callConfirm="backToOrganization"
        :closeModal="closeModal"
      />
      <saveModal
        v-if="isSaveModalVisible"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveOrganizationData"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.SAVE_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
        :imageName="saveImage
        "
      />
      <savingModal v-show="isSavingOrganization" :status="'saving'" />
      <sizeLimit
        v-if="isMaxImagePopupVisible"
        :imageSizeAlert="'Your uploaded image size is larger than 1 MB.'"
        :fileSizeAlert="'Max. size for image: 1 MB'"
        :closeModal="closeModal"
        :isMaxImagePopupVisible="isMaxImagePopupVisible"
      />
      <deletingModal v-show="isDeletingModalVisible && !$nuxt.isOffline" />
      <invalidImageVideoPopup
        v-show="isInvalidImageModalVisible && !$nuxt.isOffline"
        :closeModal="closeModal"
        :acceptedFile="' jpg,png'"
        :video="false"
        :image="true"
        :zip="false"
      />
    </content-wrapper>
</template>

<script setup>
import { ref, onMounted, watch, getCurrentInstance } from "vue";
import { useRouter, useRoute } from 'vue-router';
import { useDelayTimer } from '~/composables/useDelayTimer';
import savingModal from "@/components/saving-modal";
import sizeLimit from "@/components/size-limit.vue";
import deletingModal from "@/components/deleting-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import cancelModal from "@/components/cancel-modal.vue";
import saveModal from "@/components/save-modal.vue";
import deleteModal from "@/components/delete-modal.vue";
import OrganizationsService from "@/services/OrganizationsService";
import axios from "axios";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { useInnitAuthStore } from "../stores/innit-auth.js";

// images
import saveImage from "@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";

// composables
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useEventUtils } from "~/composables/useEventUtils";
import { useProjectLang } from "@/composables/useProjectLang";

// utility
import { useNuxtApp } from '#app';
import { useStore } from "vuex";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
const { triggerLoading, routeToPage } = useCommonUtils();
const { preventEnterAndSpaceKeyPress, onEscapeKeyPress } = useEventUtils();
const router = useRouter();
const route = useRoute();
const { delay } = useDelayTimer();
const { $eventBus, $auth } = useNuxtApp();
const { readyProject, getProject } = useProjectLang();
const { isInnitAdmin } = useInnitAuthStore();
const store = useStore();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const project = ref({});
const isDeletingModalVisible = ref(false);
const isMaxImagePopupVisible = ref(false);
const isInvalidImageModalVisible = ref(false);
const isPageLoading = ref(true);
const isImageAvailable = ref(false);
const isDeleteModalVisible = ref(false);
const isConfirmModalVisible = ref(false);
const isSaveModalVisible = ref(false);
const isDeleteImageModal = ref(false);
const isCampaignModified = ref(false);
const isImageUploading = ref(false);
const isDataSaveInProgress = ref(false);
const organizationsData = ref([]);
const productImage = ref("");
const organizationsImg = ref("");
const organizationsName = ref("");
const organizationState = ref("");
const isinOrganizations = ref([]);
const imageResponseUrl = ref("");
const imageResponseName = ref("");
const file = ref("");
const isSavingOrganization = ref(false);
const cancelImage = ref({});
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref("");
const titleName = ref(null);
const isOrganizationNameFocused = ref(false);
const productVideo = ref(null);
const isAdminCheck = ref(false);

const lang = computed(() => store.getters["userData/getDefaultLang"]);

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isPageLoading.value = true;
      isAdminCheck.value = isInnitAdmin.value;
      triggerLoading("routeloading", isPageLoading.value);
      project.value = await getProject();
      getEditOrganizationsDataAsync();
    }
  });
});

function checkEditOrganizationName() {
  if (
    titleName.value &&
    titleName.value.scrollWidth > titleName.value.clientWidth &&
    organizationsName.value.trim().length
  ) {
    isOrganizationNameFocused.value = true;
  }
}
function hideEditOrganizationNameTip() {
  isOrganizationNameFocused.value = false;
}
function uploadSameImageVideo(event) {
  event.target.value = "";
}
function checkUploadedFiles(event) {
  isCampaignModified.value = true;
  const evnt = event;
  file.value = evnt.target.files || evnt.srcElement.files;
  const fileType = file.value[0].type.split("/")[0];

  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
}
function uploadImageFile(url, file) {
  cancelImage.value =
    axios && axios.CancelToken ? axios.CancelToken.source() : {};

  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: function (progressEvent) {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunctionAsync(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
}
async function uploadedImageFunctionAsync(data) {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    await delay(2000);
    uploadImagePercentage.value = 100;
  }
}
function uploadSameImage(event) {
  event.target.value = "";
}
function deleteConfirmOrganization() {
  deleteOrganizationAsync(route.query.isin);
}
async function deleteOrganizationAsync(isin) {
  isDeleteModalVisible.value = false;
  isDeletingModalVisible.value = true;
  const payload = {
    user: $auth?.user?.value?.email,
    isin,
  };
  try {
    await store.dispatch("organizations/deleteOrganizationsListAsync", {
      payload,
      lang: lang.value,
    });
    isDeletingModalVisible.value = false;
    routeToPage("organizations");
    triggerLoading("newDeletedSuccess");
  } catch (e) {
    console.error(e);
    isDeletingModalVisible.value = false;
    triggerLoading("somethingWentWrong");
  }
}
function deleteImageData() {
  isCampaignModified.value = true;
  isImageAvailable.value = false;
  isDeleteImageModal.value = false;
  productImage.value = "";
  imageResponseUrl.value = "";
  imageResponseName.value = "";
  file.value = "";
  image.value = "";
}
function deletOrganization() {
  if (organizationState === "publishing") {
    return;
  }
  isDeleteModalVisible.value = true;
}
function closeModal() {
  isDeleteModalVisible.value = false;
  isConfirmModalVisible.value = false;
  isSaveModalVisible.value = false;
  isDeleteImageModal.value = false;
  isInvalidImageModalVisible.value = false;
  isMaxImagePopupVisible.value = false;
  isDeletingModalVisible.value = false;
}
function backToOrganization() {
  isCampaignModified.value = false;
  $eventBus.emit("campaignModified", isCampaignModified.value);
  router.push({
    path: "/organizations",
    query: {
      [QUERY_PARAM_KEY.PAGE]: route.query[QUERY_PARAM_KEY.BACK_FROM],
    },
  });
}
function confirmBack() {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToOrganization();
  }
}
function saveOrganizations() {
  isSaveModalVisible.value = true;
}

function deleteImagePopup() {
  isDeleteImageModal.value = true;
}

function campaignModified() {
  isCampaignModified.value = true;
}
async function uploadImageAsync() {
  if (file.value && image.value) {
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      async () => {
        const extension = file.value[0].type.split("/")[1];
        const payload = {
          entity: "organization",
          content: "image",
          extension: extension,
          lang: lang.value,
        };
        await store.dispatch("organizations/getPreSignedImageUrlAsync", {
          payload,
          isin: route.query.isin,
        });
        const response = store.getters['organizations/getPreSignedUrl'];
        await uploadImageFile(response.url, file.value[0]);
        await OrganizationsService.upload(response.url, file.value[0]);
        imageResponseUrl.value =
          response && response.url ? response.url : "";
        imageResponseName.value =
          response && response.fileName ? response.fileName : "";
      },
      false
    );
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  }
}
function uploadFiles() {
  isCampaignModified.value = true;
  if (file.value.length > 0) {
    let filesName = file.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!filesName.match(reg)) {
      isInvalidImageModalVisible.value = true;
      file.value = [];
      filesName.value = "";
      return;
    }
    const fileSize = file.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));
    if (size >= 1 * 1024 * 1024) {
      if (productVideo.value) {
        productVideo.value.blur();
      }
      file.value = [];
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener(
        "load",
        async () => {
          image.value = reader.result;
          if (image.value) {
            loadedImageSize.value = 0;
            uploadImagePercentage.value = 1;
            await uploadImageAsync();
          }
        },
        false
      );
      if (file.value[0]) {
        reader.readAsDataURL(file.value[0]);
      }
    }
  }
}
function saveOrganizationData() {
  isSavingOrganization.value = true;
  postOrganizationsData();
}
async function postOrganizationsData() {
  const payload = {
    isin: isinOrganizations.value,
    name: organizationsName.value.trim(),
    image: imageResponseName.value ? imageResponseName.value : "",
  };
  if (!payload?.image) {
    delete payload.image;
  }
  isDataSaveInProgress.value = true;
  const params = {
    lang: lang.value,
    user: $auth.user.email,
  };
  try {
    await store.dispatch("organizations/postOrganizationsDataAsync", {
      payload,
      params,
    });
    isCampaignModified.value = false;
    isDataSaveInProgress.value = false;
    triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
    isSavingOrganization.value = false;
    closeModal();
    routeToPage("organizations");
    triggerLoading("savedSuccess");
  } catch {
    isDataSaveInProgress.value = false;
    isSavingOrganization.value = false;
    $nuxt.$loading.finish();
  }
}
async function getEditOrganizationsDataAsync() {
  if (route.query.isin) {
    const params = {
      lang: lang.value,
      isIn: route.query.isin,
    };
    try {
      const response = await store.dispatch("organizations/getOrganizationsDataAsync", {
        params,
      });
      isPageLoading.value = false;
      triggerLoading("routeloading", isPageLoading.value);
      organizationsData.value = response ?? [];
      organizationsName.value = response?.name ?? "";
      organizationState.value = response?.state ?? "";
      organizationsImg.value = response?.image?.url ?? "";
      image.value = organizationsImg.value;
      isinOrganizations.value = response?.isin ?? [];
      if (organizationsImg.value) {
        productImage.value = organizationsImg.value;
        isImageAvailable.value = true;
        const urlString = organizationsImg.value.split("/");
        imageResponseName.value = urlString[urlString.length - 1];
        const organizationsImage = $refs.organizationsImage;
        organizationsImage.style.backgroundImage = `url(${organizationsImg.value})`;
      }
    } catch {
      isPageLoading.value = false;
      triggerLoading("routeloading", isPageLoading.value);
    }
  }
}
onEscapeKeyPress(closeModal);

watch(isCampaignModified, (newValue) => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
});
</script>
