<template>
  <content-wrapper wrapper-classes="padding-zero main-section"  :is-body-loading="isPageLoading">
    <div v-if="!isPageLoading" class="main-content">
      <dynamicHeroNewsHeader
        :image-src="image"
        :save-button-text="$t('BUTTONS.CONTINUE')"
        :replace-button-text="$t('REPLACE')"
        :is-replace-live-hero="isReplaceLiveHero"
        :is-campaign-modified="isCampaignModified"
        :news-name="newsName"
        :news-text="newsText"
        :image="image"
        :isCTALinkIsValid="isCTALinkIsValid"
        :newsCTALinkText="newsCTALinkText"
        :upload-image-percentage="uploadImagePercentage"
        @saveAction="saveNewsForm"
        @replaceAction="replaceNewsForm"
        @backToDynamicHeroList="backToDynamicHeroList"
      />
      <div class="news-intro-section">
        <dynamicHeroNewsIntro
          v-model:selectedDate="selectedDate"
          :title="$t('DYNAMIC_HERO.NEWS_FORM')"
          :startDateLabel="$t('DYNAMIC_HERO.START_DATE')"
          :isLiveHeroReplaced="isReplaceLiveHero"
          :isLiveHero="false"
          :disabledDates="disabledDates"
          @date-click="handleDateClick"
          @day-hover="call()"
          :isRange="false"
          :markers="markers"
        />
        <div class="image-input-container">
          <processImage
            :image="image"
            :uploadImagePercentage="uploadImagePercentage"
            :loadedImageSize="loadedImageSize"
            :uploadImageSize="uploadImageSize"
            :checkUploadedFiles="checkUploadedFiles"
            :uploadSameImageVideo="uploadSameImageVideo"
          />
          <dynamicHeroNewsForm
            v-model:newsName="newsName"
            :selectedDate="selectedDate"
            v-model:isNewsStatus="isNewsStatus"
            :isReplaceLiveHero="isReplaceLiveHero"
            :isLiveHero="isLiveHero"
            @scheduleToggle="scheduleToggle"
            :hasNewsNameFocus="hasNewsNameFocus"
            :checkNewsName="checkNewsName"
            :hideNewsTip="hideNewsTip"
          />
        </div>
      </div>
    </div>
    <dynamicHeroNewsContent
      :isPageLoading="isPageLoading"
      :isLiveHero="false"
      :showPreviewToggle="true"
      :newsText="newsText"
      :newsCTAInput="newsCTAInput"
      v-model:newsCTALinkText="newsCTALinkText"
      :isHeroPreview="isHeroPreview"
      :isCTALinkValid="isCTALinkIsValid"
      :isCTALinkBroken="isCTALinkBroken"
      :isCTALinkInProgress="isCTALinkInprogress"
      @update:newsText="newsText = $event"
      @update:newsCTAInput="newsCTAInput = $event"
      @update:newsCTALinkText="newsCTALinkText = $event"
      @update:isHeroPreview="isHeroPreview = $event"
      @validate-cta-link="validateCTALinkInprogress"
    />
    <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && !$nuxt.isOffline"
      :closeModal="closeModal"
      :acceptedFile="'jpg/ jpeg/ png'"
      :video="false"
      :image="true"
      :zip="false"
    />
    <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
    />
    <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
    />
    <saveModal
      v-if="isSaveNewsModalVisible"
      :closeModal="closeModal"
      :saveAndPublishFunction="postDynamicHeroAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="'Do you want to save as draft your News form?'"
      :imageName="'@/assets/images/quiz-form.png'"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduledDateModalVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="closeModal"
      @schedule="closeModal"
      :markers="markers"
      :PatchScheduledHero="postDynamicHeroAsync"
      :selectedDateValue="scheduleDateConfirm"
      @date-click="handleDateClickPopup"
    />
    <savingModal v-show="isNewsSaving" :status="'saving'" />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="postDynamicHeroAsync"
      :closeReplacement="closeModal"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, onMounted, watch, onBeforeUnmount, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import replacementModal from "@/components/confirm-replacement-modal.vue";
import dynamicHeroNewsHeader from "../components/pages/dynamic-hero/dynamic-hero-news-header.vue";
import dynamicHeroNewsIntro from "../components/pages/dynamic-hero/dynamic-hero-news-intro.vue";
import dynamicHeroNewsForm from "../components/pages/dynamic-hero/dynamic-hero-news-form.vue";
import dynamicHeroNewsContent from "../components/pages/dynamic-hero/dynamic-hero-news-content.vue";
import saveModal from "@/components/save-modal.vue";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import sizeLimit from "@/components/size-limit.vue";
import savingModal from "@/components/saving-modal";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import axios from "axios";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useConnectionStatus } from '~/composables/useConnectionStatus';
import { useProjectLang } from "@/composables/useProjectLang";
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useRefUtils } from "@/composables/useRefUtils";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import processImage from "../components/pages/dynamic-hero/process-image.vue";
import cancelModal from "@/components/cancel-modal";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";

const {
  getHeroUUIDAsync,
  dynamicHeroUUIDData,
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  postDynamicHeroDataAsync,
} = useDynamicHeroStore();
const heroData = ref([
  {template: 'News'}
]);
const { watchReactiveValue } = useWatcherUtils();
const { triggerLoading, routeToPage, processScheduledElement, isScheduledWithPublishDate, checkDuplicate, getDisabledDates, getDisableList, useCalendarMarkers } = useCommonUtils();
const { getRef } = useRefUtils();
const { formatJsonTimestamp } = useTimeUtils();
const { isInnitAdmin } = useInnitAuthStore();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const store = useStore();
const route = useRoute();
const { readyProject } = useProjectLang();
const { isOffline } = useConnectionStatus();
const disableList = ref([]);
const incId = ref(0);
const todos = ref([]);
const heroID = ref("");
const isNewsSaving = ref(false);
const hasNewsNameFocus = ref(false);
const isHeroPreview = ref(false);
const scheduleDateConfirm = ref("");
const isScheduledDateModalVisible = ref(false);
const isSaveNewsModalVisible = ref(false);
const isConfirmModalVisible = ref(false);
const uploadImageConfirm = ref("");
const isCampaignModified = ref(false);
const isLiveHero = ref(false);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref("");
const newsCTAInput = ref("");
const newsCTALinkText = ref("");
const isCTALinkInprogress = ref(false);
const isCTALinkIsValid = ref(false);
const isCTALinkBroken = ref(false);
const selectedDate = ref("");
const newsText = ref("");
const isNewsStatus = ref(false);
const newsName = ref("");
const imageFile = ref([]);
const cancelImage = ref({});
const imageResponseUrl = ref("");
const uploadImagePercentage = ref(0);
const isInvalidImageModalVisible = ref(false);
const isUploadingImagePopup = ref(false);
const isMaxImagePopupVisible = ref(false);
const createDuplicate = ref(false);
const isPageLoading = ref(false);
const disabledDates = ref([]);
const isReplaceLiveHero = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const lang = ref("");
const isAdminCheck = ref(false);

onMounted(() => {
  readyProject(({ isProjectReady }) => {
    if (isProjectReady) {
      initializeDataAsync();
      addEventListeners();
    }
  });
});
const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

  lang.value = store.getters["userData/getDefaultLang"];
  await getDynamicHeroDataAsync();

  const sourceUrl = window.location.href;
  if (sourceUrl.includes("replace-live-hero")) {
    isReplaceLiveHero.value = true;
  }
  if (sourceUrl.includes("create-duplicate")) {
    isCampaignModified.value = true;
    createDuplicate.value = true;
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    await getEditDynamicHeroDataAsync();
  } else {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
  disabledDates.value = getDisabledDates().value;
  disableList.value = getDisableList().value;
  if (disabledDates.value.length) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString())
    };
    todos.value.push(newTodo);
  }

  incId.value = todos.value.length;
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};
const checkNewsName = () => {
  const name = document.getElementById("newsNameField");
  if (
    name?.scrollWidth > name?.clientWidth &&
    name !== document.activeElement &&
    newsName.value.trim().length
  ) {
    hasNewsNameFocus.value = true;
  }
};

const hideNewsTip = () => {
  hasNewsNameFocus.value = false;
};
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;

    if (Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getDynamicHeroDataAsync:", error);
  }
};
const handleDateClickPopup = (newValue) => {
  selectedDate.value = newValue;
};
const handleDateClick = () => {
    isNewsStatus.value = true;
    isCampaignModified.value = true;
};
const getUUIDAsync = async () => {
  try {
    await getHeroUUIDAsync({ lang: lang.value });
    const response = await dynamicHeroUUIDData.value;
    heroID.value = response?.uuid;
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getUUIDAsync:", error);
  }
};
const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  imageFile.value = event.target.files || event.srcElement.files;

  const fileType = imageFile.value[0].type.split("/")[0];
  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};
const getEditDynamicHeroDataAsync = async () => {
  const uuid = route.query[QUERY_PARAM_KEY.UUID];
  if (!uuid) return;

  try {
    await getEditPageDynamicHeroDataAsync({
      lang: lang.value,
      uuid,
    });
    const response = await editDynamicHeroDataList.value;
    if (Object.keys(response).length) {
      processDynamicHeroData(response);
    }
    finalizeLoading();
  } catch (error) {
    handleError();
  }
};
const processDynamicHeroData = (response) => {
  if (Object.keys(response).length) {
    isLiveHero.value = response.state === "live" && !createDuplicate.value;
    isNewsStatus.value = !!response.publishDate;
    newsName.value = response.title || "";
    selectedDate.value =
      !createDuplicate.value && response.publishDate
        ? formatJsonTimestamp(response.publishDate)
        : "";
    heroID.value = response.uuid || "";

    const data = response.data || {};
    newsText.value = data.body || "";
    newsCTAInput.value = data.ctaText || "";
    image.value = data.image || "";
    imageResponseUrl.value = data.image || "";
    newsCTALinkText.value = data.ctaLink || "";

    if (newsCTALinkText.value.trim()) {
      validateCTALinkInprogress();
    }
  }
};
const finalizeLoading = () => {
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
};
const handleError = (error) => {
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  console.error(`${$keys.KEY_NAMES.ERROR_IN} getEditDynamicHeroDataAsync:`, error);
};
const uploadFiles = () => {
  if (imageFile.value?.length) {
    const file = imageFile.value[0];
    const fileName = file.name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;

    if (!fileName.match(reg)) {
      imageFile.value = [];
      isInvalidImageModalVisible.value = true;
      return;
    }

    const fileSize = file.size;
    uploadImageSize.value = fileSize;
    if (fileSize > 1 * 1024 * 1024 && fileSize < 15 * 1024 * 1024) {
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = imageFile.value;
      imageFile.value = [];
    } else if (fileSize >= 15 * 1024 * 1024) {
      isMaxImagePopupVisible.value = true;
      imageFile.value = [];
    } else {
      const reader = new FileReader();
      reader.onload = async () => {
        image.value = reader.result;
        if (image.value) {
          loadedImageSize.value = 0;
          uploadImagePercentage.value = 1;
          await uploadImageAsync();
        }
      };
      reader.readAsDataURL(file);
    }
  }
};

const uploadImageAsync = async () => {
  if (imageFile.value?.length && image.value) {
    if (!heroID.value) {
      await getUUIDAsync();
    }

    const file = imageFile.value[0];
    const extension = file.type.split("/")[1];
    const params = {
      entity: "article",
      content: "image",
      extension,
      lang: lang.value,
      public: true,
    };

    await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
      isin: heroID.value,
      params,
    });

    const response = store.getters["preSignedUrl/getPreSignedUrl"];
    imageResponseUrl.value = response?.data?.url || "";

    await uploadImageFile(imageResponseUrl.value || "", file);
  }
};
const continueImage = async () => {
  imageFile.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.addEventListener(
    "load",
    async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
      }
    },
    false
  );
  if (imageFile.value[0]) {
    reader.readAsDataURL(imageFile.value[0]);
  }
};
const uploadImageFile = (url, file) => {
  cancelImage.value = axios.CancelToken.source();

  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunction(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .then(() => {})
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};
const uploadedImageFunction = (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    setTimeout(() => {
      uploadImagePercentage.value = 100;
    }, 2000);
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = "";
};
const saveNewsForm = () => {
  isSaveNewsModalVisible.value = true;
  if (selectedDate.value !== "" && isNewsStatus.value) {
    isScheduledDateModalVisible.value = true;
    scheduleDateConfirm.value = selectedDate.value;
  }
};

const replaceNewsForm = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isNewsStatus.value = newStatus;
};
const postDynamicHeroAsync = async () => {
  try {
    isNewsSaving.value = true;
    const scheduleDateVal = calculateScheduleDate();
    const payload = preparePayload(scheduleDateVal);

    await postDynamicHeroDataAsync({ payload });

    handleSuccess();
    closeModal();
  } catch (error) {
    handleErrors(error);
  } finally {
    isNewsSaving.value = false;
  }
};
const calculateScheduleDate = () => {
  if (isReplaceLiveHero.value) {
    isNewsStatus.value = true;
    isReplacementConfirmPopupVisible.value = false;
    selectedDate.value = new Date();
    selectedDate.value.setHours(0, 0, 0, 0);
  }

  const date = new Date(selectedDate.value);
  return Math.floor(date.getTime() / 1000);
};
const preparePayload = (scheduleDateVal) => {
  const payload = {
    title: newsName.value?.trim() ?? "",
    template: "news",
    publishDate: !selectedDate.value || !isNewsStatus.value ? "" : scheduleDateVal,
    image: imageResponseUrl.value?.replace(/\?.*/, "") ?? "",
    data: {
      body: newsText.value ?? "",
      image: imageResponseUrl.value?.replace(/\?.*/, "") ?? "",
      ctaText: newsCTAInput.value ?? "",
      ctaLink: newsCTALinkText.value?.trim() ?? "",
    },
    state: isNewsStatus.value && selectedDate.value ? "scheduled" : "draft",
    preview: isHeroPreview.value ?? false,
  };

  if (!newsCTAInput) {
    delete payload.data.ctaText;
  }
  if (!newsCTALinkText.value?.trim()) {
    delete payload.data.ctaLink;
  }

  return payload;
};
const handleSuccess = () => {
  isSaveNewsModalVisible.value = false;

  if ((!isNewsStatus.value || !selectedDate.value) && !isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("newsSaved");
  } else if (isNewsStatus.value && selectedDate.value && !isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("newScheduled");
  } else if (isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("contentLive");
  } else if (isReplaceLiveHero.value) {
    triggerLoading("heroReplaced");
  }

  backToDynamicHeroListConfirm();
};
const handleErrors = (error) => {
  console.error(`${$keys.KEY_NAMES.ERROR_IN} postDynamicHeroAsync:`, error);
  closeModal();
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};
const validateCTALinkInprogress = () => {
  if (newsCTALinkText.value.trim() === "") {
    isCTALinkInprogress.value = false;
    isCTALinkIsValid.value = false;
    isCTALinkBroken.value = false;
  } else {
    isCTALinkInprogress.value = true;
    checkCTALink();
  }
};
const checkCTALink = () => {
  const urlPattern = /^(https?|ftp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const rocheAppPattern = /^(rocheapp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const linkInput = newsCTALinkText.value.trim();

  if (urlPattern.test(linkInput) || (rocheAppPattern.test(linkInput) && linkInput !== "")) {
    isCTALinkIsValid.value = true;
  } else {
    isCTALinkBroken.value = true;
    isCTALinkIsValid.value = false;
  }

  setTimeout(() => {
    isCTALinkInprogress.value = false;
  }, 1000);
};
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
  routeToPage("dynamic-hero");
};
const closeModal = () => {
  scheduleDateConfirm.value = selectedDate.value;
  if (!isReplaceLiveHero.value) {
    isReplaceLiveHero.value = false;
  }
  isReplacementConfirmPopupVisible.value = false;
  isNewsSaving.value = false;
  isInvalidImageModalVisible.value = false;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  isConfirmModalVisible.value = false;
  isSaveNewsModalVisible.value = false;
  isScheduledDateModalVisible.value = false;
};
const handleTypeInput = (event) => {
  const newsNameField = getRef("newsNameField");
  const newsTextField = getRef("newsTextField");
  const newsCTAField = getRef("newsCTAField");
  const newsCTALinkField = getRef("newsCTALinkField");

  if (newsNameField?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (newsTextField?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (newsCTAField?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (newsCTALinkField?.contains(event.target)) {
    isCampaignModified.value = true;
  }
};
watch(isOffline, (offlineStatus) => {
  if (offlineStatus) {
    closeModal();
  }
});
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onBeforeUnmount(() => {
  document.removeEventListener("input", handleTypeInput);
  document.removeEventListener("keyup", handleESCClickOutside);
});
</script>
