<template>
  <client-only>
    <content-wrapper :is-body-loading="isPageLoading" wrapper-classes="users">
      <template v-slot:title>
        <span data-test-id="users-heading">{{ $t("USERS.USERS_TEXT") }}</span>
      </template>
      <block-wrapper
        :titleClass="!isSearchActive ? 'display-title' : 'users-block-wrapper'"
      >
        <template v-slot:title>
          {{ !isSearchActive ? "Active Users" : "Search Results" }}
          <div class="invite-section" id="invite-popup">
            <button
              v-show="!isSearchActive"
              @click="inviteUsers()"
              type="button"
              class="btn-green"
              data-test-id="invite-button"
            >
              {{ $t("USERS.INVITE") }}
            </button>
          </div>
        </template>

        <div class="users-details-container">
          <template v-if="users.length">
            <div class="table-head">
              <div data-test-id="users-names" class="name-head">
                {{ $t("USERS.NAME") }}
              </div>
              <div data-test-id="users-permissions" class="permission-head">
                {{ $t("USERS.PERMISSIONS") }}
              </div>
            </div>
            <div class="line-break"></div>
            <div data-test-id="users-list" class="user-content">
              <div class="name-part">
                <div
                  v-for="(user, index) in users"
                  :key="index"
                  class="name-coloumn"
                >
                  <div
                    :style="{ backgroundColor: user.profileColor }"
                    class="name-symbol"
                  >
                    {{ getFirstChar(user.email) }}
                  </div>
                  <div class="name text-light-h3">{{ user.email }}</div>
                </div>
              </div>

              <div class="controls-section">
                <div class="controls-header">
                  <div class="project-header">Project</div>
                  <div class="user-header">Users</div>
                  <div class="content-header">Content</div>
                </div>
                <div
                  v-for="(control, index) in users"
                  :key="index"
                  class="controls"
                >
                  <label
                    v-for="permission in permissions"
                    :key="permission.key"
                    class="checkbox checkbox-without-text"
                    :for="`checkbox-${index}-${permission.key}`"
                    :aria-label="permission.type"
                  >
                    <input
                      type="checkbox"
                      :id="`checkbox-${index}-${permission.key}`"
                      v-model="control[permission.key]"
                      @change="editPermissions(control)"
                      :checked="control[permission.key]"
                      :disabled="isCurrentUserCheckboxEnable(control.id, permission.type)"
                    />
                    <span
                      data-test-id="update-users-permissions"
                      class="checkmark"
                    ></span>
                  </label>
                </div>
              </div>
            </div>
          </template>

          <div v-if="!users.length" class="no-users-found">
            <h5>{{ $t("USERS.NO_USER_FOUND") }}</h5>
          </div>

          <div
            v-if="!isSearchActive && users.length"
            class="invite-section"
            id="invite-popup"
          >
            <button
              type="button"
              class="btn-green users-green-button"
              :class="{ 'simple-data-tooltip': isApplyBtnDisabled }"
              :disabled="!isCheckedPermissions || isApplyBtnDisabled"
              :data-tooltip-text="isApplyBtnDisabled && tooltipText"
              data-test-id="users-apply-changes"
              @click="applyChanges()"
            >
              {{ $t("USERS.INVITE_APPLY_CHANGES") }}
            </button>
          </div>
        </div>
      </block-wrapper>
    </content-wrapper>
  </client-only>
</template>

<script setup>
import {
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
} from "vue";
import InviteUserPopup from "@/components/invite-user-popup.vue";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import { PROJECT_PERMISSIONS } from "@/сonstants/project-permissions";
import { AxiosError } from "axios";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useNuxtApp } from "#app";
import { useI18n } from "vue-i18n";
import { useStore } from "vuex";
import { useSearchStore } from "../stores/search.js";

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { t } = useI18n();
const { $tracker, $eventBus, $auth, $axios } = useNuxtApp();
const store = useStore();
const { triggerLoading } = useCommonUtils();
const { isAdmin, readyProject } = useProjectLang();
const { searchContexts } = useSearchStore();

const { openModal, updateModalProps, } = useBaseModal({
  "inviteUserPopup": InviteUserPopup,
});

const editedUsers = ref([]);
const primaryUsers = ref([]);
const isNewUser = ref(false);
const users = ref([]);
const isCheckedPermissions = ref(false);
const isApplyBtnDisabled = ref(false);
const email = ref("");
const isEmailErrorMessage = ref(false);
const isPageLoading = ref(true);
const warning = ref(false);
const isSearchActive = ref(false);
const isAdminCheck = ref(false);
const permissions = ref([
  {
    type: PROJECT_PERMISSIONS.MANAGE_PROJECT,
    key: PROJECT_PERMISSIONS.MANAGE_PROJECT,
  },
  {
    type: PROJECT_PERMISSIONS.MANAGE_USERS,
    key: PROJECT_PERMISSIONS.MANAGE_USERS,
  },
  {
    type: PROJECT_PERMISSIONS.MANAGE_CONTENT,
    key: PROJECT_PERMISSIONS.MANAGE_CONTENT,
  },
]);
const errorMessage = "Unable to change permission";
const errorMessageSomeWrong = "Something went wrong";
const tooltipText =
  "User permissions cannot be changed. \n Project must have at least one admin \n (full permissions) user.";

const handleTriggerSearch = (data) => {
  if (data.trim()) {
    isSearchActive.value = true;
    primaryUsers.value = [...users.value];
    searchUser(data);
  }
};

const editPermissions = (control) => {
  const existingUserIndex = editedUsers.value.findIndex(user => user?.id === control?.id);
  if (existingUserIndex !== -1) {
    editedUsers.value[existingUserIndex] = { ...editedUsers.value[existingUserIndex], ...control };
  } else {
    editedUsers.value.push(control);
  }

  checkUserPermissionEvent('$keys.EVENT_KEY_NAMES.CLICK_USER_PERMISSION', control);
  isCheckedPermissions.value = true;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, true);

  if (!isSearchActive.value) {
    checkFullUser();
  }
};

const checkFullUser = () => {
  const isFullUser = users.value?.find(
    (user) =>
      user[PROJECT_PERMISSIONS.MANAGE_USERS] &&
      user[PROJECT_PERMISSIONS.MANAGE_PROJECT]
  );
  isApplyBtnDisabled.value = isFullUser === undefined;
};

const isCurrentUserCheckboxEnable = (userId, type) => {
  if (type === PROJECT_PERMISSIONS.MANAGE_USERS && !isAdminCheck.value) {
    return userId === String($auth?.user?.value?.sub);
  }
  return false;
};

const isValidEmail = () => {
  isEmailErrorMessage.value = false;
  if (email.value.trim() === "") {
    warning.value = false;
    return;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  warning.value = !emailRegex.test(email.value);
};

const searchUser = (data) => {
  if (!data.trim()) {
    return;
  }
  const escapedData = data.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const regex = new RegExp(escapedData, "i");
  users.value = users.value.filter((user) => regex.test(user.email));
};

const inviteUsers = () => {
  checkUserEvent($keys.EVENT_KEY_NAMES.CLICK_INVITE_USER);
  email.value = "";
  openModal({
    name: "inviteUserPopup",
    hideCloseBtn: false,
    props: {
      email: email.value,
      hasInviteSent: false,
      emails: users.value,
    },
    onCallback: (email) => email && postEmailData(email),
    onClose: () => reset(),
  });
  checkUserEvent($keys.EVENT_KEY_NAMES.VIEW_INVITE_USER);
};

const postInvite = () => {
  if (!email.value) {
    return;
  }
  const permission = [PROJECT_PERMISSIONS.MANAGE_CONTENT];
  checkUserEvent(
    $keys.EVENT_KEY_NAMES.CLICK_INVITE_USER_SUBMIT,
    email.value,
    permission
  );
  isEmailErrorMessage.value = false;

  const endpoint = store.getters["config/getClientEndpoint"](
    "flite",
    "inviteUser"
  );
  const baseURL = store.getters["config/getClientConfig"]("flite")?.host;

  $axios
    .post(
      endpoint,
      {},
      {
        params: {
          email: email.value,
          permissions: PROJECT_PERMISSIONS.MANAGE_CONTENT,
        },
        baseURL,
      }
    )
    .then((data) => {
      if (data?.response?.data?.errors) {
        isPageLoading.value = false;
        isEmailErrorMessage.value = true;
        return;
      }
      isPageLoading.value = false;
      isEmailErrorMessage.value = false;
      getAllUsersDataAsync();
      isCheckedPermissions.value = false;
      updateModalProps({
        name: "inviteUserPopup",
        props: {
          hasInviteSent: true,
        },
      });
    })
    .catch((err) => {
      checkUserEvent(
        $keys.EVENT_KEY_NAMES.CLICK_INVITE_USER_ERROR,
        email.value,
        permission,
        err.message
      );
      isPageLoading.value = false;
      console.error(err);
      emitErrorNotification(errorMessageSomeWrong);
    });
};

const getFirstChar = (userName) => {
  return userName?.[0]?.toUpperCase();
};


const clearPageSearch = () => {
  isSearchActive.value = false;
  isPageLoading.value = true;
  getAllUsersDataAsync();
};

const applyChanges = () => {
  isPageLoading.value = true;
  let payload = { users: [] };
  users.value.forEach((user) => {
    let permissions = [
      user[PROJECT_PERMISSIONS.MANAGE_CONTENT]
        ? PROJECT_PERMISSIONS.MANAGE_CONTENT
        : "",
      user[PROJECT_PERMISSIONS.MANAGE_USERS]
        ? PROJECT_PERMISSIONS.MANAGE_USERS
        : "",
      user[PROJECT_PERMISSIONS.MANAGE_PROJECT]
        ? PROJECT_PERMISSIONS.MANAGE_PROJECT
        : "",
    ].filter(Boolean);
    payload.users.push({ id: user.id, permissions });
  });
  putControlAsync(payload).then(() => {
    if (editedUsers.value?.length) {
      editedUsers.value = editedUsers.value.map(user => ({
        ...user,
        [PROJECT_PERMISSIONS?.MANAGE_CONTENT]: true,
      }));
    }
  });
};

const postEmailData = (emailValue) => {
  email.value = emailValue;
  isNewUser.value = true;
  postInvite();
};

const deleteControlAsync = async (control, type, data) => {
  const response = await store
    .dispatch("userData/removeProjectUserPermissionsAsync", {
      params: {
        userId: data.id,
        permissions: type,
      },
    })
    .catch(() => {
      emitErrorNotification(errorMessage);
    });

  if (response instanceof AxiosError) {
    emitErrorNotification(errorMessage);
  }

  await store.dispatch("userData/fetchUserPermissionsAsync", {
    isHotRefresh: true,
  });
  await getAllUsersDataAsync();
  isPageLoading.value = false;
};

const putControlAsync = async (payload) => {
  const response = await store
    .dispatch("userData/updateProjectUserPermissionsAsync", payload)
    .catch(() => {
      emitErrorNotification(errorMessage);
    });

  if (response instanceof AxiosError) {
    emitErrorNotification(errorMessage);
  }

  await store.dispatch("userData/fetchUserPermissionsAsync", {
    isHotRefresh: true,
  });
  await store.dispatch('userData/fetchProjectsAsync', {
    isHotRefresh: true,
    isAdmin: isAdminCheck.value,
  }).catch();
  if (!(response instanceof AxiosError)) {
    triggerLoading($keys.KEY_NAMES.APPLY);
  }
  await getAllUsersDataAsync();
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
  isPageLoading.value = false;
  isCheckedPermissions.value = false;
};

const getAllUsersDataAsync = async () => {
  await store.dispatch("userData/getAllUsersDataAsync").catch();

  const usersData = store.getters["userData/getProjectUsers"];
  users.value = usersData?.map((item) => {
    const { permissions } = item;
    return {
      ...item,
      [PROJECT_PERMISSIONS.MANAGE_CONTENT]: permissions.includes(
        PROJECT_PERMISSIONS.MANAGE_CONTENT
      ),
      [PROJECT_PERMISSIONS.MANAGE_PROJECT]: permissions.includes(
        PROJECT_PERMISSIONS.MANAGE_PROJECT
      ),
      [PROJECT_PERMISSIONS.MANAGE_USERS]: permissions.includes(
        PROJECT_PERMISSIONS.MANAGE_USERS
      ),
    };
  });
  if (!isNewUser.value && editedUsers.value?.length && users.value?.length) {
    const editedUsersMap = new Map(editedUsers.value.map((user) => [user.id, user]));

    users.value.forEach((user) => {
      const editedUser = editedUsersMap.get(user.id);
      if (editedUser) Object.assign(user, editedUser);
    });
  }
  isPageLoading.value = false;
  checkFullUser();
};

const checkUserEvent = (
  description,
  emailAddress,
  activePermission,
  errorMessage
) => {
  const eventProperties = {
    ...(emailAddress && { [t("EVENT_NAMES.EMAIL_ADDRESS")]: emailAddress }),
    ...(activePermission && {
      [t("EVENT_NAMES.ACTIVE_PERMISSION")]: activePermission,
    }),
    ...(errorMessage && { [t("EVENT_NAMES.ERROR_MESSAGE")]: errorMessage }),
  };

  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};

const checkUserPermissionEvent = (description, user) => {
  const projectName = store.getters["userData/getProject"]?.displayName || "";
  const eventProperties = {
    [t("EVENT_NAMES.PERMISSION_NAME")]: [
      t("EVENT_NAMES.PROJECT"),
      t("EVENT_NAMES.USERS"),
      t("EVENT_NAMES.CONTENT"),
    ],
    [t("EVENT_NAMES.PERMISSION_ACTIVE")]: [
      user[PROJECT_PERMISSIONS.MANAGE_PROJECT],
      user[PROJECT_PERMISSIONS.MANAGE_USERS],
      user[PROJECT_PERMISSIONS.MANAGE_CONTENT],
    ],
    [t("EVENT_NAMES.USER_ID")]: user.id,
    [t("EVENT_NAMES.PROJECT_NAME")]: projectName,
  };

  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};

const reset = () => {
  email.value = "";
  isNewUser.value = false;
};

const emitErrorNotification = (message) => {
  if (!message) {
    return;
  }
  $eventBus.emit("iqUsersNotification", {
    message,
    color: "red",
  });
};

watch(searchContexts, (val) => {
  const str = val?.global?.str;
  if (str) {
    handleTriggerSearch(str);
  } else {
    clearPageSearch();
  }
});

onMounted(async () => {
  isPageLoading.value = true;

  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await getAllUsersDataAsync();
      isAdminCheck.value = await isAdmin.value;

      const str = searchContexts.value.global?.str;
      if (str) {
        handleTriggerSearch(str);
      }
    }
  });
  checkUserEvent($keys.EVENT_KEY_NAMES.VIEW_USERS);
});

onBeforeUnmount(() => {
  reset();
});
</script>
