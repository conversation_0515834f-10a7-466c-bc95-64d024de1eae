<template>
  <content-wrapper wrapper-classes="padding-zero" :is-body-loading="isPageLoading">
    <div v-if="!isPageLoading" class="main-content">
      <dynamicHeroAdviceHeader
        :image="image"
        :isReplaceLiveHero="isReplaceLiveHero"
        :isCampaignModified="isCampaignModified"
        :adviceName="adviceName"
        :adviceText="adviceText"
        :uploadImagePercentage="uploadImagePercentage"
        @back="backToDynamicHeroList"
        @save="saveAdviceForm"
        @replace="replaceAdviceForm"
      />
      <div class="advice-intro-section">
        <dynamicHeroAdviceIntro
          v-model:selectedDate="selectedDate"
          :title="$t('DYNAMIC_HERO.ADVICE_FORM')"
          :startDateLabel="$t('DYNAMIC_HERO.START_DATE')"
          :isLiveHeroReplaced="isReplaceLiveHero"
          :isLiveHero="false"
          :disabledDates="disabledDates"
          @date-click="handleDateClick"
          @day-hover="call()"
          :isRange="false"
          :markers="markers"
        />
        <div class="image-input-container">
          <processImage
            :image="image"
            :uploadImagePercentage="uploadImagePercentage"
            :loadedImageSize="loadedImageSize"
            :uploadImageSize="uploadImageSize"
            :checkUploadedFiles="checkUploadedFiles"
            :uploadSameImageVideo="uploadSameImageVideo"
          />
          <dynamicHeroAdviceForm
            v-model:adviceName="adviceName"
            @scheduleToggle="scheduleToggle"
            :selectedDate="selectedDate"
            v-model:isAdviceStatusVisible="isAdviceStatus"
            :isReplaceLiveHero="isReplaceLiveHero"
            :isLiveHero="isLiveHero"
            :isAdviceNameFocus="isAdviceNameFocus"
            :checkAdviceName="checkAdviceName"
            :hideAdviceTip="hideAdviceTip"
          />
        </div>
      </div>
    </div>
    <dynamicHeroAdviceContent
      :isPageLoading="isPageLoading"
      :isLiveHero="isLiveHero"
      :isHeroPreview="isHeroPreview"
      @update:isHeroPreview="isHeroPreview = $event"
      :checkAdviceText="checkAdviceText"
      v-model:adviceText="adviceText"
    />
    <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && !isOffline"
      :closeModal="closeModal"
      :acceptedFile="'jpg/ jpeg/ png'"
      :video="false"
      :image="true"
      :zip="false"
    />
    <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
    />
    <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
    />
    <saveModal
      v-if="isSaveNewsModalVisible"
      :closeModal="closeModal"
      :saveAndPublishFunction="postDynamicHeroAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="$t('DYNAMIC_HERO.ADVICE_DRAFT_FORM')"
      :imageName="saveImage"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduledDateModalVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="closeModal"
      @schedule="closeModal"
      :markers="markers"
      :PatchScheduledHero="postDynamicHeroAsync"
      :selectedDateValue="scheduleDateConfirm"
      @date-click="handleDateClickPopup"
    />
    <savingModal v-show="isNewsSaving" :status="'saving'" />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="postDynamicHeroAsync"
      :closeReplacement="closeModal"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted, watch } from "vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import replacementModal from "@/components/confirm-replacement-modal.vue";
import saveModal from "@/components/save-modal.vue";
import cancelModal from "@/components/cancel-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import sizeLimit from "@/components/size-limit.vue";
import ArticlesService from "@/services/ArticlesService";
import savingModal from "@/components/saving-modal";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useRefUtils } from "~/composables/useRefUtils";
import saveImage from "@/assets/images/quiz-form.png"
import { useProjectLang } from "@/composables/useProjectLang";
import { useCommonUtils } from "@/composables/useCommonUtils";
import axios from "axios";
import dynamicHeroAdviceHeader from "../components/pages/dynamic-hero/dynamic-hero-advice-header.vue";
import dynamicHeroAdviceIntro from "../components/pages/dynamic-hero/dynamic-hero-advice-intro.vue";
import processImage from "../components/pages/dynamic-hero/process-image.vue";
import dynamicHeroAdviceForm from "../components/pages/dynamic-hero/dynamic-hero-advice-form.vue";
import dynamicHeroAdviceContent from "../components/pages/dynamic-hero/dynamic-hero-advice-content.vue";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useConnectionStatus } from '~/composables/useConnectionStatus';

const {
  getHeroUUIDAsync,
  dynamicHeroUUIDData,
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  postDynamicHeroDataAsync,
  postDynamicHeroDataList,
} = useDynamicHeroStore();
const heroData = ref([
  {template: 'Advice'}
]);
const { readyProject } = useProjectLang();
const { watchReactiveValue } = useWatcherUtils();
const { isInnitAdmin } = useInnitAuthStore();
const { triggerLoading, routeToPage, processScheduledElement, isScheduledWithPublishDate, checkDuplicate, getDisabledDates, getDisableList, useCalendarMarkers } = useCommonUtils();
const { formatDate, formatJsonTimestamp, isDateDisabled, checkDate } = useTimeUtils();
const { getRef } = useRefUtils();
const disableList = ref([]);
const incId = ref(0);
const todos = ref([]);
const heroID = ref("");
const isNewsSaving = ref(false);
const isAdviceNameFocus = ref(false);
const isHeroPreview = ref(false);
const scheduleDateConfirm = ref("");
const isScheduledDateModalVisible = ref(false);
const isSaveNewsModalVisible = ref(false);
const isConfirmModalVisible = ref(false);
const uploadImageConfirm = ref("");
const isCampaignModified = ref(false);
const isLiveHero = ref(false);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref("");
const selectedDate = ref("");
const adviceText = ref("");
const isAdviceStatus = ref(false);
const adviceName = ref("");
const imageFile = ref([]);
const cancelImage = ref({});
const imageResponseUrl = ref("");
const imageFileName = ref("");
const uploadImagePercentage = ref(0);
const isInvalidImageModalVisible = ref(false);
const isUploadingImagePopup = ref(false);
const isMaxImagePopupVisible = ref(false);
const createDuplicate = ref(false);
const isPageLoading = ref(false);
const lang = ref("");
const store = useStore();
const route = useRoute();
const disabledDates = ref([]);
const isReplaceLiveHero = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const isAdminCheck = ref(false);
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { isOffline } = useConnectionStatus();

onMounted(async () => {
  await readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
    }
  });
});
const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  lang.value = store.getters["userData/getDefaultLang"];
  await getDynamicHeroDataAsync();
  const sourceUrl = window.location.href;
  if (sourceUrl.includes("replace-live-hero")) {
    isReplaceLiveHero.value = true;
  }

  if (sourceUrl.includes("create-duplicate")) {
    isCampaignModified.value = true;
    createDuplicate.value = true;
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    await getEditDynamicHeroDataAsync();
  } else {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
  disabledDates.value = getDisabledDates().value;
  disableList.value = getDisableList().value;
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString())
    };
    todos.value.push(newTodo);
  }
  incId.value = todos.value.length;
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};
const checkAdviceText = () => {
  if (!adviceText.value.trim().length) {
    adviceText.value = "";
  } else {
    adviceText.value = adviceText.value.replace(/\s+/g, " ");
  }
};
const checkAdviceName = () => {
  const name = getRef("adviceNameField");
  if (
    name.scrollWidth > name.clientWidth &&
    name !== document.activeElement &&
    adviceName.value.trim().length > 0
  ) {
    isAdviceNameFocus.value = true;
  }
};
const hideAdviceTip = () => {
  isAdviceNameFocus.value = false;
};
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;

    if (Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getDynamicHeroDataAsync:", error);
  }
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isAdviceStatus.value = newStatus;
};
const handleDateClickPopup = (newValue) => {
  selectedDate.value = newValue;
};
const handleDateClick = () => {
    isAdviceStatus.value = true;
    isCampaignModified.value = true;
};
const getUUIDAsync = async () => {
  try {
    await getHeroUUIDAsync({ lang: lang.value });
    const response = await dynamicHeroUUIDData.value;
    heroID.value = response?.uuid;
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN}getUUIDAsync:`, error);
  }
};
const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  const evnt = event;
  imageFile.value = evnt.target.files || evnt.srcElement.files;
  const fileType = imageFile.value[0].type.split("/")[0];

  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};
const getEditDynamicHeroDataAsync = async () => {
  const uuid = route.query[QUERY_PARAM_KEY.UUID];
  if (uuid) {
    try {
      await getEditPageDynamicHeroDataAsync({
        lang: lang.value,
        uuid
      });
      const response = await editDynamicHeroDataList.value;

      if (Object.keys(response).length) {
        processResponse(response);
      }
    } catch (error) {
      handleLoading(false);
      console.error(`${$keys.KEY_NAMES.ERROR_IN}getEditDynamicHeroDataAsync:`, error);
    }
  }
};
const processResponse = (response) => {
  updateHeroState(response);
  updateNewsStatus(response);
  updateAdviceData(response);
  updateImageData(response);
  handleLoading(false);
};
const updateHeroState = (response) => {
  if (response?.state) {
    isLiveHero.value = response.state === "live" && !createDuplicate.value;
  }
};
const updateNewsStatus = (response) => {
  isAdviceStatus.value = !!response?.publishDate;
  if (!createDuplicate.value) {
    selectedDate.value = response?.publishDate
      ? formatJsonTimestamp(response.publishDate)
      : "";
  }
};
const updateAdviceData = (response) => {
  adviceName.value = response?.title || "";
  heroID.value = response?.uuid || "";
  adviceText.value = response?.data?.body || "";
};
const updateImageData = (response) => {
  image.value = response?.data?.image || "";
  imageResponseUrl.value = response?.data?.image || "";
};
const handleLoading = (isLoading) => {
  isPageLoading.value = isLoading;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isLoading);
};
const resetImageState = () => {
  imageFile.value = "";
  imageFileName.value = "";
};
const uploadFiles = () => {
  isCampaignModified.value = true;

  if (imageFile.value?.length) {
    imageFileName.value = imageFile.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;

    if (!imageFileName.value.match(reg)) {
      resetImageState();
      isInvalidImageModalVisible.value = true;
      return;
    }

    const fileSize = imageFile.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));

    if (size > 1 * 1024 * 1024 && size < 15 * 1024 * 1024) {
      let element = getRef("productVideo");
      element?.blur();
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = imageFile.value;
      imageFile.value = null;
      return;
    } else if (size >= 15 * 1024 * 1024) {
      let element = getRef("productVideo");
      element?.blur();
      imageFile.value = null;
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener(
        "load",
        async () => {
          image.value = reader.result;
          if (image.value) {
            loadedImageSize.value = 0;
            uploadImagePercentage.value = 1;
            await uploadImageAsync();
          }
        },
        false
      );
      reader.readAsDataURL(imageFile.value[0]);
    }
  }
};
const uploadImageAsync = async () => {
  if (imageFile.value && image.value) {
    if (!heroID.value) {
      await getUUIDAsync();
    }
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      async () => {
        const extension = imageFile.value[0].type.split("/")[1];
        const params = {
          entity: "article",
          content: "image",
          extension: extension,
          lang: lang.value,
          public: true,
        };
        await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
          isin: heroID.value,
          params,
        });
        const response = store.getters["preSignedUrl/getPreSignedUrl"];
        imageResponseUrl.value = response?.data?.url || "";
        await uploadImageFile(response?.data?.url || "", imageFile.value[0]);
        await ArticlesService.upload(response?.data?.url || "", imageFile.value[0]);
      },
      false
    );
    if (imageFile.value[0]) {
      reader.readAsDataURL(imageFile.value[0]);
    }
  }
};
const continueImage = () => {
  imageFile.value = uploadImageConfirm.value;

  const reader = new FileReader();
  reader.addEventListener(
    "load",
    async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
      }
    },
    false
  );
  reader.readAsDataURL(imageFile.value[0]);
};
const uploadImageFile = async (url, file) => {
  cancelImage.value = axios?.CancelToken?.source() || null;

  try {
    await axios.put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value?.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunction(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    });
  } catch (e) {
    if (axios.isCancel(e)) {
      console.error("Image request canceled.");
    } else {
      console.error(e);
    }
  }
};
const uploadedImageFunction = (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    setTimeout(() => {
      uploadImagePercentage.value = 100;
    }, 2000);
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = "";
};

const saveAdviceForm = () => {
  isSaveNewsModalVisible.value = true;
  if (selectedDate.value && isAdviceStatus.value) {
    isScheduledDateModalVisible.value = true;
    scheduleDateConfirm.value = selectedDate.value;
  }
};
const replaceAdviceForm = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const postDynamicHeroAsync = async () => {
  const scheduleDate = calculateScheduleDate();

  isNewsSaving.value = true;
  const payload = createPayload(scheduleDate);

  try {
    await postDynamicHeroDataAsync({ payload });
    const response = await postDynamicHeroDataList.value;

    if (Object.keys(response).length) {
      isSaveNewsModalVisible.value = false;
      handleSuccess();
      closeModal();
      backToDynamicHeroListConfirm();
    }
  } catch (error) {
    closeModal();
    console.error(`Error in postDynamicHeroAsync:`, error);
  } finally {
    isNewsSaving.value = false;
  }
};
const calculateScheduleDate = () => {
  if (isReplaceLiveHero.value) {
    isAdviceStatus.value = true;
    isReplacementConfirmPopupVisible.value = false;
    selectedDate.value = new Date();
    selectedDate.value.setHours(0, 0, 0, 0);
  }

  const date = new Date(selectedDate.value)
  return Math.floor(date.getTime() / 1000)
};
const createPayload = (scheduleDate) => {
  return {
    title: adviceName.value?.trim() ?? "",
    template: "advice",
    publishDate: selectedDate.value && isAdviceStatus.value ? scheduleDate : "",
    image: imageResponseUrl.value?.replace(/\?.*/, "") ?? "",
    data: {
      body: adviceText.value ?? "",
      image: imageResponseUrl.value?.replace(/\?.*/, "") ?? "",
    },
    state: isAdviceStatus.value && selectedDate.value ? "scheduled" : "draft",
    preview: isHeroPreview.value ?? false,
  };
};
const handleSuccess = () => {
  if ((!isAdviceStatus.value || !selectedDate.value) && !isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("AdviceSaved");
  } else if (!isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("AdviceScheduled");
  } else if (isLiveHero.value) {
    triggerLoading("contentLive");
  } else if (isReplaceLiveHero.value) {
    triggerLoading("heroReplaced");
  }
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
  routeToPage("dynamic-hero");
};
const closeModal = () => {
  scheduleDateConfirm.value = selectedDate.value;
  if (!isReplaceLiveHero.value) {
    isReplaceLiveHero.value = false;
  }
  isReplacementConfirmPopupVisible.value = false;
  isNewsSaving.value = false;
  isInvalidImageModalVisible.value = false;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  isConfirmModalVisible.value = false;
  isSaveNewsModalVisible.value = false;
  isScheduledDateModalVisible.value = false;
};
const handleTypeInput = (event) => {
  if (document.querySelector("#adviceNameField").contains(event.target)) {
    isCampaignModified.value = true;
  }
};
watch(isOffline, (offlineStatus) => {
  if (offlineStatus) {
    closeModal();
  }
});
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onBeforeUnmount(() => {
  document.removeEventListener('input', handleTypeInput);
  document.removeEventListener('keyup', handleESCClickOutside);
});
</script>
