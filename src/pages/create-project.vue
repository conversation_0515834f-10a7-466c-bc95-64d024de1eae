<template>
  <client-only>
    <div class="create-page-main-container">
      <div class="create-page-section">
        <div class="left-section">
          <img
            src="@/assets/images/create-project-image.jpg"
            alt="create project"
          />
        </div>
        <div class="right-section">
          <div class="create-project-section">
            <div class="innit-iq-logo">
              <img src="@/assets/images/innit.png" alt="Innit logo" />
            </div>
            <div class="create-project-zone">
              <div class="heading-text">Create a project</div>
              <div class="project-name">
                <div class="project-name-heading">Project name</div>
                <input
                  type="text"
                  class="project-name-input"
                  v-model.trim="projectName"
                />
              </div>
              <div class="create-project-button-container">
                <button
                  type="button"
                  class="create-project-button"
                  :disabled="!projectName"
                  @click="createProject()"
                  :class="{ 'disable-button': !isValidProjectName }"
                >
                  Create
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </client-only>
</template>

<script setup>
import { useContext } from "../composables/useContext.js";
import { useProjectLang } from "../composables/useProjectLang.js";
import { useConfigStore } from "../stores/config.js";

definePageMeta({
  layout: 'none',
});
import { ref, computed, onMounted, getCurrentInstance } from 'vue';
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useNuxtApp } from '#app';

const projectName = ref('');
const isAdminCheck = ref(false);

const { $tracker } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { isAdmin, readyProject } = useProjectLang();
const configStore = useConfigStore();
const store = useStore();
const router = useRouter();
const context = useContext();

const isValidProjectName = computed(() => projectName.value.trim() !== '');

onMounted(async () => {
  isAdminCheck.value = await isAdmin.value;
  readyProject(({ isProjectReady }) => {
    if (isProjectReady && !isAdminCheck.value) {
      goToOverviewPage();
    }
  });
  $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_CREATE_PROJECT, {}, { ...LOCAL_TRACKER_CONFIG });
  Promise.resolve().then(() => context.trackerIdentify(true));
});

const createProject = async () => {
  if (!isValidProjectName.value) {
    return;
  }

  $tracker.sendEvent($keys.EVENT_KEY_NAMES.CLICK_CREATE_PROJECT, {}, { ...LOCAL_TRACKER_CONFIG });

  const payload = {
    displayName: projectName.value,
  };

  try {
    await store.dispatch('userData/postProjectAsync', { payload });
    await store.dispatch('userData/fetchProjectsAsync', { isHotRefresh: true, isAdmin: isAdminCheck.value });
    await store.dispatch('userData/fetchUserPermissionsAsync', { isHotRefresh: true });
    await configStore.fetchFeaturesAsync({ isHotRefresh: true });

    await goToOverviewPage();
  } catch (err) {
    console.error(err);
  }
};
const goToOverviewPage = async () => {
  await router.push({ path: "/overview" });
};
</script>
