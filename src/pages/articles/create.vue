<template>
  <client-only>
    <article-item
      :is-loading="isLoading"
    />
  </client-only>
</template>

<script setup>
import ArticleItem from "../../components/pages/articles/article-item.vue";
import { useArticleItemStore } from "../../stores/article-item.js";
import { useStore } from "vuex";

const { $keys } = useNuxtApp();
const { readyProject } = useProjectLang();
const store = useStore();
const { getArticleUUIDAsync } = useArticleItemStore();
const { triggerLoading } = useCommonUtils();

const isLoading = ref(true);

onMounted(() => {
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);
  readyProject( async ({ isProjectReady }) => {
    if (isProjectReady) {
      const lang = store.getters["userData/getDefaultLang"];
      await getArticleUUIDAsync({ lang });
      isLoading.value = false;
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
    }
  });
});
</script>
