<template>
  <client-only>
    <article-item
      :is-edit="true"
      :is-loading="isLoading"
    />
  </client-only>
</template>

<script setup>
import ArticleItem from "../../components/pages/articles/article-item.vue";
import { useArticleItemStore } from "../../stores/article-item.js";
import { useStore } from "vuex";

const { $keys } = useNuxtApp();
const route = useRoute();
const store = useStore();
const { readyProject } = useProjectLang();
const { isLoading, getArticleAsync } = useArticleItemStore();
const { triggerLoading } = useCommonUtils();

const getArticleByUUIDAsync = async () => {
  try {
    await getArticleAsync({
      lang: store.getters["userData/getDefaultLang"],
      uuid: route.params.uuid,
    });
  } catch {
    triggerLoading($keys.KEY_NAMES.ERROR_OCCURRED);
    navigateTo("/articles");
  } finally {
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
  }
};

onMounted(() => {
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);
  readyProject(({ isProjectReady }) => {
    if (isProjectReady) {
      getArticleByUUIDAsync();
    }
  });
});
</script>
