<template>
  <client-only>
    <content-wrapper :is-body-loading="!projectList.length">
      <template v-slot:title>Settings</template>

      <block-wrapper :is-loading="showLoading">
        <template v-slot:title>Project</template>

        <p class="setting-container-title black">Project Name</p>

        <template v-for="item in projectList">
          <div class="setting-container-block"
               :class="{'setting-container-block-column': !isAdminCheck || projectList.length < minProjectsForContentDirection}"
          >
            <div class="setting-container-field">
              <input
                type="text"
                class="form-control"
                v-model.trim="item.displayName"
                placeholder="Project name"
                @input="projectNameInput()"
              >
            </div>
            <div class="setting-project-actions">
              <button
                type="button"
                class="btn-green"
                @click="updateProject(item.id, item.displayName)"
                :disabled="!item.displayName || item.displayName === item.oldDisplayName"
              >{{ $t('BUTTONS.RENAME_BUTTON') }}</button>
              <button
                type="button"
                class="btn-green-outline"
                @click="confirmRemoveProject(item)"
              >{{ $t('BUTTONS.DELETE_BUTTON') }}</button>
            </div>
          </div>
        </template>
        <div v-if="isAdminCheck" class="setting-container-create">
          <button type="button" class="btn-green" @click="createProject()">{{ $t('BUTTONS.CREATE_BUTTON') }}</button>
        </div>
      </block-wrapper>

      <delete-modal
        v-if="isDeleteProjectModalVisible"
        :closeModal="closeModal"
        :productInfoTitle="$t('DESCRIPTION_POPUP.DELETE_PROJECT')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
        :productDescriptionTwo="$t('DESCRIPTION_POPUP.PROJECT')"
        :deleteItem="removeProject"
      />
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from "vue";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import DeleteModal from "@/components/delete-modal";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import { useProjectLang } from "@/composables/useProjectLang";
import { useNuxtApp } from "#app";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useRouter } from 'vue-router';

const store = useStore();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { readyProject, isAdmin } = useProjectLang();
const { $tracker, $auth } = useNuxtApp();
const { triggerLoading } = useCommonUtils();
const { t } = useI18n();
const router = useRouter();

const projectList = ref([]);
const showLoading = ref(false);
const minProjectsForContentDirection = 3;
const selectedProjectName = ref("");
const selectedDeleteProjectId = ref("");
const isDeleteProjectModalVisible = ref(false);
const isAdminCheck = ref(false);

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isAdminCheck.value = await isAdmin.value;
      getProjectList();
    }
  });
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
  document.addEventListener("keyup", handleESCClickOutside);
  checkSettingEvent($keys.EVENT_KEY_NAMES.VIEW_SETTINGS);
});

const handleESCClickOutside = (event) => {
  if (event.key === "Escape") {
    closeModal();
  }
};

const getProjectList = () => {
  const list = store.getters["userData/getProjectList"];
  const mapItem = ({ id, displayName }) => ({
    id,
    displayName,
    oldDisplayName: displayName,
  });
  projectList.value = list?.map(mapItem) || [];
};

const projectNameInput = () => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, true);
};

const updateProject = (id, displayName) => {
  if (!id || !displayName) return;

  const project = store.getters["userData/getProjectById"](id);
  if (displayName === project.displayName) return;

  checkSettingEvent(
    $keys.EVENT_KEY_NAMES.CLICK_RENAME_PROJECT,
    null,
    project.displayName,
    displayName
  );
  showLoading.value = true;

  store
    .dispatch("userData/updateProjectAsync", {
      id,
      displayName,
      isAdmin: isAdminCheck.value,
    })
    .catch((error) => {
      console.warn("Could not update project", error);
      checkSettingEvent(
        $keys.EVENT_KEY_NAMES.VIEW_RENAME_PROJECT_ERROR,
        null,
        project.displayName,
        displayName,
        error.message
      );
    })
    .finally(() => {
      getProjectList();
      showLoading.value = false;
      triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
    });
};

// Confirm remove project
const confirmRemoveProject = (item) => {
  selectedProjectName.value = item.displayName;
  selectedDeleteProjectId.value = item.id;
  isDeleteProjectModalVisible.value = true;
};

// Remove a project
const removeProject = async () => {
  if (!selectedDeleteProjectId.value) return;

  showLoading.value = true;
  isDeleteProjectModalVisible.value = false;

  checkSettingEvent(
    $keys.EVENT_KEY_NAMES.CLICK_DELETE_PROJECT,
    selectedProjectName.value
  );

  await store
    .dispatch("userData/removeProjectAsync", {
      id: selectedDeleteProjectId.value,
      isAdmin: isAdminCheck.value,
    })
    .catch((error) => {
      checkSettingEvent(
        $keys.EVENT_KEY_NAMES.VIEW_DELETE_PROJECT_ERROR,
        selectedProjectName.value,
        null,
        null,
        error.message
      );
    });

  getProjectList();

  if (projectList.value?.length <= 1) {
    createProject();
  }

  showLoading.value = false;
};

// Create new project
const createProject = () => {
  router.push({ path: "/create-project" })?.catch();
  // window.zE($keys.ZENDESK_WIDGET_EVENTS.WIDGET, $keys.ZENDESK_WIDGET_EVENTS.CLOSE);
};

// Track setting events
const checkSettingEvent = (
  description,
  projectName = null,
  oldProjectName = null,
  newProjectName = null,
  errorMessage = null
) => {
  const eventProperties = {
    ...(projectName && { [t("EVENT_NAMES.PROJECT_NAME")]: projectName }),
    ...(oldProjectName && {
      [t("EVENT_NAMES.OLD_PROJECT_NAME")]: oldProjectName,
    }),
    ...(newProjectName && {
      [t("EVENT_NAMES.NEW_PROJECT_NAME")]: newProjectName,
    }),
    ...(errorMessage && { [t("EVENT_NAMES.ERROR_MESSAGE")]: errorMessage }),
  };

  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};

const closeModal = () => {
  selectedDeleteProjectId.value = "";
  selectedProjectName.value = "";
  isDeleteProjectModalVisible.value = false;
};
onBeforeUnmount(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
});
</script>
