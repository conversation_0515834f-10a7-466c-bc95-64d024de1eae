<template>
  <section class="auth-redirecting-container">
    <div class="auth-redirecting-logo">
      <img
          src="@/assets/images/innit.png"
          alt="Innit logo"
      />
    </div>

    <h1 class="display-2 font-weight-black color-green">Redirecting</h1>

    <p class="text-h3 font-normal color-black">
      <span>If you are not automatically redirected within 10 seconds,</span>
      <nuxt-link
          v-if="pathForLink && !isFullLink"
          :to="{ path: pathForLink }"
          class="color-green"
      >click here</nuxt-link>
      <a
          v-if="isFullLink"
          class="color-green"
          :href="sanitize(pathForLink)"
      >click here</a>
      <button
          v-if="!pathForLink && !isFullLink"
          type="button"
          class="btn-reset auth-text-btn color-green"
          @click="signupAsync"
      >click here</button>
    </p>
  </section>
</template>

<script setup>
import { AUTH_SCOPE } from "../сonstants/auth0-options.js";
import { ref, onBeforeMount } from 'vue';
import updateProtocol from "@/mixins/updateProtocol";
import { sanitizeUrl } from "@braintree/sanitize-url";
import { useRouter, useRoute } from 'vue-router';
import { useNuxtApp } from '#app';
import { useStore } from 'vuex';

definePageMeta({
  layout: 'none',
  auth: false,
});

defineComponent({
  mixins: [updateProtocol],
});

const store = useStore();
const { $auth } = useNuxtApp();
const isFullLink = ref(false);
const pathForLink = ref(undefined);
const router = useRouter();
const route = useRoute();

onBeforeMount(async () => {
  window.history.pushState({}, document.title, window.location.pathname);
  const { redirectUrl } = route.query;

  if (redirectUrl) {
    const isValid = isValidURL(redirectUrl);

  // If valid redirectUrl, redirect to it
    if (isValid) {
      isFullLink.value = true;
      pathForLink.value = redirectUrl;
      window.location.href = redirectUrl.toString();
      return;
    }

    // Check if the redirectUrl exists in application routes
    const path = redirectUrl.startsWith("/") ? redirectUrl : `/${redirectUrl}`;
    const isRouteFromApp = router.options.routes.findIndex((route) => route.path === path);
    if (isRouteFromApp !== -1) {
      try {
        await getProjectsAsync();
        await router.push(path);
      } catch (e) {
        pathForLink.value = path;
      }
      return;
    }
  }

  await signupAsync();
});

const signupAsync = async () => {
  await $auth.loginWithRedirect("auth0", {
    params: {
      scope: AUTH_SCOPE,
      screen_hint: "signup",
    },
  });
};

const getProjectsAsync = async () => {
  const project = store.getters["userData/getProject"];
  if (!project) {
    await store.dispatch('userData/fetchProjectsAsync', { isAdmin: true, isHotRefresh: true });
  }
};

const isValidURL = (str) => {
  const a = document.createElement('a');
  a.href = str;
  return a.host && a.host !== window.location.host;
};

const sanitize = (url) => sanitizeUrl(url);

</script>
