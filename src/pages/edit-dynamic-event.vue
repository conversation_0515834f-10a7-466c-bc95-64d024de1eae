<template>
  <content-wrapper wrapper-classes="padding-zero" :is-body-loading="isPageLoading">
    <div class="edit-event-form-hero">
      <div v-if="!isPageLoading" class="main-content">
        <div class="dynamic-hero-form-edit-id">ID: {{ eventUuid }}</div>
        <DynamicHeroEventHeader
          :image-src="image"
          :replace-button-text="$t('REPLACE')"
          :is-replace-live-hero="isLiveHeroReplaced"
          :isLiveHero="isLiveHero"
          :is-campaign-modified="isCampaignModified"
          :event-name="eventName"
          :subtext="eventSubtext"
          :description="eventDescriptionText"
          :eventDate="eventDate"
          :isCTALinkIsValid="isCTALinkValid"
          :eventCTALinkText="eventCTALinkText"
          :upload-image-percentage="uploadImagePercentage"
          :is-page-overview-visible="isPageOverviewVisible"
          @saveAction="openSavePopup"
          @replaceAction="openReplacePopup"
          @backToDynamicHeroList="backToDynamicHeroList"
        />
        <div class="edit-event-intro-section">
          <dynamicHeroEventIntro
            containerClass="edit-event-intro-container"
            headingClass="edit-event-heading"
            logoClass="edit-event-icon"
            :startDateLabel="'Start date:'"
            :isLiveHeroReplaced="isLiveHeroReplaced"
            :isLiveHero="isLiveHero"
            v-model:selectedDate="scheduleDate"
            :isRange="false"
            :markers="markers"
            :disabledDates="disabledDates"
            @date-click="handleDateClick"
            @day-hover="call()"
          />
          <div class="image-input-container">
            <processImage
              :image="image"
              :uploadImagePercentage="uploadImagePercentage"
              :loadedImageSize="loadedImageSize"
              :uploadImageSize="uploadImageSize"
              :checkUploadedFiles="checkUploadedFiles"
              :uploadSameImageVideo="uploadSameImageVideo"
            />
            <dynamicHeroEventForm
              baseClass="edit"
              v-model:eventName="eventName"
              :dateValue="scheduleDate"
              :isReplaceLiveHero="isLiveHeroReplaced"
              :isLiveHero="isLiveHero"
              :isEditPage="true"
              @scheduleToggle="scheduleToggle"
              v-model:isEventStatus="isEventStatusDisplayed"
              :deleteEvent="deleteEvent"
              :checkEventName="checkEventName"
              :hideEventTip="hideEventTip"
              :isEventNameInFocus="isEventNameInFocus"
            />
          </div>
        </div>
        <dynamicHeroEventContent
          v-model:description="eventDescriptionText"
          v-model:subtext="eventSubtext"
          v-model:eventDate="eventDate"
          v-model:ctaText="eventCTAText"
          v-model:ctaLink="eventCTALinkText"
          :isHeroPreview="isHeroPreview"
          :isLiveHero="isLiveHero"
          @update:isHeroPreview="[(isHeroPreview = $event), (isCampaignModified = true)]"
          :isCTALinkInprogress="isCTALinkInProgress"
          :isCTALinkIsValid="isCTALinkValid"
          :isCTALinkBroken="isCTALinkBroken"
          @update:eventCTALinkText="eventCTALinkText = $event"
          @validate-cta-link="validateisCTALinkInprogress"
        />
      </div>
    </div>
    <DynamicHeroScheduleModal
      :isVisible="isScheduleEventPopupVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="closeModal"
      @schedule="closeModal"
      :markers="markers"
      :PatchScheduledHero="patchDynamicHeroEventDataAsync"
      :selectedDateValue="scheduleDateConfirm"
      @date-click="handleDateClickPopup"
    />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="closeModal"
      :closeReplacement="patchDynamicHeroEventDataAsync"
    />
    <saveModal
      v-if="isSaveDynamicEvent"
      :closeModal="closeModal"
      :saveAndPublishFunction="saveDynamicEvent"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="'Do you want to save as draft your Event form?'"
      :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
    />
    <deleteModal
      v-if="isTheEventDeleted"
      :closeModal="closeModal"
      :productInfoTitle="'Delete the event?'"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
      :productDescriptionTwo="'event?'"
      :deleteItem="deleteDynamicHeroEventDataAsync"
      :availableLanguage="0"
    />
    <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && isOffline"
      :closeModal="closeModal"
      :acceptedFile="' jpg/ jpeg/ png'"
      :video="false"
      :image="true"
      :zip="false"
    />
    <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
    />
    <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
    />
    <updatingLiveHero
      v-if="isLiveHeroSaveDynamicHero"
      :closeModal="closeModal"
      :callConfirm="saveDynamicEvent"
      :updatingText="'Please, confirm the updating live Hero'"
    />
    <deletingModal v-show="isDeletingModalVisible" />
    <savingModal v-show="isEventSaving" :status="'saving'" />
  </content-wrapper>
</template>
<script setup>
import { ref, onMounted, watch, getCurrentInstance } from "vue";
import replacementModal from "@/components/confirm-replacement-modal";
import deleteModal from "@/components/delete-modal";
import updatingLiveHero from "@/components/updating-live";
import deletingModal from "@/components/deleting-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import sizeLimit from "@/components/size-limit.vue";
import ArticlesService from "@/services/ArticlesService";
import axios from "axios";
import saveModal from "@/components/save-modal";
import savingModal from "@/components/saving-modal";
import { useRefUtils } from "~/composables/useRefUtils";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useConnectionStatus } from '~/composables/useConnectionStatus';
import { useInnitAuthStore } from "../stores/innit-auth.js";
import cancelModal from "@/components/cancel-modal";
import processImage from "../components/pages/dynamic-hero/process-image.vue";
import { useProjectLang } from "@/composables/useProjectLang";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";
import DynamicHeroEventHeader from "../components/pages/dynamic-hero/dynamic-hero-event-header.vue";
import dynamicHeroEventIntro from "../components/pages/dynamic-hero/dynamic-hero-event-intro.vue";
import dynamicHeroEventForm from "../components/pages/dynamic-hero/dynamic-hero-event-form.vue";
import dynamicHeroEventContent from "../components/pages/dynamic-hero/dynamic-hero-event-content.vue";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";

const { watchReactiveValue } = useWatcherUtils();
const { getRef } = useRefUtils();
const { triggerLoading, routeToPage, useCalendarMarkers, checkDuplicate, processScheduledElement, isScheduledWithPublishDate, getDisabledDates, getDisableList } = useCommonUtils();
const heroData = ref([
  {template: 'Event'}
]);
const { isInnitAdmin } = useInnitAuthStore();
const { readyProject } = useProjectLang();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const store = useStore();
const route = useRoute();
const disableList = ref([]);
const { isOffline } = useConnectionStatus();
const eventCTAText = ref("");
const isHeroPreview = ref(false);
const isEventSaving = ref(false);
const isTheEventDeleted = ref(false);
const isSaveDynamicEvent = ref(false);
const isCampaignModified = ref(false);
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const isUploadingImagePopup = ref(false);
const uploadImageConfirm = ref("");
const scheduleDateConfirm = ref("");
const isMaxImagePopupVisible = ref(false);
const eventCTALinkText = ref("");
const eventUuid = ref("");
const eventState = ref("");
const isCTALinkInProgress = ref(false);
const imageResponseUrl = ref("");
const isCTALinkValid = ref(false);
const isCTALinkBroken = ref(false);
const imageFile = ref([]);
const imageFileName = ref("");
const eventDate = ref("");
const eventSubtext = ref("");
const isConfirmModalVisible = ref(false);
const eventDescriptionText = ref("");
const isEventStatusDisplayed = ref(false);
const eventName = ref("");
const isPageLoading = ref(false);
const isPageOverviewVisible = ref(false);
const isScheduleEventPopupVisible = ref(false);
const isDeletingModalVisible = ref(false);
const isInvalidImageModalVisible = ref(false);
const isEventNameInFocus = ref(false);
const scheduleDate = ref("");
const image = ref("");
const isLiveHero = ref(false);
const isLiveHeroSaveDynamicHero = ref(false);
const heroID = ref("");
const isLiveHeroReplaced = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const disabledDates = ref([]);
const incId = ref(0);
const cancelImage = ref({});
const todos = ref([]);
const lang = ref("");
const isAdminCheck = ref(false);
const {
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  getHeroUUIDAsync,
  dynamicHeroUUIDData,
  patchDynamicHeroDataAsync,
  deleteDynamicHeroDataAsync
} = useDynamicHeroStore();

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
    }
  });
});

const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  lang.value = store.getters["userData/getDefaultLang"];
  await getDynamicHeroDataAsync();
  await getEditDynamicHeroDataAsync();
  let sourceUrl = window.location.href;
  if (sourceUrl.includes("replace-live-hero")) {
    isLiveHeroReplaced.value = true;
  }
  disabledDates.value = await getDisabledDates().value;
  disableList.value = await getDisableList().value;
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString()),
    };
    todos.value.push(newTodo);
  }
  incId.value = todos.value.length;
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};

const checkEventName = () => {
  let name = getRef("eventInputField");
  if (
    name?.scrollWidth > name?.clientWidth &&
    name !== document.activeElement &&
    eventName.value.trim().length
  ) {
    isEventNameInFocus.value = true;
  }
};
const hideEventTip = () => {
  isEventNameInFocus.value = false;
};
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const handleDateClick = () => {
  isEventStatusDisplayed.value = true;
  isCampaignModified.value = true;
};
const handleDateClickPopup = (newValue) => {
  scheduleDate.value = newValue;
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;
    if (response && Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error("Error in getDynamicHeroDataAsync:", error);
  }
};
const getUUIDAsync = async () => {
  try {
    await getHeroUUIDAsync({ lang: lang.value });
    const response = await dynamicHeroUUIDData.value;
    heroID.value = response?.uuid;
  } catch (error) {
    console.error("Error in getUUIDAsync:", error);
  }
};

const getTime = (jsonTimestamp) => {
  const timestamp = jsonTimestamp * 1000;
  const date = new Date(timestamp);
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};

const deleteEvent = () => {
  isTheEventDeleted.value = true;
};

const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  const evnt = event;
  imageFile.value = evnt.target.files || evnt.srcElement.files;
  const fileType = imageFile.value[0].type.split("/")[0];
  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};

const uploadFiles = () => {
  isCampaignModified.value = true;
  if (imageFile.value.length) {
    imageFileName.value = imageFile.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!imageFileName.value.match(reg)) {
      imageFile.value = [];
      imageFileName.value = "";
      isInvalidImageModalVisible.value = true;
      return;
    }
    const fileSize = imageFile.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));
    if (size > 1 * 1024 * 1024 && size < 15 * 1024 * 1024) {
      let ele = document.querySelector('#productVideo');
      ele?.blur();
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = imageFile.value;
      imageFile.value = [];
      return;
    } else if (size >= 15 * 1024 * 1024) {
      let element = document.querySelector('#productVideo');
      element?.blur();
      imageFile.value = [];
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener(
        "load",
        async () => {
          image.value = reader.result;
          if (image.value) {
            loadedImageSize.value = 0;
            uploadImagePercentage.value = 1;
            await uploadImageAsync();
          }
        },
        false
      );
      if (imageFile.value[0]) {
        reader.readAsDataURL(imageFile.value[0]);
      }
    }
  }
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isEventStatusDisplayed.value = newStatus;
};
const continueImage = () => {
  imageFile.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.addEventListener(
    "load",
    async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
      }
    },
    false
  );
  if (imageFile.value[0]) {
    reader.readAsDataURL(imageFile.value[0]);
  }
};
const uploadImageAsync = async () => {
  if (imageFile.value && image.value) {
    if (!heroID.value) {
      await getUUIDAsync();
    }
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      async () => {
        const extension = imageFile.value[0].type.split("/")[1];
        const params = {
          entity: "article",
          content: "image",
          extension: extension,
          lang: lang.value,
          public: true
        };
        await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
          isin: heroID.value,
          params
        });
        const response = store.getters['preSignedUrl/getPreSignedUrl'];
        imageResponseUrl.value = response?.data?.url ?? "";
        await uploadImageFile(imageResponseUrl.value, imageFile.value[0]);
        await ArticlesService.upload(
          response.url ?? "",
          imageFile.value[0]
        );
      },
      false
    );
    if (imageFile.value[0]) {
      reader.readAsDataURL(imageFile.value[0]);
    }
  }
};
const uploadImageFile = (url, file) => {
  cancelImage.value = axios && axios.CancelToken ? axios.CancelToken.source() : {};
  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: function (progressEvent) {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunction(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      }
    })
    .then(function () {})
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};
const uploadedImageFunction = (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    setTimeout(() => {
      uploadImagePercentage.value = 100;
    }, 2000);
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = "";
};
const getEditDynamicHeroDataAsync = async () => {
  const uuid = route.query[QUERY_PARAM_KEY.UUID];
  if (!uuid) return;

  isPageLoading.value = true;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

  try {
    await getEditPageDynamicHeroDataAsync({
      lang: lang.value,
      uuid,
    });
    const response = await editDynamicHeroDataList.value;

    if (Object.keys(response).length) {
      processResponseState(response);
      processResponseData(response);
      handleDraftState();
    }
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getEditDynamicHeroDataAsync:", error);
  } finally {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};
const processResponseState = (response) => {
  if (response?.state) {
    isLiveHero.value = response.state === "live";
    isHeroPreview.value = response.preview ?? false;
  }
  eventState.value = response.state ?? "";
  isEventStatusDisplayed.value = !!response?.publishDate;
  scheduleDate.value = response?.publishDate ? getTime(response.publishDate) : "";
  eventName.value = response?.title ?? "";
  image.value = response?.image ?? "";
  imageResponseUrl.value = response?.image ?? "";
  eventUuid.value = response.uuid ?? "";
};

const processResponseData = (response) => {
  if (response?.data) {
    eventCTALinkText.value = response.data.ctaLink ?? "";
    eventCTAText.value = response.data.ctaText ?? "";
    eventSubtext.value = response.data.subtext ?? "";
    eventDate.value = response.data.dates ?? "";
    eventDescriptionText.value = response.data.body ?? "";

    if (eventCTALinkText.value.trim() !== "") {
      validateisCTALinkInprogress();
    }
  }
};
const handleDraftState = () => {
  if (eventState.value === "draft" && scheduleDate.value !== "") {
    scheduleDate.value = "";
    isEventStatusDisplayed.value = false;
  }
};
const saveDynamicEvent = () => {
  isSaveDynamicEvent.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  patchDynamicHeroEventDataAsync();
};
const openReplacePopup = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const patchDynamicHeroEventDataAsync = async () => {
  try {
    initializePatchProcess();
    const scheduleDate = calculateScheduleDate();
    let payload = createPayload(scheduleDate);
    cleanupPayload(payload);
    await dispatchPatchDynamicHeroDataAsync(payload);
    handlePostPatch();

    routeToPage("dynamic-hero");
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "patchDynamicHeroEventDataAsync:", error);
    isEventSaving.value = false;
  } finally {
    isScheduleEventPopupVisible.value = false;
  }
};
const initializePatchProcess = () => {
  isScheduleEventPopupVisible.value = false;
  isEventSaving.value = true;
};
const calculateScheduleDate = () => {
  let scheduleDateVal
  if (isLiveHero.value || isLiveHeroReplaced.value) {
    isEventStatusDisplayed.value = true
    isReplacementConfirmPopupVisible.value = false
    scheduleDate.value = new Date()
    scheduleDate.value.setHours(0, 0, 0, 0)
    const unixTimestampMs = scheduleDate.value.getTime()
    scheduleDateVal = Math.floor(unixTimestampMs / 1000)
  } else {
    const date = new Date(scheduleDate.value)
    const unixTimestampMs = date.getTime()
    scheduleDateVal = Math.floor(unixTimestampMs / 1000)
  }
  return scheduleDateVal
}
const createPayload = (scheduleDateVal) => {
  return {
    uuid: route.query[QUERY_PARAM_KEY.UUID],
    title: eventName.value?.trim() ?? '',
    template: 'event',
    publishDate: !scheduleDate.value || !isEventStatusDisplayed.value ? '0' : scheduleDateVal,
    image: imageResponseUrl.value?.replace(/\?.*/, '') ?? '',
    data: {
      body: eventDescriptionText.value ?? '',
      image: imageResponseUrl.value?.replace(/\?.*/, '') ?? '',
      subtext: eventSubtext.value ?? '',
      dates: eventDate.value ?? '',
      ctaLink: eventCTALinkText.value?.trim() ?? '',
      ctaText: eventCTAText.value ?? '',
    },
    state: !scheduleDate.value || !isEventStatusDisplayed.value ? 'draft' : 'scheduled',
    preview: isHeroPreview.value ?? false,
  }
}
const cleanupPayload = (payload) => {
  if (!eventCTALinkText.value.trim()) {
    delete payload.data.ctaLink
  }
  if (!eventCTAText.value) {
    delete payload.data.ctaText
  }
  if (!scheduleDate.value || !isEventStatusDisplayed.value) {
    delete payload.publishDate
  }
}
const dispatchPatchDynamicHeroDataAsync = async (payload) => {
  await patchDynamicHeroDataAsync({
    payload,
    uuid: route.query[QUERY_PARAM_KEY.UUID],
  });
}
const handlePostPatch = () => {
  isEventSaving.value = false

  if ((!isEventStatusDisplayed.value || !scheduleDate.value) && !isLiveHero.value && !isLiveHeroReplaced.value) {
    triggerLoading('eventSaved')
  } else if (isEventStatusDisplayed.value && scheduleDate.value && !isLiveHero.value && !isLiveHeroReplaced.value) {
    triggerLoading('eventScheduled')
  } else if (isLiveHero.value && !isLiveHeroReplaced.value) {
    triggerLoading('contentLive')
  } else if (isLiveHeroReplaced.value) {
    triggerLoading('heroReplaced')
  }
}
const deleteDynamicHeroEventDataAsync = async () => {
  try {
    isDeletingModalVisible.value = true
    await deleteDynamicHeroDataAsync({ uuid: route.query[QUERY_PARAM_KEY.UUID] });
    routeToPage('dynamic-hero')
    triggerLoading('deletedSucces')
    closeModal()
  } catch (error) {
    closeModal()
    console.error($keys.KEY_NAMES.ERROR_IN + 'deleteDynamicHeroEventDataAsync:', error)
  }
}
const validateisCTALinkInprogress = () => {
  if (!eventCTALinkText.value.trim()) {
    isCTALinkInProgress.value = false
    isCTALinkValid.value = false
    isCTALinkBroken.value = false
  } else {
    isCTALinkInProgress.value = true
    checkCTALink()
  }
}
const checkCTALink = () => {
  let urlPattern = /^(https?|ftp):\/\/[^/\s]+(\/[^/\s]*)*$/
  let rocheAppPattern = /^(rocheapp):\/\/[^/\s]+(\/[^/\s]*)*$/
  let linkInput = eventCTALinkText.value.trim()
  if (
    urlPattern.test(linkInput) ||
    (rocheAppPattern.test(linkInput) && linkInput !== '')
  ) {
    isCTALinkValid.value = true
  } else {
    isCTALinkBroken.value = true
    isCTALinkValid.value = false
  }
  setTimeout(() => {
    isCTALinkInProgress.value = false
  }, 1000)
}
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};

const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading('CAMPAIGN_MODIFIED', isCampaignModified.value);
  routeToPage('dynamic-hero');
};
const closeModal = () => {
  scheduleDateConfirm.value = scheduleDate.value;
  if (!isLiveHeroReplaced.value) {
    isLiveHeroReplaced.value = false;
  }
  isReplacementConfirmPopupVisible.value = false;
  isScheduleEventPopupVisible.value = false;
  isTheEventDeleted.value = false;
  isInvalidImageModalVisible.value = false;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  isSaveDynamicEvent.value = false;
  isConfirmModalVisible.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  isDeletingModalVisible.value = false;
};
const openSavePopup = () => {
  if (isLiveHero.value) {
    isLiveHeroSaveDynamicHero.value = true;
  } else {
    isSaveDynamicEvent.value = true;
    if (!scheduleDate.value) {
      isScheduleEventPopupVisible.value = false;
    } else if (scheduleDate.value && isEventStatusDisplayed.value) {
      isScheduleEventPopupVisible.value = true;
      scheduleDateConfirm.value = scheduleDate.value;
      isSaveDynamicEvent.value = false;
    }
  }
};
const handleTypeInput = (event) => {
  if (getRef("eventInputField")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("descriptionNotes")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("eventSubText")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("eventDates")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("newsCTALinkField")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("newsCTAField")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
};
watch(isOffline, (offlineStatus) => {
  if (offlineStatus) {
    closeModal();
  }
});
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onBeforeUnmount(() => {
  document.removeEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});
</script>
