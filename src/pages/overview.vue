<template>
  <content-wrapper :is-body-loading="isPageLoading">
    <div class="overview-page">
      <div class="overview-main-container">
        <div v-if="!isPageLoading" class="dashboard-main-section">
          <div data-test-id="quick-link-heading" class="quick-links-header font-bold">{{ $t('OVERVIEW.QUICK_LINKS') }}
          </div>
          <div class="quick-link-list-container">
            <div class="quick-link-list-main" v-for="(item, index) in quickLinkList" :key="index">
              <button :data-test-id="item.id" type="button" class="quick-link-list btn-reset"
                @click="openQuickLink(item)">
                <img class="quick-link-image" :src="item.image" alt="Quick Link" />
                <div class="quick-link-title">{{ item.name }}</div>
              </button>
            </div>
          </div>
          <div class="recent-activity-main-section font-bold">
            {{ $t('COMMON.RECENT_ACTIVITY') }}
          </div>
          <div v-if="!recentActivityList.length" class="recent-activity-container">
            <div class="recent-activity-content text-title-2 font-normal">
              {{ $t('RECENT_ACTIVITY.DESCRIPTION') }}
            </div>
          </div>
          <recent-activity
            :recentActivityList="recentActivityList"
            :deleteActivityAsync="deleteActivityAsync">
          </recent-activity>
          <div data-test-id="overview-recipe-page-heading" class="recipes-main-section font-bold">
            {{ $t('COMMON.RECIPES') }}
          </div>
          <div data-test-id="overview-recipe-count-container" class="recipe-count-section">
            <RecipesCount :project="project"></RecipesCount>
          </div>
          <div class="getting-started-main-section font-bold">
            {{ $t('OVERVIEW.GETTING_STARTED') }}
          </div>
          <div class="getting-started-main-container">
            <div class="getting-started-left-container">
              <div class="getting-started-left-section" v-for="(item, index) in overviewVideoData" :key="index">
                <div class="radio-button-container">
                  <div class="getting-started-radio-button">
                    <input type="radio" @click="showGettingStartedAsync(item)" :value="item.value"
                      :checked="item.isChecked" name="radio" :data-test-id="`getting-started-video-${index}`" />
                    <span class="checkmark"></span>
                  </div>
                </div>
                <div class="title-and-time-section">
                  <div class="getting-started-title-container">
                    <button type="button" class="getting-started-title btn-reset"
                      @click="showGettingStartedAsync(item)">
                      {{ item.name }}
                    </button>
                  </div>
                  <div class="getting-started-total-time text-light-h3">
                    {{
                    item.duration
                    ? parseDurationString(item.duration)
                    : item.duration
                    }}
                  </div>
                </div>
              </div>
            </div>
            <div class="getting-started-right-section">
              <div class="getting-started-video-container">
                <div class="getting-started-video-player" id="getting-started-video-player">
                  <video
                    v-for="(video, index) in overviewVideoData"
                    :key="index"
                    v-show="video?.isChecked"
                    :id="video?.isChecked ? 'getting-started-video-container-popup' : null"
                    type="video/mp4"
                    @canplaythrough="checkVideoLoaded"
                    @dblclick="onFullScreen"
                    :src="video.link"
                  >
                    <track
                      v-if="video.subtitles"
                      :src="video.subtitles"
                      kind="subtitles"
                      rclang="en"
                      label="English"
                    >
                    <track
                      v-if="video.description"
                      :src="video.description"
                      kind="description"
                      srclang="en"
                      label="English"
                    >
                  </video>
                  <button type="button" id="getting-started-play-pause">
                    <img alt="Play" class="getting-started-play-video-icon-image"
                      id="getting-started-play-video-icon-image" :src="videoPlaybutton" />
                  </button>
                </div>
                <div v-show="isVideoLoaded" class="getting-started-seek-container">
                  <span class="getting-started-seek">
                    <input type="range" id="getting-started-seek-bar" class="getting-started-bar" value="0" />
                  </span>
                </div>
                <div v-show="isVideoLoaded" id="getting-started-video-controls">
                  <button
                    type="button"
                    @click="onFullScreen()"
                    class="getting-started-full-screen-icon"
                  >
                    <span class="getting-started-oval">
                      <img alt="Full Screen" class="getting-started-full-screen-image" :src="fullScreenIcon" />
                    </span>
                  </button>
                  <button
                    type="button"
                    @click="onMute()"
                    id="getting-started-mute"
                  >
                    <span class="getting-started-oval">
                      <img :alt="hasVideoMuted ? 'Mute' : 'Unmute'" id="mute" :src="hasVideoMuted ? mutedImage : muteNowImage" />
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </content-wrapper>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, getCurrentInstance } from 'vue';
import { useNuxtApp } from '#app'
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useRefUtils } from '~/composables/useRefUtils';
import { useProjectLang } from "@/composables/useProjectLang";
import { useStore } from 'vuex';
import recentActivity from '@/components/recent-activity.vue';
import ContentWrapper from '@/components/content-wrapper/content-wrapper.vue';
import RecipesCount from '@/components/recipes-count';
import { useDelayTimer } from '~/composables/useDelayTimer';
import { useCommonUtils } from '~/composables/useCommonUtils';
import { useTimeUtils } from '~/composables/useTimeUtils';
import { useI18n } from 'vue-i18n';
import generateRecipeImg from '@/assets/images/generate-recipe.png';
import addNewRecipeImg from '@/assets/images/add-new.png';
import manageCategoriesImg from '@/assets/images/Categories.png';
import fullScreenIcon from '~/assets/images/full-screen-icon.png';
import videoPlaybutton from '~/assets/images/video-play-button.svg?skipsvgo=true';
import mutedImage from '~/assets/images/muted.png';
import muteNowImage from '~/assets/images/mutenow.png';
import { useAuth0 } from '@auth0/auth0-vue';
import { useConfigStore } from "../stores/config.js";

const { $tracker, $eventBus, $auth } = useNuxtApp();

const { isAuthenticated, loginWithRedirect, logout, user } = useAuth0();

const { getRef } = useRefUtils();
const { delay, batchDelay } = useDelayTimer();
const { getFormattedTimeFromTimestamp, parseDurationString } = useTimeUtils();
const { routeToPage, setOpacity } = useCommonUtils();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { t } = useI18n();
const store = useStore();
const isFullScreenDisplay = ref(false);
const isPageLoading = ref(true);
const hasVideoMuted = ref(true);
const isVideoLoaded = ref(false);
const isGeneratorEnabled = ref(false);
const { readyProject } = useProjectLang();
const video = ref("");
const playButton = ref("");
const seekBar = ref("");
const time = ref("");
const value = ref("");
const overviewVideoData = ref([]);
const quickLinkList = ref([
  {
    value: "1",
    name: t('OVERVIEW.GENERATE_RECIPE'),
    image: generateRecipeImg,
    id: "generate-recipe-button"
  },
  {
    value: "2",
    name: t('OVERVIEW.ADD_NEW_RECIPE'),
    image: addNewRecipeImg,
    id: "create-recipe-button"
  },
  {
    value: "3",
    name: t('OVERVIEW.MANAGE_CATEGORIES'),
    image: manageCategoriesImg,
    id: "view-category-button"
  },
]);
const recentActivityList = ref([]);
const timeoutId = ref(null);

const lang = computed(() => {
  return store?.getters["userData/getDefaultLang"] || null; // Fallback to null
});

const project = computed(() => {
  return store?.getters["userData/getProject"] || null; // Fallback to null
});

const configStore = useConfigStore();

const updateLangBeforeApiCall = async () => {
  await store.dispatch("userData/updateLanguageOnProjectChange");
};

onMounted(() => {
  $eventBus.on($keys.RECENT_ACTIVITIES.REFRESH_ACTIVITY, fetchRecentActivityAsync);
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      try {
        await Promise.all([
          getOverviewVideosAsync(),
          fetchRecentActivityAsync()
        ]);
        filterQuickLinks();
        isPageLoading.value = false;
        await delay(2500);
        eventVideoLoaded();
      } catch (error) {
        console.error($keys.KEY_NAMES.ERROR_IN + "readyProject", error);
      }
    }
  });

  checkEvent($keys.EVENT_KEY_NAMES.VIEW_OVERVIEW);
  document.addEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
});

onBeforeUnmount(function () {
  if (playButton.value) {
    playButton.value.removeEventListener("click", playButtonEvent);
  }
  if (seekBar.value) {
    seekBar.value.removeEventListener("mousedown", seekBarOnMouseDown);
    seekBar.value.removeEventListener("mouseup", seekBarOnMouseUp);
    seekBar.value.removeEventListener("change", seekBarListener);
  }
  if (video.value) {
    video.value.removeEventListener("timeupdate", videoTimeUpdate);
    video.value.removeEventListener("canplaythrough", checkVideoLoaded);
  }
  document.removeEventListener("fullscreenchange", fullScreenToggle);
  $eventBus.off($keys.RECENT_ACTIVITIES.REFRESH_ACTIVITY, fetchRecentActivityAsync);
  document.removeEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
  clearBatchDelay();
});

const filterQuickLinks = () => {
  if (!configStore.featuresRef[$keys.KEY_NAMES.GENERATOR]) {
    const itemName = t('OVERVIEW.GENERATE_RECIPE');
    const index = quickLinkList.value.findIndex(data => data.name === itemName);
    if (index !== -1) {
      quickLinkList.value.splice(index, 1);
    }
  }
};
const clearBatchDelay = () => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
    timeoutId.value = null;
  }
};
const handleClickOutside = (event) => {
  let currentElement = event.target;
  while (currentElement) {
    if (currentElement.classList.contains('btn-container')) {
      return;
    }
    currentElement = currentElement.parentElement;
  }
  recentActivityList.value.forEach((item) => {
    item.isDeleteFlagVisible = false;
  });
};
const deleteActivityAsync = async (uuid) => {
  if (uuid) {
    await store.dispatch("overview/deleteRecentActivityAsync", {
      uuid: uuid,
      lang: lang.value,
    });
  }
  await fetchRecentActivityAsync();
};
const getOverviewVideosAsync = async () => {
  try {
    await store.dispatch("recipe/getOverviewVideosAsync");
    const response = store.getters["recipe/selfServeVideo"];
    if (response && Array.isArray(response)) {
      overviewVideoData.value = response.map((item, index) => {
        if (item.link) {
          item.link = `https://${item.link}`;
        }
        item.isChecked = index === 0;
        return item;
      });
    }
  } catch (e) {
    console.error(e);
  }
};
const filterRecentUpdates = (response) => {
  const { results } = response;
  return results.sort((a, b) => b.lastUpdate - a.lastUpdate);
};

const fetchRecentActivityAsync = async () => {
  await updateLangBeforeApiCall();
  const currentDate = new Date();
  const oneWeekBefore = new Date(currentDate.setDate(currentDate.getDate() - 7));
  const startTime = oneWeekBefore.toISOString();
  const payload = {
    lang: lang.value,
    from: 0,
    size: 10,
    startTime: startTime,
  };

  try {
    await store.dispatch('overview/getRecentActivityAsync', payload);
    const activities = store.getters['overview/getRecentActivityResponse'];
    if (!activities?.data?.results?.length) {
      recentActivityList.value = [];
      return;
    }
    const filterRecipes = filterRecentUpdates(activities?.data);
    if (filterRecipes?.length) {
      recentActivityList.value = await mapRecentActivityListAsync(filterRecipes);
      await checkBatchGeneratedStatusAsync();
    }
    const isGeneratorEnabled = configStore.featuresRef[$keys.KEY_NAMES.GENERATOR];
    if (!isGeneratorEnabled) {
      filterRecentActivity();
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} fetchRecentActivityAsync:`, error);
  }
};
const checkBatchGeneratedStatusAsync = async () => {
  isPageLoading.value = false;
  if (recentActivityList.value?.length) {
    if (timeoutId.value) {
      clearTimeout(timeoutId.value);
    }
    await Promise.all(recentActivityList.value.map(async (item) => {
      if (item?.type === $keys.RECENT_ACTIVITIES.BATCH_GENERATED && item?.batchPromptKey) {
        const key = item?.batchPromptKey;
        await store.dispatch("batchGenerator/getBatchRecipesAsync", { lang: lang.value, key });
        const batchRecipeList = store.getters["batchGenerator/getBatchRecipeList"];
        let count = 0;

        if (batchRecipeList?.operationProgressState === $keys.BATCH_GENERATOR_KEYS.STATUS_RUNNING_LOWER ||
            batchRecipeList?.operationProgressState === $keys.BATCH_GENERATOR_KEYS.STATUS_PENDING_LOWER) {
          batchRecipeList?.taskResults?.forEach((task) => {
            if (task.progressState === $keys.BATCH_GENERATOR_KEYS.STATUS_RUNNING_LOWER ||
                task.progressState === $keys.BATCH_GENERATOR_KEYS.STATUS_PENDING_LOWER) {
              count++;
              item.progressState = task.progressState;
            }
          });
        }

        if (count) {
          item.totalCount = `${count} of ${batchRecipeList.taskResults.length}`;
          const { promise, timeoutId: newTimeoutId } = batchDelay(10000);
          timeoutId.value = newTimeoutId;
          await promise;
          await checkBatchGeneratedStatusAsync();
        } else {
          item.totalCount = batchRecipeList.taskResults.length;
          if (item.progressState) {
            delete item.progressState;
          }
        }
      }
    }));
  }
};
const mapRecentActivityListAsync = async (results) => {
  const mappedActivities = [];
  results?.forEach((activity) => {
    const commonData = {
      value: getValueByType(activity?.type),
      time: getFormattedTimeFromTimestamp(activity?.createTime, t),
      uuid: activity?.uuid,
      type: activity?.type,
      isDeleteFlagVisible: false,
    };

    switch (activity.type) {
      case $keys.RECENT_ACTIVITIES.BATCH_GENERATED:
        mappedActivities.push({
          ...commonData,
          totalCount: activity?.data?.totalbatchRecipe || 0,
          recipe: $keys.RECENT_ACTIVITIES.BATCH_GENERATED_RECIPES,
          batchPromptKey: activity?.data?.batchPromptKey || ''
        });
        break;
      case $keys.RECENT_ACTIVITIES.GENERATED_RECIPES:
        mappedActivities.push({
          ...commonData,
          recipeImage: activity?.data?.recipeImage,
          recipe: activity?.data?.recipeName,
          recipeIsin: activity?.data?.recipeIsin,
          scope: activity?.scope
        });
        break;
      case $keys.RECENT_ACTIVITIES.ADD_RECIPES:
        mappedActivities.push({
          ...commonData,
          recipeImage: activity?.data?.recipeImage,
          recipe: activity?.data?.recipeName,
          recipeIsin: activity?.data?.recipeIsin
        });
        break;
      case $keys.RECENT_ACTIVITIES.EXPORT_RECIPES:
        mappedActivities.push({
          ...commonData,
          totalCount: activity?.data?.totalExportRecipe,
          recipe: $keys.RECENT_ACTIVITIES.RECIPES_EXPORTED,
        });
        break;

      default:
        break;
    }
  });
  return mappedActivities;
};
const getValueByType = (type) => {
  switch (type) {
    case $keys.RECENT_ACTIVITIES.BATCH_GENERATED:
      return $keys.RECENT_ACTIVITIES.BATCH_GENERATOR_CARD_POSITION;
    case $keys.RECENT_ACTIVITIES.GENERATED_RECIPES:
      return $keys.RECENT_ACTIVITIES.GENERATED_RECIPES_CARD_POSITION;
    case $keys.RECENT_ACTIVITIES.ADD_RECIPES:
      return $keys.RECENT_ACTIVITIES.ADD_RECIPE_CARD_POSITION;
    case $keys.RECENT_ACTIVITIES.EXPORT_RECIPES:
      return $keys.RECENT_ACTIVITIES.EXPORT_RECIPES_CARD_POSTITION;
    default:
      return "";
  }
};
const filterRecentActivity = () => {
  recentActivityList.value = recentActivityList.value.filter(item => {
    return !(item.type === $keys.RECENT_ACTIVITIES.GENERATED_RECIPES ||
             item.type === $keys.RECENT_ACTIVITIES.BATCH_GENERATED);
  });
};
const eventVideoLoaded = () => {
  video.value = getRef("getting-started-video-container-popup");
  playButton.value = getRef("getting-started-video-player");
  seekBar.value = getRef("getting-started-seek-bar");
  const playIcon = getRef("getting-started-play-pause");
  if (video.value) {
    initializeVideo();
  }
  if (playButton.value) {
    playButton.value.addEventListener("click", playButtonEvent);
  }
  if (seekBar.value) {
    initializeSeekBar();
  }
  if (playIcon) {
    playIcon.style.opacity = "1";
  }
  document.addEventListener("fullscreenchange", fullScreenToggle);
  document.addEventListener("click", handleClickOutside);
};
const initializeVideo = () => {
  if (video.value) {
    video.value.pause();
    video.value.addEventListener("canplaythrough", checkVideoLoaded);
    video.value.addEventListener("timeupdate", videoTimeUpdate);
    video.value.currentTime = 0;
    video.value.muted = true;
  }
};
const initializeSeekBar = () => {
  if (seekBar.value) {
    seekBar.value.addEventListener("change", seekBarListener);
    seekBar.value.addEventListener("click", seekBarClickListener);
    seekBar.value.addEventListener("mousedown", seekBarOnMouseDown);
    seekBar.value.addEventListener("mouseup", seekBarOnMouseUp);
    seekBar.value.value = 0;
  }
};
const seekBarClickListener = (event) => {
  if (video.value) {
    video.value.pause();
    setOpacity("getting-started-play-pause", "1");
    const offsetX = event.offsetX;
    const seekBarWidth = seekBar.value.offsetWidth;
    if (seekBarWidth === 0) return;
    const newTime = video.value.duration * (offsetX / seekBarWidth);
    if (Number.isFinite(newTime)) {
      video.value.currentTime = newTime;
    }
    setOpacity("getting-started-play-pause", "0");
    video.value.play();
  }
};
const checkVideoLoaded = () => {
  isVideoLoaded.value = true;
};
const onMute = () => {
  hasVideoMuted.value = !hasVideoMuted.value;
  if (video.value) {
    video.value.muted = hasVideoMuted.value;
  }
};
const seekBarOnMouseUp = () => {
  video.value.play();
};
const seekBarOnMouseDown = () => {
  video.value.pause();
};
const videoTimeUpdate = () => {
  value.value = (100 / video.value?.duration) * video.value?.currentTime;
  if (seekBar.value) {
    seekBar.value.value = value.value;
  }
};
const seekBarListener = () => {
  time.value = video.value?.duration * (seekBar.value?.value / 100);
  if (video.value) {
    video.value.currentTime = time.value;
  }
  setOpacity("getting-started-play-pause", "0");
};
const fullScreenToggle = () => {
  isFullScreenDisplay.value = !!document.fullscreenElement;
  if (!isFullScreenDisplay.value) {
    const videoElement = getRef("getting-started-video-container-popup");
    videoElement.pause();
    setOpacity("getting-started-play-pause", "1");
  }
};
const playButtonEvent = () => {
  if (!isFullScreenDisplay.value) {
    if (video.value?.paused) {
      video.value.play();
      setOpacity("getting-started-play-pause", "0");
    } else {
      video.value.pause();
      setOpacity("getting-started-play-pause", "1");
    }
  }
};
const onFullScreen = () => {
  const mainVideo = getRef("getting-started-video-container-popup");
  if (mainVideo) {
    requestFullscreen(mainVideo);
  }
};
const requestFullscreen = (element) => {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  }
};
const showGettingStartedAsync = async (selectedData) => {
  isVideoLoaded.value = false;
  setOpacity("getting-started-play-pause", "1");
  checkEvent($keys.EVENT_KEY_NAMES.CLICK_GETTING_STARTED, { videoName: selectedData?.name ?? "" });

  overviewVideoData.value.forEach((item) => {
    item.isChecked = selectedData.uuid === item.uuid;
  });

  await delay(500);
  eventVideoLoaded();
};
const openQuickLink = (link) => {
  if (link.value === "1") {
    checkEvent($keys.EVENT_KEY_NAMES.CLICK_QUICK_LINK, { linkName: t('OVERVIEW.GENERATE_RECIPE') });
    routeToPage("iq-recipe-generator");
  } else if (link.value === "2") {
    checkEvent($keys.EVENT_KEY_NAMES.CLICK_QUICK_LINK, { linkName: t('OVERVIEW.ADD_NEW_RECIPE') });
    routeToPage('recipe-detail', 'OverviewPage');
  } else if (link.value === "3") {
    checkEvent($keys.EVENT_KEY_NAMES.CLICK_QUICK_LINK, { linkName: t('OVERVIEW.MANAGE_CATEGORIES') });
    routeToPage("categories");
  }
};
const checkEvent = (description, params = {}) => {
  const eventProperties = {};
  if (params.linkName) {
    eventProperties[t('EVENT_NAMES.LINK_NAME')] = params.linkName;
  }
  if (params.videoName) {
    eventProperties[t('EVENT_NAMES.VIDEO_NAME')] = params.videoName;
  }
  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};
</script>
