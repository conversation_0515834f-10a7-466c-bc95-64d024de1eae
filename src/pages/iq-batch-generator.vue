<template>
  <client-only>
    <content-wrapper :is-body-loading="isLoading" wrapper-classes="iq-b-g">
      <template v-slot:title>
        <span data-test-id="batch-generator-heading">{{
          $t("BATCH_GENERATOR.BATCH_GENERATOR_TEXT")
        }}</span>
      </template>
      <batch-generator-header
        :isNewBatch="isNewBatch"
        :promptCount="tableData.length"
        @createNewBatch="createNewBatch"
        @add-prompt="addPrompt"
      />
      <batch-prompt-data-table
        :isNewBatch="isNewBatch"
        :tableData="tableData"
        @generateBatch="generateBatch"
        @createNewBatch="createNewBatch"
      />
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import BatchGeneratorHeader from "@/components/batch-generator/batch-generator-header.vue";
import batchPromptDataTable from "@/components/batch-generator/batch-prompt-data-table.vue";
import { useProjectLang } from "@/composables/useProjectLang";
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useDelayTimer } from '~/composables/useDelayTimer';

const store = useStore();
const lang = ref("");
const tableData = ref([]);
const isNewBatch = ref(false);
const finalPromptString = ref([]);
const isLoading = ref(true);
const timeoutCountId = ref(null);
const { readyProject } = useProjectLang();
const { sortAscendingAlphabetically } = useCommonUtils();
const { batchDelay } = useDelayTimer();

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

const batchRecipeData = computed(
  () => store.getters["batchGenerator/getBatchRecipeList"]
);

// Lifecycle hooks
onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      lang.value = store.getters["userData/getDefaultLang"];
      getBatchPromptsListAsync();
    }
  });
});

onBeforeUnmount(() => {
  cancelBatchDelay();
});

// Method to cancel batch delay
const cancelBatchDelay = () => {
  if (timeoutCountId.value) {
    clearTimeout(timeoutCountId.value);
    timeoutCountId.value = null;
  }
};

const setHiddenBatch = () => {
  const lastBatchGeneratedData = store.getters["batchGenerator/getBatchPromptsList"];
  if (lastBatchGeneratedData.length) {
    const payload = {
      "type": lastBatchGeneratedData?.[0]?.operationType,
      "itemId": lastBatchGeneratedData?.[0]?.key
    };
    store.dispatch("batchGenerator/postHiddenItemsAsync", { payload });
  }
};

const clearHiddenBatch = async () => {
  const lastBatchGeneratedData = store.getters["batchGenerator/getBatchPromptsList"];
  const payload = {
    "type": $keys.RECENT_ACTIVITIES.BATCH_TYPE,
    "itemId": lastBatchGeneratedData?.[0]?.key
  };
  await store.dispatch("batchGenerator/getHiddenItemsAsync", { payload });
  const hiddenBatchData = store.getters["batchGenerator/getHiddenBatchList"];
  if (lastBatchGeneratedData.length) {
    const uuid = hiddenBatchData?.data?.uuid;
    store.dispatch("batchGenerator/deleteHiddenItemsAsync", { uuid });
  }
};

// Method to add a new prompt to the table
const addPrompt = (newPrompt) => {
  if (tableData.value?.length < 10) {
    tableData.value.push({
      prompt: newPrompt,
      progressState: "Added",
      recipeTitle: "",
      isDeleteVisible: true,
      hasDeleteClicked: false,
    });
    tableData.value = sortAscendingAlphabetically(
      tableData.value,
      $keys.BATCH_GENERATOR_KEYS.PROMPT
    );
  }
};

// Method to create a new batch
const createNewBatch = () => {
  tableData.value = [];
  isNewBatch.value = true;
  setHiddenBatch();
};

// Async method to get batch prompts list
const getBatchPromptsListAsync = async () => {
  try {
    const payload = {
      lang: lang.value,
      from: 0,
      size: 1,
      startTime: "",
      endTime: "",
      state: "",
    };

    const response = await store.dispatch("batchGenerator/getBatchPromptsAsync", payload);
    if (response?.length) {
      const key = response[0]?.key;
      if (key) {
        store.dispatch("batchGenerator/setBatchGenerationInProgressAsync", true);

        const newPayload = {
          "type": $keys.RECENT_ACTIVITIES.BATCH_TYPE,
          "itemId": key
        };
        await store.dispatch("batchGenerator/getHiddenItemsAsync",  { payload: newPayload } );

        const hiddenBatchData = store.getters["batchGenerator/getHiddenBatchList"];

        if (hiddenBatchData.data?.itemId === key) {
          store.dispatch("batchGenerator/setBatchGenerationInProgressAsync", false);
          isLoading.value = false;
          createNewBatch();
        } else {
          getBatchRecipesAsync(key);
        }
      }
    } else {
      isNewBatch.value = true;
      isLoading.value = false;
      store.dispatch("batchGenerator/setBatchGenerationInProgressAsync", false);
    }
  } catch (error) {
    console.error($keys.BATCH_GENERATOR_KEYS.ERROR_FETCHING_BATCH_PROMPTS, error);
  }
};

// Method to generate a new batch
const generateBatch = () => {
  const promptList = tableData.value.map((item) => item.prompt);
  finalPromptString.value = promptList;
  clearHiddenBatch();
  postBatchPromptsAsync();
};

// Method to post recent batch activity
const postRecipeBatchActivityAsync = async (totalCount) => {
  const payload = {
    type: $keys.RECENT_ACTIVITIES.BATCH_GENERATED,
    data: {
      totalbatchRecipe: totalCount,
      batchPromptKey: store.getters["batchGenerator/getBatchPromptKey"],
    },
  };
  try {
    await store.dispatch("recipe/postRecentActivityAsync", {
      lang: lang.value,
      payload,
    });
  } catch (error) {
    console.error("Error posting batch activity:", error);
  }
};

// Async method to post batch prompts
const postBatchPromptsAsync = async () => {
  const payload = {
    prompts: finalPromptString.value,
  };
  const params = {
    lang: lang.value,
  };
  try {
    await store.dispatch("batchGenerator/postBatchPromptsAsync", {
      params,
      payload,
    });
    const promptGeneratedKey =
      store.getters["batchGenerator/getBatchPromptKey"];
    if (promptGeneratedKey) {
      store.dispatch("batchGenerator/setBatchGenerationInProgressAsync", true);
      const promptList = tableData.value.map((item) => item.prompt);
      const totalCount = promptList?.length;
      postRecipeBatchActivityAsync(totalCount);
      getBatchRecipesAsync(promptGeneratedKey);
    }
  } catch (error) {
    console.error("Error posting batch prompts:", error);
  }
};

// Async method to fetch batch recipes
const getBatchRecipesAsync = async (key) => {
  await store.dispatch("batchGenerator/getBatchRecipesAsync", {
    lang: lang.value,
    key,
  });

  const results = batchRecipeData.value.taskResults?.filter((result) => {
    return result.progressState === $keys.KEY_NAMES.DONE
      ? result.recipeExists ||
          result.progressState ===
            $keys.BATCH_GENERATOR_KEYS.STATUS_FAILED_LOWER
      : true;
  });

  tableData.value = sortAscendingAlphabetically(
    results,
    $keys.BATCH_GENERATOR_KEYS.PROMPT
  );
  isLoading.value = false;

  if (
    [
      $keys.BATCH_GENERATOR_KEYS.STATUS_PENDING_LOWER,
      $keys.BATCH_GENERATOR_KEYS.STATUS_RUNNING_LOWER,
    ].includes(batchRecipeData.value.operationProgressState)
  ) {
    const { promise, timeoutId } = batchDelay(10000);
    timeoutCountId.value = timeoutId;
    await promise;
    getBatchRecipesAsync(key);
  } else {
    isNewBatch.value = !tableData.value.length;
    store.dispatch("batchGenerator/setBatchGenerationInProgressAsync", false);
  }
};
</script>