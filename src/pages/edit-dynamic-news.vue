<template>
  <content-wrapper wrapper-classes="padding-zero" :is-body-loading="isPageLoading">
    <div v-if="!isPageLoading" class="main-content">
      <div class="dynamic-hero-form-edit-id">ID: {{ newsUuid }}</div>
      <dynamicHeroNewsHeader
        :image-src="image"
        :replace-button-text="$t('REPLACE')"
        :isLiveHero="isLiveHero"
        :is-replace-live-hero="isLiveHeroReplaced"
        :is-campaign-modified="isCampaignModified"
        :news-name="newsName"
        :news-text="newsText"
        :isCTALinkIsValid="isCTALinkValid"
        :newsCTALinkText="newsCTALinkText"
        :upload-image-percentage="uploadImagePercentage"
        :is-page-overview-visible="isPageOverviewVisible"
        @saveAction="saveNewsForm"
        @replaceAction="replaceNewsForm"
        @backToDynamicHeroList="backToDynamicHeroList"
      />
      <div class="news-intro-section">
        <dynamicHeroNewsIntro
          :title="$t('DYNAMIC_HERO.NEWS_FORM')"
          :startDateLabel="$t('DYNAMIC_HERO.START_DATE')"
          :isLiveHeroReplaced="isLiveHeroReplaced"
          :isLiveHero="isLiveHero"
          v-model:selectedDate="selectedDate"
          :disabledDates="disabledDates"
          :isRange="false"
          :markers="markers"
          @date-click="handleDateClick"
          @day-hover="call()"
        />
        <div class="image-input-container">
          <processImage
            :image="image"
            :uploadImagePercentage="uploadImagePercentage"
            :loadedImageSize="loadedImageSize"
            :uploadImageSize="uploadImageSize"
            :checkUploadedFiles="checkUploadedFiles"
            :uploadSameImageVideo="uploadSameImageVideo"
          />
          <dynamicHeroNewsForm
            v-model:newsName="newsName"
            :selectedDate="selectedDate"
            v-model:isNewsStatus="isNewsStatusDisplayed"
            :isReplaceLiveHero="isLiveHeroReplaced"
            @scheduleToggle="scheduleToggle"
            :isLiveHero="isLiveHero"
            :hasNewsNameFocus="isNewsNameInFocus"
            :deleteEvent="deleteEvent"
            :isEditPage="true"
            :hideNewsTip="hideNewsTip"
            :checkNewsName="checkNewsName"
          />
        </div>
      </div>
    </div>
    <dynamicHeroNewsContent
      :isPageLoading="isPageLoading"
      :isLiveHero="isLiveHero"
      :showPreviewToggle="false"
      v-model:newsText="newsText"
      v-model:newsCTAInput="newsCTAInput"
      v-model:newsCTALinkText="newsCTALinkText"
      :isHeroPreview="isHeroPreview"
      :isCTALinkValid="isCTALinkValid"
      :isCTALinkBroken="isCTALinkBroken"
      :isCTALinkInProgress="isCTALinkInProgress"
      @update:isHeroPreview="[(isHeroPreview = $event), (isCampaignModified = true)]"
      @validate-cta-link="validateCTALinkInprogress"
      @check-news-text="checkNewsText"
      @check-news-cta="checkNewsCTA"
    />
    <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && !$nuxt.isOffline"
      :closeModal="closeModal"
      :acceptedFile="' jpg/ jpeg/ png'"
      :video="false"
      :image="true"
      :zip="false"
    />
    <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
    />
    <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
    />
    <deleteModal
      v-if="isTheNewsDeleted"
      :closeModal="closeModal"
      :productInfoTitle="'Delete the News Form?'"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
      :productDescriptionTwo="'News Form?'"
      :deleteItem="deleteDynamicHeroAsync"
      :availableLanguage="0"
    />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduledDateModalVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="closeModal"
      @schedule="closeModal"
      :markers="markers"
      :PatchScheduledHero="patchDynamicHeroAsync"
      :selectedDateValue="scheduleDateConfirm"
      @date-click="handleDateClickPopup"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="closeModal"
      :closeReplacement="patchDynamicHeroAsync"
    />
    <saveModal
      v-if="isSaveDynamicContent"
      :closeModal="closeModal"
      :saveAndPublishFunction="saveDynamicContent"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="'Do you want to save as draft your News form?'"
      :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
    />
    <deletingModal v-show="isDeletingModalVisible" />
    <savingModal v-show="isNewsSaving" :status="'saving'" />
    <updatingLiveHero
      v-if="isLiveHeroSaveDynamicHero"
      :closeModal="closeModal"
      :callConfirm="patchDynamicHeroAsync"
      :updatingText="'Please, confirm the updating live Hero'"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, onMounted, watch, getCurrentInstance } from "vue";
import { useConnectionStatus } from "~/composables/useConnectionStatus";
import dynamicHeroNewsHeader from "../components/pages/dynamic-hero/dynamic-hero-news-header.vue";
import dynamicHeroNewsIntro from "../components/pages/dynamic-hero/dynamic-hero-news-intro.vue";
import dynamicHeroNewsForm from "../components/pages/dynamic-hero/dynamic-hero-news-form.vue";
import dynamicHeroNewsContent from "../components/pages/dynamic-hero/dynamic-hero-news-content.vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import replacementModal from "@/components/confirm-replacement-modal";
import deleteModal from "@/components/delete-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import sizeLimit from "@/components/size-limit.vue";
import updatingLiveHero from "@/components/updating-live";
import deletingModal from "@/components/deleting-modal";
import saveModal from "@/components/save-modal";
import savingModal from "@/components/saving-modal";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useCommonUtils } from "@/composables/useCommonUtils";
import processImage from "../components/pages/dynamic-hero/process-image.vue";
import cancelModal from "@/components/cancel-modal";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import { useProjectLang } from "@/composables/useProjectLang";
import { useRefUtils } from "@/composables/useRefUtils";
import axios from "axios";

const store = useStore();
const route = useRoute();
const router = useRouter();
const { readyProject } = useProjectLang();
const {
  getHeroUUIDAsync,
  dynamicHeroUUIDData,
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  deleteDynamicHeroDataAsync,
  patchDynamicHeroDataAsync,
} = useDynamicHeroStore();
const { watchReactiveValue } = useWatcherUtils();
const { isInnitAdmin } = useInnitAuthStore();
const heroData = ref([
  {template: 'News'}
]);
const { getRef } = useRefUtils();
const { isOffline } = useConnectionStatus();
const { triggerLoading, routeToPage, useCalendarMarkers, checkDuplicate, processScheduledElement, isScheduledWithPublishDate, getDisabledDates, getDisableList } = useCommonUtils();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const disableList = ref([]);
const heroID = ref('');
const newsState = ref('');
const imageFile = ref([]);
const isNewsNameInFocus = ref(false);
const cancelImage = ref({});
const isHeroPreview = ref(false);
const isPageLoading = ref(false);
const isNewsSaving = ref(false);
const imageResponseUrl = ref('');
const scheduleDateConfirm = ref('');
const isUploadingImagePopup = ref(false);
const uploadImageConfirm = ref('');
const isMaxImagePopupVisible = ref(false);
const isInvalidImageModalVisible = ref(false);
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref('');
const selectedDate = ref('');
const isScheduledDateModalVisible = ref(false);
const isSaveDynamicContent = ref(false);
const isDeletingModalVisible = ref(false);
const isTheNewsDeleted = ref(false);
const isConfirmModalVisible = ref(false);
const isCampaignModified = ref(false);
const newsCTAInput = ref('');
const newsCTALinkText = ref('');
const isCTALinkInProgress = ref(false);
const isCTALinkValid = ref(false);
const isCTALinkBroken = ref(false);
const newsText = ref('');
const isNewsStatusDisplayed = ref(false);
const newsName = ref('');
const isPageOverviewVisible = ref(false);
const newsUuid = ref('');
const isLiveHero = ref(false);
const isLiveHeroSaveDynamicHero = ref(false);
const isLiveHeroReplaced = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const todos = ref([]);
const disabledDates = ref([]);
const lang = ref('');
const isAdminCheck = ref(false);
const imageFileName = ref('');

onMounted(() => {
  readyProject(({ isProjectReady }) => {
    if (isProjectReady) {
      initializeDataAsync();
      addEventListeners();
    }
  });
});
const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  lang.value = store.getters["userData/getDefaultLang"];
  await getDynamicHeroDataAsync();
  await getEditDynamicHeroDataAsync();
  disabledDates.value = await getDisabledDates().value;
  disableList.value = await getDisableList().value;
  if (disabledDates.value.length) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString()),
    };
    todos.value.push(newTodo);
  }

  let sourceUrl = window.location.href;
  if (sourceUrl.includes("overviewNews")) {
    isPageOverviewVisible.value = true;
  }
  if (sourceUrl.includes("replace-live-hero")) {
    isLiveHeroReplaced.value = true;
  }
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};
const checkNewsName = () => {
  let name = getRef("newsNameField");
  if (
    name?.scrollWidth > name?.clientWidth &&
    name !== document.activeElement &&
    newsName.value.trim().length
  ) {
    isNewsNameInFocus.value = true;
  }
};
const hideNewsTip = () => {
  isNewsNameInFocus.value = false;
};

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;
    if (response && Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error(
      `${$keys.KEY_NAMES.ERROR_IN} getDynamicHeroDataAsync:`,
      error
    );
  }
};
const checkNewsText = () => {
  if (newsText.value.length === 1 && newsText.value[0] === " ") {
    newsText.value = "";
  } else {
    newsText.value = newsText.value.replace(/\s+/g, " ");
  }
};

const checkNewsCTA = () => {
  if (newsCTAInput.value.length === 1 && newsCTAInput.value[0] === " ") {
    newsCTAInput.value = "";
  } else {
    newsCTAInput.value = newsCTAInput.value.replace(/\s+/g, " ");
  }
};
const handleDateClickPopup = (newValue) => {
  selectedDate.value = newValue;
};
const handleDateClick = () => {
    isNewsStatusDisplayed.value = true;
    isCampaignModified.value = true;
};
const getUUIDAsync = async () => {
  try {
    await getHeroUUIDAsync({ lang: lang.value });
    const response = await dynamicHeroUUIDData.value;
    heroID.value = response?.uuid;
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getUUIDAsync:", error);
  }
};

const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  imageFile.value = event.target.files || event.srcElement.files;
  const fileType = imageFile.value[0].type.split("/")[0];
  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};
const uploadFiles = () => {
  isCampaignModified.value = true;

  if (imageFile.value.length) {
    imageFileName.value = imageFile.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!imageFileName.value.match(reg)) {
      imageFile.value = [];
      imageFileName.value = '';
      isInvalidImageModalVisible.value = true;
      return;
    }
    const fileSize = imageFile.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));
    if (size > 1 * 1024 * 1024 && size < 15 * 1024 * 1024) {
      let element = getRef('productVideo');
      element?.blur();
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = imageFile.value;
      imageFile.value = [];
      return;
    } else if (size >= 15 * 1024 * 1024) {
      let element = getRef('productVideo');
      element?.blur();
      imageFile.value = [];
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener(
        'load',
        async () => {
          image.value = reader.result;
          if (image.value) {
            loadedImageSize.value = 0;
            uploadImagePercentage.value = 1;
            await uploadImageAsync();
          }
        },
        false
      );
      if (imageFile.value[0]) {
        reader.readAsDataURL(imageFile.value[0]);
      }
    }
  }
};
const continueImage = () => {
  imageFile.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.addEventListener(
    'load',
    async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
      }
    },
    false
  );
  if (imageFile.value[0]) {
    reader.readAsDataURL(imageFile.value[0]);
  }
};
const uploadImageAsync = async () => {
  if (imageFile.value && image.value) {
    if (!heroID.value) {
      await getUUIDAsync();
    }
    const reader = new FileReader();
    reader.addEventListener(
      'load',
      async () => {
        const extension = imageFile.value[0].type.split('/')[1];
        const params = {
          entity: 'article',
          content: 'image',
          extension: extension,
          lang: lang.value,
          public: true,
        };
        await store.dispatch('preSignedUrl/getPreSignedImageUrlAsync', {
          isin: heroID.value,
          params,
        });
        const response = store.getters['preSignedUrl/getPreSignedUrl'];
        imageResponseUrl.value = response?.data?.url || "";
        await uploadImageFile(imageResponseUrl.value, imageFile.value[0]);
      },
      false
    );
    if (imageFile.value[0]) {
      reader.readAsDataURL(imageFile.value[0]);
    }
  }
};
const uploadImageFile = (url, file) => {
  cancelImage.value = axios.CancelToken.source();
  axios
    .put(url, file, {
      headers: {
        'Content-Type': file.type,
        'x-amz-acl': 'public-read',
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunction(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .then(() => {})
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error('Image request canceled.');
      } else {
        console.error(e);
      }
    });
};
const uploadedImageFunction = (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    setTimeout(() => {
      uploadImagePercentage.value = 100;
    }, 2000);
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = '';
};
const saveDynamicContent = () => {
  patchDynamicHeroAsync();
};
const saveNewsForm = () => {
  if (isLiveHero.value) {
    isLiveHeroSaveDynamicHero.value = true;
  } else if (selectedDate.value !== "" && isNewsStatusDisplayed.value) {
    isScheduledDateModalVisible.value = true;
    scheduleDateConfirm.value = selectedDate.value;
    isSaveDynamicContent.value = false;
  } else {
    isSaveDynamicContent.value = true;
    isScheduledDateModalVisible.value = false;
  }
};
const getTime = (jsonTimestamp) => {
  const timestamp = jsonTimestamp * 1000;
  const date = new Date(timestamp);
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};
const replaceNewsForm = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isNewsStatusDisplayed.value = newStatus;
};
const patchDynamicHeroAsync = async () => {
  try {
    initializePatchProcess();

    const scheduleDate = calculateScheduleDate();

    let payload = createPayload(scheduleDate);

    cleanupPayload(payload);

    await dispatchPatchDynamicHeroDataAsync(payload);

    handlePostPatch();

    router.push({ name: "dynamic-hero" });
  } catch (error) {
    console.error("Error in patchDynamicHeroAsync:", error);
  }
};
const initializePatchProcess = () => {
  isScheduledDateModalVisible.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  isSaveDynamicContent.value = false;
  isNewsSaving.value = true;
};
const calculateScheduleDate = () => {
  let scheduleDate;
  if (isLiveHeroReplaced.value || isLiveHero.value) {
    isNewsStatusDisplayed.value = true;
    selectedDate.value = new Date();
    selectedDate.value.setHours(0, 0, 0, 0);
    scheduleDate = Math.floor(selectedDate.value.getTime() / 1000);
  } else {
    const date = new Date(selectedDate.value);
    scheduleDate = Math.floor(date.getTime() / 1000);
  }
  return scheduleDate;
};
const createPayload = (scheduleDate) => ({
  uuid: route.query.uuid,
  title: newsName.value?.trim() ?? "",
  template: "news",
  publishDate: selectedDate.value && isNewsStatusDisplayed.value ? scheduleDate : "0",
  image: imageResponseUrl.value?.replace(/\?.*/, "") ?? "",
  data: {
    body: newsText.value ?? "",
    image: imageResponseUrl.value?.replace(/\?.*/, "") ?? "",
    ctaText: newsCTAInput.value ?? "",
    ctaLink: newsCTALinkText.value ?? "",
  },
  state: selectedDate.value && isNewsStatusDisplayed.value ? "scheduled" : "draft",
  preview: isHeroPreview.value ?? false,
});
const cleanupPayload = (payload) => {
  if (!newsCTAInput.value) {
    delete payload.data.ctaText;
  }
  if (!newsCTALinkText.value.trim()) {
    delete payload.data.ctaLink;
  }
  if (!selectedDate.value || !isNewsStatusDisplayed.value) {
    delete payload.publishDate;
  }
};
const dispatchPatchDynamicHeroDataAsync = async (payload) => {
  await patchDynamicHeroDataAsync({
      payload,
      uuid: route.query[QUERY_PARAM_KEY.UUID],
    });
};
const handlePostPatch = () => {
  isNewsSaving.value = false;
  if ((!isNewsStatusDisplayed.value || !selectedDate.value) && !isLiveHero.value && !isLiveHeroReplaced.value) {
    triggerLoading("newsSaved");
  } else if (
    selectedDate.value &&
    !isLiveHero.value &&
    isNewsStatusDisplayed.value &&
    !isLiveHeroReplaced.value
  ) {
    triggerLoading("newsScheduled");
  } else if (isLiveHero.value && !isLiveHeroReplaced.value) {
    triggerLoading("contentLive");
  } else if (isLiveHeroReplaced.value) {
    triggerLoading("heroReplaced");
  }
};
const getEditDynamicHeroDataAsync = async () => {
  const uuid = route.query.uuid;
  if (!uuid) return;

  try {
    startPageLoading();
    await fetchDynamicHeroDataAsync();
    const response = await editDynamicHeroDataList.value;

    if (Object.keys(response).length) {
      processHeroData(response);
      processHeroDataDetails(response.data ?? {});
    }
  } catch (error) {
    console.error("Error in getEditDynamicHeroDataAsync:", error);
  } finally {
    endPageLoading();
  }
};
const startPageLoading = () => {
  isPageLoading.value = true;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
};
const endPageLoading = () => {
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
};
const fetchDynamicHeroDataAsync = async () => {
  await getEditPageDynamicHeroDataAsync({
    lang: lang.value,
    uuid: route.query[QUERY_PARAM_KEY.UUID],
  });
};
const processHeroData = (response) => {
  isLiveHero.value = response?.state === "live";
  isHeroPreview.value = response?.preview || false;
  newsState.value = response?.state || "";
  newsUuid.value = response?.uuid ?? "";
  newsName.value = response?.title || "";
  selectedDate.value = response?.publishDate
    ? getTime(response.publishDate)
    : "";
  isNewsStatusDisplayed.value = !!response?.publishDate;

  if (newsState.value === "draft" && selectedDate.value) {
    isNewsStatusDisplayed.value = false;
    selectedDate.value = "";
  }
};
const processHeroDataDetails = (data) => {
  newsText.value = data?.body || "";
  newsCTAInput.value = data?.ctaText || "";
  image.value = data?.image || "";
  imageResponseUrl.value = data?.image || "";
  newsCTALinkText.value = data?.ctaLink || "";

  if (newsCTALinkText.value.trim()) {
    validateCTALinkInprogress();
  }
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};
const deleteEvent = () => {
  isTheNewsDeleted.value = true;
};

const deleteDynamicHeroAsync = async () => {
  try {
    isDeletingModalVisible.value = true;
    await deleteDynamicHeroDataAsync({ uuid: route.query[QUERY_PARAM_KEY.UUID] });
    routeToPage("dynamic-hero");
    triggerLoading("deletedSuccess");
    closeModal();
  } catch (error) {
    closeModal();
    console.error($keys.KEY_NAMES.ERROR_IN + "deleteDynamicHeroAsync:", error);
  }
};
const validateCTALinkInprogress = () => {
  if (!newsCTALinkText.value.trim()) {
    isCTALinkInProgress.value = false;
    isCTALinkValid.value = false;
    isCTALinkBroken.value = false;
  } else {
    isCTALinkInProgress.value = true;
    checkCTALink();
  }
};
const checkCTALink = () => {
  const urlPattern = /^(https?|ftp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const rocheAppPattern = /^(rocheapp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const linkInput = newsCTALinkText.value.trim();

  if (
    urlPattern.test(linkInput) ||
    (rocheAppPattern.test(linkInput) && linkInput !== "")
  ) {
    isCTALinkValid.value = true;
  } else {
    isCTALinkBroken.value = true;
    isCTALinkValid.value = false;
  }
  setTimeout(() => {
    isCTALinkInProgress.value = false;
  }, 1000);
};
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);

  if (isPageOverviewVisible.value) {
    routeToPage("overview");
  } else {
    routeToPage("dynamic-hero");
  }
};
const closeModal = () => {
  scheduleDateConfirm.value = selectedDate.value;
  if (!isLiveHeroReplaced.value) {
    isLiveHeroReplaced.value = false;
  }
  isReplacementConfirmPopupVisible.value = false;
  isTheNewsDeleted.value = false;
  isInvalidImageModalVisible.value = false;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  isScheduledDateModalVisible.value = false;
  isSaveDynamicContent.value = false;
  isConfirmModalVisible.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  isDeletingModalVisible.value = false;
};
const handleTypeInput = (event) => {
  if (getRef("newsNameField")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("newsTextField")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("newsCTAField")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("newsCTALinkField")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
};
watch(isOffline, (offlineStatus) => {
  if (offlineStatus) {
    closeModal();
  }
});
onUnmounted(() => {
  document.removeEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
</script>
