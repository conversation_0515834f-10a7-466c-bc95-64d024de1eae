<template>
  <client-only>
    <content-wrapper class="padding-zero" :is-body-loading="isPageLoading">
      <div class="collection-master-list-main-section">
        <div class="text-h1" :class="isPageLoading ? 'loader' : ''" data-test-id="featured-heading">
          {{ $t('COLLECTION.COLLECTION_OF_FEATURED_RECIPES') }}
        </div>
        <div v-if="!isPageLoading">
          <div v-if="!collectionListData?.length" class="collection-master-list-detail-main">
            <div class="collection-details-count-container">
              <div class="collection-star-image">
                <img src="@/assets/images/collection-star.png" alt="star" />
              </div>
              <div class="collection-count-main-section text-title-1">
                <div class="collection-count-text text-title-1 font-normal">
                  <span class="count font-bold">{{ collectionCount }}</span>
                  {{ $t('COMMON.COLLECTIONS') }}
                </div>
                <div class="add-collection-detail">
                  {{ $t('COLLECTION.CLICK_ON_ADD_COLLECTION') }}
                </div>
              </div>
            </div>
            <div class="add-button-section">
              <button
                type="button"
                class="btn-green"
                @click="addCollections()"
              >
                {{ $t('COLLECTION.ADD_COLLECTION') }}
              </button>
            </div>
          </div>
          <div v-if="collectionListData?.length" class="collection-section">
            <div class="collection-count-recipe-main">
              <div class="collection-count-section">
                <div class="collection-star-image">
                  <img src="@/assets/images/collection-star.png" alt="star" />
                </div>
                <div v-if="collectionListData?.length" class="collection-count-main-section">
                  <div class="collection-count-text text-title-1 font-normal">
                    <span class="count font-bold">{{ collectionCount }}</span>
                    {{ $t('COMMON.COLLECTIONS') }}
                  </div>
                </div>
              </div>
              <div class="collection-active-recipe">
                <div class="collection-active-recipe-count">
                  <span class="count">{{ activeFeaturedRecipes }}</span>
                  <p class="active-recipe-text">{{ $t('COLLECTION.ACTIVE_FEATURED_RECIPES') }}</p>
                </div>
              </div>
            </div>
            <div class="master-list-main-container">
              <div class="master-list-text text-title-2" data-test-id="master-list-text">{{ $t('COLLECTION.MASTER_LIST') }}</div>
              <div class="add-button-section">
                <button
                  type="button"
                  class="btn-green"
                  data-test-id="add-collection-button"
                  @click="addCollections()"
                >
                  {{ $t('COLLECTION.ADD_COLLECTION') }}
                </button>
              </div>
            </div>
          </div>
          <div v-if="collectionListData?.length" class="collection-list-master-data-main">
            <div class="collection-list-table-content">
              <div class="collection-list-content">
                <table class="collection-table" aria-label="promote-table">
                  <thead class="collection-table-head">
                    <tr class="collection-title">
                      <td>
                        <button
                          type="button"
                          class="collection-title-button"
                          @click="sortCollectionRecipes"
                        >
                          <span
                            class="collection-title-section"
                            @mouseover="onCollectionTitleHover(true)"
                            @mouseleave="onCollectionTitleHover(false)"
                            :class="{
                              'green-text': !!sortCollectionState,
                              hovered: isCollectionTitleHovered,
                            }"
                          >
                            <span>{{ $t('COLLECTION.COLLECTION_TITLE') }}</span>
                            <img
                              v-show="
                                isCollectionTitleHovered ||
                                isCollectionArrowVisible
                              "
                              :src="collectionArrowImage"
                              alt="arrow"
                              :class="collectionImageClasses"
                            />
                          </span>
                        </button>
                      </td>
                      <th class="collection-tags">
                        <span class="collection-tags">{{ $t('TAG.TAG_TEXT') }}</span>
                      </th>
                      <th class="collection-recipe-count">
                        <span>{{ $t('COLLECTION.RECIPE_COUNT') }}</span>
                      </th>
                      <th class="collection-state">
                        <span>{{ $t('COLLECTION.PUBLISHED_DATE') }}</span>
                      </th>
                      <th class="collection-status">
                        <span>{{ $t('COLLECTION.STATUS') }}</span>
                      </th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      class="collection-table-body"
                      v-for="(data, index) in collectionListData"
                      :key="index"
                      :data-test-id="`collection-row-${index}`"
                    >
                      <td class="collection-title">
                        <div class="collection-title-text text-h3">
                          {{ data?.collectionTitle }}
                        </div>
                      </td>
                      <td class="collection-tags">
                        <div class="collection-tags-text">
                          {{ data?.collectionTags }}
                        </div>
                      </td>
                      <td class="collection-recipe-count">
                        <div class="collection-recipe-number">
                          {{ data?.collectionRecipeCount }}
                        </div>
                      </td>
                      <td class="collection-published-date">
                        <div class="published-date">
                          {{ data?.collectionPublishedDate }}
                        </div>
                      </td>
                      <td class="collection-state">
                        <div
                          v-if="data?.collectionState === $t('COLLECTION.PUBLISHED')"
                          class="published-state text-light-h4"
                          data-test-id="published-state"
                        >
                          <span>
                            <img alt="publish-icon" src="@/assets/images/published-icon.png" />
                            {{ $t('COMMON.PUBLISHED') }}
                          </span>
                        </div>
                        <div
                          class="unpublished-state"
                          v-if="data?.collectionState === $t('COLLECTION.UNPUBLISHED')"
                        >
                          <span>
                            <img alt="unpublish-icon" class="image" src="@/assets/images/unpublished-icon.png" />
                            {{ $t('COMMON.UNPUBLISHED') }}
                          </span>
                        </div>
                      </td>
                      <td class="buttons-section">
                        <div class="menu">
                          <button
                            type="button"
                            :class="data?.isDropdown
                              ? 'menu-container menu-selected btn-reset'
                              : 'menu-container btn-reset'"
                            :data-test-id="`${index}-menu-container`"
                            :ref="data?.isDropdown ? 'menuSelected' : null"
                            @click="displayOption(data)"
                          >
                            <img
                              alt="edit-btn"
                              class="table-edit-btn"
                              :src="data?.isDropdown
                                ? greenEditButton
                                : editButton"
                            />
                          </button>
                          <div class="menu-box" v-if="data?.isDropdown">
                            <ul class="menu-list">
                              <li>
                                <button
                                  type="button"
                                  @click="editCollectionForm(data?.uuid)"
                                  class="btn-reset" data-test-id="collection-edit"
                                >
                                  {{ $t('BUTTONS.EDIT_BUTTON') }}
                                </button>
                              </li>
                              <li v-if="data?.collectionState === $t('COLLECTION.UNPUBLISHED')">
                                <button
                                  type="button"
                                  @click="publishCollection(data?.uuid)"
                                  class="btn-reset"
                                  data-test-id="collection-publish"
                                >
                                  {{ $t('BUTTONS.PUBLISH_BUTTON') }}
                                </button>
                              </li>
                              <li
                                :class="{
                                  'disabled-delete': data?.collectionState === $t('COLLECTION.PUBLISHED'),
                                  'simple-data-tooltip': data?.collectionState === $t('COLLECTION.PUBLISHED')
                                }"
                                :data-test-id="data?.collectionState === $t('COLLECTION.PUBLISHED')
                                  ? 'disabled-delete-button'
                                  : 'delete-button'"
                                :data-tooltip-text="data?.collectionState === $t('COLLECTION.PUBLISHED') && $t('COLLECTION.TO_DELETE_PUBLISHED_COLLECTION')"
                              >
                                <button
                                  type="button"
                                  @click="displayDeleteCollection(data?.uuid, data)"
                                  :class="data?.collectionState === $t('COLLECTION.PUBLISHED')
                                    ? 'disabled-button btn-reset'
                                    : 'btn-reset'"
                                  data-test-id="collection-delete"
                                >
                                  {{ $t('BUTTONS.DELETE_BUTTON') }}
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <pagination
            :currentPage="currentPage"
            :list="collectionListData"
            :listTotal="collectionCount"
            :sizePerPage="sizeCollections"
            :pageRange="6"
            @pageChange="pageChange"
          ></pagination>
        </div>
      </div>
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, onMounted, computed, onBeforeUnmount, getCurrentInstance } from "vue";
import { useI18n } from 'vue-i18n';
import { useNuxtApp } from "#app";
import { useStore } from 'vuex';
import pagination from "@/components/pagination.vue";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";

//composables
import { useProjectLang } from "@/composables/useProjectLang";
import { useCommonUtils } from "~/composables/useCommonUtils";

// images
import greenEditButton from "~/assets/images/green-edit-btn.svg?skipsvgo=true";
import editButton from "~/assets/images/edit-btn.svg?skipsvgo=true";
import grayArrow from "@/assets/images/arrow-gray.png";
import greenArrow from "@/assets/images/arrow-green.png";

//utility
import { useRouter, useRoute } from 'vue-router';
import { useRefUtils } from '@/composables/useRefUtils';
import { useTimeUtils } from '~/composables/useTimeUtils';
import ConfirmModal from "../components/modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import ProcessModal from "../components/modals/process-modal.vue";
import { PROCESS_MODAL_TYPE } from "../models/process-modal.model.js";

const { t } = useI18n();
const store = useStore();
const { routeToPage, triggerLoading } = useCommonUtils();
const { getRef } = useRefUtils();
const activeFeaturedRecipes = ref(0);
const sizeCollections = ref(10);
const collectionCount = ref(0);
const fromCollections = ref(0);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const { convertTimeStamp } = useTimeUtils();
const project = ref([]);
const isSaveModalVisible = ref(false);
const isDeletingModalVisible = ref(false);
const dropdownItem = ref([]);
const hasSelectedCollectionUuid = ref("");
const isPublishCollectionUuid = ref("");
const isCollectionDeletePopupVisible = ref(false);
const isPageLoading = ref(true);
const hasDescriptionDetails = ref(t('COLLECTION.CLICK_ON_PUBLISH'));
const isDeleteCollectionVisible = ref(false);
const defaultCollectionListData = ref([]);
const collectionListData = ref([]);
const isCollectionArrowVisible = ref(false);
const isCollectionTitleHovered = ref(false);
const sortCollectionState = ref(0);
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $tracker } = useNuxtApp();
const router = useRouter();
const route = useRoute();
const { readyProject } = useProjectLang();

const { openModal, closeModal } = useBaseModal({
  "collectionDeleteModal": {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      width: "510px",
      title: t('COLLECTION.DO_YOU_WANT_DELETE'),
      confirmBtnLabel: t('BUTTONS.DELETE_BUTTON'),
    },
  },
  "deletingModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
    props: {
      modalType: PROCESS_MODAL_TYPE.DELETING,
    },
  },
  "collectionSaveModal": {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.SAVE,
      width: "510px",
      title: t('COLLECTION.DO_YOU_WANT_TO_PUBLISH'),
      description: t('COLLECTION.CLICK_ON_PUBLISH'),
      confirmBtnLabel: t('BUTTONS.PUBLISH_BUTTON'),
    },
  }
});

const collectionArrowImage = computed(() => {
  return sortCollectionState.value === 0
    ? grayArrow
    : greenArrow;
});

const collectionImageClasses = computed(() => {
  return {
    "hovered-image": sortCollectionState.value === 0,
    "green-image": sortCollectionState.value !== 0,
    "collection-rotate-arrow": sortCollectionState.value === 1,
  };
});

const lang = computed(() => store.getters["userData/getDefaultLang"]);

const pageChange = (page) => {
  currentPage.value = page;
  fromCollections.value = page * sizeCollections.value - sizeCollections.value;
  isPageLoading.value = true;
  getCollectionListData();
};
const addCollections = () => {
  routeToPage("collection-details");
};
const getCollectionListData = async () => {
  await store.dispatch("collection/getCollectionDataAsync", {
    lang: lang.value,
    from: fromCollections.value,
    size: sizeCollections.value,
  });

  let collectionList = store.getters["collection/getCollectionData"];
  if (collectionList) {
    collectionCount.value = collectionList.total;
    collectionListData.value = collectionList.results.map((item) =>
      mapItemToCollection(item)
    );

    defaultCollectionListData.value = collectionListData.value
      ? [...collectionListData.value]
      : [];
    if (sortCollectionState.value) {
      sortCollectionState.value = sortCollectionState.value === 2 ? 1 : 0;
      sortCollectionRecipes();
    }

    activeFeaturedRecipes.value = collectionList?.totalActiveRecipe || 0;
    isPageLoading.value = false;
  } else {
    isPageLoading.value = false;
  }
};
const getRecipeCountText = (recipeCount) => {
  if (!recipeCount) {
    return "0 Recipes";
  } else {
    return recipeCount === 1 ? "1 Recipe" : `${recipeCount} Recipes`;
  }
};

const mapItemToCollection = (item) => {
  return {
    collectionTitle: item.title || "",
    collectionRecipeCount: getRecipeCountText(item.recipeCount),
    collectionPublishedDate: getTime(item.lastUpdate),
    collectionState:
      item.state === t('COLLECTION.PUBLISHED')
        ? t('COLLECTION.PUBLISHED')
        : t('COLLECTION.UNPUBLISHED'),
    isDropdown: false,
    uuid: item.uuid,
    collectionTags: returnTagName(item.tags),
  };
};

const returnTagName = (data) => {
  const tagNames = [];
  if (data.length) {
    data.forEach((item) => {
      tagNames.push(item.name);
    });
  }
  return tagNames.join(", ");
};
const editCollectionForm = (uuid) => {
  router.push({
    path: "collection-details",
    query: { uuid: uuid },
  });
};
const getTime = (jsonTimestamp) => {
  return convertTimeStamp(jsonTimestamp);
};

const publishCollection = (uuid) => {
  openModal({
    name: "collectionSaveModal",
    onClose: (data) => data && patchCollection(uuid),
  });
};
const notifySuccess = () => {
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.COLLECTION_PUBLISH_TOAST, true);
  if (currentPage.value > 1) {
    currentPage.value = 1;
    pageChange(currentPage.value);
  } else {
    getCollectionListData();
  }
};
const patchCollection = async (uuid) => {
  isPageLoading.value = true;

  const payload = {
    state: "published",
  };

  await store.dispatch("collection/patchCollectionDataAsync", {
    uuid,
    payload,
    onSuccess: notifySuccess,
  });
};
const displayDeleteCollection = (uuid, data) => {
  if (data?.collectionState === t('COLLECTION.UNPUBLISHED')) {
    openModal({
      name: "collectionDeleteModal",
      onClose: (data) => data && deleteCollection(uuid)
    });
  }
};
const deleteSuccess = () => {
  triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  isPageLoading.value = true;

  if (collectionListData.value.length === 1 && currentPage.value > 1) {
    currentPage.value -= 1;
    pageChange(currentPage.value);
  }
  getCollectionListData();
  closeModal("deletingModal");
};
const deleteCollection = (uuid) => {
  openModal({ name: "deletingModal"});
  store.dispatch("collection/deleteCollectionDataAsync", {
    uuid,
    onDeleteSuccess: deleteSuccess,
  });
};
const sortCollectionRecipes = () => {
  sortCollectionState.value += 1;

  if (sortCollectionState.value > 2) {
    sortCollectionState.value = 0;
  }

  isCollectionArrowVisible.value = sortCollectionState.value !== 0;

  if (sortCollectionState.value === 0) {
    collectionListData.value = defaultCollectionListData.value.slice();
  } else {
    const ascending = sortCollectionState.value === 1;

    collectionListData.value.sort((collectionFirst, collectionSecond) =>
      collectionFirst.collectionTitle.localeCompare(
        collectionSecond.collectionTitle,
        undefined,
        { numeric: true, sensitivity: "base" }
      ) * (ascending ? 1 : -1)
    );
  }
  isPageLoading.value = false;
};
const onCollectionTitleHover = (isHovered) => {
  isCollectionTitleHovered.value = isHovered;
};
const handleClickOutside = (event) => {
  if (dropdownItem.value?.isDropdown) {
    const menuSelected = document.querySelector(".menu-selected");
    if (!menuSelected?.contains(event.target)) {
      dropdownItem.value.isDropdown = false;
    }
  }
};
const displayOption = (item) => {
  dropdownItem.value = item;
  item.isDropdown = !item.isDropdown;

  if (collectionListData.value) {
    collectionListData.value.forEach((data) => {
      if (item.uuid !== data.uuid) {
        data.isDropdown = false;
      }
    });
  }
};

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
      document.addEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
      getCollectionListData();
      $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_COLLECTIONS, {}, { ...LOCAL_TRACKER_CONFIG });
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
