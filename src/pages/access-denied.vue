<template>
  <div class="acess-denied-container">
    <access-denied-popup></access-denied-popup>
  </div>
</template>
<script setup>
import { onMounted, getCurrentInstance, watch } from 'vue';
import AccessDeniedPopup from "@/components/access-denied-popup.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { PAGE_PERMISSIONS } from "@/сonstants/page-permissions";
import { useNuxtApp } from '#app';
import { useContext } from "@/composables/useContext.js";

const { $tracker, $nuxt } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { projectPermissions } = useProjectLang();
const { readyProject } = useProjectLang();
const {
  getPreviousPageBeforeAccessDenied,
  clearPreviousPageBeforeAccessDenied
} = useContext();

const checkAndRedirectIfPermissionsRestored = async () => {
  if (!projectPermissions.value) {
    return;
  }

  const previousPage = getPreviousPageBeforeAccessDenied();

  if (!previousPage) {
    const overviewPermissions = PAGE_PERMISSIONS['/overview'];
    if (overviewPermissions && overviewPermissions.every(permission => projectPermissions.value.includes(permission))) {
      await navigateTo('/overview');
    }
    return;
  }

  const pagePermissions = PAGE_PERMISSIONS[previousPage];
  if (!pagePermissions) {
    clearPreviousPageBeforeAccessDenied();
    await navigateTo(previousPage);
    return;
  }

  const hasPermission = pagePermissions.every(permission => projectPermissions.value.includes(permission));
  if (hasPermission) {
    clearPreviousPageBeforeAccessDenied();
    await navigateTo(previousPage);
  }
};

onMounted(() => {
  readyProject(({ displayName, isProjectReady }) => {
    if (isProjectReady) {
      $tracker.sendEvent(
        $keys.EVENT_KEY_NAMES.VIEW_ACCESS_DENIED,
        {
          "user permissions": projectPermissions.value,
          "project name": displayName,
          "previous url": $nuxt?.context?.from?.path,
        },
        LOCAL_TRACKER_CONFIG
      );
      checkAndRedirectIfPermissionsRestored();
    }
  });
});
watch(
  () => projectPermissions.value,
  async (newPermissions) => {
    if (!newPermissions || newPermissions.length === 0) {
      return;
    }
    await checkAndRedirectIfPermissionsRestored();
  },
  { immediate: false }
);
</script>
