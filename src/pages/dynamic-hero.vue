<template>
  <client-only>
    <content-wrapper :is-body-loading="isLoading" wrapperClasses="dynamic-hero">
      <template v-slot:title>
        <span v-if="!isCreateNewVisible">{{ $t('DYNAMIC_HERO.DYNAMIC_HEROES_LIST') }}</span>
        <span v-else class="font-size-24 color-black">{{ $t('DYNAMIC_HERO.REPLACE_LIVE_HERO_TEXT') }}</span>
      </template>

      <template v-slot:head>
        <div class="dynamic-hero-actions">
          <button v-if="!isCreateNewVisible && !isLoading" type="button" class="btn-green" @click="openNewHeroPopup">{{ $t('DYNAMIC_HERO.NEW_HERO') }}</button>

          <template v-if="isCreateNewVisible && !isLoading">
            <button type="button" class="btn-white" @click="cancelSelected">{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
            <button type="button" class="btn-green" :disabled="!isSelected" @click="openSavePopup">{{ $t('BUTTONS.SAVE_BUTTON') }}</button>
          </template>
        </div>
      </template>

      <block-wrapper is-transparent>
        <simple-table
          :data-source="rows"
          :column-keys="columnKeys"
          :column-names="columnNames"
          :sortable-columns="sortableColumns"
          :expired-data-source="rowsExpired"
          :expired-rows-btn-open-label="$t('DYNAMIC_HERO.VIEW_EXPIRED_HEROES')"
          :expired-rows-btn-hide-label="$t('DYNAMIC_HERO.HIDE_EXPIRED_HEROES')"
          table-class="dynamic-hero-table"
          @sortChange="sortChange"
        >
          <template v-slot:publishDate="{ data }">
            <span v-if="data.publishDate" class="font-size-14 color-black">{{ convertTimeStamp(data.publishDate) }}</span>
            <span v-else class="font-size-14 color-grey">no date</span>
          </template>

          <template v-slot:image="{ data }">
            <img
              alt="hero"
              class="dynamic-hero-image"
              :src="data?.image || defaultImage"
              @error="$event.target.src = `${defaultImage}`"
              width="60"
              height="60"
            />
          </template>

          <template v-slot:uuid="{ data }">
            <span class="dynamic-hero-uuid font-size-12 color-gunmetal-grey">{{ data.uuid }}</span>
          </template>

          <template v-slot:title="{ data }">
            <div v-if="dynamicHeroTemplateBadgeType[data.template]" class="dynamic-hero-title-wrapper">
              <div>
                <badge :label="data.template" :badge-type="dynamicHeroTemplateBadgeType[data.template]" is-small />
                <badge v-if="data.preview" :label="$t('BUTTONS.PREVIEW_BUTTON')" :badge-type="BADGE_TYPE.LIGHT_GREEN" is-small is-outline />
              </div>
              <div class="font-size-14 font-bold">{{ data.title }}</div>
            </div>
          </template>

          <template v-slot:lastUpdate="{ data }">
            <span class="font-size-14 color-black">{{ convertTimeStamp(data.lastUpdate) || "" }}</span>
          </template>

          <template v-if="!isCreateNewVisible" v-slot:status="{ data }">
            <badge
              :label="data.state"
              :img-src="(data.state === HERO_STATE.PREVIEW && data?.publishDate) ? dynamicHeroStateBadgeType.previewWithPublishDate.icon :  dynamicHeroStateBadgeType[data.state]?.icon"
              :badge-type="(data.state === HERO_STATE.PREVIEW && data?.publishDate) ? dynamicHeroStateBadgeType.previewWithPublishDate.type : dynamicHeroStateBadgeType[data.state]?.type"
            />
          </template>

          <template v-if="isCreateNewVisible" v-slot:select="{ data }">
            <button
              type="button"
              :class="{
                'btn-green': uuidForSelectedHero === data.uuid,
                'btn-white': uuidForSelectedHero !== data.uuid,
              }"
              :disabled="dataForSelectedHero?.uuid && data.uuid !== dataForSelectedHero?.uuid"
              :data-selected-highlight="(uuidForSelectedHero !== data.uuid && data.uuid === dataForSelectedHero?.uuid) ? 'highlight' : 'none'"
              @click="handleSelectedAction(data)"
            >
              <span v-if="uuidForSelectedHero === data.uuid">{{ $t('DYNAMIC_HERO.CREATE_NEW') }}</span>
              <span v-if="uuidForSelectedHero !== data.uuid && data.uuid !== dataForSelectedHero?.uuid">{{ $t('COMMON.SELECT') }}</span>
              <span v-if="uuidForSelectedHero !== data.uuid && data.uuid === dataForSelectedHero?.uuid">{{ $t('COMMON.SELECTED') }}</span>
            </button>
          </template>

          <template v-slot:actions="{ data }">
            <body-menu
              v-if="!isCreateNewVisible"
              :actions="getBodyMenuActionsArray(data)"
              :isFullText="true"
              @call-actions="(key) => callBodyMenuAction(key, data)"
            />
          </template>

        </simple-table>
      </block-wrapper>
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useStore } from 'vuex';
import { useNuxtApp } from "#app";
import { useRouter } from "vue-router";
import { useI18n } from 'vue-i18n';

import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import { useCommonUtils } from '~/composables/useCommonUtils';
import { useProjectLang } from "@/composables/useProjectLang";
import { useTimeUtils } from '../composables/useTimeUtils';

import IconScheduled from "~/assets/images/calendar.png?skipsvgo=true";
import IconLive from "~/assets/images/green-arrow-published.png?skipsvgo=true";
import IconDraft from "~/assets/images/unpublished-icon.png?skipsvgo=true";
import IconExpired from "~/assets/images/timer-small.png?skipsvgo=true";
import defaultImage from "~/assets/images/default_recipe_image.png?skipsvgo=true";
import IconQuizForm from "~/assets/images/quiz-form.png";

import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import DynamicHeroSelectType from '../components/pages/dynamic-hero/dynamic-hero-select-type.vue';
import SimpleTable from "../components/simple-table/simple-table.vue";
import BlockWrapper from "../components/block-wrapper/block-wrapper.vue";
import Badge from "../components/badge/badge.vue";
import BodyMenu from "../components/body-menu.vue";
import DynamicHeroScheduleTemplateModal
  from "../components/pages/dynamic-hero/dynamic-hero-schedule-template-modal.vue";
import AlertModal from "../components/modals/alert-modal.vue";
import ProcessModal from "../components/modals/process-modal.vue";
import ConfirmModal from "../components/modals/confirm-modal.vue";

import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { BADGE_TYPE } from "../components/badge/badge-type.js";
import {
  BODY_MENU_ACTION_KEY,
  DYNAMIC_HERO_PATH_MAP,
  DYNAMIC_HERO_TEMPLATE,
  getBodyMenuActions,
  getColumnKeys,
  getColumnNames,
  HERO_STATE
} from "../models/dynamic-hero.model.js";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import { PROCESS_MODAL_TYPE } from "../models/process-modal.model.js";
import { sortDynamicHero } from "../utils/sort-dynamic-hero.js";


const { $eventBus, $keys } = useNuxtApp();
const { t } = useI18n();
const store = useStore();
const router = useRouter();

const { convertTimeStamp } = useTimeUtils();
const {
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  deleteDynamicHeroDataAsync,
  patchDynamicHeroDataAsync,
} = useDynamicHeroStore();
const { triggerLoading, useCalendarMarkers } = useCommonUtils();
const { readyProject } = useProjectLang();
const { openModal, closeModal: closeBaseModal } = useBaseModal({
  "DynamicHeroSelectTypeModal": DynamicHeroSelectType,
  "DynamicHeroConfirmModal": ConfirmModal,
  "DynamicHeroAlertModal": AlertModal,
  "DynamicHeroProcessModal": ProcessModal,
  "DynamicHeroScheduleTemplateModal": {
    component: DynamicHeroScheduleTemplateModal,
    skipClickOutside: true,
    hideCloseBtn: false,
  },
});

const isLoading = ref(true);
const disableList = ref([]);
const disabledDates = ref([]);
const uuidForSelectedHero = ref("");
const isCreateNewVisible = ref(false);
const isSelected = ref(false);
const dynamicHeroList = ref([]);
const dataForSelectedHero = ref({});
const lang = ref("");
const isAdminCheck = ref(false);
const selectDynamicTypes = ref([
  { key: DYNAMIC_HERO_TEMPLATE.QUIZ, options: "Quiz" },
  { key: DYNAMIC_HERO_TEMPLATE.CONTENT, options: "Content" },
  { key: DYNAMIC_HERO_TEMPLATE.NEWS, options: "News" },
  { key: DYNAMIC_HERO_TEMPLATE.ADVICE, options: "Advice"  },
  { key: DYNAMIC_HERO_TEMPLATE.EVENT, options: "Event" },
]);
const dynamicHeroTemplateBadgeType = ref({
  [DYNAMIC_HERO_TEMPLATE.NEWS]: BADGE_TYPE.LIGHT_GREEN,
  [DYNAMIC_HERO_TEMPLATE.QUIZ]: BADGE_TYPE.RED,
  [DYNAMIC_HERO_TEMPLATE.EVENT]: BADGE_TYPE.PURPLE,
  [DYNAMIC_HERO_TEMPLATE.CONTENT]: BADGE_TYPE.YELLOW,
  [DYNAMIC_HERO_TEMPLATE.ADVICE]: BADGE_TYPE.BLUE,
});
const dynamicHeroStateBadgeType = ref({
  [HERO_STATE.SCHEDULED]: {
    type: BADGE_TYPE.YELLOW,
    icon: IconScheduled,
  },
  [HERO_STATE.LIVE]: {
    type: BADGE_TYPE.LIGHT_GREEN,
    icon: IconLive,
  },
  [HERO_STATE.DRAFT]: {
    type: BADGE_TYPE.SILVER,
    icon: IconDraft,
  },
  [HERO_STATE.PREVIEW]: {
    type: BADGE_TYPE.YELLOW,
    icon: IconScheduled,
  },
  "previewWithPublishDate": {
    type: BADGE_TYPE.SILVER,
    icon: IconDraft,
  },
  [HERO_STATE.EXPIRED]: {
    type: BADGE_TYPE.RED,
    icon: IconExpired,
  }
});
const sortableColumns = ref({
  "publishDate": { direction: "" },
  "title": { direction: "" },
  "lastUpdate": { direction: "" },
});

const { markers } = useCalendarMarkers(disableList);

const columnKeys = computed(() => getColumnKeys(isCreateNewVisible.value));
const columnNames = computed(() => getColumnNames(isCreateNewVisible.value, t));

const rows = computed(() => dynamicHeroList.value?.filter((item) => item.state !== "expired"));
const rowsExpired = computed(() => isCreateNewVisible.value ? [] : dynamicHeroList.value?.filter((item) => item.state === "expired"));

const sortChange = ({ key = "", direction = "" }) => {
  dynamicHeroList.value = sortDynamicHero(dynamicHeroList.value, key, direction);
};

const getBodyMenuActionsArray = (data) => getBodyMenuActions(data, t);
const callBodyMenuAction = (key, data) => {
  switch (key) {
    case BODY_MENU_ACTION_KEY.REPLACE_LIVE_HERO:
      isCreateNewVisible.value = true;
      uuidForSelectedHero.value = data.uuid;
      break;
    case BODY_MENU_ACTION_KEY.EDIT:
      navigateToEditPage(data.template, data.uuid);
      break;
    case BODY_MENU_ACTION_KEY.CREATE_DUPLICATE:
      navigateToCreateDuplicate(data.template, data.uuid);
      break;
    case BODY_MENU_ACTION_KEY.DELETE:
      openModal({
        name: "DynamicHeroConfirmModal",
        props: {
          title: "Delete the hero?",
          description: "Are you sure you want to delete this hero?",
          modalType: CONFIRM_MODAL_TYPE.DELETE,
        },
        onClose: (response) => response && deleteHeroAsync(data),
      });
      break;
    case BODY_MENU_ACTION_KEY.SCHEDULE:
      openModal({
        name: "DynamicHeroScheduleTemplateModal",
        props: {
          heroName: data.template,
          markers: markers.value,
          disabledDates: disabledDates.value,
        },
        onCallback: (response) => {
          if (response?.formattedDate) {
            dataForSelectedHero.value = data;
            closeBaseModal("DynamicHeroScheduleTemplateModal");
            patchDynamicHeroDataByTypeAsync(data.template, response.formattedDate, HERO_STATE.SCHEDULED);
          }
        },
      });
      break;
    case BODY_MENU_ACTION_KEY.UNSCHEDULE: {
      const template = data.template;
      const capitalized = template.charAt(0).toUpperCase() + template.slice(1);
      openModal({
        name: "DynamicHeroConfirmModal",
        props: {
          image: IconQuizForm,
          title: `Please confirm unscheduling ${capitalized} form`,
          descriptionRed: `${capitalized} will be moved to a draft.`
        },
        onClose: (response) => {
          if (response) {
            dataForSelectedHero.value = data;
            patchDynamicHeroDataByTypeAsync(template, 0, HERO_STATE.DRAFT);
          }
        },
      });
      break;
    }
  }
};

const handleSelectedAction = (data) => {
  if (uuidForSelectedHero.value === data.uuid) {
    openNewHeroPopup(data);
  } else if (uuidForSelectedHero.value !== data.uuid && data.uuid !== dataForSelectedHero.value?.uuid) {
    isSelected.value = true;
    dataForSelectedHero.value = data;
  } else if (uuidForSelectedHero.value !== data.uuid && data.uuid === dataForSelectedHero.value?.uuid) {
    isSelected.value = !isSelected.value;
    dataForSelectedHero.value = {};
  }
};

const navigateToCreateDuplicate = (template, uuid) => {
  const pathMap = {
    [DYNAMIC_HERO_TEMPLATE.NEWS]: "add-dynamic-news",
    [DYNAMIC_HERO_TEMPLATE.QUIZ]: "add-dynamic-quiz",
    [DYNAMIC_HERO_TEMPLATE.EVENT]: "add-dynamic-event",
    [DYNAMIC_HERO_TEMPLATE.CONTENT]: "add-dynamic-content",
    [DYNAMIC_HERO_TEMPLATE.ADVICE]: "add-dynamic-advice",
  };

  const path = pathMap[template];
  if (path) {
    router.push({
      path,
      query: { "create-duplicate": template, [QUERY_PARAM_KEY.UUID]: uuid },
    });
  }
};


const patchDynamicHeroDataByTypeAsync = async (type, selectedDate, stateType) => {
  openModal({
    name: "DynamicHeroProcessModal",
    props: {
      modalType: stateType === HERO_STATE.SCHEDULED ? PROCESS_MODAL_TYPE.SCHEDULING : PROCESS_MODAL_TYPE.UNSCHEDULING
    },
  });

  const parsedDate = selectedDate ? Date.parse(selectedDate) / 1000 : selectedDate;
  const {
    uuid,
    title,
    template,
    image,
    preview,
    data,
  } = dataForSelectedHero.value;

  const typeDataMap = {
    [DYNAMIC_HERO_TEMPLATE.NEWS]: {
      body: data?.body,
      image: data?.image,
      ctaText: data?.ctaText,
      ctaLink: data?.ctaLink,
    },
    [DYNAMIC_HERO_TEMPLATE.ADVICE]: {
      body: data?.body,
      image: data?.image,
    },
    [DYNAMIC_HERO_TEMPLATE.QUIZ]: {
      answer: data?.answer,
      body: data?.body,
      ctaLink: data?.ctaLink,
      commentary: data?.commentary,
    },
    [DYNAMIC_HERO_TEMPLATE.CONTENT]: {
      title: data?.title,
      image: data?.image,
      backgroundColor: data?.backgroundColor,
      titleColor: data?.titleColor,
      ctaLink: data?.ctaLink,
      ctaText: data?.ctaText,
      body: data?.body,
    },
    [DYNAMIC_HERO_TEMPLATE.EVENT]: {
      body: data?.body,
      image: data?.image,
      subtext: data?.subtext,
      dates: data?.dates,
      ctaLink: data?.ctaLink,
      ctaText: data?.ctaText,
    },
  };

  const payload = {
    uuid,
    title,
    template,
    publishDate: parsedDate,
    image,
    state: stateType,
    preview,
    data: typeDataMap[type],
  };

  if (stateType === HERO_STATE.DRAFT) {
    delete payload.publishDate;
  }

  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: payload.uuid,
    });
    await getDynamicHeroDataAsync();
    closeBaseModal("DynamicHeroProcessModal");

    if (stateType === HERO_STATE.LIVE) {
      triggerLoading("heroChange");
    } else if (!selectedDate && stateType !== HERO_STATE.LIVE) {
      triggerLoading("heroUnscheduled");
    } else if (selectedDate && stateType !== HERO_STATE.LIVE) {
      triggerLoading("heroScheduled");
    }
  } catch (error) {
    isLoading.value = false;
    closeBaseModal("DynamicHeroProcessModal");
    console.error(`Error in patchDynamicHeroDataByTypeAsync for type "${type}":`, error);
  } finally {
    dataForSelectedHero.value = {};
  }
};

const deleteHeroAsync = async (data) => {
  openModal({name: "DynamicHeroProcessModal", props: { modalType: PROCESS_MODAL_TYPE.DELETING }});

  const handleResponse = (key) => {
    closeBaseModal("DynamicHeroProcessModal");
    triggerLoading(key);
  };

  try {
    await deleteDynamicHeroDataAsync({ uuid: data.uuid, });
    await getDynamicHeroDataAsync();
    handleResponse($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  } catch (error) {
    console.error("[IQ][Dynamic hero page] Error in deleteHeroAsync:", error);
    handleResponse($keys.KEY_NAMES.ERROR_OCCURRED);
  }
};
const getDynamicHeroDataAsync = async () => {
  isCreateNewVisible.value = false;
  isLoading.value = true;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isLoading.value);

  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = dynamicHeroDataList.value;

    if (response.length) {
      const disabledArr = [];
      const disabledDatesArr = [];

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTimestamp = Math.floor(today.getTime() / 1000);

      const list = response.map((item) => {
        if (item.state === HERO_STATE.SCHEDULED && item.publishDate) {
          if (todayTimestamp < item.publishDate) {
            disabledDatesArr.push(new Date(item.publishDate * 1000));
          }
          disabledArr.push({
            date: item.publishDate || '',
            description: item.title || '',
            template: item.template || '',
          });
        }

        return {
          ...item,
          publishDate: item.state === HERO_STATE.DRAFT ? 0 : item?.publishDate,
        };
      });

      disabledDates.value = disabledDatesArr;
      disableList.value = disabledArr;
      dynamicHeroList.value = sortDynamicHero(list, "", "");
    }

    isLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isLoading.value);
    triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
  } catch (error) {
    isLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isLoading.value);
    console.error('[IQ][Dynamic hero page] Error in getDynamicHeroDataAsync:', error);
  }
};

const navigateToEditPage = (template, uuid, replace) => {
  const QUERY_PARAM_KEY = { UUID: 'uuid' };

  const basePaths = {
    [DYNAMIC_HERO_TEMPLATE.NEWS]: 'dynamic-news',
    [DYNAMIC_HERO_TEMPLATE.QUIZ]: 'dynamic-quiz',
    [DYNAMIC_HERO_TEMPLATE.EVENT]: 'dynamic-event',
    [DYNAMIC_HERO_TEMPLATE.CONTENT]: 'dynamic-content',
    [DYNAMIC_HERO_TEMPLATE.ADVICE]: 'dynamic-advice',
  };

  const path = basePaths[template];

  const pathPrefix = replace === "replace" ? "add-" : "edit-";
  const queryParams =
    replace === "replace"
      ? { replace: "replace-live-hero" }
      : { [QUERY_PARAM_KEY.UUID]: uuid };

  if (path) {
    router.push({
      path: `${pathPrefix}${path}`,
      query: queryParams,
    });
  }
};

const openNewHeroPopup = (item) => {
  openModal({
    name: "DynamicHeroSelectTypeModal",
    props: {
      heroTypesList: selectDynamicTypes.value,
      defaultValue: DYNAMIC_HERO_TEMPLATE.QUIZ,
    },
    onCallback: (dynamicType) => {
      if (dynamicType) {
        const { template, uuid, state } = item || {};

        if (dynamicType === template) {
          navigateToEditPage(template, uuid, "replace");
        } else {
          const path = DYNAMIC_HERO_PATH_MAP[dynamicType];
          if (path) {
            const query = state === "live" ? { replace: "replace-live-hero" } : undefined;
            router.push({ path, ...(query && { query }) });
          }
        }
      }

      closeBaseModal("DynamicHeroSelectTypeModal");
    },
  });
};

const openSavePopup = () => {
  const isDraft = (dataForSelectedHero?.value?.state === HERO_STATE.DRAFT);
  const props = isDraft
    ? {
      title: t("TEXT_POPUP.UNABLE_TO_REPLACE_LIVE_HERO"),
      description: t("TEXT_POPUP.NOT_PUBLISHED_MESSAGE"),
      modalType: CONFIRM_MODAL_TYPE.UNABLE,
      hideCancelBtn: true,
    }
    : {
      title: t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO'),
      modalType: CONFIRM_MODAL_TYPE.BANNER_TYPE_REPLACE,
    };

  openModal({
    name: "DynamicHeroConfirmModal",
    props,
    onClose: (response) => {
      if (response && !isDraft) {
        const date = new Date();
        date.setHours(0, 0, 0, 0);
        patchDynamicHeroDataByTypeAsync(dataForSelectedHero.value.template, date, HERO_STATE.LIVE);
      }
    },
  });
};

const cancelSelected = () => {
  isCreateNewVisible.value = false;
  isSelected.value = false;
  uuidForSelectedHero.value = "";
  dataForSelectedHero.value = {};
};

watch(isSelected, (newValue) => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
});

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isAdminCheck.value = store.state.userData.isAdmin;
      lang.value = store.getters["userData/getDefaultLang"];

      await getDynamicHeroDataAsync();

      $eventBus.on($keys.KEY_NAMES.CURRENT_LINK, (link) => link.includes("/dynamic-hero") && getDynamicHeroDataAsync());
    }
  });
});

onBeforeUnmount(() => {
  $eventBus.off($keys.KEY_NAMES.CURRENT_LINK);
});
</script>
