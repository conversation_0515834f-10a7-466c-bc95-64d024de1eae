<template>
    <content-wrapper
      v-show="hasContentPermission"
      class="recipe-generator-main-container"
      wrapper-classes="padding-zero"
    >
      <div class="main-inner-section generator-dashboard-main-section">
        <div class="generator-title">
          {{ $t('GENERATOR.GENERATOR_TEXT') }}
        </div>
        <div class="generator-output-main-section">
          <div class="generator-select-filter-list">
            <div class="generator-left-section">
              <div class="generator-prompt">
                <div class="generator-prompt-text">
                  {{ $t('GENERATOR.PROMPT') }}
                </div>
                <div class="line"></div>
              </div>
              <div
                :class="
                  !isGenerating && !isSaving
                    ? 'generator-reset-text'
                    : 'disable generator-reset-text'
                "
                @click="reset"
              >
                {{ $t('GENERATOR.RESET') }}
              </div>
            </div>
          </div>
        </div>
        <div class="prompt-container">
          <div class="prompt-text-container">
            <div class="prompt-text-box">
              <input
                autocomplete="off"
                type="text"
                v-model.trim="recipePrompt"
                placeholder="Enter prompt"
                data-test-id="generate-button"
                @keyup.enter="generate()"
              />
            </div>
            <button
              :class="isGenerating || isSaving ? 'disabled-button' : ''"
              class="btn-green"
              @click="generate()"
            >
              <img
                class="generate-icon"
                src="@/assets/images/generator-white-icon.png"
                alt=""
              />
              <div class="text">
                {{ isGenerationComplete ? "Regenerate" : "Generate" }}
              </div>
            </button>
          </div>
          <div class="line"></div>
          <button
            class="btn-green save-button"
            :class="
              isGenerationComplete && !isSaving
                ? ''
                : 'btn-green disabled-button save-button'
            "
            @click="saveRecipe()"
          >
            <img
              class="generate-save-icon"
              src="@/assets/images/generator-save.png"
              alt=""
            />
            <span>Save</span>
          </button>
        </div>

        <div class="image-refresh-button">
          <div class="generated-images">{{ $t('GENERATOR.IMAGES') }}</div>
          <div
            @click="refreshImages()"
            :class="
              isRefreshImagesDisabled ? 'disable-refresh' : 'refresh-image'
            "
          >
            {{ $t('GENERATOR.REFRESH') }}
          </div>
        </div>
        <div class="generated-image-container">
          <div
            v-if="imageList.length > itemsPerPage && !isGeneratingImages"
            class="slider-controls"
          >
            <div
              @click="prevSlide"
              class="slider-control"
              :class="isPrevButtonDisabled ? 'disabled' : ''"
            >
              {{ arrowPrev }}
            </div>
          </div>
          <div
            class="generated-image-list"
            :class="
              selectedImage && selectedImage.id === item.id
                ? 'border-image'
                : ''
            "
            v-for="(item, index) in visibleItems"
            :key="index"
            @mouseover="showImageDetails(item)"
            @mouseleave="hideImageDetails"
          >
            <div v-if="isGeneratingImages" class="loader-section">
              <div class="loader-image"></div>
            </div>
            <img
              v-if="isImagesGenerated"
              :src="item.imageUrl"
              :alt="item.id"
              class="image-preview"
            />
            <div class="image-title">{{ item.id }}</div>
            <div
              @click="selectImage(item)"
              class="select-image"
              v-if="
                isImagesGenerated &&
                isHovered(item) &&
                !(selectedImage && selectedImage.id === item.id)
              "
            >
              <img
                class="select-img-section"
                src="@/assets/images/select-generator.png"
                alt=""
              />
            </div>
            <div
              v-if="
                isImagesGenerated &&
                selectedImage &&
                selectedImage.id === item.id &&
                !isHovered(item)
              "
              class="selected-image"
            >
              <img
                class="selected-img-section"
                src="@/assets/images/selected-generator.png"
                alt=""
              />
            </div>
          </div>
          <div
            v-if="imageList.length > itemsPerPage && !isGeneratingImages"
            class="slider-controls"
          >
            <div
              @click="nextSlide"
              class="slider-control"
              :class="isNextButtonDisabled ? 'disabled' : ''"
            >
              {{ arrowNext }}
            </div>
          </div>
        </div>
        <div class="generator-output-main-section" ref="generatorOutputSection">
          <div class="generator-select-filter-list">
            <div class="generator-selection">
              <div
                v-for="(data, index) in generatorPanel"
                :key="index"
                class="generator-type"
                :class="index == generatorPanel.length - 1 ? 'last-panel' : ''"
              >
                <div
                  @click="selectPanel(data.id)"
                  class="selector"
                  :class="{
                    'selector-click': data.isChecked,
                    'disable-generator-button':
                      (!isRecipeReadyToShow && data.disabledUntilGenerated) ||
                      isSaving,
                  }"
                >
                  {{ data.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="generator-output-result-section">
            <div
              class="generating-recipe-loader-container"
              v-if="
                (isGeneratingRecipe || isDeterminingValidators) &&
                selectedPanelId !== 'advanced'
              "
            >
              <img
                class="generating-recipe-image"
                src="~/assets/images/generate-recipe.png"
                alt=""
              />
              <span class="generating-recipe-text">Generating Recipe</span>
            </div>
            <div
              class="generator-response-checkers"
              v-if="
                (isGeneratingRecipe || isDeterminingValidators) &&
                selectedPanelId !== 'advanced'
              "
            >
              <div class="response-checker-text">
                <span v-if="isDeterminingValidators"
                  >Determining the Validators...</span
                >
                <template v-else>
                  <span>Determining the Validators</span>
                  <img
                    src="~/assets/images/generator-validation-check.png"
                    alt=""
                  />
                </template>
              </div>
              <div class="response-checker-text">
                <span v-if="isGeneratingRecipe">Generating Recipe...</span>
                <template v-else>
                  <span>Generating Recipe</span>
                  <img
                    src="~/assets/images/generator-validation-check.png"
                    alt=""
                  />
                </template>
              </div>
            </div>
            <div
              class="recipe-result-modify-main"
              v-if="isRecipeReadyToShow && selectedPanelId === 'modify'"
            >
              <textarea
                v-model="modifyPrompt"
                class="recipe-modify-input-field"
              ></textarea>
              <div class="recipe-modify-button">
                <p class="cancel-button" @click="closeModify()">Cancel</p>
                <p
                  :class="
                    modifyPrompt.trim()
                      ? 'confirm-button'
                      : 'disable-confirm-button confirm-button'
                  "
                  @click="confirmModify()"
                >
                  Confirm
                </p>
              </div>
            </div>
            <div :class="isEditRecipe ? 'recipe-result-output-section' : ''">
              <div
                class="recipe-result-output-details-main"
                v-if="isRecipeReadyToShow && selectedPanelId !== 'advanced'"
              >
                <div class="recipe-result-left-container">
                  <div class="recipe-name-ingredients-details">
                    <div v-if="!isEditRecipe" class="recipe-name">
                      {{ recipeName }}
                    </div>
                    <input
                      v-if="isEditRecipe"
                      type="text"
                      class="recipe-input"
                      v-model="editedRecipeName"
                      @input="trackEdit()"
                    />
                    <div class="recipe-ingredients">Ingredients</div>
                    <div v-if="!isEditRecipe" class="recipe-ingredients-list">
                      <p
                        v-for="(ingredient, index) in ingredients"
                        :key="index"
                      >
                        {{ ingredient }}
                      </p>
                    </div>
                    <div v-if="isEditRecipe" class="recipe-edit-mode">
                      <textarea
                        v-model="editedIngredients"
                        ref="editTextarea"
                        class="ingredients-textarea"
                        @input="
                          handleResizeTextArea();
                          trackEdit();
                        "
                      ></textarea>
                    </div>
                  </div>
                </div>
                <div class="recipe-result-right-container">
                  <div class="serve-count-section">
                    <div class="serves-count">Serves:</div>
                    <div v-if="!isEditRecipe" class="serves-count">
                      {{ servesCount }}
                    </div>
                    <input
                      v-if="isEditRecipe"
                      type="text"
                      maxlength="2"
                      class="serve-input"
                      v-model="editedServesCount"
                      @input="trackEdit()"
                    />
                  </div>
                  <div class="recipe-result-instructions-detials">
                    <div class="instructions-text">Instructions</div>
                    <div v-if="!isEditRecipe" class="instructions-list">
                      <p
                        v-for="(instruction, index) in instructions"
                        :key="index"
                      >
                        {{ instruction }}
                      </p>
                    </div>
                    <div v-if="isEditRecipe" class="recipe-edit-mode">
                      <textarea
                        v-model="editedInstructions"
                        ref="editInstructionTextarea"
                        class="instruction-textarea"
                        @input="
                          handleResizeTextArea();
                          trackEdit();
                        "
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="isEditRecipe" class="recipe-result-edit-buttons">
                <p class="cancel-button" @click="closeRecipeEdit()">Cancel</p>
                <p :class="getConfirmButtonClass()" @click="confirmEdit()">
                  Confirm
                </p>
              </div>
            </div>
            <div
              class="advanced-output-details-main"
              v-if="selectedPanelId === 'advanced'"
            >
              <div class="advanced-validation-section">
                <div class="validation-text">Recipe Generation</div>
                <div class="validation-result">
                  {{ recipeStreamText }}
                </div>
              </div>
              <div class="advanced-validation-section">
                <div class="validation-text">Static Validation</div>
                <div class="validation-result">
                  {{ validationStreamText }}
                </div>
              </div>
              <div class="advanced-validation-section">
                <div class="validation-text">Taste Validation</div>
                <div class="validation-result">
                  {{ tasteValidationStreamText }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </content-wrapper>

</template>

<script>
import AIService from "@/services/AIService";
import RecipeService from "@/services/RecipeService";
import OrganizationsService from "@/services/OrganizationsService";
import { refUtils } from "@/mixins/refUtils";
import commonUtils from "@/mixins/commonUtils";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue"
export default {
  name: "dashboard",
  mixins: [refUtils, commonUtils],
  components: { ContentWrapper },
  data() {
    return {
      isAdminCheck: false,
      project: {},
      lang: "",
      isSaving: false, // true if saving recipe is in progress
      recipePrompt: "", // recipe generation prompt
      modifyPrompt: "", // recipe modify prompt

      // generation flags
      isGeneratingRecipe: false, // true if recipe generation is in progress
      isRecipeGenerated: false, // true if we've completed generating recipe data
      isDeterminingValidators: false, // true if determining validators is in progress
      isValidatorsDetermined: false, // true if we've retrieved the validators to run
      isRunningValidators: false, // true if running validators is in progress
      isRunningTasteValidator: false, // true if running taste validator (review) is in progress
      isValidatorsRan: false, // true if we've completed running validators
      isTasteValidatorRan: false, // true if we've completed running taste validator (review)
      isGeneratingImages: false, // true if image generation is in progress
      isImagesGenerated: false, // true if we've completed generating images
      isImagesGenerationFailed: false, // true if image generation failed

      // timeouts for SSE events
      generatingRecipeTimeout: null,
      runningValidatorsTimeout: null,
      runningTasteValidatorTimeout: null,

      // controllers for aborting streams
      recipeGenerationCtrl: null,
      tasteValidatorCtrl: null,
      validatorsCtrl: null,

      // recipe data
      recipeName: "",
      servesCount: 4,
      ingredients: [],
      instructions: [],

      // edit recipe data
      editedRecipeName: "",
      editedServesCount: "",
      editedIngredients: "",
      editedInstructions: "",
      isEditResultModified: false,

      // validator data
      validators: {},

      // advanced output data
      tasteValidationStreamText: "",
      validationStreamText: "",
      recipeStreamText: "",

      // image data
      imageList: [],
      itemsPerPage: 3,
      currentIndex: 0,
      selectedImage: null,
      hoveredImage: null,
      arrowPrev: "<",
      arrowNext: ">",

      // panel selectors
      generatorPanel: [
        {
          id: "recipe",
          name: "Recipe",
          isChecked: true,
          disabledUntilGenerated: false,
        },
        {
          id: "modify",
          name: "Modify",
          isChecked: false,
          disabledUntilGenerated: true,
        },
        {
          id: "edit",
          name: "Edit",
          isChecked: false,
          disabledUntilGenerated: true,
        },
        {
          id: "advanced",
          name: "Advanced Output",
          isChecked: false,
          disabledUntilGenerated: false,
        },
      ],
    };
  },
  computed: {
    visibleItems() {
      const start = this.currentIndex;
      const end = start + this.itemsPerPage;
      return this.imageList.slice(start, end);
    },
    isRefreshImagesDisabled() {
      return (
        (!this.isImagesGenerated && !this.isImagesGenerationFailed) ||
        this.isGeneratingImages ||
        !this.isRecipeGenerated ||
        this.isSaving
      );
    },
    isPrevButtonDisabled() {
      return (
        this.currentIndex === 0 || this.isGeneratingImages || this.isSaving
      );
    },
    isNextButtonDisabled() {
      const maxIndex = Math.min(
        this.currentIndex + this.itemsPerPage,
        this.imageList.length
      );
      return (
        maxIndex === this.imageList.length ||
        this.isGeneratingImages ||
        this.isSaving
      );
    },
    selectedPanelId() {
      return this.generatorPanel.find((panel) => panel.isChecked).id;
    },
    isEditRecipe() {
      return this.selectedPanelId === "edit";
    },
    isGenerating() {
      return (
        this.isGeneratingRecipe ||
        this.isDeterminingValidators ||
        this.isRunningValidators ||
        this.isRunningTasteValidator ||
        this.isGeneratingImages
      );
    },
    isGenerationComplete() {
      return (
        this.isRecipeGenerated && this.isImagesGenerated && !this.isGenerating
      );
    },
    isRecipeReadyToShow() {
      return this.isRecipeGenerated && this.isValidatorsDetermined;
    },
    shouldRunValidators() {
      return (
        this.isRecipeGenerated &&
        this.isValidatorsDetermined &&
        !this.isRunningValidators &&
        !this.isValidatorsRan
      );
    },
  },
  async mounted() {
    this.project = await this.getProject();
    if (!(this.project && this.project.id)) {
      this.$router.push({ path: "/create-project" }).catch();
      return;
    }

    this.lang = await this.getDefaultLang();
    this.isAdminCheck = this.isAdmin;
    this.reset();
  },
  methods: {
    getConfirmButtonClass() {
      return [
        this.editedInstructions,
        this.editedServesCount,
        this.editedRecipeName,
        this.editedIngredients,
      ].every((field) => field.trim()) && this.isEditResultModified
        ? "confirm-button"
        : "disable-confirm-button";
    },
    trackEdit() {
      this.editedServesCount = this.numericOnly(this.editedServesCount);
      this.isEditResultModified = true;
    },
    selectPanel(id) {
      this.generatorPanel.forEach((panel) => {
        panel.isChecked = panel.id === id;
      });
      if (id === "edit") {
        this.$nextTick(() => {
          this.handleResizeTextArea();
        });
      }
    },
    generate() {
      if (!this.recipePrompt || this.isSaving || this.isGenerating) {
        return;
      }
      this.scrollToGenerateOutputSection();
      this.resetRecipeData(false);
      this.resetImageData();
      if (this.selectedPanelId !== "advanced") {
        this.selectPanel("recipe");
      }

      this.generateRecipe();
      this.determineValidators();
    },
    scrollToGenerateOutputSection() {
      const scrollTarget = this.getRef("generatorOutputSection");
      scrollTarget.scrollIntoView({ behavior: "smooth", block: "end" });
    },
    generateRecipe() {
      this.isGeneratingRecipe = true;
      this.isRecipeGenerated = false;

      // Generate recipe
      let payload = {
        input: this.recipePrompt,
      };
      this.recipeGenerationCtrl = new AbortController();
      AIService.generateRecipe(
        this.$store,
        this.$auth,
        payload,
        (event) => {
          // Kill the connection if we haven't received an event in 30s
          clearTimeout(this.recipeGenerationTimeout);
          this.recipeGenerationTimeout = setTimeout(
            () => this.cancelRecipeGeneration(),
            30 * 1000
          );

          if (event && event.data) {
            const chunk = JSON.parse(event.data);
            this.processRecipeChunk(chunk, true);
          }
        },
        (event) => {
          this.isGeneratingRecipe = false;
        },
        (err) => {
          this.isGeneratingRecipe = false;
          throw err;
        },
        this.recipeGenerationCtrl
      );

      // Kill the connection if we don't receive any events in 30s
      this.recipeGenerationTimeout = setTimeout(
        () => this.cancelRecipeGeneration(),
        30 * 1000
      );
    },
    async determineValidators() {
      this.isDeterminingValidators = true;
      this.isValidatorsDetermined = false;

      this.validationStreamText += "**** Detecting validators ****\n\n";
      let ctrl = new AbortController();
      setTimeout(() => {
        ctrl.abort();
        this.isDeterminingValidators = false;
      }, 120 * 1000);
      try {
        const validatorsResponse = await AIService.getValidators(
          this.$store,
          this.$auth,
          this.recipePrompt,
          ctrl
        );
        if (
          validatorsResponse &&
          validatorsResponse.content &&
          validatorsResponse.content.validation_requirements
        ) {
          this.validators = validatorsResponse.content.validation_requirements;
          this.printValidators();
        }
      } catch (e) {
        this.isDeterminingValidators = false;
        console.error(e);
      }

      this.isDeterminingValidators = false;
      this.isValidatorsDetermined = true;

      if (this.shouldRunValidators) {
        this.runValidators();
      }
    },
    printValidators() {
      this.printValidator("avoidables", this.validators["avoidables"]);
      this.printValidator("diets", this.validators["diets"]);
      this.printValidator(
        "presence_of_ingredients",
        this.validators["presence_of_ingredients"]
      );
      this.printValidator(
        "absence_of_ingredients",
        this.validators["absence_of_ingredients"]
      );
      this.validationStreamText += "\n\n";
    },
    printValidator(type, values) {
      if (values && values instanceof Array) {
        this.validationStreamText += type + ": " + values.join(", ") + "\n";
      }
    },
    runValidators() {
      this.validationStreamText += "**** Running validators ****\n\n";

      const hasValidators =
        this.validators?.["avoidables"]?.length ||
        this.validators?.["diets"].length ||
        this.validators?.["presence_of_ingredients"].length ||
        this.validators?.["absence_of_ingredients"].length;
      if (!hasValidators) {
        this.isValidatorsRan = true;
        this.validationStreamText += "Nothing to run";
        return;
      }

      this.isRunningValidators = true;
      this.isValidatorsRan = false;

      let payload = {
        recipe: {
          title: this.recipeName,
          servings: this.servesCount,
          ingredients: this.ingredients,
          instructions: this.instructions,
        },
        validators: this.validators,
      };
      this.validatorsCtrl = new AbortController();
      AIService.runValidators(
        this.$store,
        this.$auth,
        payload,
        (event) => {
          // Kill the connection if we haven't received an event in 30s
          clearTimeout(this.validatorsTimeout);
          this.validatorsTimeout = setTimeout(
            () => this.cancelValidators(),
            30 * 1000
          );

          const chunk = JSON.parse(event.data);
          if (chunk.content.finished) {
            this.isRunningValidators = false;
            this.isValidatorsRan = true;
          } else {
            this.validationStreamText += chunk.content.delta;
          }
        },
        (event) => {
          this.isRunningValidators = false;
        },
        (err) => {
          this.isRunningValidators = false;
          throw err;
        },
        this.validatorsCtrl
      );

      // Kill the connection if we don't receive any events in 30s
      this.validatorsTimeout = setTimeout(
        () => this.cancelValidators(),
        30 * 1000
      );
    },
    runTasteValidator() {
      this.isRunningTasteValidator = true;
      this.isTasteValidatorRan = false;

      let payload = {
        recipe: {
          title: this.recipeName,
          servings: this.servesCount,
          ingredients: this.ingredients,
          instructions: this.instructions,
        },
      };
      this.tasteValidatorCtrl = new AbortController();
      AIService.generateRecipeReview(
        this.$store,
        this.$auth,
        payload,
        (event) => {
          // Kill the connection if we haven't received an event in 30s
          clearTimeout(this.tasteValidatorTimeout);
          this.tasteValidatorTimeout = setTimeout(
            () => this.cancelTasteValidator(),
            30 * 1000
          );

          const chunk = JSON.parse(event.data);
          this.tasteValidationStreamText += chunk.content.delta;
        },
        (event) => {
          this.isRunningTasteValidator = false;
          this.isTasteValidatorRan = true;
        },
        (err) => {
          this.isRunningTasteValidator = false;
          throw err;
        },
        this.tasteValidatorCtrl
      );

      // Kill the connection if we don't receive any events in 30s
      this.tasteValidatorTimeout = setTimeout(
        () => this.cancelTasteValidator(),
        30 * 1000
      );
    },
    refreshImages() {
      if (this.isImagesGenerated) {
        this.pushImagePlaceholders();
      }
      this.generateImages();
    },
    async generateImages() {
      this.isGeneratingImages = true;
      this.isImagesGenerated = false;
      this.currentIndex = 0;
      this.selectedImage = null;
      this.hoveredImage = null;

      let payload = {
        title: this.recipeName,
        ingredients: this.ingredients,
        instructions: this.instructions,
      };
      try {
        let ctrl = new AbortController();
        setTimeout(() => {
          ctrl.abort();
          this.isGeneratingImages = false;
          this.isImagesGenerationFailed = true;
        }, 360 * 1000);
        const imageResponse = await AIService.getImages(
          this.$store,
          this.$auth,
          payload,
          ctrl
        );
        if (
          imageResponse &&
          imageResponse.public_url &&
          imageResponse.public_url.length > 0
        ) {
          for (let i = 0; i < this.itemsPerPage; i++) {
            this.imageList[i].imageUrl = imageResponse.public_url[i];
          }
          this.isGeneratingImages = false;
          this.isImagesGenerated = true;
        }
      } catch (e) {
        this.isGeneratingImages = false;
        this.isImagesGenerationFailed = true;
        console.error(e);
      }
    },
    closeModify() {
      this.selectPanel("recipe");
      this.modifyPrompt = "";
    },
    confirmModify() {
      let payload = {
        input: this.modifyPrompt,
        recipe: {
          title: this.recipeName,
          servings: this.servesCount,
          ingredients: this.ingredients,
          instructions: this.instructions,
        },
      };

      this.resetRecipeData(true);
      if (this.selectedPanelId !== "advanced") {
        this.selectPanel("recipe");
      }
      this.isGeneratingRecipe = true;
      this.isRecipeGenerated = false;

      // Modify recipe
      this.validationStreamText += "**** Validators ****\n\n";
      this.printValidators();
      this.recipeGenerationCtrl = new AbortController();
      AIService.modifyRecipe(
        this.$store,
        this.$auth,
        payload,
        (event) => {
          // Kill the connection if we haven't received an event in 30s
          clearTimeout(this.recipeGenerationTimeout);
          this.recipeGenerationTimeout = setTimeout(
            () => this.cancelRecipeGeneration(),
            30 * 1000
          );

          if (event && event.data) {
            const chunk = JSON.parse(event.data);
            this.processRecipeChunk(chunk, false);
          }
        },
        (event) => {
          this.isGeneratingRecipe = false;
        },
        (err) => {
          this.isGeneratingRecipe = false;
          console.error(err);
          throw err;
        },
        this.recipeGenerationCtrl
      );

      // Kill the connection if we don't receive any events in 30s
      this.recipeGenerationTimeout = setTimeout(
        () => this.cancelRecipeGeneration(),
        30 * 1000
      );
    },
    processRecipeChunk(chunk, runImageGeneration) {
      if (!this.isValidChunk(chunk)) return;

      if (chunk.content.finished) {
        this.handleFinishedChunk(chunk.content.recipe);
        this.isGeneratingRecipe = false;
        this.isRecipeGenerated = true;

        if (runImageGeneration) {
          this.generateImages();
        }
        this.runTasteValidator();
        if (this.shouldRunValidators) {
          this.runValidators();
        }
      } else {
        this.appendToRecipeStream(chunk.content.delta);
      }
    },
    isValidChunk(chunk) {
      return chunk?.content != null;
    },
    handleFinishedChunk(recipe) {
      const defaultRecipe = {
        title: "",
        servings: "",
        ingredients: [],
        instructions: [],
      };
      const { title = "", servings = "", ingredients = [], instructions = [] } = recipe || defaultRecipe;

      this.recipeName = title;
      this.servesCount = servings;
      this.ingredients = ingredients;
      this.instructions = instructions;

      this.copyRecipeToEdited();
    },
    appendToRecipeStream(delta) {
      this.recipeStreamText += delta;
    },
    handleResizeTextArea() {
      const textarea = this.$refs.editTextarea;
      textarea.style.height = "auto";
      textarea.style.height = `${textarea.scrollHeight}px`;
      const textareaInstruction = this.$refs.editInstructionTextarea;
      textareaInstruction.style.height = "auto";
      textareaInstruction.style.height = `${textareaInstruction.scrollHeight}px`;
    },
    closeRecipeEdit() {
      this.copyRecipeToEdited();
      this.selectPanel("recipe");
      this.isEditResultModified = false;
    },
    confirmEdit() {
      this.copyEditedToRecipe();
      this.selectPanel("recipe");
    },
    copyRecipeToEdited() {
      this.editedRecipeName = this.recipeName;
      this.editedServesCount = this.servesCount.toString();
      this.editedIngredients = this.ingredients.join("\n");
      this.editedInstructions = this.instructions.join("\n");
      this.isEditResultModified = false;
    },
    copyEditedToRecipe() {
      this.recipeName = this.editedRecipeName.trim();
      const newServes = parseInt(this.editedServesCount);
      if (!isNaN(newServes)) {
        this.servesCount = newServes;
      }
      this.ingredients = this.editedIngredients
        .split("\n")
        .filter((ingredient) => ingredient.trim() !== "");
      this.instructions = this.editedInstructions
        .split("\n")
        .filter((instruction) => instruction.trim() !== "");
      this.isEditResultModified = false;
    },
    prevSlide() {
      if (this.currentIndex > 0) {
        this.currentIndex -= this.itemsPerPage;
      }
    },
    nextSlide() {
      if (this.currentIndex + this.itemsPerPage < this.imageList.length) {
        this.currentIndex += this.itemsPerPage;
        this.prevSlideable = true;
      }
    },
    showImageDetails(item) {
      if (!this.isImagesGenerated) {
        return;
      }
      if (this.selectedImage && this.selectedImage.id === item.id) {
        this.hoveredImage = null;
      } else {
        this.hoveredImage = item;
      }
    },
    hideImageDetails() {
      this.hoveredImage = null;
    },
    isHovered(item) {
      return this.hoveredImage === item;
    },
    selectImage(item) {
      if (!this.isImagesGenerated || this.isSaving) {
        return;
      }
      if (this.selectedImage && this.selectedImage.id === item.id) {
        this.selectedImage = null;
      } else {
        this.selectedImage = item;
      }
    },
    pushImagePlaceholders() {
      this.imageList.forEach((image) => (image.id += this.itemsPerPage));
      for (let i = this.itemsPerPage; i > 0; i--) {
        this.imageList.unshift({ id: i });
      }
    },
    async saveRecipe() {
      if (this.isSaving) {
        return;
      }

      this.$nuxt.$loading.start();
      this.isSaving = true;
      if (!this.selectedPanelId === "advanced") {
        this.selectPanel("recipe");
      }

      try {
        const isin = await this.getIsin();
        const imageUrl =
          this.selectedImage && this.selectedImage.imageUrl
            ? await this.uploadImage(isin, this.selectedImage.imageUrl)
            : null;

        let payload = {
          isin: isin,
          title: this.recipeName,
          image: imageUrl,
          servings: this.servesCount,
          ingredients: this.ingredients,
          instructions: this.instructions,
        };
        await RecipeService.postSimpleRecipe(
          this.project,
          payload,
          "cmsAI",
          this.$store,
          this.$auth,
          this.lang
        );

        this.isSaving = false;
        this.reset();
        this.$nuxt.$loading.finish();
        this.triggerLoading("savedSuccess");
      } catch (e) {
        this.isSaving = false;
        this.$nuxt.$loading.finish();
        console.error(e);
      }
    },
    async getIsin() {
      const response = await OrganizationsService.getNewIsins(
        this.project,
        "recipe",
        this.lang,
        this.$auth && this.$auth.user && this.$auth.user.email
          ? this.$auth.user.email
          : "",
        this.$store,
        this.$auth
      );
      return response && response.isin ? response.isin : "";
    },
    async uploadImage(isin, imageUrl) {
      let payload = {
        isin: isin,
        externalUrl: imageUrl,
        entityType: "recipe",
        contentType: "image",
        extension: "jpeg",
      };
      const response = await RecipeService.uploadImageFromUrl(
        this.project,
        payload,
        this.$store,
        this.$auth,
        this.lang
      );
      return response && response.url ? response.url : null;
    },
    cancelRecipeGeneration() {
      if (this.recipeGenerationCtrl) {
        this.recipeGenerationCtrl.abort();
      }
      if (this.recipeGenerationTimeout) {
        clearTimeout(this.recipeGenerationTimeout);
      }
      this.isGeneratingRecipe = false;
    },
    cancelValidators() {
      if (this.validatorsCtrl) {
        this.validatorsCtrl.abort();
      }
      if (this.validatorsTimeout) {
        clearTimeout(this.validatorsTimeout);
      }
      this.isRunningValidators = false;
    },
    cancelTasteValidator() {
      if (this.tasteValidatorCtrl) {
        this.tasteValidatorCtrl.abort();
      }
      if (this.tasteValidatorTimeout) {
        clearTimeout(this.tasteValidatorTimeout);
      }
      this.isRunningTasteValidator = false;
    },
    reset() {
      this.recipePrompt = "";
      this.resetRecipeData();
      this.resetImageData();
      this.selectPanel("recipe");
    },
    resetRecipeData(keepValidators) {
      // recipe data
      this.modifyPrompt = "";
      this.isRecipeGenerated = false;
      this.isGeneratingRecipe = false;
      this.recipeName = "";
      this.servesCount = 4;
      this.ingredients = [];
      this.instructions = [];
      this.copyRecipeToEdited();
      this.recipeStreamText = "";

      // clear timeouts and cancel running validators if any are running
      this.cancelRecipeGeneration();
      this.cancelValidators();
      this.cancelTasteValidator();

      // validator data
      if (!keepValidators) {
        this.isDeterminingValidators = false;
        this.isValidatorsDetermined = false;
        this.validators = {};
      }
      this.isRunningValidators = false;
      this.isRunningTasteValidator = false;
      this.isValidatorsRan = false;
      this.isTasteValidatorRan = false;
      this.tasteValidatorCtrl = null;
      this.validatorsCtrl = null;
      this.validationStreamText = "";
      this.tasteValidationStreamText = "";
    },
    resetImageData() {
      this.isGeneratingImages = false;
      this.isImagesGenerated = false;
      this.isImagesGeneratedFailed = false;
      this.imageList = [];
      this.pushImagePlaceholders();
      this.currentIndex = 0;
      this.selectedImage = null;
      this.hoveredImage = null;
    },
  },
};
</script>
