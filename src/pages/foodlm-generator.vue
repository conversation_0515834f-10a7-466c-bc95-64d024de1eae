<template>
    <section class="foodlm-generator-main iq-r-g">
        <div class="try-for-free-text display-2">{{ $t('AI_GENERATOR_PROMPT') }}</div>
        <div class="create-free-account-main">
            <div class="foodLM-container text-title-1 font-weight-black">
                <img src="@/assets/images/auth-signup-icon-1.png" alt="food LM icon" class="foodLM-image">
                <span class="color-green">{{ $t('FOOD') }}</span><span class="color-forest-green">{{ $t('LM') }}</span>
            </div>
            <div class="complete-access-text">
                {{ $t('CREATE_FREE_ACCOUNT') }}
            </div>
            <div class="sign-up-free">
                <img class="sign-up-arrow" src="@/assets/images/foodlm-arrow.png" alt="arrow">
                <button type="button" @click="signupFree()" class="btn-reset sign-up-text">
                    {{ $t('SIGN_UP_FREE') }}
                </button>
            </div>
        </div>
        <div class="foodlm-generator-container">
            <content-wrapper :is-body-loading="false" wrapper-classes="foodlm-generator">
                <template v-slot:title>{{ $t('GENERATOR.GENERATOR_TEXT') }}</template>
                <recipe-generator :isFoodLMGenerator="true" />
            </content-wrapper>
        </div>
        <footer class="foodlm-generator-footer">
            <signup-footer :isFoodLMGenerator="true"></signup-footer>
        </footer>
    </section>
  </template>

<script setup>
definePageMeta({
  layout: 'none',
  auth: false,
});
import { onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useNuxtApp } from '#app';
import { useAuth0 } from '@auth0/auth0-vue';
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import SignupFooter from "@/components/pages/signup/signup-footer.vue";
import recipeGenerator from "@/components/recipe-generator/recipe-generator.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useNonTrackUserDetails } from "@/composables/useNonTrackUserDetails";

const { $tracker, $keys } = useNuxtApp();
const { isAuthenticated } = useAuth0();
const store = useStore();
const router = useRouter();
const { setUnknownTrackerIdentityAsync } = useNonTrackUserDetails();

onMounted(async () => {
  if (!isAuthenticated.value) {
    await setUnknownTrackerIdentityAsync();
  }

  $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_FOODLM_GENERATOR, {}, { ...LOCAL_TRACKER_CONFIG });

  await store.dispatch("recipeGeneration/setFoodLMGenerator", true);
});

const signupFree = () => {
  $tracker.sendEvent($keys.EVENT_KEY_NAMES.CLICK_SIGN_UP, {}, { ...LOCAL_TRACKER_CONFIG });
  router.push({ path: "redirecting" });
};
</script>
