<template>
  <client-only>
    <banner-form :is-edit="true" :is-loading="isLoading" />
  </client-only>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import BannerForm from "@/components/pages/banner/banner-form.vue";
import { useBannerStore } from "@/stores/banner";
import { useStore } from "vuex";
import { useNuxtApp } from "#app";
import { useProjectLang } from "@/composables/useProjectLang";
import { useCommonUtils } from "~/composables/useCommonUtils";

const { $keys } = useNuxtApp();
const route = useRoute();
const store = useStore();
const { readyProject } = useProjectLang();
const { triggerLoading } = useCommonUtils();
const bannerStore = useBannerStore();
const isLoading = ref(true);

const getBannerByUUIDAsync = async () => {
  try {
    await bannerStore.getEditBannerAsync({
      lang: store.getters["userData/getDefaultLang"],
      uuid: route.params.uuid,
    });
  } catch (error) {
    console.error("Error fetching banner data:", error);
    triggerLoading($keys.KEY_NAMES.ERROR_OCCURRED);
  } finally {
    isLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
  }
};

onMounted(() => {
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);
  readyProject(({ isProjectReady }) => {
    if (isProjectReady) {
      getBannerByUUIDAsync();
    }
  });
});
</script>
