<template>
  <client-only>
    <banner-form />
  </client-only>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import BannerForm from '@/components/pages/banner/banner-form.vue';
import { useNuxtApp } from '#app';
import { useProjectLang } from '@/composables/useProjectLang';
import { useCommonUtils } from '~/composables/useCommonUtils';

const { $keys } = useNuxtApp();
const { readyProject } = useProjectLang();
const { triggerLoading } = useCommonUtils();

const isLoading = ref(true);

onMounted(() => {
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isLoading.value = false;
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
    }
  });
});
</script>
