<template>
  <client-only>
    <content-wrapper :is-body-loading="isLoading">
      <template v-slot:title>
        <span>{{ $t("BANNER.BANNER_LIST") }}</span>
      </template>

      <template v-slot:head>
        <button
          v-if="!hasReloadTextCheck"
          type="button"
          class="btn-green"
          @click="openNewBannerPopup()"
        >
          {{ $t("BANNER.NEW_BANNER") }}
        </button>
      </template>

      <no-result-found
        v-if="!isLoading && !bannerListData.length"
        :isReloadRequired="hasReloadTextCheck"
        :noResultText="$t('BANNER.CREATE_OR_RELOAD')"
      ></no-result-found>

      <block-wrapper
        is-transparent
        v-if="!isLoading && bannerListData && bannerListData.length > 0"
      >
        <simple-table
          :column-names="columnNames"
          :column-keys="columnKeys"
          :sortable-columns="sortableColumns"
          :data-source="rows"
          :expired-data-source="rowsExpired"
          :expired-rows-btn-open-label="$t('BANNER.VIEW_EXPIRED_BANNERS')"
          :expired-rows-btn-hide-label="$t('BANNER.HIDE_EXPIRED_BANNERS')"
          table-class="banner-table"
          table-column-class="banner-body"
          @sortChange="sortChange"
        >
          <template v-slot:publishDate="{ data }">
            <span class="font-size-14 color-black">
              {{ data.publishDate ? convertTimeStamp(data.publishDate) : "no date" }}
            </span>
          </template>

          <template v-slot:endDate="{ data }">
            <span class="font-size-14 color-black">
              {{ data.endDate ? convertTimeStamp(data.endDate) : "no date" }}
            </span>
          </template>

          <template v-slot:uuid="{ data }">
            <div class="banner-uuid font-size-12 color-gunmetal-grey">{{ data.uuid || "" }}</div>
          </template>

          <template v-slot:title="{ data }">
            <div v-if="data.preview" class="banner-item-preview-type font-size-12 font-normal color-green-l">
              {{ $t("BUTTONS.PREVIEW_BUTTON") }}
            </div>
            <div class="font-size-12 font-normal">
              <span v-if="data.type === 'all_text'">{{$t("BANNER.ALL_TEXT") }}</span>
              <span v-if="data.type === 'text_button'">{{$t("BANNER.TEXT_BUTTON") }}</span>
            </div>
            <div class="font-size-14 font-bold">
              {{ data.title || "" }}
            </div>
          </template>

          <template v-slot:lastUpdate="{ data }">
            <span class="font-size-14 color-gunmetal-grey">{{ data.lastUpdate ? convertTimeStamp(data.lastUpdate) : "" }}</span>
          </template>

          <template v-slot:status="{ data }">
            <badge
              :label="$t(STATE_MAPPING[data.state].tKey)"
              :badge-type="STATE_MAPPING[data.state].badgeType"
              :img-src="STATE_MAPPING[data.state].icon"
            />
          </template>

          <template v-slot:actions="{ data }">
            <body-menu
              :actions="data.state === 'expired' ? prepareExpiredActions() : prepareActions(data.state)"
              :isFullText="true"
              @call-actions="(key) => callAction(key, data)"
            />
          </template>
        </simple-table>
      </block-wrapper>
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { storeToRefs } from "pinia";
import { useNuxtApp } from "#app";

import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import ProcessModal from "@/components/modals/process-modal.vue";
import ConfirmModal from "@/components/modals/confirm-modal.vue";
import BannerTypeModal from "@/components/pages/banner/banner-type-modal.vue";
import BannerScheduleModal from "@/components/pages/banner/banner-schedule-modal.vue";
import NoResultFound from "@/components/no-result-found.vue";
import Badge from "@/components/badge/badge.vue";
import BodyMenu from "@/components/body-menu.vue";

import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useProjectLang } from "@/composables/useProjectLang";
import { useBannerStore } from "@/stores/banner";
import { useBaseModal } from "@/composables/useBaseModal";
import { useTimeUtils } from "@/composables/useTimeUtils";
import { PROCESS_MODAL_TYPE } from "@/models/process-modal.model";
import { CONFIRM_MODAL_TYPE } from "@/models/confirm-modal.model";
import { STATE_MAPPING } from "@/сonstants/state-mapping";
import { sortBanners } from "../../utils/sort-banners.js";

const router = useRouter();
const store = useStore();
const { $keys, $t } = useNuxtApp();
const { readyProject } = useProjectLang();
const { triggerLoading, useCalendarMarkers } = useCommonUtils();
const {
  getTimeStamp,
  formatDateToReadableString,
  convertTimeStamp,
} = useTimeUtils();
const bannerStore = useBannerStore();
const { bannerList } = storeToRefs(bannerStore);

const { openModal, closeModal } = useBaseModal({
  BannerTypeModal: BannerTypeModal,
  BannerScheduleModal: BannerScheduleModal,
  BannerConfirmModal: ConfirmModal,
  BannerProcessModal: {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
});

const BANNER_STATE_TYPE = {
  SCHEDULED: "scheduled",
  DRAFT: "draft",
};
const selectBannerTypes = ref([
  { key: "all text", options: "All Text" },
  { key: "text & button", options: "Text & Button" },
]);
const bannerListData = ref([]);
const hasReloadTextCheck = ref(false);
const disabledDates = ref([]);
const disableList = ref([]);
const lang = ref("");
const isLoading = ref(true);
const columnNames = ref([
  $t("COLLECTION.PUBLISHED_DATE"),
  $t("COLLECTION.END_DATE"),
  $t("BANNER.BANNER_ID"),
  $t("BANNER.BANNER_TITLE"),
  $t("MODIFIED"),
  $t("COMMON.STATUS"),
  "",
]);
const columnKeys = ref([
  "publishDate",
  "endDate",
  "uuid",
  "title",
  "lastUpdate",
  "status",
  "actions",
]);
const sortableColumns = ref({
  "publishDate": { direction: "" },
  "endDate": { direction: "" },
  "title": { direction: "" },
  "lastUpdate": { direction: "" },
});

const { markers } = useCalendarMarkers(disableList, "rawTitle");

const rows = computed(() => bannerListData.value.filter((item) => item.state !== "expired"));
const rowsExpired = computed(() => bannerListData.value.filter((item) => item.state === "expired"));

const getBannerByUUID = (uuid) => bannerListData.value?.find((item) => item.uuid === uuid);

const callAction = (key, item) => {
  switch (key) {
    case "edit":
      router.push({ path: `/banner/${item.uuid}` })?.catch();
      break;
    case "delete":
      openModal({
        name: "BannerConfirmModal",
        props: {
          modalType: CONFIRM_MODAL_TYPE.DELETE,
          title: $t("BANNER.BANNER_DELETE"),
          description: $t("BANNER.BANNER_DELETE_MESSAGE"),
        },
        onClose: (response) => response && deleteBannerAsync(item.uuid),
      });
      break;
    case "duplicate":
      router.push({
        path: `/banner/${item.uuid}`,
        query: { duplicate: true },
      })?.catch();
      break;
    case "schedule":
      scheduleBanner(item);
      break;
    case "unschedule":
      openModal({
        name: "BannerConfirmModal",
        props: {
          modalType: CONFIRM_MODAL_TYPE.DRAFT,
          title: "Please confirm unscheduling Banner form",
          descriptionRed: "Banner form will be moved to a draft.",
          confirmBtnLabel: $t("BUTTONS.CONFIRM_BUTTON"),
        },
        onClose: (response) => {
          if (response) {
            patchBannerDataAsync({
              data: getBannerByUUID(item.uuid),
              bannerStateType: BANNER_STATE_TYPE.DRAFT,
            })
          }
        },
      });
      break;
    default:
      break;
  }
};

const prepareActions = (state) => {
  return [
    {
      isDisable: state !== "scheduled",
      key: "unschedule",
      label: "Unschedule",
    },
    {
      isDisable: state !== "draft",
      key: "schedule",
      label: "Schedule",
    },
    {
      isDisable: false,
      key: "edit",
      label: $t("BUTTONS.EDIT_BUTTON"),
    },
    {
      isDisable: false,
      key: "delete",
      label: $t("BUTTONS.DELETE_BUTTON"),
    },
    {
      isDisable: false,
      key: "duplicate",
      label: "Create Duplicate",
    },
  ];
};

const prepareExpiredActions = () => {
  return [
    {
      isDisable: false,
      key: "duplicate",
      label: "Create Duplicate",
    },
    {
      isDisable: false,
      key: "delete",
      label: $t("BUTTONS.DELETE_BUTTON"),
    },
  ];
};

const sortChange = ({ key = "", direction = "" }) => {
  bannerListData.value = sortBanners(bannerListData.value, key, direction);
};

const openNewBannerPopup = () => {
  let modalSelectedType = "all text";

  openModal({
    name: "BannerTypeModal",
    props: {
      bannerTypes: selectBannerTypes.value,
      selectedType: modalSelectedType,
    },
    onCallback: (key, value) => {
      if (key === "update:selectedType") {
        modalSelectedType = value;
      }
    },
    onClose: (response) => {
      if (typeof response === "string") {
        let bannerType;

        if (response === "text & button") {
          bannerType = "text-button";
        } else {
          bannerType = "all-text";
        }
        router.push({
          path: "/banner/create",
          query: { [QUERY_PARAM_KEY.DATA]: bannerType },
        });
      } else if (response === true) {
        let bannerType;

        if (modalSelectedType === "text & button") {
          bannerType = "text-button";
        } else {
          bannerType = "all-text";
        }

        router.push({
          path: "/banner/create",
          query: { [QUERY_PARAM_KEY.DATA]: bannerType },
        });
      }
    },
  });
};

const deleteBannerAsync = async (uuid) => {
  openModal({ name: "BannerProcessModal", props: { modalType: PROCESS_MODAL_TYPE.DELETING } });

  try {
    await bannerStore.deleteBannerAsync({ uuid, lang: lang.value });
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  } catch (error) {
    console.error("[IQ][Banner page] Error in deleteBannerAsync", error);
  } finally {
    closeModal("BannerProcessModal");
    await getBannerListDataAsync();
  }
};

const scheduleBanner = (item) => {
  let startDate = "";
  let endDate = "";
  let range = {
    start: null,
    end: null,
  };
  let bannerStartDate = 0;
  let bannerEndDate = 0;

  const selectedBannerData = getBannerByUUID(item.uuid);
  if (selectedBannerData?.publishDate && selectedBannerData?.endDate) {
    const startDateObj = new Date(data.publishDate * 1000);
    const endDateObj = new Date(data.endDate * 1000);

    startDate = formatDateToReadableString(startDateObj);
    endDate = formatDateToReadableString(endDateObj);

    bannerStartDate = data.publishDate;
    bannerEndDate = data.endDate;

    range.start = startDateObj;
    range.end = endDateObj;
  }

  openModal({
    name: "BannerScheduleModal",
    props: {
      range: range,
      startDate: startDate,
      endDate: endDate,
      markers: Array.isArray(markers) ? markers : markers.value || [],
      disabledDates: disabledDates.value,
      onDateChange: (newValue) => {
        newValue[0]?.setHours(0, 0, 0, 0);
        newValue[1]?.setHours(23, 59, 59, 999);
        bannerStartDate = getTimeStamp(newValue[0]);
        bannerEndDate = getTimeStamp(newValue[1]);
      },
    },
    onCallback: (key, value) => {
      if (key === "update:range") {
        range = value;
      }
    },
    onClose: (response) => {
      if (response && bannerStartDate && bannerEndDate) {
        patchBannerDataAsync({
          data: selectedBannerData,
          bannerStateType: BANNER_STATE_TYPE.SCHEDULED,
          bannerStartDate,
          bannerEndDate,
        });
      }
    },
  });
};

const patchBannerDataAsync = async (
  {
    data,
    bannerStateType = BANNER_STATE_TYPE.DRAFT,
    bannerStartDate = 0,
    bannerEndDate = 0
  }
) => {
  const modalType = bannerStateType === BANNER_STATE_TYPE.SCHEDULED ? PROCESS_MODAL_TYPE.SCHEDULING : PROCESS_MODAL_TYPE.UNSCHEDULING;
  openModal({ name: "BannerProcessModal", props: { modalType } });

  try {
    const payload = {
      uuid: data.uuid || "",
      title: data?.title || "",
      state: bannerStateType,
      publishDate: bannerStartDate,
      endDate: bannerEndDate,
      ctaText: data?.ctaText || "",
      ctaLink: data?.ctaLink || "",
      preview: data?.preview || false,
    };

    if (bannerStateType === BANNER_STATE_TYPE.DRAFT) {
      delete payload.publishDate;
      delete payload.endDate;
    }

    if (!payload.ctaLink || !payload.ctaText) {
      delete payload.ctaLink;
      delete payload.ctaText;
    }

    await bannerStore.patchBannerAsync({
      payload,
      uuid: data.uuid,
      lang: lang.value,
    });

    if (bannerStartDate && bannerEndDate && bannerStateType === BANNER_STATE_TYPE.SCHEDULED) {
      triggerLoading("bannerFormScheduled");
    }
    if (bannerStateType === BANNER_STATE_TYPE.DRAFT) {
      triggerLoading("bannerFormUnscheduled");
    }

    closeModal("BannerProcessModal");
    await getBannerListDataAsync();
  } catch (error) {
    console.error("[IQ][Banner page] Error in patchBannerDataAsync", error);
    closeModal("BannerProcessModal");
  }
};

const getBannerListDataAsync = async () => {
  isLoading.value = true;
  resetBannerData();

  try {
    await bannerStore.getBannerListAsync({ lang: lang.value });
    processBannerData();
  } catch (error) {
    console.error(`[IQ][Banner page] ${$keys.KEY_NAMES.ERROR_IN} getBannerListDataAsync`, error);
    hasReloadTextCheck.value = true;
  } finally {
    isLoading.value = false;
  }
};

const resetBannerData = () => {
  bannerListData.value = [];
  disabledDates.value = [];
  disableList.value = [];
};

const processBannerData = () => {
  const activeStateList = [$keys.KEY_NAMES.SCHEDULED, $keys.KEY_NAMES.LIVE];
  const isBannerActive = ({ state, publishDate, endDate }) => (activeStateList.includes(state) && publishDate && endDate);
  const list = sortBanners(bannerList.value, "", "");

  list.forEach((item) => {
    if (isBannerActive(item)) {
      handleActiveBanner(item);
    }

    if (item?.state === $keys.KEY_NAMES.DRAFT) {
      item.publishDate = 0;
      item.endDate = 0;
    }
  });

  bannerListData.value = list;
};

const handleActiveBanner = (item) => {
  const startDisableDate = new Date(item.publishDate * 1000);
  startDisableDate.setHours(0, 0, 0, 0);

  const endDisableDate = new Date(item.endDate * 1000);
  endDisableDate.setHours(23, 59, 59, 999);

  const tempDisableDate = getBetweenDates(startDisableDate, endDisableDate);
  disabledDates.value.push(...tempDisableDate);
  tempDisableDate.forEach((date) => {
    disableList.value.push({
      date: new Date(date),
      title: item.title || "",
      state: item.state || "",
    });
  });
};

const getBetweenDates = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  return Array.from({ length: days }, (_, index) => {
    const currentDate = new Date(start);
    currentDate.setDate(start.getDate() + index);
    return currentDate;
  });
};

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      lang.value = store.getters["userData/getDefaultLang"];
      await getBannerListDataAsync();
    }
  });
});
</script>
