<template>
  <client-only>
    <content-wrapper :is-body-loading="false" wrapper-classes="iq-r-g">
      <template v-slot:title>
        <span data-test-id="generator-heading">{{ $t('GENERATOR.GENERATOR_TEXT') }}</span>
      </template>

      <recipe-generator />

    </content-wrapper>
  </client-only>
</template>

<script setup>
import { onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import recipeGenerator from "@/components/recipe-generator/recipe-generator.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useNuxtApp } from '#app';

const { triggerLoading } = useCommonUtils();
const instance = getCurrentInstance();
const { $tracker, $keys } = useNuxtApp();
onMounted(() => {
  $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_GENERATOR, {}, { ...LOCAL_TRACKER_CONFIG });
});

onBeforeUnmount(() => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
});
</script>
