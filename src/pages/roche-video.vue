<template>
  <client-only>
    <content-wrapper
      :is-body-loading="isRocheVideoPageLoading"
    >
      <div v-show="!isRocheVideoPageLoading" class="main-inner-section display-1">
        <div class="header-section">
          <div class="heading-main color-green-dark">{{ $t("ROCHE.VIDEOS") }}</div>
          <div class="header-button">
            <button
              type="button"
              @click="uploadVideo($event)"
              v-if="!hasReloadTextCheck"
              class="new-video-button btn-reset"
              :class="
                isUploadVideoProcessLoading ? 'disable-new-video-button' : ''
              "
            >
              <label for="file" class="add-btn new-roche-video-button"
                >{{ $t("ROCHE.NEW_VIDEO") }}</label
              >
              <input
                id="file"
                type="file"
                class="upload-input"
                title="NEW VIDEO"
                @change="checkUploadedVideoFileAsync"
                accept=".mp4"
              />
            </button>
            <div v-if="!hasReloadTextCheck" class="video-description-text">
              {{ $t("ROCHE.MAX_SIZE_FORMAT") }}
            </div>
          </div>
        </div>
        <div
          v-if="isUploadVideoProcessLoading"
          class="video-uploading-container"
        >
          <div class="video-loading-main">
            <div class="content">
              <div class="input-loading">
                <div class="loader-image"></div>
              </div>
              <div class="loading-text">
                <p>Upload is in progress...</p>
              </div>
            </div>
          </div>
        </div>
        <div class="roche-video-main-table">
          <div v-if="videoData.length > 0" class="table-section">
            <table class="table-container">
              <caption></caption>
              <thead class="table-head">
                <tr class="table-head-title">
                  <th class="table-head-row-column"></th>
                  <th class="table-head-row-column">
                    {{ $t("ROCHE.VIDEO_NAME") }}
                  </th>
                  <th class="table-head-row-column">
                    {{ $t("ROCHE.CLOUDFRONT_LINK") }}:
                  </th>
                  <th class="table-head-row-column">
                    {{ $t("ROCHE.SIZE") }}
                  </th>
                  <th class="table-head-row-column">
                    {{ $t("ROCHE.ADDED") }}
                  </th>
                  <th class="table-head-row-column"></th>
                </tr>
              </thead>
              <tbody class="table-body">
                <tr
                  v-for="(data, index) in videoData"
                  :class="{
                    'change-background-color-for-selected':
                      data.selectedText == 'DELETE',
                  }"
                  :key="index"
                  class="table-body-row"
                >
                  <td class="table-body-row-column blank-row"></td>
                  <td class="table-body-row-column video-name-row">
                    <div class="video-container">
                      <div class="video-name-text">
                        {{ data && data.name ? data.name : "" }}
                      </div>
                      <div class="publisher-form-main-container">
                        <button
                          type="button"
                          @click="playVideo(data.link)"
                          :class="[
                            'play-video-icon',
                            'simple-data-tooltip',
                            { disable: isUploadVideoProcessLoading }
                          ]"
                          :data-tooltip-text="playButtonTooltip"
                        >
                          <img
                            src="~/assets/images/circle-play.svg?skipsvgo=true"
                            alt="Play Video"
                          />
                        </button>
                      </div>
                    </div>
                  </td>
                  <td class="table-body-row-column video-cloudfront-row">
                    <div class="cloudfront-row">
                      <div
                        class="video-cloudfront-text"
                        :class="isUploadVideoProcessLoading ? 'disable' : ''"
                      >
                        <span
                          class="text"
                          :id="`cloudfrontID${index}`"
                          @click="openVideoTab(data)"
                          >{{ data && data.link ? data.link : "" }}</span
                        >
                      </div>
                      <div
                        class="video-cloudfront-copy-clipboard"
                        :class="isUploadVideoProcessLoading ? 'disable' : ''"
                      >
                        <button
                          type="button"
                          :data-tooltip-text="copyLinkTooltip"
                          class="clipboard-image btn-reset simple-data-tooltip"
                          @click="copyVideoCloudfront(index)"
                        >
                          <img
                            src="~/assets/images/copy-icon.png"
                            alt="copy"
                          />
                        </button>
                      </div>
                    </div>
                  </td>
                  <td class="table-body-row-column video-size-row">
                    {{ data && data.size ? bytesToMegabytes(data.size) : "" }}
                  </td>
                  <td class="table-body-row-column video-time-row">
                    <span class="text">{{
                      data && data.createDate
                        ? convertTimestampToCustomFormat(data.createDate)
                        : ""
                    }}</span>
                  </td>
                  <td
                    :class="
                      isUploadVideoProcessLoading
                        ? 'video-delete-row-disable table-body-row-column video-delete-row'
                        : 'table-body-row-column video-delete-row'
                    "
                  >
                    <div class="delete-video" @click="deleteVideo(data.name)">
                      <img
                        alt=""
                        src="@/assets/images/delete-icon.png"
                        class="delete-icon"
                      />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <noResultFound
          v-if="!videoData.length && !isUploadVideoProcessLoading"
          :description="hasReloadTextCheck ? $t('COMMON.RELOAD_PAGE') : $t('NO_RESULT.VIDEO')"
        />
      </div>
      <recipeVideo
        v-if="openVideo"
        :videoLink="videoLink"
        :closeModal="closeModal"
      />
      <deletingModal v-show="isDeletingModalVisible" />
      <deleteModal
        v-if="isDeleteRocheVideoModalOpen"
        :closeModal="closeModal"
        :productInfoTitle="$t('DESCRIPTION_POPUP.DELETE_VIDEO')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
        :productDescriptionTwo="$t('DESCRIPTION_POPUP.VIDEO')"
        :deleteItem="deleteRocheVideoAsync"
        :availableLanguage="0"
      />
      <invalidImageVideoPopup
        v-show="isInvalidVideoModalVisible && !$nuxt.isOffline"
        :closeModal="closeModal"
        :acceptedFile="acceptedFile"
        :video="true"
        :image="false"
        :zip="false"
      />
      <sizeLimit
        v-if="isMaxVideoPopupVisible"
        :imageSizeAlert="'Your uploaded video size is larger than 250MB.'"
        :fileSizeAlert="'Max. size for video: 250MB'"
        :closeModal="closeModal"
        :isMaxVideoPopupVisible="isMaxVideoPopupVisible"
      />
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import recipeVideo from "@/components/recipeVideo";
import deletingModal from "@/components/deleting-modal";
import deleteModal from "@/components/delete-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import sizeLimit from "@/components/size-limit.vue";
import noResultFound from "@/components/no-result-found";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import { useTimeUtils } from "~/composables/useTimeUtils";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useRefUtils } from "~/composables/useRefUtils";
import { useNuxtApp } from "#app";
import { useProjectLang } from "../composables/useProjectLang.js";

const { convertTimestampToCustomFormat } = useTimeUtils();
const { $tracker, $eventBus } = useNuxtApp();

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const $t = instance.appContext.config.globalProperties.$t;
const store = useStore();

const { getRef } = useRefUtils();
const { triggerLoading } = useCommonUtils();
const { readyProject } = useProjectLang();

const openVideo = ref(false);
const videoLink = ref("");
const videoData = ref([]);
const hasReloadTextCheck = ref(false);
const isRocheVideoPageLoading = ref(true);
const isDeletingModalVisible = ref(false);
const isDeleteRocheVideoModalOpen = ref(false);
const isInvalidVideoModalVisible = ref(false);
const isMaxVideoPopupVisible = ref(false);
const isUploadVideoProcessLoading = ref(false);
const acceptedFile = ref("");
const videoResponseUrl = ref("");
const videoFile = ref([]);
const videoFilesName = ref([]);
const deleteName = ref("");
const existingVideoNamesList = ref([]);
const lang = ref("");
const playButtonTooltip = ref("Click to play video");
const copyLinkTooltip = ref("Copy link to clipboard");


onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      document.addEventListener("keyup", handleESCClickOutside);
      lang.value = store.getters["userData/getDefaultLang"];
      await getVideoDataAsync();
      $eventBus.on("current-link", (link) => {
        if (link.includes("/roche-video")) {
          getVideoDataAsync();
        }
      });
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
  $eventBus.off("current-link");
});

const handleESCClickOutside = (event) => {
  if (event?.key === 'Escape') {
    closeModal();
  }
};

const playVideo = (data) => {
  videoLink.value = data;
  openVideo.value = true;
};

const openVideoTab = (data) => {
  window.open(data.link, "_blank");
};

const bytesToMegabytes = (bytes) => {
  const megabytes = bytes / (1024 * 1024);
  return `${megabytes.toFixed(2)} MB`;
};

const getVideoDataAsync = async () => {
  isRocheVideoPageLoading.value = true;
  try {
    await store.dispatch("video/getVideoDataAsync");
    const response = store.getters["video/getVideoDataList"];
    existingVideoNamesList.value = [];
    videoData.value = response || [];

    if (videoData.value.length) {
      videoData.value = videoData.value
        .map((item) => ({
          ...item,
          selectedText: "",
          link: item.link ? `https://${item.link}` : item.link,
        }))
        .reverse();

      existingVideoNamesList.value = videoData.value.map((item) =>
        item?.name.toLowerCase()
      );
    }
  } catch (e) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getVideoDataAsync`, e);
    hasReloadTextCheck.value = true;
  }
  finally {
    isRocheVideoPageLoading.value = false;
  }
};

const copyVideoCloudfront = (index) => {
  const copyElement = getRef(`cloudfrontID${index}`);
  navigator.clipboard.writeText(copyElement.innerHTML);
  $eventBus.emit("copyToClipboard");
};

const deleteVideo = (name) => {
  deleteName.value = name;
  isDeleteRocheVideoModalOpen.value = true;
  videoData.value.map((item) => {
    if (name == item.name) {
      item.selectedText = "DELETE";
    }
  });
};


const deleteRocheVideoAsync = async () => {
  isDeleteRocheVideoModalOpen.value = false;
  isDeletingModalVisible.value = true;

  try {
    await store.dispatch("video/deleteVideoAsync", { name: deleteName.value });
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
    await getVideoDataAsync();
  } catch (e) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} deleteRocheVideoAsync`, e);
  } finally {
    isDeletingModalVisible.value = false;
    closeModal();
  }
};

const resetFileInput = () => {
  const fileInput = document.getElementById('file');
  if (fileInput) {
    fileInput.value = ''; // Reset to empty string
  }
};


const checkUploadedVideoFileAsync = async (event) => {
  const files = event.target.files;
  videoFile.value = files.length > 0 ? files : null;

  if (videoFile?.value?.length > 0) {
    videoFilesName.value = videoFile.value[0].name
      .toLowerCase()
      .replaceAll(" ", "_");
    const reg = /(.*?)\.(mp4)$/;

    if (!videoFilesName.value.match(reg)) {
      acceptedFile.value = " mp4";
      isInvalidVideoModalVisible.value = true;
      resetFileInput();
      return;
    }

    const fileSize = videoFile.value[0].size;
    if (fileSize > 250 * 1024 * 1024) {
      resetFileInput();
      isMaxVideoPopupVisible.value = true;
      return;
    } else if (existingVideoNamesList.value.includes(videoFilesName.value)) {
      $eventBus.emit("videoNameExist");
      return;
    } else {
      isUploadVideoProcessLoading.value = true;
      const payload = new FormData();
      payload.append("file", videoFile.value[0]);
      payload.append("name", videoFilesName.value);

      try {
        $eventBus.emit($keys.KEY_NAMES.ROUTE_LOADING, isUploadVideoProcessLoading.value);
        await store.dispatch("video/postVideoDataAsync", { payload });
        triggerLoading($keys.KEY_NAMES.VIDEO_UPLOADED);
        await getVideoDataAsync();
        uploadVideo(event);
      } catch (error) {
        console.error(`${$keys.KEY_NAMES.ERROR_IN} checkUploadedVideoFileAsync`, error);
        triggerLoading($keys.KEY_NAMES.VIDEO_UNEXPECTED_ERROR);
      } finally {
        isUploadVideoProcessLoading.value = false;
        $eventBus.emit($keys.KEY_NAMES.ROUTE_LOADING, isUploadVideoProcessLoading.value);
      }
    }
  }
};

const uploadVideo = (event) => {
  event.target.value = "";
};

const closeModal = () => {
  if (videoData.value.length > 0) {
    videoData.value.forEach((data) => {
      data.selectedText = "";
    });
  }
  openVideo.value = false;
  isDeleteRocheVideoModalOpen.value = false;
  isInvalidVideoModalVisible.value = false;
  isMaxVideoPopupVisible.value = false;
  isUploadVideoProcessLoading.value = false;
  isDeletingModalVisible.value = false;
  videoResponseUrl.value = "";
};
</script>
