<template>
  <content-wrapper wrapper-classes="padding-zero main-section"  :is-body-loading="isPageLoading">
    <div class="add-event-form-hero">
      <loader v-if="isloading" :status="'saving'" />
      <div v-if="!isPageLoading" class="main-content">
        <DynamicHeroEventHeader
          :image-src="image"
          :save-button-text="$t('BUTTONS.CONTINUE')"
          :replace-button-text="$t('REPLACE')"
          :is-replace-live-hero="isReplaceLiveHero"
          :is-campaign-modified="isCampaignModified"
          :event-name="eventName"
          :subtext="subtext"
          :description="description"
          :eventDate="eventDate"
          :isCTALinkIsValid="isCTALinkIsValid"
          :eventCTALinkText="eventCTALinkText"
          :upload-image-percentage="uploadImagePercentage"
          @saveAction="openSavePopup"
          @replaceAction="openReplacePopup"
          @backToDynamicHeroList="backToDynamicHeroList"
        />
        <div class="add-event-intro-section">
          <dynamicHeroEventIntro
            containerClass="add-event-intro-container"
            headingClass="add-event-heading"
            logoClass="add-event-icon"
            :startDateLabel="'Start date:'"
            v-model:selectedDate="selectedDate"
            :isLiveHeroReplaced="isReplaceLiveHero"
            :isRange="false"
            :markers="markers"
            :isLiveHero="false"
            :disabledDates="disabledDates"
            @date-click="handleDateClick"
            @day-hover="call()"
          />
          <div class="image-input-container">
            <processImage
              :image="image"
              :uploadImagePercentage="uploadImagePercentage"
              :loadedImageSize="loadedImageSize"
              :uploadImageSize="uploadImageSize"
              :checkUploadedFiles="checkUploadedFiles"
              :uploadSameImageVideo="uploadSameImageVideo"
            />
            <dynamicHeroEventForm
              baseClass="add"
              @scheduleToggle="scheduleToggle"
              v-model:eventName="eventName"
              :dateValue="selectedDate"
              :isReplaceLiveHero="isReplaceLiveHero"
              v-model:isEventStatus="isEventStatus"
              :isLiveHero="isLiveHero"
              :checkEventName="checkEventName"
              :hideEventTip="hideEventTip"
              :isEventNameInFocus="hasEventNameFocus"
            />
          </div>
        </div>
        <dynamicHeroEventContent
          v-model:description="description"
          v-model:subtext="subtext"
          v-model:eventDate="eventDate"
          v-model:ctaText="ctaText"
          v-model:ctaLink="eventCTALinkText"
          :isCTALinkInprogress="isCTALinkInprogress"
          :isCTALinkIsValid="isCTALinkIsValid"
          :isCTALinkBroken="isCTALinkBroken"
          :isHeroPreview="isHeroPreview"
          @update:isHeroPreview="[(isHeroPreview = $event)]"
          @update:eventCTALinkText="eventCTALinkText = $event"
          @validate-cta-link="validateisCTALinkInprogress"
        />
      </div>
    </div>
    <saveModal
      v-if="isEventDraftPopupVisible"
      :closeModal="closeModal"
      :saveAndPublishFunction="postDynamicFormDataAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="$t('DYNAMIC_HERO.EVENT_DRAFT_FORM')"
      :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduleQuizPopupVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="closeModal"
      @schedule="closeModal"
      :markers="markers"
      :PatchScheduledHero="postDynamicFormDataAsync"
      :selectedDateValue="scheduleDateConfirm"
      @date-click="handleDateClickPopup"
    />
    <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && isOffline"
      :closeModal="closeModal"
      :acceptedFile="' jpg/ jpeg/ png'"
      :video="false"
      :image="true"
      :zip="false"
    />
    <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
    />
    <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
    />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="postDynamicFormDataAsync"
      :closeReplacement="closeModal"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, onMounted, watch, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import { useRefUtils } from "~/composables/useRefUtils";
import { useRoute } from "vue-router";
import saveModal from "@/components/save-modal.vue";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";
import DynamicHeroEventHeader from "../components/pages/dynamic-hero/dynamic-hero-event-header.vue";
import dynamicHeroEventIntro from "../components/pages/dynamic-hero/dynamic-hero-event-intro.vue";
import dynamicHeroEventForm from "../components/pages/dynamic-hero/dynamic-hero-event-form.vue";
import dynamicHeroEventContent from "../components/pages/dynamic-hero/dynamic-hero-event-content.vue";
import replacementModal from "@/components/confirm-replacement-modal.vue";
import loader from "@/components/saving-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import sizeLimit from "@/components/size-limit.vue";
import axios from "axios";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useConnectionStatus } from '~/composables/useConnectionStatus';
import { useInnitAuthStore } from "../stores/innit-auth.js";
import cancelModal from "@/components/cancel-modal";
import processImage from "../components/pages/dynamic-hero/process-image.vue";
import { useProjectLang } from "@/composables/useProjectLang";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";

const heroData = ref([
  {template: 'Event'}
]);
const { watchReactiveValue } = useWatcherUtils();
const { triggerLoading, routeToPage, checkDuplicate, processScheduledElement, isScheduledWithPublishDate, getDisabledDates, getDisableList, useCalendarMarkers } = useCommonUtils();
const { getRef } = useRefUtils();
const { isInnitAdmin } = useInnitAuthStore();
const { readyProject } = useProjectLang();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const store = useStore();
const route = useRoute();
const disableList = ref([]);
const disabledDates = ref([]);
const isloading = ref(false);
const isHeroPreview = ref(false);
const incId = ref(0);
const todos = ref([]);
const heroID = ref("");
const ctaText = ref("");
const { isOffline } = useConnectionStatus();
const uploadImagePercentage = ref(0);
const isPageLoading = ref(false);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const isConfirmModalVisible = ref(false);
const isCampaignModified = ref(false);
const isUploadingImagePopup = ref(false);
const imageFile = ref("");
const cancelImage = ref("");
const imageResponseUrl = ref("");
const scheduleDateConfirm = ref("");
const uploadImageConfirm = ref("");
const isMaxImagePopupVisible = ref(false);
const eventDate = ref("");
const subtext = ref("");
const imageFileName = ref("");
const description = ref("");
const isEventStatus = ref(false);
const eventName = ref("");
const isEventPopupVisible = ref(false);
const isEventDraftPopupVisible = ref(false);
const isScheduleQuizPopupVisible = ref(false);
const isInvalidImageModalVisible = ref(false);
const selectedDate = ref("");
const image = ref("");
const eventCTALinkText = ref("");
const isCTALinkInprogress = ref(false);
const isCTALinkIsValid = ref(false);
const isCTALinkBroken = ref(false);
const isLiveHero = ref(false);
const isReplaceLiveHero = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const isCreateDuplicate = ref(false);
const hasEventNameFocus = ref(false);
const lang = ref("");
const isAdminCheck = ref(false);
const {
  getHeroUUIDAsync,
  dynamicHeroUUIDData,
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  postDynamicHeroDataAsync,
  postDynamicHeroDataList,
} = useDynamicHeroStore();

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
    }
  });
});

const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

  lang.value = store.getters["userData/getDefaultLang"];
  await getDynamicHeroDataAsync();

  const sourceUrl = window.location.href;

  if (sourceUrl.includes("create-duplicate")) {
    isCampaignModified.value = true;
    isCreateDuplicate.value = true;
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    await getEditDynamicHeroDataAsync();
  } else {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }

  if (sourceUrl.includes("replace-live-hero")) {
    isReplaceLiveHero.value = true;
  }
  disabledDates.value = getDisabledDates().value;
  disableList.value = getDisableList().value;
  if (disabledDates.value.length) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString())
    };
    todos.value.push(newTodo);
  }
  incId.value = todos.value.length;
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
  document.addEventListener("DOMContentLoaded", () => {
    const linkInput = document.querySelector("#cta");
    linkInput.addEventListener($keys.KEY_NAMES.INPUT, validateLink);
  });
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;

    if (Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error("Error in getDynamicHeroDataAsync:", error);
  }
};
const handleDateClick = () => {
    isEventStatus.value = true;
    isCampaignModified.value = true;
};
const handleDateClickPopup = (newValue) => {
  selectedDate.value = newValue;
};

const getUUIDAsync = async () => {
  try {
    await getHeroUUIDAsync({ lang: lang.value });
    const response = await dynamicHeroUUIDData.value;
    heroID.value = response?.uuid;
  } catch (error) {
    console.error("Error in getUUIDAsync:", error);
  }
};
const getEditDynamicHeroDataAsync = async () => {
  const uuid = route.query[QUERY_PARAM_KEY.UUID];
  if (uuid) {
    try {
      await getEditPageDynamicHeroDataAsync({
        lang: lang.value,
        uuid
      });
      const response = await editDynamicHeroDataList.value;
      if (Object.keys(response).length) {
        updateHeroState(response);
        updateEventStatus(response);
        updateEventData(response);
      }
      finalizeLoading();
    } catch (error) {
      handleError();
      console.error("Error in getEditDynamicHeroDataAsync:", error);
    }
  }
};
const updateHeroState = (response) => {
  isLiveHero.value = response.state === "live" && !isCreateDuplicate.value;
};
const updateEventStatus = (response) => {
  isEventStatus.value = !!response.publishDate && response.publishDate !== "";
  if (!isCampaignModified.value) {
    selectedDate.value = response.publishDate
      ? getTime(response.publishDate)
      : "";
  }
};
const updateEventData = (response) => {
  eventName.value = response.title || "";
  image.value = response.image || "";
  imageResponseUrl.value = response.image || "";
  heroID.value = response.uuid || "";
  if (response.data) {
    eventCTALinkText.value = response.data.ctaLink || "";
    ctaText.value = response.data.ctaText || "";
    subtext.value = response.data.subtext || "";
    eventDate.value = response.data.dates || "";
    description.value = response.data.body || "";
    if (eventCTALinkText.value) {
      validateisCTALinkInprogress();
    }
  }
};
const finalizeLoading = () => {
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
};
const handleError = () => {
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
};
const getTime = (jsonTimestamp) => {
  const timestamp = jsonTimestamp * 1000;
  const date = new Date(timestamp);
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};
const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  imageFile.value = event.target.files || event.srcElement.files;
  const fileType = imageFile.value[0].type.split("/")[0];
  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};
const uploadFiles = () => {
  isCampaignModified.value = true;
  if (imageFile.value.length) {
    imageFileName.value = imageFile.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!imageFileName.value.match(reg)) {
      imageFile.value = "";
      imageFileName.value = "";
      isInvalidImageModalVisible.value = true;
      return;
    }

    const fileSize = imageFile.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));

    if (size > 1 * 1024 * 1024 && size < 15 * 1024 * 1024) {
      const element = getRef("productVideo");
      element?.blur();
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = imageFile.value;
      imageFile.value = "";
      return;
    } else if (size >= 15 * 1024 * 1024) {
      const element = getRef("productVideo");
      element?.blur();
      imageFile.value = "";
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener(
        "load",
        async () => {
          image.value = reader.result;
          if (image.value) {
            loadedImageSize.value = 0;
            uploadImagePercentage.value = 1;
            await uploadImageAsync();
          }
        },
        false
      );
      if (imageFile.value[0]) {
        reader.readAsDataURL(imageFile.value[0]);
      }
    }
  }
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isEventStatus.value = newStatus;
};
const uploadImageAsync = async () => {
  if (imageFile.value && image.value) {
    if (!heroID.value) {
      await getUUIDAsync();
    }
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      async () => {
        const file = imageFile.value[0];
        const extension = file.type.split("/")[1];
        const params = {
          entity: "article",
          content: "image",
          extension: extension,
          lang: lang.value,
          public: true,
        };

        await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
          isin: heroID.value,
          params,
        });

        const response = store.getters["preSignedUrl/getPreSignedUrl"];
        imageResponseUrl.value = response?.data?.url || "";

        await uploadImageFile(imageResponseUrl.value || "", file);
      },
      false
    );
    if (imageFile.value[0]) {
      reader.readAsDataURL(imageFile.value[0]);
    }
  }
};
const continueImage = () => {
  imageFile.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.addEventListener(
    "load",
    async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
      }
    },
    false
  );
  if (imageFile.value[0]) {
    reader.readAsDataURL(imageFile.value[0]);
  }
};
const uploadImageFile = (url, file) => {
  cancelImage.value =
    axios && axios.CancelToken ? axios.CancelToken.source() : {};
  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunction(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};

const uploadedImageFunction = (data) => {
  if (data == 100) {
    uploadImagePercentage.value = 99;
    setTimeout(() => {
      uploadImagePercentage.value = 100;
    }, 2000);
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = "";
};
const openReplacePopup = () => {
  isReplacementConfirmPopupVisible.value = true;
};

const postDynamicFormDataAsync = async () => {
  isScheduleQuizPopupVisible.value = false;
  isloading.value = true;

  const scheduleDate = calculateScheduleDate();
  const payload = createPayload(scheduleDate);
  try {
    await postDynamicHeroDataAsync({ payload, lang: lang.value });
    const response = await postDynamicHeroDataList.value;
    if (Object.keys(response).length) {
      handleSuccess();
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN}postDynamicFormDataAsync:`, error);
    backToDynamicHeroListConfirm();
  } finally {
    isloading.value = false;
  }
};
const calculateScheduleDate = () => {
  if (isReplaceLiveHero.value) {
    isEventStatus.value = true;
    isReplacementConfirmPopupVisible.value = false;
    selectedDate.value = new Date();
    selectedDate.value.setHours(0, 0, 0, 0);
  }

  if (selectedDate.value) {
    const date = new Date(selectedDate.value);
    return Math.floor(date.getTime() / 1000);
  }

  return '';
};
const createPayload = (scheduleDate) => {
  const payload = {
    publishDate: selectedDate.value && isEventStatus.value ? scheduleDate : '',
    title: eventName.value?.trim() ?? '',
    image: imageResponseUrl.value?.replace(/\?.*/, "") ?? '',
    template: "event",
    state: isEventStatus.value && selectedDate.value ? "scheduled" : "draft",
    preview: isHeroPreview.value ?? false,
    data: {
      body: description.value ?? '',
      image: imageResponseUrl.value?.replace(/\?.*/, "") ?? '',
      subtext: subtext.value ?? '',
      dates: eventDate.value?.trim() ?? '',
      ctaText: ctaText.value ?? '',
      ctaLink: eventCTALinkText.value?.trim() ?? '',
    },
  };

  if (!eventCTALinkText.value.trim()) {
    delete payload.data.ctaLink;
  }
  if (!ctaText.value) {
    delete payload.data.ctaText;
  }

  return payload;
};
const handleSuccess = () => {
  if ((!isEventStatus.value || !selectedDate.value) && !isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("eventSaved");
  } else if (isEventStatus.value && selectedDate.value && !isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("eventScheduled");
  } else if (isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("contentLive");
  } else if (isReplaceLiveHero.value) {
    triggerLoading("heroReplaced");
  }

  backToDynamicHeroListConfirm();
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};

const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
  routeToPage("dynamic-hero");
};
const closeModal = () => {
  scheduleDateConfirm.value = selectedDate.value;
  if (!isReplaceLiveHero.value) {
    isReplaceLiveHero.value = false;
  }
  isReplacementConfirmPopupVisible.value = false;
  isInvalidImageModalVisible.value = false;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  isEventDraftPopupVisible.value = false;
  isEventPopupVisible.value = false;
  isConfirmModalVisible.value = false;
  isScheduleQuizPopupVisible.value = false;
};
const openSavePopup = () => {
  isEventPopupVisible.value = true;
  isEventDraftPopupVisible.value = true;
  isScheduleQuizPopupVisible.value = false;
  if (selectedDate.value !== "" && isEventStatus.value) {
    isEventPopupVisible.value = true;
    isEventDraftPopupVisible.value = false;
    isScheduleQuizPopupVisible.value = true;
    scheduleDateConfirm.value = selectedDate.value;
  }
};
const validateisCTALinkInprogress = () => {
  if (eventCTALinkText.value.trim() === "") {
    isCTALinkInprogress.value = false;
    isCTALinkIsValid.value = false;
    isCTALinkBroken.value = false;
  } else {
    isCTALinkInprogress.value = true;
    checkCTALink();
  }
};

const checkEventName = () => {
  let name = getRef("eventInputField");
  if (
    name?.scrollWidth > name?.clientWidth &&
    name !== document.activeElement &&
    eventName.value.trim().length
  ) {
    hasEventNameFocus.value = true;
  }
};

const hideEventTip = () => {
  hasEventNameFocus.value = false;
};

const handleTypeInput = (event) => {
  const refs = [
    "eventInputField",
    "descriptionNotes",
    "eventSubText",
    "eventDates",
    "newsCTALinkField",
    "newsCTAField"
  ];

  refs.forEach((refName) => {
    if (getRef(refName)?.contains(event.target)) {
      isCampaignModified.value = true;
    }
  });
};
const checkCTALink = () => {
  const urlPattern = /^(https?|ftp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const rocheAppPattern = /^(rocheapp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const linkInput = eventCTALinkText.value.trim();
  if (urlPattern.test(linkInput) || (rocheAppPattern.test(linkInput) && linkInput !== "")) {
    isCTALinkIsValid.value = true;
  } else {
    isCTALinkBroken.value = true;
    isCTALinkIsValid.value = false;
  }
  setTimeout(() => {
    isCTALinkInprogress.value = false;
  }, 1000);
};
watch(isOffline, (offlineStatus) => {
  if (offlineStatus) {
    closeModal();
  }
});
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onBeforeUnmount(() => {
  document.removeEventListener("input", handleTypeInput);
  document.removeEventListener("keyup", handleESCClickOutside);
});

</script>
