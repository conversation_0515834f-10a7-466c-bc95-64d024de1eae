<template>
  <div class="unauthorized">
    You do not have permissions to view this page. Please contact Innit to
    resolve.
    <button class="logout-button" @click="userLogout">Log out</button>
  </div>
</template>

<script setup>
import updateProtocol from "@/mixins/updateProtocol";
import { useContext } from "../composables/useContext.js";

defineComponent({
  mixins: [updateProtocol],
});
const { appLogout } = useContext();

const userLogout = async () => {
  await appLogout();
};
</script>
