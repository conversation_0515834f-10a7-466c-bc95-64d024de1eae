<template>
  <section class="auth auth-signup">
    <div class="auth-signup-container auth-signup-head">
      <div class="auth-signup-head-logo">
        <img alt="Innit logo" src="@/assets/images/innit.png" />
      </div>

      <div class="auth-signup-head-body">
        <div class="auth-signup-left-section">
          <signup-food-lm-not-available></signup-food-lm-not-available>
          <div class="auth-signup-description">
            <div class="auth-signup-description-text color-black font-weight-semi-bold">Trusted AI at the Intersection of Food & Health</div>
          </div>
          <div v-if="isMobile" class="auth-signup-video-mobile-wrapper">
            <video
                id="vjs-mobile-video-player"
                ref="mobileVideoPlayer"
                class="video-js vjs-iq vjs-iq-hide-all-controls"
                webkit-playsinline
                playsinline
                autoplay
                loop
                muted
                controlsList="nodownload noremoteplayback"
            ></video>
          </div>
          <div class="auth-signup-sub-description">
            <div class="auth-signup-sub-description-text text-h2 color-black font-normal">Generate real, great tasting recipes with photorealistic images in minutes.</div>
          </div>
          <signup-action-block classes="auth-signup-action-mt-56" hint-classes="auth-signup-action-hint-black"></signup-action-block>
        </div>
        <div class="auth-signup-right-section">
          <div v-if="!isMobile" v-show="isPlayerReady" class="auth-signup-video-wrapper">
            <video
                id="vjs-video-player"
                ref="videoPlayer"
                class="video-js vjs-iq vjs-iq-hide-all-control"
                playsinline
                autoplay
                loop
                muted
                controlsList="nodownload noremoteplayback"
            ></video>
          </div>
        </div>
      </div>
    </div>

    <div class="auth-signup-container">
      <div class="auth-signup-wrapper">
        <div class="auth-signup-wrapper-head">
          <h2 class="auth-signup-title-2">
            <span class="__featuring font-weight-semi-bold">Featuring</span>
            <img src="@/assets/images/auth-signup-icon-1.png" class="__icon-1" alt="">
            <span class="__food">FoodLM</span>
            <span class="__generator">Generator</span>
          </h2>
        </div>
        <div class="auth-signup-wrapper-body">
          <signup-details></signup-details>
        </div>
      </div>

      <div class="auth-signup-wrapper">
        <div class="auth-signup-wrapper-head">
          <h2 class="auth-signup-title-2">
            <img src="@/assets/images/auth-signup-icon-unlock.png" class="__icon-unlock" alt="" />
            <span class="__unlock">Unlock </span>
            <span class="__food-content font-weight-black">Personalized Food Content</span>
          </h2>
        </div>
        <div class="auth-signup-wrapper-body">
          <signup-unlock></signup-unlock>
        </div>
      </div>
    </div>

    <div class="auth-signup-container auth-signup-costs">
      <div class="auth-signup-wrapper auth-signup-wrapper-mt-0">
        <div class="auth-signup-wrapper-head">
          <h2 class="auth-signup-title-2">
            <img src="@/assets/images/auth-signup-icon-1.png" class="__icon-2" alt="">
            <span class="__slash font-weight-semi-bold">Slash Production Costs up to</span>
            <span class="__p font-weight-semi-bold">90%</span>
          </h2>
        </div>
        <div class="auth-signup-wrapper-body">
          <signup-costs></signup-costs>
        </div>
      </div>
    </div>

    <div class="auth-signup-container auth-signup-green-block">
      <signup-action-block></signup-action-block>
    </div>
    <div class="auth-signup-container auth-signup-block-with-dots"></div>
    <footer class="auth-signup-container auth-signup-light-block">
      <signup-footer></signup-footer>
    </footer>
  </section>
</template>

<script setup>
import { useInnitAuth } from "../composables/useInnitAuth.js";
import videojs from 'video.js';
import updateProtocol from "@/mixins/updateProtocol";
import SignupDetails from "@/components/pages/signup/signup-details.vue";
import SignupUnlock from "@/components/pages/signup/signup-unlock.vue";
import SignupCosts from "@/components/pages/signup/signup-costs.vue";
import SignupFooter from "@/components/pages/signup/signup-footer.vue";
import SignupFoodLmNotAvailable from "@/components/pages/signup/signup-food-lm-not-available.vue";
import SignupActionBlock from "@/components/pages/signup/signup-action-block.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { ref, onMounted, onBeforeMount, getCurrentInstance, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { useNuxtApp } from '#app'
import { useAuth0 } from '@auth0/auth0-vue';
import { useRouter } from 'vue-router';

definePageMeta({
  layout: 'none',
  auth: false,
});

defineComponent({
  mixins: [updateProtocol],
});

const router = useRouter();
const auth = useInnitAuth();
const { isAuthenticated } = useAuth0();
const { $tracker } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const store = useStore();
const isMobile = ref(false);
const isPlayerReady = ref(false);
const player = ref(null);
const videoPlayer = ref(null);
const mobilePlayer = ref(null);
const mobileVideoPlayer = ref(null);
const options = {
  fill: true,
  fluid: true,
  responsive: true,
  muted: true,
  autoplay: true,
  loop: true,
  controls: true,
  disablePictureInPicture: true,
  sources: [{
    src: "https://videos.innit.com/Innit_FoodLM/Innit_FoodLM.m3u8",
    type: "application/x-mpegURL",
  }],
};
const optionsMobile = {
  ...options,
  html5: {
    nativeControlsForTouch: true,
  },
  nativeControlsForTouch: true,
};
onBeforeMount(async () => {
  window.history.pushState({}, document.title, window.location.pathname);

  if (isAuthenticated.value) {
    const project = await getProjectsAsync();
    const path = !(project && project.id) ? "/create-project" : "/overview";
    await router.push({ path });
  }
});
onMounted(() => {
  handleResize();
  $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_LANDING, {}, { ...LOCAL_TRACKER_CONFIG });

  const mediaQuery = window.matchMedia("(orientation: portrait)");
  mediaQuery.addEventListener("change", handleResize);
});
onBeforeUnmount(() => {
  disposeVideoPlayer();
  disposeMobileVideoPlayer();
  removeTryAutoPlayListener();
  removeTryMobileAutoPlayListener();

  const mediaQuery = window.matchMedia("(orientation: portrait)");
  mediaQuery.removeEventListener("change", handleResize);
});
const getProjectsAsync = async () => {
  if (!store.getters["userData/getProject"]) {
    const isAdmin = await auth.checkIsInnitAdminAsync();
    await store.dispatch('userData/fetchProjectsAsync', { isAdmin, isHotRefresh: true });
  }
  return store.getters["userData/getProject"];
};
const handleResize = () => {
  if (document.fullscreenElement) {
    return;
  }

  if (window.innerWidth <= 1023) {
    isMobile.value = true;
    disposeVideoPlayer();
    setTimeout(() => {
      setMobileVideoPlayer();
    });
  } else {
    isMobile.value = false;
    disposeMobileVideoPlayer();
    setTimeout(() => {
      setVideoPlayer();
    });
  }
};
const tryAutoPlay = () => {
  if (player.value) {
    player.value.play();
    removeTryAutoPlayListener();
  }
};
const removeTryAutoPlayListener = () => {
  document.removeEventListener('click', tryAutoPlay);
  document.removeEventListener('touchstart', tryAutoPlay);
};
const setVideoPlayer = () => {
  player.value = videojs(videoPlayer.value, options, () => {
    isPlayerReady.value = true;
  });

  setTimeout(() => {
    videoPlayer.value?.parentElement?.classList?.remove("vjs-iq-hide-all-control");
    isPlayerReady.value = true;
  }, 5000);

  // try to handle Low Power mode on laptop or work from battery on laptop
  player.value.ready(() => {
    const promise = player.value.play();

    if (promise !== undefined) {
      promise.catch(() => {
        document.body.addEventListener('click', tryAutoPlay);
        document.body.addEventListener('touchstart', tryAutoPlay);
      });
    }
  });
  player.value.play();
};
const tryMobileAutoPlay = () => {
  if (mobilePlayer.value) {
    mobilePlayer.value.play();
    removeTryMobileAutoPlayListener();
  }
};
const removeTryMobileAutoPlayListener = () => {
  document.removeEventListener('click', tryMobileAutoPlay);
  document.removeEventListener('touchstart', tryMobileAutoPlay);
};
const setMobileVideoPlayer = () => {
  mobilePlayer.value = videojs(mobileVideoPlayer.value, optionsMobile);

  setTimeout(() => {
    mobileVideoPlayer.value?.parentElement?.classList?.remove("vjs-iq-hide-all-control");
  }, 5000);

  // try to handle Low Power mode on iOS, also in iOS autoplay does not work as expected, we need some action from user
  mobilePlayer.value.ready(() => {
    const promise = mobilePlayer.value.play();

    if (promise !== undefined) {
      promise.catch(() => {
        document.body.addEventListener('click', tryMobileAutoPlay);
        document.body.addEventListener('touchstart', tryMobileAutoPlay);
      });
    }
  });
  mobilePlayer.value.play();
};
const disposeVideoPlayer = () => {
  if (player.value) {
    player.value.dispose();
  }
};
const disposeMobileVideoPlayer = () => {
  if (mobilePlayer.value) {
    mobilePlayer.value.dispose();
  }
};
</script>
