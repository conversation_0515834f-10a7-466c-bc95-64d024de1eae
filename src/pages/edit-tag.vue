<template>
<client-only>
  <content-wrapper class="padding-zero">
    <div class="top-section" v-show="!isTagSaving">
      <div class="back-button-container">
        <div @click="backToTagBtn()" class="tag-back-button">
          <img alt="" src="@/assets/images/back-arrow.png" />
          <p class="back-button-text text-title-2">{{ $t('TAG.BACK_TO_TAGS') }}</p>
        </div>
        <div class="tag-button-section">
          <button
            type="button"
            @click="backToTagBtn()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            class="btn-green-outline"
          >{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
          <button type="button"
            @click="displayPopup()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            :class="
              isCampaignModified && tagName.trim() !== '' && !isRecipeVariantNameEmpty
                ? 'btn-green'
                : 'disabled-button btn-green'
            "
          >{{ tagStatus == 'active' ? 'Publish' : 'Save' }}</button>
        </div>
      </div>
			<div Class="edit-tag-isin text-title-2 font-normal">ISIN: {{ tagIsin }}</div>
    </div>
   <div class="tag-containers" v-show="!isTagSaving">
      <div class="tag-content-mains">
        <div class="tag-form-titles">
          <p class="tag-form-texts">Tag Form
          </p>
          <span class="right-section">
          <div
								:class="((tagState === 'published') || (tagState === 'publishing')) ? 'publish-btn' : 'publish-btn'">
							<span class="text text-title-2">
								{{ $t('COMMON.PUBLISH')  }}
							</span>
							<label class="switch">
								<input type="checkbox" :checked="tagStatus == 'active'"
                @click.prevent="tagName.length ? publishToggleBtnAsync() : publishToggleBtnPopup()">
								<span class="slider-round"></span>
							</label>
						</div>
           </span>
        </div>
        <div class="add-title-container">
          <div
            class="tag-name-input-section"
            :class="{
              'simple-data-tooltip': isTagNameFocus,
            }"
            :data-tooltip-text="isTagNameFocus && tagName"
          >
            <input
              type="text"
              id="tagName"
              autocomplete="off"
              class="edit-title-text text-h2"
              v-model.trim="tagName"
              placeholder="Add a title"
              @mouseover="checkTagName()"
              @keydown="hidetagTip()"
              @mouseleave="hidetagTip()"
              @input="hidetagTip()"
            />
            <img alt="compulsory"
              v-if="!tagName"
              class="compulsory-feild-tags"
              src="@/assets/images/asterisk.svg?skipsvgo=true"
            />
          </div>
          <div
            class="delete-btn"
            :class="{
              'simple-data-tooltip simple-data-tooltip-warn': totalTags,
            }"
          >
            <div class="simple-data-tooltip-content" v-if="totalTags">
              <img src="@/assets/images/info.svg?skipsvgo=true" alt="info-icon" class="tooltip-icon" />
              <span>{{ $t('TAG.TAGS_TOOLTIP') }}</span>
            </div>

            <button type="button" class="btn-reset"
              :id="(totalTags === 0) && !isTagInCollection ? 'enable-button' : 'disable-button'"
              @click="openDeleteModal()">
              <div class="delete-tag">
                <img alt="delete-icon" class="image" src="@/assets/images/delete-icon.png" />
                <span class="delete-text">{{ $t('TAG.DELETE_TAG') }}</span>
              </div>
            </button>
          </div>
        </div>
        <div v-if="finalAvailableLangs && finalAvailableLangs.length > 1" class="tag-variant-section">
					<div class="tag-variants-main">
						<div class="tag-variants">
						Tag Variants:
              <div v-show="isTagAlertIcon" class="tag-variant-tooltip-section">
                <div
                  class="tooltip-main-container-for-tag-variant simple-data-tooltip"
                  :data-tooltip-text="langVariantTooltip"
                >
                  <img alt="" class="alert-image" src="@/assets/images/red-info.svg?skipsvgo=true" >
                </div>
              </div>
						</div>
            <div
              class="add-variant-section"
              :class="{
                'simple-data-tooltip': !!recipeVariantList.length,
              }"
              :data-tooltip-text="!!recipeVariantList.length && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
            >
              <div
                class="add-variant-main"
                :class="{
                  'disable-add-variant-main': !!recipeVariantList.length
                }"
                @click="!!recipeVariantList.length ? '' : openTagVariantPopUp()"
              >
                <div class="add-variant-btn">
                  <img alt="" src="@/assets/images/category-add.png">
                </div>
                <div class="add-variant-text">
                  Add variant
                </div>
              </div>
          </div>
					</div>
					<div
            v-if="recipeVariantList.length < 1 || isEditTagMessageEnable"
            class="add-tag-variant"
          >
						Add tag variants to support multiple languages.
					</div>
					<div v-else class="tag-variant-card-main">
            <template v-for="(categoryVariant, index) in recipeVariantList">
              <variant-card-field
                v-if="categoryVariant?.lang !== lang"
                v-model="categoryVariant.name"
                :prefix-label="displayLanguageCode(categoryVariant.lang)"
                input-placeholder="Enter name"
                :is-delete-action-disabled="isDeleteVariantVisible(categoryVariant)"
                delete-action-tooltip-text="Cannot delete because tag variant is used by recipes"
                @input-change="inputContentChanged()"
                @delete-action="deleteTagVariant(categoryVariant, index)"
              ></variant-card-field>
            </template>
					</div>
				</div>
      </div>
    </div>
    <div v-show="!isTagSaving">
    <div class="tag-recipes-table-content">
      <div class="content">
        <div class="recipe-tag">
          <div class="recipe-header-section">
            <div v-if="!isSearchExitEnable">
              <div class="recipe-header text-h2" v-if="(tagsTotal) <= 1">
                {{tagsTotal}} Recipe in Tag
              </div>
              <div class="recipe-header text-h2" v-if="(tagsTotal) > 1">
                {{tagsTotal}} Recipes in Tag
              </div>
            </div>
            <div v-if="isSearchExitEnable">
              <div class="recipe-header text-h2">
                Search results
              </div>
            </div>
            <div v-if="!isSelectionEnabled" class="search-section" :class="{'disable-content': isDataLoading}" >
              <div v-if="!isSearchExitEnable && recipeDataForTag.length" class="btn-green-text btn-small">
                <span  @click="selectProducts()">{{ $t('COMMON.SELECT')  }}</span>
              </div>
              <div class="search-box">
                <input
                  type="text"
                  class="search-input-box"
                  autocomplete="off"
                  placeholder="Search for recipe name"
                  v-model.trim="queryRecipe"
                  @keypress.enter="searchRecipeList()"
                  :class="{ 'align-search-input-box': queryRecipe }"
                />
                <img alt=""
                  class="search-icon-green-image"
                  @click="searchRecipeList()"
                  src="@/assets/images/search-icon-green.png"
                />
                <img alt=""
                  class="exit-search-icon"
                  v-if="isSearchExitEnable"
                  @click="resetQuery()"
                  src="@/assets/images/exit-gray.png"
                />
              </div>
              <div class="add-btn" @click="addRecipe()">
                <img alt="" class="add-image" src="@/assets/images/category-add.png" />
                <span class="text text-h3">
                  {{$t('COMMON.ADD_RECIPE') }}
                </span>
              </div>
            </div>
          </div>
          <simple-sticky-wrapper
            v-if="isSelectionEnabled"
            :top="70"
            :distance="-60"
            class="edit-selection-container"
          >
            <div class="edit-selection-panel">
              <div class="edit-select-all-checkbox-section">
                <label
                  class="edit-checkbox-section checkbox"
                >
                  <input
                    type="checkbox"
                    :checked="selectionOfRecipes[0].isSelected"
                    @click="selectAllMatches()"
                  />
                  <span class="checkmark"></span>
                </label>
              </div>
              <button
                type="button"
                @click="selectAllMatches()"
                class="btn-reset text-h3"
              >
                {{ $t("PAGE.RECIPES.SELECT_ALL") }}
              </button>
              <div class="edit-selection">
                <div class="edit-selected-text">
                  {{ checkSelectedRecipes }} {{ $t("COMMON.SELECTED") }}
                  <span
                    v-if="checkSelectedRecipes > 0"
                    class="edit-selected-cross-icon"
                  >
                    <img
                      src="@/assets/images/close.svg?skipsvgo=true"
                      @click="removeAllSelected()"
                      alt="edit-close-icon"
                    />
                  </span>
                </div>
              </div>
              <div class="edit-btn-container">
                <button
                  type="button"
                  class="btn-red"
                  :disabled="checkSelectedRecipes == 0"
                  @click="deleteSelect()"
                >
                  {{ $t("BUTTONS.REMOVE_BUTTON") }}
                </button>
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="cancelSelect()"
                >
                  {{ $t("BUTTONS.CANCEL_BUTTON") }}
                </button>
              </div>
            </div>
          </simple-sticky-wrapper>
          <div class="recipe-table-content">
            <div
              class="add-zero-section tag-recipe-section"
              v-if="recipeDataForTag.length == 0 && !isSearchExitEnable && !isDataLoading"
            >
              <div class="zero-promoted">
                <span class="bold">
                  0 RECIPE IN TAG.
                </span>
                <span class="normal">
                  Add recipes in tag.
                </span>
              </div>
            </div>
            <div class="no-result-for-tag text-title-2" v-if="(tagsTotal) == 0 && isSearchExitEnable">
              {{ $t('COMMON.NO_RESULT_FOUND') }}
            </div>
            <div class="loader" v-if="isDataLoading">
              <loader />
            </div>
            <table
              class="recipe-table"
              id="recipeTable"
              v-if="recipeDataForTag && !isDataLoading"
            >
            <caption></caption>
          	<thead class="table-head" v-if="(tagsTotal) != 0 &&recipeDataForTag.length">
              <tr class="title">
                <th></th>
                <th></th>
                <th class="category-group-isin"><span>{{ $t('COMMON.RECIPE_ISIN') }} </span></th>
                <th class="category-group-title"><span>{{ $t('COMMON.RECIPE_TITLE') }}</span></th>
                <th class="category-group-count"><span>{{ $t('COMMON.TOTAL_TIME') }}</span></th>
                <th class="ing-count"><span>{{ $t('COMMON.INGREDIENT_COUNT') }}</span></th>
                <th></th>
              </tr>
            </thead>
              <tbody :style="{ cursor: isSelectionEnabled ? 'pointer' : 'default' }">
                <tr
                  @click="selectMatchToDelete(index,recipe)" :id="recipe.isdeleteSelected ? 'delete-selected':''"
                  class="body"
                  v-for="(recipe, index) in recipeDataForTag"
                  :key="index"
                 style="position: relative;"
                  v-show="!recipe.isSearched"
                  :class="{'recipe-selected-color-category' : isSelectionEnabled && recipe.isSelectedToDelete}"
                >
                  <td>
                    <div v-if="isSelectionEnabled" class="tag-table-checkbox-wrapper edit-product-table-checkbox">
                      <div id="selectAllCheckboxId"  class="edit-select-all-checkbox-section">
                        <label class="edit-checkbox-section checkbox">
                            <input @click="selectMatchToDelete(index,recipe)" :checked="recipe.isSelectedToDelete"  type="checkbox" >
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    </div>
                  </td>
                  <td class="table-image-recipe">
                     <div class="image-recipe" v-if="recipe.media">
                        <img alt="" v-if="recipe.media && recipe.media[lang] && recipe.media[lang].image && !recipe.media[lang].externalImageUrl" class="image" :src="recipe.media && recipe.media[lang] && recipe.media[lang].image ? recipe.media[lang].image : '' "/>
                        <img alt="" v-if="recipe.media && recipe.media[lang] && recipe.media[lang].externalImageUrl" class="image" :src="recipe.media && recipe.media[lang] && recipe.media[lang].externalImageUrl ? recipe.media[lang].externalImageUrl : ''" @error="$event.target.src=`${defaultImage}`"/>
                        <img alt="" v-if="(!recipe.media || !recipe.media[lang] || !recipe.media[lang].image) && ( !recipe.media || !recipe.media[lang] || !recipe.media[lang].externalImageUrl)" class="image" :src="defaultImage"/>
                      </div>
                    <div class="image-recipe" v-if="recipe.image">
                      <img alt=""
                        class="image"
                        :src="
                          recipe.image.url
                            ? recipe.image.url
                              ? recipe.image.url
                              : defaultImage
                            : ''
                        "
                      />
                    </div>
                    <div class="image-recipe" v-if="!recipe.image&&!recipe.media">
                      <img alt=""
                        class="image"
                        :src="defaultImage"
                      />
                    </div>
                  </td>
                  <td class="table-recipe-code">
                    <div class="recipe-code text-light-h4">
                      {{ recipe.isin ? recipe.isin : "" }}
                    </div>
                  </td>
                  <td class="tag-recipe-name">
                    <div class="recipes-title-wrapper">
                      <div
                        class="recipes-title font-size-14 font-bold color-black"
                        v-tooltip-if-overflow="recipe.title[lang]"
                      >
                        <p class="simple-data-tooltip-text">{{ recipe.title[lang] }}</p>
                      </div>
                      <div
                        v-if="recipe?.langs?.length > 1"
                        class="simple-data-tooltip text-light-h4"
                        :data-tooltip-text="getAvailableLanguagesTooltip()"
                      >
                        <img alt="globe" src="@/assets/images/language-icon.png" >
                      </div>
                    </div>
                    <div class="recipe-name text-h3" v-if="!recipe?.title?.[lang]">
                      {{ recipe?.title ?? "" }}
                    </div>
                    <div class="recipe-subtitle text-h3 font-normal" v-if="recipe?.subtitle?.[lang]">
                      <span>
                        {{ recipe?.subtitle?.[lang] || ""}}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="recipe-details">
                      <div class="details text-h3 font-normal">
                        <span
                          v-if="recipe.time && parseDurationString(recipe.time.total)"
                          >{{
                            parseDurationString(recipe.time.total ? recipe.time.total : "")
                              ? parseDurationString(
                                  recipe.time.total ? recipe.time.total : ""
                                )
                              : ""
                          }}</span
                        >
                        <span v-if="!(recipe.time && recipe.time.total)">none</span>
                      </div>

                    </div>
                  </td>
                  <td> <div class="ingredient-count text-h3 font-normal">
                        <span
                          v-if="
                            recipe.ingredients &&
                              recipe.ingredients[lang] &&
                              recipe.ingredients[lang].length == 1
                          "
                          >{{
                            recipe.ingredients && recipe.ingredients[lang]
                              ? recipe.ingredients[lang].length
                              : ""
                          }}
                          ingredient</span
                        >
                        <span
                          v-if="
                            recipe.ingredients &&
                              recipe.ingredients[lang] &&
                              recipe.ingredients[lang].length > 1
                          "
                          >{{
                            recipe.ingredients && recipe.ingredients[lang]
                              ? recipe.ingredients[lang].length
                              : ""
                          }}
                          ingredients</span
                        >
                      </div></td>
                  <td class="tag-published">
                    <div v-if="!isSelectionEnabled" class="menu">
                      <div :class="recipe.dropDown ? 'menu-container menu-selected' : 'menu-container'" @click="displayOption(recipe)">
                        <img alt="" v-if="recipe.dropDown" class="table-edit-btn" src="@/assets/images/green-edit-btn.svg?skipsvgo=true"/>
                        <img alt="" v-if="!recipe.dropDown" class="table-edit-btn" src="@/assets/images/edit-btn.svg?skipsvgo=true"/>
                      </div>
                      <div class="menu-box" v-if="recipe.dropDown">
                        <ul class="menu-list text-title-2">
                          <li @click="editRecipe(recipe.isin)">{{ $t('BUTTONS.PREVIEW_BUTTON') }}</li>
                          <li @click="openRemoveModal(recipe, index)">{{  $t('BUTTONS.REMOVE_BUTTON')  }}</li>
                        </ul>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="pagination-container">
      <paginate
        id="pagination-block"
        v-if="tagsTotal>sizeTags && recipeDataForTag.length"
        v-model="currentPage"
        :total-rows="tagsTotal"
        :page-range="pageRange"
        :per-page="sizeTags"
        :page-count=Math.ceil(tagsTotal/sizeTags)
        first-button-text="<<"
        prev-text="<"
        next-text=">"
        last-button-text=">>"
        :prev-class="'prev'"
        :next-class="'next'"
        :first-last-button=true
        :click-handler="pageChangeAsync"
        :container-class="'pagination'"
        :page-class="'page-item'"
        :page-link-class="'page-link'"
        :disabled-class="'disabled-pagination'"
        :active-class="'active'"
        :margin-pages="0"
      >
      </paginate>
    </div>
    <unpublishModal
      v-if="isUnPublishModalVisible"
      :description="'Do you want to unpublish this tag?'"
      :noteMessage="''"
      :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
      :unpublishFunction="unPublishConfirm"
      :closeModal="closeModal"
    />
    <saveModal v-if="isPublishModalVisible"
      :closeModal="closeModal"
      :saveAndPublishFunction="publishConfirm"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
      :description="$t('DESCRIPTION_POPUP.PUBLISH_POPUP')"
      :imageName=unpublishedImage
    />
    <addRecipeModal
      v-if="isAddRecipeModal"
      @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
      @closeModal="closeModal"
      :recipeDataForCategories="recipeDataForTag"
      :selectedCategoryRecipe="selectedTagRecipe"
      @campaignModifiedAddRecipe="campaignModifiedAddRecipe"
      :isEditTag="isEditTag"
      :recipesAfterPageChange="recipesAfterPageChange"
      :recipeMatchesIsinsTagRemove="recipeMatchesIsinsTagRemove"
      :removeRecipeTagList="removeRecipeTagList"
      @getRecipeDataForTag="getRecipeDataForTagAsync"
    />
    <deleteModal
      v-if="isDeleteModalVisible"
      :closeModal="closeModal"
      :productInfoTitle="$t('DESCRIPTION_POPUP.DELETE_TAG')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
      :productDescriptionTwo="$t('DESCRIPTION_POPUP.TAG')"
      :deleteItem="deleteTag"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.DELETE_BUTTON')"
    />
    <deleteModal
      v-if="isRemoveModalVisible"
      :closeModal="closeModal"
      :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPE')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPE_POPUP')"
      :productDescriptionTwo="$t('DESCRIPTION_POPUP.TAG')"
      :deleteItem="deleteInstructionConfirm"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
    />
    <saveModal
      v-if="isSaveModalVisible && tagStatus != 'active'"
      :closeModal="closeModal"
      :saveAndPublishFunction="saveButtonClickAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
      :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
    />
    <saveModal
      v-if="isSaveModalVisible && tagStatus == 'active'"
      :closeModal="closeModal"
      :saveAndPublishFunction="saveButtonClickAsync"
      :availableLang="[]"
      :buttonName="'Publish'"
      :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"
      :imageName="'@/assets/images/publish-variant-icon.png'"
    />
    <cancelModal
    v-if="isConfirmModalVisible"
    :availableLang="[]"
    :isCampaignModifiedFromShoppableReview="false"
    :callConfirm="backToTag"
    :closeModal="closeModal"
    />
    <Modal v-if="hasOpenPreviewRecipe" @close="closeModal">
      <template #noProductMatches>
        <div class="open-preview-recipe-modal">
          <div class="recipe-main-preview-header">
            <div class="recipe-preview-header text-h2">{{ $t('COMMON.RECIPE_PREVIEW') }}</div>
          </div>
          <img alt=""
            class="close-preview-recipe-modal"
            @click="closeModal"
            src="@/assets/images/exit-gray.png"
          />
          <div class="open-preview-recipe-main">
            <recipePreviewDetail :rISIN="recipeIsin"
            :checkRecipePreviewVideo="checkRecipePreviewVideo"
            ></recipePreviewDetail>
          </div>
        </div>
      </template>
    </Modal>
    <selectTheLanguageModal
      v-if="hasEditTagVariantLanguagePopUp"
      :closeModal="closeModal"
      @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
      @nextVariantPopUp="nextTagVariantNameModalPopUp"
      @setRecipeVariantLanguageMatches="setTagVariantLanguageMatches"
      @showRecipeVariantLanguageMatches="showTagVariantLanguageMatches"
      :recipeVariantLanguageList="recipeVariantLanguageList"
      :hasRecipeVariantLanguageResult="hasEditTagVariantLanguageResult"
    />
    <addVariant
      v-if="isEditVariantNamePopup"
      :closeModal="closeModal"
      :typeName="'Tag'"
      :addVariantSelectedLanguage="editTagVariantSelectedLanguage"
      :itemName="tagName"
      @addConfirmVariant="addTagVariant"
      @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
      @backToRoute="backToSelectLanguageTagVariantPopUp"
    />
    <deleteModal
      v-if="isRemoveAddTagVariantVisible"
      :closeModal="closeModal"
      :productInfoTitle="'Remove Tag Variant?'"
      :productDescriptionOne="'Are you sure you want to remove this variant from the'"
      :productDescriptionTwo="$t('DESCRIPTION_POPUP.TAG')"
      :deleteItem="removeTagVariant"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
    />
    <deletingModal v-show="isDeletingModalVisible" />
    <savingModal
      v-show="isTagPublishing"
      :status="tagStatus == 'active' ? 'publishing' : 'saving'"
    />
    <unableToContentModal
        v-if="isUnableToPublishArticle"
          :closeModal="closeModal"
          :text="$t('TEXT_POPUP.NOT_PUBLISHED')"
      />
    <unableToUnpublish
      v-if="isDisplayWarningPopupConfirm"
      :title="'Unable to Unpublish. This tag is associated to one or more tag groups.'"
      :description="'Please remove all associations to unpublish in the Search filter page.'"
      :closeModal="closeModal"
    />
    <deleteModal
    v-if="isSelectDeleteModalVisible"
    :closeModal="closeModal"
    :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPES')"
    :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPES_POPUP')"
    :productDescriptionTwo="$t('DESCRIPTION_POPUP.TAG')"
    :deleteItem="deleteSelectProductMatches"
    :availableLanguage="0"
    :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
  />
    </div>
    <div class="loader-main-container" v-show="isTagSaving" @close="closeModal">
      <loader />
    </div>
  </content-wrapper>
</client-only>
</template>
<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  getCurrentInstance,
} from "vue";
import savingModal from "@/components/saving-modal";
import deletingModal from "@/components/deleting-modal";
import cancelModal from "@/components/cancel-modal";
import saveModal from "@/components/save-modal";
import unableToContentModal from "@/components/unable-to-content-modal";
import deleteModal from "@/components/delete-modal";
import addVariant from "@/components/add-variant";
import selectTheLanguageModal from "@/components/select-the-language";
import CategoriesService from "@/services/CategoriesService";
import Modal from "@/components/Modal";
import addRecipeModal from "@/components/add-recipe-modal.vue";
import recipePreviewDetail from "@/components/recipe-preview-detail";
import unableToUnpublish from "@/components/unableToUnpublish";
import Search from "@/services/Search";
import unpublishModal from "@/components/unpublish-modal";
import unpublishedImage from "@/assets/images/unpublished.png";
import { useRefUtils } from "@/composables/useRefUtils";
import { useEventUtils } from "@/composables/useEventUtils";
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useProjectLang } from "@/composables/useProjectLang";
import { useTimeUtils } from "@/composables/useTimeUtils";
import loader from "@/components/loader";
import defaultImage from "~/assets/images/default_recipe_image.png";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import { useNuxtApp } from "#app";
import { useI18n } from "vue-i18n";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
import SimpleStickyWrapper from "@/components/simple-sticky-wrapper.vue";

const store = useStore();
const router = useRouter();
const route = useRoute();
const project = ref({});
const { readyProject, getProject } = useProjectLang();
const { triggerLoading, routeToPage, scrollToTop } = useCommonUtils();
const { $tracker, $eventBus, $auth } = useNuxtApp();
const { t } = useI18n();
const { parseDurationString } = useTimeUtils();
const { getRef } = useRefUtils();
const { preventEnterAndSpaceKeyPress, onEscapeKeyPress } = useEventUtils();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const collectionData = ref([]);
const isTagInCollection = ref(false);
const isDataLoading = ref(false);
const isEditTag = ref(true);
const langVariantTooltip = "Some recipes have a language variant. Add the language variant of the tag.";
const isCheckVideoPreview = ref(false);
const copySearch = ref("");
const isDeletingModalVisible = ref(false);
const isUnableToPublishArticle = ref(false);
const isCheckSearch = ref(false);
const isTagNameFocus = ref(false);
const isTagAlertIcon = ref(false);
const isRemoveAddTagVariantVisible = ref(false);
const isTagPublishing = ref(false);
const showLoader = ref(false);
const isPublishModalVisible = ref(false);
const isUnPublishModalVisible = ref(false);
const tagState = ref("");
const tagStatus = ref("hidden");
const tagIsin = ref("");
const isDeleteModalVisible = ref(false);
const isRemoveModalVisible = ref(false);
const isSaveModalVisible = ref(false);
const isAddRecipeModal = ref(false);
const isConfirmModalVisible = ref(false);
const isSearchPopupExitEnable = ref(false);
const isCampaignModified = ref(false);
const queryPopUp = ref("");
const tagName = ref("");
const hasOpenPreviewRecipe = ref(false);
const recipeIsin = ref("");
const queryText = ref("");
const queryRecipe = ref("");
const sizeTags = ref(10);
const pageRange = ref(6);
const fromTags = ref(0);
const isSearchExitEnable = ref(false);
const recipeDataForTag = ref([]);
const selectedTagRecipe = ref([]);
const tagsTotal = ref(0);
const totalTags = ref(0);
const currentPage = ref(1);
const recipeMatchesIsinsTagRemove = ref([]);
const removeRecipeTagList = ref([]);
const removeRecipeTagData = ref({});
const removeRecipeTagIndex = ref(0);
const operationStatusDetails = ref("");
const recipesAfterPageChange = ref([]);
const dropdownItem = ref([]);
const isAddVariantTagNamePopUp = ref(false);
const hasEditTagVariantLanguagePopUp = ref(false);
const hasEditTagVariantLanguageResult = ref(false);
const recipeVariantLanguage = ref("");
const editTagVariantSelectedLanguage = ref("");
const recipeVariantLanguageList = ref([]);
const isEditVariantNamePopup = ref(false);
const variantName = ref("");
const recipeVariantList = ref([]);
const editTagVariantDataIndex = ref("");
const isGlobeIconPresent = ref(false);
const selectedDefaultLang = ref([]);
const isDeletedvariant = ref(false);
const saveRemovedTagVariants = ref([]);
const finalAvailableLangs = ref([]);
const initiallyVariantSupported = ref([]);
const isEditTagMessageEnable = ref(false);
const isTagSaving = ref(true);
const isTagLoading = ref(false);
const isRecipeVariantNameEmpty = ref(false);
const tagAssociations = ref({});
const isDisplayWarningPopup = ref(false);
const isDisplayWarningPopupConfirm = ref(false);
const selectionOfRecipes = ref([
  {
    isSelected: false,
  },
]);
const isSelectionEnabled = ref(false);
const selectedProducts = ref([]);
const isSelectDeleteModalVisible = ref(false);
const isAbortedCheckingOperationStatus = ref(false);

const lang = computed(() => store.getters["userData/getDefaultLang"]);
const checkSelectedRecipes = computed(() => {
  return selectedProducts.value.filter((data) => data.isSelectedToDelete)
    .length;
});

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isTagSaving.value = true;
      $eventBus.emit("routeloading", isTagSaving.value);
      project.value = await getProject();
      await fetchCollectionDataAsync();
      isEditTag.value = true;
      finalAvailableLangs.value = store.getters["userData/getAvailableLangs"];
      $eventBus.on("backButtonConfirm", () => {
        backToTagConfirm();
      });
      $eventBus.on("closeModal", () => {
        closeModal();
      });
      $eventBus.on("getRecipeDataForTag", () => {
        getRecipeDataForTagAsync();
      });
      $eventBus.on("campaignModifiedAddRecipe", () => {
        campaignModifiedAddRecipe();
      });
      await getTagDataAsync();
      getTagDataFlite();
      currentPage.value = 1;
      pageChangeAsync(currentPage.value);

      if (finalAvailableLangs.value) {
        finalAvailableLangs.value.forEach((lang) => {
          let langData = {};
          if (lang == "es-US") {
            langData = {
              language: lang,
              language_name: "Spanish",
              languageFlag: "/images/flags/spain-flag.png",
            };
          } else if (lang == "fr-FR") {
            langData = {
              language: lang,
              language_name: "French",
              languageFlag: "/images/flags/france-flag.png",
            };
          }
          if (!selectedDefaultLang.value.includes(lang) && Object.keys(langData).length) {
            recipeVariantLanguageList.value.push(langData);
          }
        });
      }
      await getTagAssociations();
      await getEditSearch();
      document.addEventListener("click", handleClickOutside);
      document.addEventListener("click", handleClickOutsidePopup);
      document.addEventListener("input", handleTypeInput);
    }
  });
});
onBeforeUnmount(() => {
  document.removeEventListener("scroll", checkScrollPosition);
  document.removeEventListener("input", handleTypeInput);
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("click", handleClickOutsidePopup);
});
const fetchCollectionDataAsync = async () => {
  await store.dispatch("collection/getCollectionDataAsync", { lang: lang.value });
  collectionData.value = store.getters["collection/getCollectionData"];
};

const isDeleteVariantVisible = (categoryVariant) => {
  const isTagAssociationTrue =
    tagAssociations.value && tagAssociations.value[categoryVariant.lang] > 0;
  const isRecipeVariantTrue = isGlobeIconPresent.value;
  if (!isTagAssociationTrue && isRecipeVariantTrue) {
    return true;
  } else if (!isTagAssociationTrue || !isRecipeVariantTrue ) {
    return false;
  } else if (!isTagAssociationTrue || isRecipeVariantTrue) {
    return true;
  } else if (isTagAssociationTrue || !isRecipeVariantTrue) {
    return true;
  }
};

const removeAllSelected = () => {
  recipeDataForTag.value.forEach((item) => {
    item.isSelectedToDelete = false;
  });
  selectionOfRecipes.value[0].isSelected = false;
  selectedProducts.value = [];
};

const selectAllMatches = () => {
  const isSelectAll = !selectionOfRecipes.value[0].isSelected;
  selectionOfRecipes.value[0].isSelected = isSelectAll;

  selectedProducts.value = isSelectAll ? [...recipeDataForTag.value] : [];

  recipeDataForTag.value.forEach((item) => {
    item.isSelectedToDelete = isSelectAll;
  });
};

const selectMatchToDelete = (data, product) => {
  if (!isSelectionEnabled.value) {
    return;
  }

  const selectedItem = recipeDataForTag.value[data];
  selectedItem.isSelectedToDelete = !selectedItem.isSelectedToDelete;

  if (selectedItem.isSelectedToDelete) {
    selectedProducts.value.push(selectedItem);
  } else {
    const index = selectedProducts.value.findIndex((insideData) => insideData?.isin === product?.isin);
    if (index !== -1) {
      selectedProducts.value.splice(index, 1);
    }
  }

  checkSelected();
};


const deleteSelectProductMatches = () => {
  selectedProducts.value.forEach((data) => {
    if (data.isSelectedToDelete) {
      removeRecipeTagList.value.push(data);
      recipeMatchesIsinsTagRemove.value.push(data.isin);
    }
  });

  recipesAfterPageChange.value = recipesAfterPageChange.value.filter(
    (data) => !recipeMatchesIsinsTagRemove.value.includes(data.isin)
  );

  selectedTagRecipe.value = selectedTagRecipe.value.filter(
    (data) => !recipeMatchesIsinsTagRemove.value.includes(data.isin)
  );

  isCampaignModified.value = true;
  currentPage.value = 1;
  pageChangeAsync(currentPage.value);
  closeModal();
  triggerLoading($keys.KEY_NAMES.DELETED);
  selectedProducts.value = [];
  selectionOfRecipes.value[0].isSelected = false;
  isSelectionEnabled.value = false;
};

const checkSelected = () => {
  const count = recipeDataForTag.value.filter(
    (item) => item.isSelectedToDelete
  ).length;
  selectionOfRecipes.value[0].isSelected =
    count === recipeDataForTag.value.length;
};

const cancelSelect = () => {
  isSelectionEnabled.value = false;
  selectedProducts.value = [];
  if (recipeDataForTag.value.length > 0) {
    selectionOfRecipes.value[0].isSelected = false;
    recipeDataForTag.value.forEach((item) => {
      item.isSelectedToDelete = false;
    });
  }
};

const selectProducts = () => {
  isSelectionEnabled.value = true;
};

const checkScrollPosition = () => {
  if (isSelectionEnabled.value) {
    const ele = document.querySelector("#recipeTable");
    if (
      ele.getBoundingClientRect().top < 0 &&
      recipeDataForTag.value.length >= 4
    ) {
      changeSelectbarPosition();
    }
  }
};

const changeSelectbarPosition = () => {
  const ele = document.querySelector(".edit-category-selection-container");
  const deletebtn = document.querySelector(".edit-category-btn-container");
  const cancelbtn = document.querySelector(".edit-category-cancel-btn");
  const selectText = document.querySelector(".edit-category-selected-text");
  const selectAll = document.querySelector(".edit-category-select-all-text");
  const selectBox = getRef("selectAllCheckboxId");
  const selectionPanel = document.querySelector(
    ".edit-category-selection-panel"
  );

  if (isSelectionEnabled.value) {
    ele.style.backgroundColor = "#FFFFFF";
    ele.style.height = "64px";
    ele.style.alignItems = "center";
    ele.style.paddingTop = "inherit";
    ele.style.position = "fixed";
    ele.style.zIndex = "999";
    ele.style.top = "60px";
    ele.style.width = "-webkit-fill-available";
    ele.style.marginLeft = "-20px";
    ele.style.boxShadow = "1px 1px 4px 0px #888888";
    deletebtn.style.right = "300px";
    cancelbtn.style.right = "95px";
    selectText.style.left = "161px";
    selectAll.style.left = "74px";
    selectBox.style.marginLeft = "29px";
    selectionPanel.style.marginTop = "15px";
  }
};

const deleteSelect = () => {
  if (selectedProducts.value.length > 0) {
    isSelectDeleteModalVisible.value = true;
  }
};

const campaignModifiedAddRecipe = () => {
  isCampaignModified.value = true;
};

const backToTag = () => {
  backToTagConfirm();
  closeModal();
};

const checkRecipePreviewVideo = (data) => {
  isCheckVideoPreview.value = !!data;
};

const inputContentChanged = () => {
  isCampaignModified.value = true;
};

const checkTagName = () => {
  const name = getRef("tagName");
  if (
    name.scrollWidth > name.clientWidth &&
    name !== document.activeElement &&
    tagName.value.trim().length > 0
  ) {
    isTagNameFocus.value = true;
  }
};

const hidetagTip = () => {
  isTagNameFocus.value = false;
};

const getAvailableLanguagesTooltip = () => {
  if (!recipeDataForTag?.value?.length) return "";

  const uniqueLanguages = [
    ...new Set(
      recipeDataForTag.value.flatMap(recipe =>
        recipe.langs.map((item) => displayTooltipLanguage(item))
      )
    ),
  ];

  return `Available in ${uniqueLanguages.join(" ")}`;
};

const getTagDataFlite = () => {
  return CategoriesService.getTagData(
    project.value,
    true,
    lang.value,
    store,
    $auth,
    route.query.isin
  )
    .then((response) => {
      isTagAlertIcon.value = response?.hasAlert ?? false;
    })
    .catch((e) => {
      console.error(e);
      triggerLoading("somethingWentWrong");
    });
};

const checkVariantNameEmpty = () => {
  isRecipeVariantNameEmpty.value = recipeVariantList.value.some(data => !data.name.trim());
};

const displayTooltipLanguage = (item, index, langLength) => {
  const arr = item.split("-");
  if (item !== lang.value) {
    return index < langLength
      ? arr[0].toUpperCase() + ","
      : arr[0].toUpperCase() + ".";
  }
};

const displayLanguageCode = (item) => {
  const arr = item ? item.split("-") : [];
  return arr[0] ? arr[0].toUpperCase() : "";
};

const addTagVariant = (item) => {
  const trimmedName = item.trim();
  if (trimmedName) {
    const newVariantData = {
      name: trimmedName,
      lang: recipeVariantLanguage.value,
    };
    recipeVariantList.value.push(newVariantData);
    isEditVariantNamePopup.value = false;
    variantName.value = "";
    isCampaignModified.value = true;

    recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(
      (data) => data.language !== recipeVariantLanguage.value
    );

    saveRemovedTagVariants.value = saveRemovedTagVariants.value.filter(
      (data) => data !== recipeVariantLanguage.value
    );

    getTagAssociations();
  }
};

const nextTagVariantNameModalPopUp = (item) => {
  hasEditTagVariantLanguagePopUp.value = false;
  recipeVariantLanguage.value =
    item || recipeVariantLanguageList.value[0]?.language;
  isEditVariantNamePopup.value = true;
  hasEditTagVariantLanguageResult.value = false;
};

const showTagVariantLanguageMatches = () => {
  hasEditTagVariantLanguageResult.value =
    !hasEditTagVariantLanguageResult.value;
};

const backToSelectLanguageTagVariantPopUp = () => {
  isEditVariantNamePopup.value = false;
  hasEditTagVariantLanguagePopUp.value = true;
};

const openTagVariantPopUp = () => {
  hasEditTagVariantLanguagePopUp.value = true;
  hasEditTagVariantLanguageResult.value = false;
  recipeVariantLanguage.value = "";
};

const setTagVariantLanguageMatches = (value, index) => {
  recipeVariantLanguage.value = value.language;
  hasEditTagVariantLanguageResult.value = false;
  recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(
    (data) => data.language !== recipeVariantLanguage.value
  );
  recipeVariantLanguageList.value.unshift(value);
};

const deleteTagVariant = (data, index) => {
  isRemoveAddTagVariantVisible.value = true;
  editTagVariantDataIndex.value = index;
  isDeletedvariant.value = data.lang;
};

const removeTagVariant = () => {
  initiallyVariantSupported.value = initiallyVariantSupported.value.filter(
    (data) => {
      if (data.lang === isDeletedvariant.value) {
        saveRemovedTagVariants.value.push(isDeletedvariant.value);
        return false;
      }
      return true;
    }
  );

  recipeVariantList.value.splice(editTagVariantDataIndex.value, 1);

  const langData = {
    language: isDeletedvariant.value,
    language_name: isDeletedvariant.value === "es-US" ? "Spanish" : "French",
    languageFlag: `/images/flags/${
      isDeletedvariant.value === "es-US" ? "spain" : "france"
    }-flag.png`,
  };

  if (!recipeVariantLanguageList.value.some((item) => item.language === langData.language)) {
    recipeVariantLanguageList.value.push(langData);
  }

  isCampaignModified.value = true;
  closeModal();
};

const unPublishConfirm = () => {
  isCampaignModified.value = true;
  tagStatus.value = "hidden";
  closeModal();
};

const publishConfirm = () => {
  isCampaignModified.value = true;
  tagStatus.value = "active";
  closeModal();
};

const publishToggleBtnAsync = async () => {
  if (
    tagStatus.value === "active" &&
    ["readyToPublish", "published"].includes(tagState.value)
  ) {
    if (isDisplayWarningPopup.value) {
      isUnPublishModalVisible.value = false;
      isDisplayWarningPopupConfirm.value = true;
    } else {
      isDisplayWarningPopupConfirm.value = false;
      isUnPublishModalVisible.value = true;
    }
  }
  if (
    tagStatus.value === "hidden" &&
    ["readyToPublish", "published"].includes(tagState.value)
  ) {
    isPublishModalVisible.value = true;
  }
};

const publishToggleBtnPopup = () => {
  if (
    tagStatus.value !== "active" &&
    (tagState.value !== "readyToPublish" || tagState.value !== "published")
  ) {
    isUnableToPublishArticle.value = true;
  } else {
    publishToggleBtnAsync();
  }
};

const getEditSearch = () => {
  return Search.getEditSearch(project.value, store, $auth)
    .then((response) => {
      if (response && response.filters) {
        const searchTagISINList = response.filters.filter(
          (data) => data.type === "tags"
        );
        searchTagISINList.forEach((data) => {
          data.values.forEach((value) => {
            if (value.isin === tagIsin.value) {
              isDisplayWarningPopup.value = true;
            }
          });
        });
      }
    })
    .catch(() => {});
};

const editRecipe = (isin) => {
  if (isin) {
    recipeIsin.value = isin;
    hasOpenPreviewRecipe.value = true;
  }
};
const pageChangeAsync = async (event = 1, isScrollToTop = true) => {
  fromTags.value = (event - 1) * sizeTags.value;

  if (isScrollToTop) {
    scrollToTop();
  }

  await updatePageUrlAsync(event);
  await getRecipeDataForTagAsync();
  selectionOfRecipes.value[0].isSelected = false;

  if (selectedProducts.value.length) {
    selectedProducts.value.forEach((item) => {
      recipeDataForTag.value.forEach((data) => {
        if (item?.isin === data?.isin) {
          data.isSelectedToDelete = item.isSelectedToDelete;
        }
      });
    });
    checkSelected();
  }
};

const updatePageUrlAsync = async (pageNo) => {
  await router.push({
    path: "/edit-tag",
    query: {
      ...route.query,
      [QUERY_PARAM_KEY.PAGE]: pageNo > 1 ? pageNo : undefined
    }
  });
};

const saveButtonClickAsync = async () => {
  closeModal();
  isTagPublishing.value = true;
  if (tagName && recipeVariantList.value.length < 1) {
    isEditTagMessageEnable.value = true;
  }
  await postTagAsync();
  isCampaignModified.value = false;
  $eventBus.emit("campaignModified", isCampaignModified.value);
};

const updatedTagVariantList = (variantList) => {
  variantList.forEach((item) => {
    if (item && item.name !== "" && item.lang !== "") {
      item[item.lang] = {
        name: item.name || "",
      };
    }
  });

  const updatedVariantList = Object.assign({}, ...variantList);
  delete updatedVariantList.lang;
  delete updatedVariantList.name;

  return updatedVariantList;
};

const postTagAsync = async () => {
  const defaultVariantData = {
    name: tagName.value.trim(),
    lang: lang.value,
  };
  recipeVariantList.value.push(defaultVariantData);
  const payload = {
    isin: route.query.isin,
    type: "tag",
    data: updatedTagVariantList(recipeVariantList.value),
  };

  try {
    await store.dispatch("categories/postCategoryOrCategoryGroupAsync", {
      payload,
      lang: lang.value,
    });

    const hasSelectedRecipes = selectedTagRecipe.value.length > 0;
    const hasTagRemovals = recipeMatchesIsinsTagRemove.value.length > 0;

    if (hasSelectedRecipes) {
      await postTagRecipeAsync();
      if (checkAndAbortIfNecessary()) return;
    }

    if (hasTagRemovals) {
      await removeTagRecipeAsync();
      if (checkAndAbortIfNecessary()) return;
    }

    await patchPublishTagAsync();
    if (saveRemovedTagVariants.value.length) {
      await deleteVariant();
    }
  } catch (error) {
    console.error(error);
    showLoader.value = false;
    isTagPublishing.value = false;
  }
};

const checkAndAbortIfNecessary = () => {
  if (isAbortedCheckingOperationStatus.value) {
    abortPostingTag();
    return true;
  }
  return false;
};

const abortPostingTag = () => {
  $eventBus.emit("show-floating-notification", {
    popupMessage: "Warning",
    popupSubMessage: "The tag may not have been saved properly",
    popupType: $keys.KEY_NAMES.ERROR
  });
  isAbortedCheckingOperationStatus.value = false;
  showLoader.value = false;
  isTagPublishing.value = false;
  closeModal();
}

const deleteVariant = async () => {
  try {
    await store.dispatch('categories/deleteLanguageVariantAsync', {
      isin: route.query.isin,
      lang: saveRemovedTagVariants.value,
    });
  } catch (error) {
    console.error(error);
  }
};

const postTagRecipeAsync = async () => {
  const payload = {
    sourceId: $keys.KEY_NAMES.SOURCE_ID,
    data: {
      action: "add",
      isin: route.query.isin,
      targets: selectedTagRecipe.value,
    },
  };

  try {
    const response = await store.dispatch(
      "categories/postCategoryRecipeAsync",
      { payload }
    );
    const operationId = response.opId;
    await checkOperationStatusAsync(operationId);
  } catch (error) {
    console.error(error);
    showLoader.value = false;
    isTagPublishing.value = false;
  }
};

const patchPublishTagAsync = async () => {
  if (route.query.isin) {
    const payload = {
      status: tagStatus.value,
    };

    try {
      await store.dispatch("categories/patchCategoryAsync", {
        payload,
        isin: route.query.isin,
      });
      isTagPublishing.value = false;
      const loadingStatus = tagStatus.value !== 'active' ? "savedSuccess" : "isPublishedData";
      triggerLoading(loadingStatus);
      routeToPage("tags");
    } catch (error) {
      console.error(error);
      isTagPublishing.value = false;
      showLoader.value = false;
    }
  }
};

const removeTagRecipeAsync = async () => {
  const isin = route.query.isin;
  const payload = {
    sourceId: $keys.KEY_NAMES.SOURCE_ID,
    data: {
      action: "remove",
      isin,
      targets: recipeMatchesIsinsTagRemove.value,
    },
  };

  try {
    const response = await store.dispatch(
      "categories/postCategoryRecipeAsync",
      { payload }
    );
    const operationId = response.opId;
    await checkOperationStatusAsync(operationId);
  } catch (error) {
    console.error(error);
    showLoader.value = false;
  }
};

const checkOperationStatusAsync = async (operationId) => {
  let timeout = 0;
  const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];
  const abortedTimer = setTimeout(() => isAbortedCheckingOperationStatus.value = true, 1000 * 60 * 2);

  while (true) {
    await new Promise((resolve) => setTimeout(resolve, timeout)); // Add delay to prevent excessive requests
    await getOperationStatusAsync(operationId);

    if (states.includes(operationStatusDetails.value.state) || isAbortedCheckingOperationStatus.value) {
      if (abortedTimer) {
        clearTimeout(abortedTimer);
      }

      $eventBus.emit("routeloading", isTagLoading.value);
      operationStatusDetails.value = "";
      break;
    }

    timeout = 1000;
  }
};

const getOperationStatusAsync = async (operationId) => {
  try {
    await store.dispatch("categories/getOperationStatusAsync", { operationId });
    operationStatusDetails.value =
      store.getters["categories/getOperationStatus"];
  } catch (error) {
    console.error(error);
  }
};
const deleteTag = () => {
  deleteTagList(tagIsin.value);
};

const deleteTagList = async (isin) => {
  isCampaignModified.value = false;
  const payload = {
    sourceId: 210030,
    data: {
      action: "removeAll",
      entityType: "recipe",
      isin: isin,
    },
  };

  closeModal();
  isDeletingModalVisible.value = true;

  try {
    const response = await CategoriesService.postCategoryRecipe(
      project.value,
      payload,
      store,
      $auth
    );
    const deleteTagOperationId = response.opId;
    await checkOperationDeleteStatusAsync(deleteTagOperationId);
  } catch (error) {
    showLoader.value = false;
    isDeletingModalVisible.value = false;
    console.error("Error deleting tag list:", error);
    $eventBus.emit("somethingWentWrong");
  }
};

const checkOperationDeleteStatusAsync = async (operationId) => {
  while (true) {
    await getOperationStatusAsync(operationId);
    if (
      operationStatusDetails.value.state === "done" ||
      operationStatusDetails.value.state === "failed"
    ) {
      isTagLoading.value = false;
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isTagLoading.value);
      await deleteTags();
      operationStatusDetails.value = "";
      break;
    }
  }
};

const deleteTags = async () => {
  if (route.query.isin) {
    try {
      await CategoriesService.deleteCategoryList(
        project.value,
        store,
        $auth,
        route.query.isin
      );
      checkTagEvent($keys.EVENT_KEY_NAMES.CLICK_REMOVE_TAG);
      isDeletingModalVisible.value = false;
      router.push("/tags");
      $eventBus.emit($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
    } catch (error) {
      checkTagEvent($keys.EVENT_KEY_NAMES.VIEW_TAG_ERROR);
      showLoader.value = false;
      isDeletingModalVisible.value = false;
      router.push({ path: "/tags" });
      $nuxt.$loading.finish();
      console.error("Error deleting tags:", error);
    }
  }
};

const checkTagEvent = (description) => {
  const eventProperties = {
    [t("EVENT_NAMES.TAG_NAME")]: tagName.value.trim(),
  };

  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};

const searchRecipeList = () => {
  isCheckSearch.value = true;
  queryText.value = queryRecipe.value;
  recipeDataForTag.value.forEach((data) => {
    data.isSearched = false;
  });
  pageChangeAsync(currentPage.value, false);
};

const resetQuery = () => {
  isCheckSearch.value = false;
  queryRecipe.value = "";
  searchRecipeList();
};

const addRecipe = () => {
  resetQuery();
  isAddRecipeModal.value = true;
};

const openRemoveModal = (recipe, index) => {
  recipe.dropDown = false;
  isRemoveModalVisible.value = true;
  removeRecipeTagData.value = recipe;
  removeRecipeTagIndex.value = index;
};

const backToTagBtn = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToTagConfirm();
  }
};

const backToTagConfirm = () => {
  isCampaignModified.value = false;
  $eventBus.emit("campaignModified", isCampaignModified.value);

  router.push({
    path: "/tags",
    query: {
      [QUERY_PARAM_KEY.PAGE]: route.query[QUERY_PARAM_KEY.BACK_FROM],
      [QUERY_PARAM_KEY.SEARCH]: route.query[QUERY_PARAM_KEY.SEARCH] || undefined,
    },
  });
};

const openDeleteModal = () => {
  isDeleteModalVisible.value = true;
};

const closeModal = () => {
  isSelectDeleteModalVisible.value = false;
  isDeletingModalVisible.value = false;
  isUnableToPublishArticle.value = false;
  isPublishModalVisible.value = false;
  isUnPublishModalVisible.value = false;
  isAddRecipeModal.value = false;
  hasOpenPreviewRecipe.value = false;
  isDeleteModalVisible.value = false;
  isRemoveAddTagVariantVisible.value = false;
  isRemoveModalVisible.value = false;
  isSaveModalVisible.value = false;
  isConfirmModalVisible.value = false;
  queryPopUp.value = "";
  isSearchPopupExitEnable.value = false;
  hasEditTagVariantLanguagePopUp.value = false;
  isAddVariantTagNamePopUp.value = false;
  variantName.value = "";
  isEditVariantNamePopup.value = false;
  isDisplayWarningPopupConfirm.value = false;
};

const deleteInstructionConfirm = () => {
  isDeleteModalVisible.value = false;
  isRemoveModalVisible.value = false;
  isCampaignModified.value = true;
  removeRecipeTagList.value.push(removeRecipeTagData.value);
  recipeMatchesIsinsTagRemove.value.push(removeRecipeTagData.value.isin);

  recipesAfterPageChange.value.forEach((data, index) => {
    if (data.isin == removeRecipeTagData.value.isin) {
      recipesAfterPageChange.value.splice(index, 1);
    }
  });

  selectedTagRecipe.value.forEach((data, index) => {
    if (data.isin == removeRecipeTagData.value.isin) {
      selectedTagRecipe.value.splice(index, 1);
    }
  });

  removeRecipeTagIndex.value = 0;
  removeRecipeTagData.value = {};

  if (recipeDataForTag.value.length == 1 && currentPage.value != 1) {
    currentPage.value -= 1;
    pageChangeAsync(currentPage.value);
  } else {
    getRecipeDataForTagAsync();
  }

  closeModal();
  triggerLoading($keys.KEY_NAMES.DELETED);
};
const displayPopup = () => {
  if (recipeDataForTag.value?.length) {
    recipeDataForTag.value.forEach((data) => {
      data.dropDown = false;
    });
  }
  isSaveModalVisible.value = true;
};

const displayOption = (item) => {
  dropdownItem.value = item;
  item.dropDown = !item.dropDown;
  recipeDataForTag.value.forEach((data) => {
    if (item.isin !== data.isin) {
      data.dropDown = false;
    }
  });
};

const handleClickOutside = (event) => {
  if (dropdownItem.value && dropdownItem.value.dropDown) {
    if (!document.querySelector(".menu-selected").contains(event.target)) {
      dropdownItem.value.dropDown = false;
    }
  }
};

const handleClickOutsidePopup = (event) => {
  if (hasEditTagVariantLanguageResult.value) {
    if (
      !document.querySelector(".category-group-dropdown").contains(event.target)
    ) {
      hasEditTagVariantLanguageResult.value = false;
    }
  }
  if (dropdownItem.value && dropdownItem.value.dropDown) {
    if (!document.querySelector(".menu-selected").contains(event.target)) {
      dropdownItem.value.dropDown = false;
    }
  }
};

const openDeleteModel = () => {
  isDeleteModalVisible.value = true;
};

const getTagAssociations = async () => {
  const promises = [];
  const variantList = [];
  recipeVariantList.value.forEach((langs) => {
    if (langs.lang !== lang.value) {
      promises.push(
        CategoriesService.getTagAssociations(
          project.value,
          route.query.isin,
          0,
          15,
          lang.value,
          store,
          $auth
        ).then((response) => {
          const object = {
            [langs.lang]: response?.recipes?.length || 0,
          };
          variantList.push(object);
          tagAssociations.value = Object.assign({}, ...variantList);
        })
      );
    }
  });
  return Promise.all(promises);
};

const getTagDataAsync = async () => {
  try {
    await store.dispatch("categories/getEditCategoryGroupListAsync", {
      isin: route.query.isin,
      sectionType: "tag",
    });
    const response = store.getters["categories/getEditCategoryGroupList"];
    const languages = Object.keys(response.data);
    languages.forEach((language) => {
      if (response.data[language] && language !== lang.value) {
        const newVariantData = {
          name: response.data[language].name,
          lang: language,
        };
        recipeVariantList.value.push(newVariantData);
        initiallyVariantSupported.value.push(newVariantData);
      }
    });
    tagName.value = response.data[lang.value].name;
    tagState.value = response.state;
    tagStatus.value = response.status;
    tagIsin.value = response.isin;

    if (collectionData.length && tagIsin.value) {
      const collectionIsins = [];
      collectionData.forEach((collectionData) => {
        collectionData.tags.forEach((tag) => {
          collectionIsins.push(tag.isin);
        });
      });
      isTagInCollection.value = collectionIsins.includes(tagIsin.value);
    }
  } catch (e) {
    console.error(e);
  }
};

async function getRecipeDataForTagAsync() {
  if (route.query?.isin) {
    copySearch.value = queryRecipe.value;
  }

  const excludIsins = getFilteredIsins(removeRecipeTagList.value);
  const includeIsins = getFilteredIsins(recipesAfterPageChange.value);
  const filteredExcludIsins = filterIsins(
    excludIsins || [],
    includeIsins || []
  );

  const payload = {
    country: lang.value.split("-")[1],
    q: copySearch.value.trim(),
    excludingIsins: filteredExcludIsins.join(","),
    groupsIncludingIsins: includeIsins.join(","),
    groups: route.query.isin,
    from: fromTags.value,
    size: sizeTags.value,
    sort: "lastMod",
  };

  try {
    isDataLoading.value = true;
    await store.dispatch("categories/getRecipeForCategoriesAsync", { payload });

    const response = store.getters["categories/getRecipeForCategories"];
    handleRecipeDataResponse(response);
  } catch {
    handleRecipeDataError();
  } finally {
    isDataLoading.value = false;
    isTagSaving.value = false;
    isSearchExitEnable.value = !!copySearch.value.trim();
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isTagSaving.value);
  }
}

const getFilteredIsins = (list) => {
  return list.reduce((acc, item) => {
    if (item?.isin) {
      acc.push(item.isin);
    }
    return acc;
  }, []);
};

const filterIsins = (excludIsins, includeIsins) => {
  if (!Array.isArray(excludIsins) || !Array.isArray(includeIsins)) {
    return [];
  }

  return excludIsins.filter((isin) => !includeIsins.includes(isin));
};

const handleRecipeDataResponse = (response) => {
  const defaultProperties = {
    isAlreadyinTagRecipe: false,
    dropDown: false,
    isAdded: false,
    isSearched: false,
    isSelected: true,
    isSelectedToDelete: false,
  };
  recipeDataForTag.value = (response?.results || []).map((data) => ({
    ...data,
    ...defaultProperties,
  }));
  tagsTotal.value = response.total || 0;
  totalTags.value = tagsTotal.value || 0;
  isGlobeIconPresent.value = recipeDataForTag.value.some(
    (data) => data?.langs?.length > 1
  );
};

const handleRecipeDataError = () => {
  showLoader.value = false;
};

const handleTypeInput = (event) => {
  if (getRef("tagName").contains(event.target)) {
    isCampaignModified.value = true;
  }
};
onEscapeKeyPress(closeModal);

watchEffect(() => {
  if (isCampaignModified.value) {
    triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
    checkVariantNameEmpty();
  }
});
</script>
