/**
 * Utility functions for time and date operations. (en-US locale)
 */

const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

export default {
  methods: {
    formatDateToLocaleString(date, options) {
      return date.toLocaleString("en-US", options).replace(",", "");
    },
    formatJsonTimestamp(jsonTimestamp) {
      const date = new Date(jsonTimestamp);
      const options = { month: "short", day: "numeric", year: "numeric" };
      return this.formatDateToLocaleString(date, options);
    },
    convertToTimestamp(dateString) {
      const currentDateGMT = new Date(dateString);
      currentDateGMT.setUTCHours(8, 0, 0, 0);
      return Math.floor(currentDateGMT.getTime() / 1000);
    },
    formatDateToReadableString(dateString) {
      const dateObject = new Date(dateString);
      const day = dateObject.getDate();
      const monthIndex = dateObject.getMonth();
      const year = dateObject.getFullYear();
      return `${monthNames[monthIndex]} ${day}, ${year}`;
    },
    formatScheduleDateRange(start, end) {
      const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp * 1000);
        if (isNaN(date)) return null;
        const month = monthNames[date.getMonth()];
        return `${month} ${date.getDate()}, ${date.getFullYear()}`;
      };
      const formattedStartDate = formatTimestamp(start);
      const formattedEndDate = formatTimestamp(end);
      if (!formattedStartDate || !formattedEndDate) {
        return "Invalid Date";
      }
      if (new Date(end * 1000) < new Date(start * 1000)) {
        return formattedStartDate;
      }
      return `${formattedStartDate} to ${formattedEndDate}`;
    },
    convertTimeStamp(jsonTimestamp) {
      const timestamp = jsonTimestamp * 1000;
      const date = new Date(timestamp);
      const options = { month: "long", day: "numeric", year: "numeric" };
      const formattedDate = date.toLocaleDateString("en-US", options);
      return formattedDate;
    },
    formatDate(inputDate) {
      const dateParts = inputDate.split("-");
      const year = dateParts[0];
      const month = monthNames[parseInt(dateParts[1]) - 1];
      const day = dateParts[2];
      return `${month} ${day}, ${year}`;
    },
    convertTimestampToCustomFormat(timestamp) {
      const milliseconds = timestamp * 1000;
      const dateObject = new Date(milliseconds);
      const months = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ];
      const monthName = months[dateObject.getMonth()];
      const day = dateObject.getDate();
      const year = dateObject.getFullYear();
      const hours = dateObject.getHours().toString().padStart(2, "0");
      const minutes = dateObject.getMinutes().toString().padStart(2, "0");
      const seconds = dateObject.getSeconds().toString().padStart(2, "0");
      const utcOffsetHours = -Math.floor(dateObject.getTimezoneOffset() / 60);
      const utcOffsetMinutes = Math.abs(dateObject.getTimezoneOffset() % 60);
      const utcOffset =
        (utcOffsetHours >= 0 ? "+" : "-") +
        utcOffsetHours.toString().padStart(2, "0") +
        ":" +
        utcOffsetMinutes.toString().padStart(2, "0");
      const dateString = `${monthName} ${day}, ${year}, ${hours}:${minutes}:${seconds} (UTC${utcOffset})`;
      return dateString;
    },
    parseDurationString(durationString) {
      const parts = this.extractDurationParts(durationString);
      let totalSeconds = 0;
      parts.forEach((part) => {
        const value = parseInt(part, 10);
        if (part.includes("H")) {
          totalSeconds += value * 3600;
        } else if (part.includes("M")) {
          totalSeconds += value * 60;
        } else if (part.includes("S")) {
          totalSeconds += value;
        }
      });
      const hours = this.calculateTimeUnit(totalSeconds, 3600);
      const minutes = this.calculateTimeUnit(totalSeconds % 3600, 60);
      const seconds = totalSeconds % 60;
      let formattedTime = [];
      if (hours > 0) formattedTime.push(`${hours} hr`);
      if (minutes > 0) formattedTime.push(`${minutes} min`);
      if (seconds > 0) formattedTime.push(`${seconds} sec`);
      return formattedTime.join(" ").trim();
    },
    extractDurationParts(durationString) {
      const regex = /\d+[HMS]/g;
      return durationString.match(regex) || [];
    },
    calculateTimeUnit(value, time) {
      return Math.floor(value / time);
    },
    formatTimestampToDate(jsonTimestamp, locale = this.$lang) {
      const milliseconds = jsonTimestamp * 1000;
      const date = new Date(milliseconds);
      const options = { month: "short", day: "numeric", year: "numeric" };
      const formattedDate = date.toLocaleString(locale, options);
      const [month, day, year] = formattedDate.split(" ");
      const formattedDateString = `${month.charAt(0).toUpperCase()}${month
        .slice(1)
        .toLowerCase()} ${day} ${year}`;
      return formattedDateString;
    },
    convertIso8601ToTimestamp() {
      return /^PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?$/;
    },
    getFormattedTimeFromTimestamp(timestamp) {
      const currentDate = new Date();
      const inputDate = new Date(timestamp * 1000);
      const timeDifferenceInMs = currentDate - inputDate;
      const daysDifference = Math.floor(
        timeDifferenceInMs / (1000 * 60 * 60 * 24)
      );

      if (daysDifference === 0) {
        return this.$t("COMMON.TODAY");
      } else if (daysDifference === 1) {
        return this.$t("COMMON.1_DAY_AGO");
      } else if (daysDifference < 30) {
        return `${daysDifference} ${this.$t("COMMON.DAYS_AGO")}`;
      } else if (daysDifference < 365) {
        const monthsDifference = Math.floor(daysDifference / 30);
        return `${monthsDifference} month${
          monthsDifference > 1 ? "s" : ""
        } ${this.$t("COMMON.AGO")}`;
      } else {
        const yearsDifference = Math.floor(daysDifference / 365);
        return `${yearsDifference} year${
          yearsDifference > 1 ? "s" : ""
        } ${this.$t("COMMON.AGO")}`;
      }
    },
  },
};
