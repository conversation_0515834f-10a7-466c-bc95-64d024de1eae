export default {
    methods: {
        updateProtocol() {
            if (window.location.protocol === "http:" && !window.location.href.includes('localhost')) {
                window.location.protocol = "https:";
            }
        },
    },
    watch: {
        "window.location.href": function () {
            this.updateProtocol();
        }
    },
    mounted() {
        this.updateProtocol();
    }
};
