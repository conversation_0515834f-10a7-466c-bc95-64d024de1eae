import {
  CONTENT_GENERATION_TYPE,
  getStreamMessageForAdvanceOutput,
  RECIPE_GENERATION_FLAG,
} from "@/models/recipe-generator.model";
import commonUtils from "@/mixins/commonUtils";
import { STATUS_CODE_MESSAGES } from "@/сonstants/status-code-messages";
import { computed } from 'vue';
import { useStore } from 'vuex';

export default {
  mixins: [commonUtils],
  setup() {
    const store = useStore();

    const isLoading = computed(() => {
      const isGenerating = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATING);
      const isImageRefreshing = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING);
      const isRecipeModifying = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING);
      const isSaving = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_SAVING);
      return isGenerating || isImageRefreshing || isRecipeModifying || isSaving;
    });

    const isGenerating = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATING));
    const isImageRefreshing = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING));
    const isRecipeModifying = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING));
    const isSaving = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_SAVING));
    const isGenerationComplete = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATION_COMPLETE));
    const isGeneratedCorrectly = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATED_CORRECTLY));
    const flow = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.FLOW));
    const resetAt = computed(() => store.getters["recipeGeneration/getResetAt"]);

    return {
      isLoading,
      isGenerating,
      isImageRefreshing,
      isRecipeModifying,
      isSaving,
      isGenerationComplete,
      isGeneratedCorrectly,
      flow,
      resetAt,
    };
  },
  watch: {
    isLoading: 'handleCampaignModified',
  },
  methods: {
    setStreamText(text) {
      this.$store.dispatch('recipeGeneration/setStreamText', { value: text }).catch();
    },
    handleCampaignModified(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.triggerLoading(this.$keys.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
      }
    },
    setOnMessageProgressSteps(type, step, result) {
      this.$store.dispatch('recipeGeneration/setGenerationProgressSteps', {
        type: type,
        step: step,
        result: type === CONTENT_GENERATION_TYPE.PROGRESS ? result?.progress : "",
        isStreamOutput: true,
        isHidden: false,
        reason: type === CONTENT_GENERATION_TYPE.FAILURE ? result?.reason : undefined,
      }).catch();
    },
    setInnerProgressSteps(type, step) {
      this.$store.dispatch('recipeGeneration/setGenerationProgressSteps', {
        type: type,
        step: step,
        isStreamOutput: false,
        isDone: true,
        isHidden: false,
      }).catch();
    },
    clearTimer(timer) {
      if (timer) {
        clearTimeout(timer);
      }
    },
    abortConnection(ctrl) {
      if (ctrl) {
        ctrl.abort();
      }
    },
    setTraceId(response) {
      const id = response?.headers?.get("X-Forensic-Id") || response?.headers?.get("x-forensic-id");
      if (!id) {
        return;
      }

      this.$store.dispatch("recipeGeneration/setTraceId", { value: id }).catch();

      const pageContainerEl = document?.querySelector(".iq-r-g");
      pageContainerEl?.setAttribute('data-trace-id', id);
    },
    handleStreamError(response, {
      isStreamOnOpen,
      isEventFatalError,
      isStreamOnError,
      progressMessage,
    }) {
      if (isStreamOnOpen) {
        let progress;
        if (response.status >= 400 && response.status < 500 && response.status !== 429) {
          progress = "# Client-side errors";
        } else if (response.status >= 500) {
          progress = "# Server-side errors";
        }

        if (progress) {
          const id = response?.headers?.get("X-Forensic-Id") || response?.headers?.get("x-forensic-id");
          this.setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.PROGRESS, { progress }));
          this.setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.FAILURE, {
            status: response.status,
            "Trace ID": id,
            detail: STATUS_CODE_MESSAGES[response.status],
          }));
        }
        return;
      }

      if (isEventFatalError) {
        this.setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.PROGRESS, {
          progress: progressMessage ? `# ${progressMessage}` : "# Stream event error",
        }));
        this.setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.FAILURE, {
          "Trace ID": this.$store.getters["recipeGeneration/getTraceId"],
          detail: response,
        }));
        return;
      }

      if (isStreamOnError) {
        this.setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.PROGRESS, {
          progress: progressMessage ? `# ${progressMessage}` : "# Stream on error.",
        }));
        this.setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.FAILURE, {
          "Trace ID": this.$store.getters["recipeGeneration/getTraceId"],
          detail: response,
          message: response.message || response.errorMessage,
        }));
      }
    },
    scrollToPageBottom() {
      window.scrollTo({
        top: document.body.scrollHeight,
        left: 0,
        behavior: "smooth",
      });
    },
  },
};