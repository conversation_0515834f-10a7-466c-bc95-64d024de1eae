import { generateUUID } from "@/utils/generateUUID";

export default {
    methods: {
        routeToPage(page, query = null) {
            this.$router.push({
                path: `/${page}`,
                query: query ? { routeFrom: query } : {},
            });
        },
        setOpacity(refId, value) {
            this.getRef(refId).style.opacity = value;
        },
        numericOnly(number){
            return number.replace(/\D/g, '');
        },
        triggerLoading(message,value){
            this.$root.$emit(message,value)
        },
        isEmptyOrWhitespace(str) {
            return (str?.trim() ?? '') === '';
        },
        generateUUID() {
            return generateUUID();
        },
        splitAndTrimUrls(urlString) {
            return urlString.includes(",")
            ? urlString
                .split(",")
                .map((url) => url.trim())
                .filter((url) => !!url)
            : [urlString.trim()];
        },
        validateURL(url) {
            const urlPattern = /^(https?:\/\/)?([^\s$.?#].[^\s]*)$/;
            return !urlPattern.test(url)
        },
        validateRecipeURL(url){
            const pattern = /^(http:\/\/|https:\/\/).*\w+/;
            return pattern.test(url)
        },
        formatSlug(slug) {
            if (!slug) return "";
            return slug.replace(/\d/g, "").toLowerCase().replace(/\s+/g, "-");
        },
        formatAndTrimSlug(slug, slugLength) {
            if (!slug) return "";
            let trimmedString = slug.substring(0, slugLength).trim();
            return trimmedString.replace(/\s+/g, "-");
        },
        generateIncrementedSlug(baseSlug, count) {
            const incrementedCount = count >= 2 ? count + 1 : -2;
            return `${baseSlug}${incrementedCount}`;
        },
        extractNumericCount(slug) {
            const match = slug.match(/\d+/);
            return match ? parseInt(match[0]) : 0;
        },
        parseInputString(inputString) {
            if (!inputString) return;
            return inputString.includes(",")
              ? inputString.split(",")
                .map((splitString) => splitString.trim())
                .filter(trimmedSplitString =>  !!trimmedSplitString)
              : [inputString.trim()];
        },
        checkDuplicate() {
            const sourceUrl = window.location.href;
            this.isCreateDuplicate = sourceUrl.includes("create-duplicate");
        },
        isScheduledWithPublishDate(element) {
            return element.state === "scheduled" && element.publishDate;
        },
        processScheduledElement(element) {
            const timestampInSeconds = this.getCurrentTimestampInSeconds();
            const timestamp = element.publishDate;
            if (timestampInSeconds < timestamp) {
              const date = new Date(timestamp * 1000);
              this.disabledDates.push(date);
            }
            this.disableList.push({
              date: element.publishDate || "",
              description: element.title || "",
              template: element.template || "",
              state: element.state || "",
            });
        },
        getCurrentTimestampInSeconds() {
            const currentDate = new Date();
            currentDate.setHours(0, 0, 0, 0);
            return Math.floor(currentDate.getTime() / 1000);
        },                        
        isValidImageUrl(url) {
            const imageExtensions = new Set(['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']);
            try {
                const { pathname } = new URL(url);
                const extension = pathname.slice(pathname.lastIndexOf('.')).toLowerCase();
                const isValidExtension = imageExtensions.has(extension);
                const isHttpUrl = url.startsWith('http://') || url.startsWith('https://');
                return isValidExtension || isHttpUrl;
            } catch {
                return false;
            }
        },
        extractTimeParts(time) {
            const iso8601DurationRegex = this.convertIso8601ToTimestamp();
            const matches = time.match(iso8601DurationRegex) || [];
            return {
              hour: matches[1] || "",
              minute: matches[2] || "",
              second: matches[3] || ""
            };
        },
        splitLangAndCountry(lang) {
            const [language, country] = lang.split('-');
            return { language, country };
        },
        sortAscendingAlphabetically(array, property) {
            return array.slice().sort((a, b) => a[property].localeCompare(b[property]));
        }
    },
};
