export default {
    methods: {
        isReadOnlyProvider(provider) {
            const readOnlyProviders = [this.$keys.KEY_NAMES.FIMS, this.$keys.KEY_NAMES.LPN];
            const providers = readOnlyProviders || [];
            return providers.includes(provider);
        },
        printConsole(item, ...theArgs) {
            if (import.meta.env.SSR === false) {
                return;
            }
            return console.log(item, ...theArgs);
        },
    }
};
