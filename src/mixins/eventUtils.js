export default {
    methods: {
        preventDefault(event) {
            event.preventDefault();
        },
        isKeyInArray(event, keys) {
            return keys.includes(event.keyCode);
        },
        isAlphaNumeric(event) {
            const keyCode = event.which || event.code;
            return (keyCode >= 97 && keyCode <= 122) || // lowercase letters
                (keyCode >= 65 && keyCode <= 90) || // uppercase letters
                (keyCode >= 48 && keyCode <= 57) || // numbers
                keyCode === 32; // space
        },
        preventTabKeyPress(event) {
            if (this.isKeyInArray(event, [9])) {
                this.preventDefault(event);
            }
        },
        preventInvalidNumberInput(event) {
            if (this.isKeyInArray(event, [45, 43])) { // '-' and '+'
                this.preventDefault(event);
            }
        },
        preventEnterAndSpaceKeyPress(event) {
            if (this.isKeyInArray(event, [13, 32])) { // Enter and Space keys
                this.preventDefault(event);
            }
        },
        preventSpecialCharacters(event) {
            if (this.isKeyInArray(event, [124, 126, 47])) { // '|', '~', '/'
                this.preventDefault(event);
            }
        },
        restrictNumericInput(event) {
            if (!this.isKeyInArray(event, [46, 17]) && !this.isKeyInArray(event, [48, 49, 50, 51, 52, 53, 54, 55, 56, 57])) {
                this.preventDefault(event);
            }
        },
        restrictToAlphabets(event) {
            if (!this.isAlphaNumeric(event)) {
                this.preventDefault(event);
            }
        },
        restrictToAlphanumeric(event) {
            if (!this.isAlphaNumeric(event) &&
                !this.isKeyInArray(event, [45, 18, 43])) { // '-', Ctrl, '+'
                this.preventDefault(event);
            }
        },
        preventNonNumericInput(event) {
            if (!this.isKeyInArray(event, [8, 44]) && !this.isKeyInArray(event, [48, 49, 50, 51, 52, 53, 54, 55, 56, 57])) {
                this.preventDefault(event);
            }
        },
        restrictSpecialCharacters(event) {
            const keyCode = event.keyCode;
            const isNumberKey = keyCode >= 48 && keyCode <= 57; // '0' to '9'
            const isRestrictedKey = keyCode === 189 || keyCode === 43; // '-' or '+'
            if (isRestrictedKey || !isNumberKey) {
                this.preventDefault(event);
            }
        },
    },
};
