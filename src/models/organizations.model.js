/** @typedef {object} OrganizationsObject
 * @property {string} isin
 * @property {string} name
 * @property {object} image
 * @property {string} image.url
 * @property {object} image.sizes
 * @property {string} image.sizes.300
 * @property {string} image.sizes.500
 * @property {string} image.sizes.1000
 * @property {string} state
 */

/** @typedef {object} OrganizationsListResponse
 * @property {number} from
 * @property {number} size
 * @property {number} total
 * @property {OrganizationsObject[]} results
 */

