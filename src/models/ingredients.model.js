/** @typedef {object} Ingredient
 * @property {string} ingredient
 * @property {object} products
 *    @property {number} products.from
 *    @property {number} products.size
 *    @property {number} products.total
 *    @property {Array.<any>} products.results
 * @property {object} size
 *    @property {number} size.value
 * @property {'shoppable'|'shoppablePantry'|'nonShoppable'} shoppableFlag
 * @property {boolean} onlyIncluded
 * @property {boolean} onlyPromoted
 * @property {number} totalPromotedProducts
 */

/** @typedef {object} SearchIngredientsResponse
 * @property {number} from
 * @property {number} size
 * @property {number} total
 * @property {Ingredient[]} results
 */

/**@typedef {object} IngredientObject
 * @property {string} ingredient
 * @property {boolean} onlyIncluded
 * @property {boolean} onlyPromoted
 * @property {boolean} isShoppable
 * @property {number} totalPromotedProducts
 * @property {number} productCount
 */

import { KEYS } from "@/сonstants/keys";

/**
 *
 * @param {Ingredient} data
 * @return {IngredientObject}
 */
export const IngredientObject = (data) => {
  const {
    ingredient,
    onlyIncluded,
    onlyPromoted,
    shoppableFlag,
    totalPromotedProducts,
    products,
  } = data;
  return {
    ingredient,
    onlyIncluded,
    onlyPromoted,
    isShoppable: shoppableFlag !== KEYS.SHOPPABLE_FLAG.NON_SHOPPABLE,
    totalPromotedProducts,
    productCount: Math.max(0, products?.total - totalPromotedProducts),
  };
}
