/**
 * @typedef {object} CategoryGroupResponse
 * @property {number} from
 * @property {number} size
 * @property {number} total
 * @property {object[]} results
 *    @property {string} results.isin
 *    @property {string} results.state
 *    @property {string} results.name
 *    @property {string[]} results.langs
 *    @property {number} results.totalRecipes
 *    @property {string} results.image
 *    @property {object[]} results.categories
 *        @property {string} results.categories.isin
 *        @property {string} results.categories.state
 *        @property {string} results.categories.name
 *        @property {string[]} results.categories.langs
 *        @property {string} results.categories.image
 */

/**
 * @typedef CategoryGroupObject
 * @type Object
 * @property {string} image
 * @property {string} isin
 * @property {string} name
 * @property {string} state - published, unpublished
 * @property {number} totalRecipes
 * @property {number} totalCategories
 * @property {string} nameCategories
 * @property {string[]} langs
 */


/**
 *
 * @param data
 * @returns CategoryGroupObject
 */
export const categoryGroupModel = (data) => {
  const nameCategories = data.categories.map((category) => category.name).join(', ');
  return {
    image: data.image,
    isin: data.isin,
    name: data.name,
    state: data.state,
    totalRecipes: data.totalRecipes,
    totalCategories: data.categories?.length || 0,
    nameCategories,
    langs: data.langs,
  };
}
