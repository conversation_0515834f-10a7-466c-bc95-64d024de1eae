/**
 * @typedef RecipeImageObject
 * @type {Object}
 * @property {string} key - image link
 * @property {string} assessment - assessment of the image
 * @property {number} averageValue - average image value
 * @property {boolean} isSelected - mark the image as selected
 * @property {number} selectedOrder - selected image order
 * @property {boolean} isMainImage - mark the image as primary, can be only one
 * @property {string} source - image source
 * @property {Object} image - image details
 *    @property {string} image.link - image link
 *    @property {string} image.model - model that generated the image
 *    @property {string} image.prompt - image description
 * @property {object} review - image review details
 *    @property {object} review.color_rating_star - color rating
 *    @property {object} review.accuracy_rating_star - accuracy rating
 *    @property {object} review.realism_rating_star - realism rating
 */

/**
 * @typedef TextGenerationModelObject
 * @type {Object}
 * @property {string} id - model identifier
 * @property {string} name - image model name
 * @property {boolean} selected - marker for selected model
 * @property {boolean} disabled - marker for disabled model
 * @property {boolean} enabled - marker that model is enabled and can be used
 */

/**
 * @typedef ImageGenerationModelObject
 * @type {Object}
 * @property {string} id - model identifier
 * @property {string} name - image model name
 * @property {boolean} selected - marker for selected model
 * @property {boolean} disabled - marker for disabled model
 * @property {boolean} enabled - marker that model is enabled and can be used
 */

import { KEYS } from "@/сonstants/keys";
import { CONFIG } from "../composables/useConfig.js";


export const RECIPE_GENERATION_FLOW = {
  RECIPE_BLANK: "recipeBlank",
  RECIPE_GENERATING: "recipesGenerating",
  RECIPE_REGENERATING: "recipesReGenerating",
  RECIPE_REFRESH_IMAGE: "recipesRefreshImage",
  RECIPE_MODIFYING: "recipesModifying",
  INTERMEDIATE: "intermediate",
}

export const RECIPE_GENERATION_FLAG = {
  IS_SAVING: "isSaving",

  // generation process
  IS_GENERATING: "isGenerating",
  IS_GENERATION_COMPLETE: "isGenerationComplete", // if one generation process was completed correctly, then true
  IS_GENERATED_CORRECTLY: "isGeneratedCorrectly",

  IS_RECIPE_DETAILS_GENERATING: "isRecipeDetailsGenerating",
  IS_RECIPE_DETAILS_GENERATION_COMPLETE: "isRecipeDetailsGenerationComplete",

  IS_RECIPE_REVIEW_TASTE_GENERATING: "isRecipeTasteGenerating",
  IS_RECIPE_REVIEW_TASTE_GENERATION_COMPLETE: "isRecipeReviewTasteGenerationComplete",

  IS_IMAGE_GENERATING: "isImageGenerating",
  IS_IMAGE_GENERATION_COMPLETE: "isImageGenerationComplete",

  // image refreshing process
  IS_IMAGE_REFRESHING: "isImageRefreshing",
  IS_IMAGE_REFRESHING_RESULT: "isImageRefreshingResult",
  IS_IMAGE_REFRESH_COMPLETE: "isImageRefreshComplete",

  // recipe modifying process
  IS_RECIPE_MODIFYING: "isRecipeModifying",
  IS_RECIPE_MODIFYING_RESULT: "isRecipeModifyingResult",
  IS_RECIPE_MODIFY_COMPLETE: "isRecipeModifyComplete",

  FLOW: RECIPE_GENERATION_FLOW.RECIPE_BLANK,
}

export const RECIPE_OUTPUT_PANEL = {
  PROGRESS: "progress",
  RECIPE: "recipe",
  MODIFY: "modify",
  EDIT: "edit",
  ADVANCED_OUTPUT: "advanced-output",
}

export const GET_RECIPE_GENERATION_FLAGS = () => {
  const obj = Object.keys(RECIPE_GENERATION_FLAG)
    .reduce((acc, key) => {
      acc[RECIPE_GENERATION_FLAG[key]] = false;
      return acc;
    }, {});

  obj[RECIPE_GENERATION_FLAG.IS_GENERATED_CORRECTLY] = true;
  obj[RECIPE_GENERATION_FLAG.FLOW] = RECIPE_GENERATION_FLOW.RECIPE_BLANK;

  return obj;
}

// text generation models
export const TEXT_GENERATION_MODEL = {
  GPT_4O: "gpt-4o",
  GEMINI_PRO: "gemini-pro",
  CLAUDE_3_5: "claude-3.5",
}

export const TEXT_GENERATION_MODEL_NAME = {
  [TEXT_GENERATION_MODEL.GPT_4O]: "GPT",
  [TEXT_GENERATION_MODEL.GEMINI_PRO]: "Gemini",
  [TEXT_GENERATION_MODEL.CLAUDE_3_5]: "Claude",
}

/**
 *
 * @return {Array.<TextGenerationModelObject>}
 * @constructor
 */
export const GET_TEXT_GENERATION_MODELS = () => {
  return Object.values(TEXT_GENERATION_MODEL).map((id) => {
    return {
      id,
      name: TEXT_GENERATION_MODEL_NAME[id],
      selected: id === TEXT_GENERATION_MODEL.GPT_4O,
      disabled: true,
      enabled: true,
    };
  })
}

// image generation models
export const IMAGE_GENERATION_MODEL = {
  IMAGEN_1: "imagen-1",
  IMAGEN_3: "imagen-3",
  STABLE_DIFFUSION_SD_3_MEDIUM: "sd3-medium",
  STABLE_DIFFUSION_SD_3_LARGE: "sd3-large",
  STABLE_DIFFUSION_SD_ULTRA: "sd-ultra",
}

export const IMAGE_GENERATION_MODEL_NAME = {
  [IMAGE_GENERATION_MODEL.IMAGEN_1]: "Imagen 1",
  [IMAGE_GENERATION_MODEL.IMAGEN_3]: "Imagen 3",
  [IMAGE_GENERATION_MODEL.STABLE_DIFFUSION_SD_3_MEDIUM]: "Stable Diffusion - Medium",
  [IMAGE_GENERATION_MODEL.STABLE_DIFFUSION_SD_3_LARGE]: "Stable Diffusion - Large",
  [IMAGE_GENERATION_MODEL.STABLE_DIFFUSION_SD_ULTRA]: "Stable Diffusion - Ultra",
}

/**
 *
 * @return {Array.<ImageGenerationModelObject>}
 * @constructor
 */
export const GET_IMAGE_GENERATION_MODELS = () => {
  const prodModels = [IMAGE_GENERATION_MODEL.IMAGEN_1, IMAGE_GENERATION_MODEL.IMAGEN_3];
  return Object.values(IMAGE_GENERATION_MODEL).map((id) => ({
    id,
    name: IMAGE_GENERATION_MODEL_NAME[id],
    selected: prodModels.includes(id),
    disabled: CONFIG.IS_PROD,
    enabled: true,
  }));
}

// Event stream
export const CONTENT_GENERATION_TYPE = {
  PROGRESS: "progress",
  RESULT: "result",
  FAILURE: "failure",
};

export const CONTENT_GENERATION_STEP = {
  MODERATE_PROMPT: "moderate_prompt",
  FIND_KNOWN_RECIPE: "find_known_recipe",
  GENERATE_CREATIVE_RECIPE_IDEAS: "generate_creative_recipe_ideas",
  SELECT_KNOWN_RECIPE_IDEA: "select_known_recipe_idea",
  SELECT_CREATIVE_RECIPE_IDEA: "select_creative_recipe_idea",
  SEARCH_RECIPE_ONLINE: "search_recipe_online",
  SCRAPE_ONLINE_RECIPES: "scrape_online_recipes",
  CREATE_RECIPE_OUTLINE: "create_recipe_outline",
  SEARCH_COOKING_GUIDELINES: "search_cooking_guidelines",
  GENERATE_RECIPE_CONTENT: "generate_recipe_content",
  REVIEW_RECIPE_TASTE: "review_recipe_taste",
  FIND_RECIPE_VALIDATION_REQUIREMENTS: "find_recipe_validation_requirements",
  VALIDATE_RECIPE: "validate_recipe",
  CREATE_RECIPE_IMAGE_GENERATION_PROMPT: "create_recipe_image_generation_prompt",
  RECREATE_RECIPE_IMAGE_GENERATION_PROMPT: "recreate_recipe_image_generation_prompt",
  GENERATE_RECIPE_IMAGES: "generate_recipe_images",
  REVIEW_RECIPE_IMAGE: "review_recipe_image",
};

// false - if not pass a step
export const GET_CONTENT_GENERATION_STEPS = () => Object.keys(CONTENT_GENERATION_STEP)
  .reduce((acc, key) => {
    acc[CONTENT_GENERATION_STEP[key]] = false;
    return acc;
  }, {});


// messages
export const RECIPE_GENERATION_MESSAGES_KEY = {
  GENERATE_CORRECTLY_ON_CLOSE: "generate_correctly_on_close",
  GENERATE_INCORRECTLY_ON_CLOSE: "generate_incorrectly_on_close",
  GENERATE_ON_ERROR: "generate_on_error",
  GENERATE_ON_ABORT: "generate_on_abort",
  GENERATE_ON_MESSAGE_FATAL_ERROR: "generate_on_message_fatal_error",
  GENERATE_ON_ERROR_FATAL_ERROR: "generate_on_error_fatal_error",

  REFRESH_IMAGES_CORRECTLY_ON_CLOSE: "refresh_images_correctly_on_close",
  REFRESH_IMAGES_INCORRECTLY_ON_CLOSE: "refresh_images_incorrectly_on_close",
  REFRESH_IMAGES_ON_ERROR: "refresh_images_on_error",
  REFRESH_IMAGES_ON_ABORT: "refresh_images_on_abort",
  REFRESH_IMAGES_ON_MESSAGE_FATAL_ERROR: "refresh_images_on_message_fatal_error",
  REFRESH_IMAGES_ON_ERROR_FATAL_ERROR: "refresh_images_on_error_fatal_error",

  MODIFY_RECIPE_CORRECTLY_ON_CLOSE: "modify_recipe_correctly_on_close",
  MODIFY_RECIPE_INCORRECTLY_ON_CLOSE: "modify_recipe_incorrectly_on_close",
  MODIFY_RECIPE_ON_ERROR: "modify_recipe_on_error",
  MODIFY_RECIPE_ON_ABORT: "modify_recipe_on_abort",
  MODIFY_RECIPE_ON_MESSAGE_FATAL_ERROR: "modify_recipe_on_message_fatal_error",
  MODIFY_RECIPE_ON_ERROR_FATAL_ERROR: "modify_recipe_on_error_fatal_error",
};

export const RECIPE_GENERATION_MESSAGE = {
  [RECIPE_GENERATION_MESSAGES_KEY.GENERATE_CORRECTLY_ON_CLOSE]: "Success! Recipe generated.",
  [RECIPE_GENERATION_MESSAGES_KEY.GENERATE_INCORRECTLY_ON_CLOSE]: "Recipe generation failed. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_ERROR]: "Recipe generation failed unexpectedly. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_ABORT]: "Recipe generation timed out. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_MESSAGE_FATAL_ERROR]: "Recipe generation failed. Stream event error.",
  [RECIPE_GENERATION_MESSAGES_KEY.GENERATE_ON_ERROR_FATAL_ERROR]: "Recipe generation failed. Stream on error.",

  [RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_CORRECTLY_ON_CLOSE]: "Success! New images generated.",
  [RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_INCORRECTLY_ON_CLOSE]: "Image generation failed. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_ERROR]: "Image generation failed unexpectedly. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_ABORT]: "Image generation timed out. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_MESSAGE_FATAL_ERROR]: "Image generation failed. Stream event error.",
  [RECIPE_GENERATION_MESSAGES_KEY.REFRESH_IMAGES_ON_ERROR_FATAL_ERROR]: "Image generation failed. Stream on error.",

  [RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_CORRECTLY_ON_CLOSE]: "Success! Recipe modified.",
  [RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_INCORRECTLY_ON_CLOSE]: "Recipe modification failed. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_ERROR]: "Recipe modification failed unexpectedly. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_ABORT]: "Recipe modification timed out. Try again.",
  [RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_MESSAGE_FATAL_ERROR]: "Recipe modification failed. Stream event error.",
  [RECIPE_GENERATION_MESSAGES_KEY.MODIFY_RECIPE_ON_ERROR_FATAL_ERROR]: "Recipe modification failed. Stream on error.",
};

export const GENERATED_DATA_RESULT = {
  GOOD_RATING: "good",
  BAD_RATING: "bad",
};


// helpers

/**
 * Helper. Get event stream message for the Advance output
 *
 * @param {string} type - event type, one of the CONTENT_GENERATION_TYPE
 * @param {Object} result - event step result object
 * @return {string|string}
 */
export const getStreamMessageForAdvanceOutput = (type, result) => {
  if (type === CONTENT_GENERATION_TYPE.PROGRESS) {
    return `*** ${result?.progress} *** \n`;
  }

  return  typeof result === "object" ? `${JSON.stringify(result)}\n\n` : '';
};

export const parseEventData = (eventData) => {
  const defaultValue = {
    type: undefined,
    step: undefined,
    result: undefined,
    isData: false,
  };

  if (!eventData) {
    return defaultValue;
  }

  try {
    return {
      ...JSON.parse(eventData),
      isData: true,
    };
  } catch (err) {
    console.warn('Cannot parse event data!', err);
    return defaultValue;
  }
}

// models

/**
 * @param {string} link - image url
 * @param {string} model - model that generated the image
 * @param {string} prompt - image description
 * @return RecipeImageObject
 */
export const getRecipeImageObject = (
  {
    link,
    model,
    prompt,
  }
) => {
  return {
    key: link,
    averageValue: 0,
    assessment: null,
    isSelected: false,
    selectedOrder: null,
    isMainImage: false,
    source: KEYS.KEY_NAMES.AI_GENERATED,
    image: {
      link,
      model: model,
      prompt: prompt,
    },
    review: {
      color_rating_star: 0,
      accuracy_rating_star: 0,
      realism_rating_star: 0,
    }
  };
}
