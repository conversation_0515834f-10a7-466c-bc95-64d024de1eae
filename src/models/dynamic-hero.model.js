export const HERO_STATE = {
  SCHEDULED: "scheduled",
  LIVE: "live",
  DRAFT: "draft",
  PREVIEW: "preview",
  EXPIRED: "expired",
};

export const DYNAMIC_HERO_TEMPLATE = {
  NEWS: "news",
  QUIZ: "quiz",
  EVENT: "event",
  CONTENT: "content",
  ADVICE: "advice",
};

export const BODY_MENU_ACTION_KEY = {
  EDIT: "edit",
  REPLACE_LIVE_HERO: "replaceLiveHero",
  CREATE_DUPLICATE: "createDuplicate",
  UNSCHEDULE: "unschedule",
  SCHEDULE: "schedule",
  DELETE: "delete",
};

export const DYNAMIC_HERO_PATH_MAP = {
  news: "add-dynamic-news",
  quiz: "add-dynamic-quiz",
  event: "add-dynamic-event",
  content: "add-dynamic-content",
  advice: "add-dynamic-advice",
  banner: "/banner/create",
};

export const getColumnKeys = (isCreateNewVisible) => {
  return [
    "publishDate",
    "image",
    "uuid",
    "title",
    "lastUpdate",
    isCreateNewVisible ? undefined : "status",
    isCreateNewVisible ? "select" : undefined,
    "actions",
  ].filter((item) => item !== undefined);
};

export const getColumnNames = (isCreateNewVisible, t) => {
  return [
    t("COLLECTION.PUBLISHED_DATE"),
    "",
    t("DYNAMIC_HERO.HERO_ID"),
    t("DYNAMIC_HERO.HERO_TYPE_TITLE"),
    t("MODIFIED"),
    isCreateNewVisible ? undefined : t("COMMON.STATUS"),
    isCreateNewVisible ? "" : undefined,
    "",
  ].filter((item) => item !== undefined);
};

export const getBodyMenuActions = (data, t) => {
  switch (data.state) {
    case HERO_STATE.LIVE:
      return [
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.EDIT,
          label: t("BUTTONS.EDIT_BUTTON"),
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.REPLACE_LIVE_HERO,
          label: "Replace live hero",
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.CREATE_DUPLICATE,
          label: "Create duplicate",
        },
      ];
    case HERO_STATE.SCHEDULED:
      return [
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.UNSCHEDULE,
          label: "Unschedule"
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.EDIT,
          label: t("BUTTONS.EDIT_BUTTON"),
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.DELETE,
          label: "Delete"
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.CREATE_DUPLICATE,
          label: "Create duplicate",
        },
      ];
    case HERO_STATE.DRAFT:
      return [
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.SCHEDULE,
          label: "Schedule",
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.EDIT,
          label: t("BUTTONS.EDIT_BUTTON"),
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.DELETE,
          label: "Delete"
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.CREATE_DUPLICATE,
          label: "Create duplicate",
        },
      ];
    case HERO_STATE.EXPIRED:
      return data.currentDateExpired ? [] : [
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.CREATE_DUPLICATE,
          label: "Create duplicate",
        },
        {
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.DELETE,
          label: "Delete"
        },
      ];
    case HERO_STATE.PREVIEW:
      return data.publishDate
        ? [{
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.UNSCHEDULE,
          label: "Unschedule"
        }]
        : [{
          isDisable: false,
          key: BODY_MENU_ACTION_KEY.SCHEDULE,
          label: "Schedule"
        }];
    default:
      return [];
  }
};
