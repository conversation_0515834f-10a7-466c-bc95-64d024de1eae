module.exports = function (req, res, next) {
    const config = useRuntimeConfig();

    const host = req.headers.host;
    const url = req.url;
    const env = config.public.ENVIRONMENT;
    const canonicalDomain = 'www.innit.com';
    const expectedDomains = ['innit.com'];

    if (env === 'prod' && expectedDomains.includes(host)) {
        res.writeHead(301, { Location: 'https://' + canonicalDomain + url })
        return res.end()
    }

    return next()
};