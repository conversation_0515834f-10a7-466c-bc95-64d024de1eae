const express = require('express');
const app = express();
const config = require('config');

// Middleware to handle CORS
app.use(function(req, res, next) {
  // You might want to restrict this to specific domains in production
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
  next();
});

// Endpoint to fetch 'config.init' from the configuration
app.get('/config/init', (req, res, next) => {
  try {
    const configInit = config.util.toObject(config.get('config.init'));
    res.json(configInit);
  } catch (error) {
    console.error("Error fetching config.init:", error);
    res.status(500).json({ error: 'Failed to load config.init' });
  }
});

// Endpoint to fetch 'config.menus' from the configuration
app.get('/config/menus', (req, res, next) => {
  try {
    const configMenus = config.util.toObject(config.get('config.menus'));
    res.json(configMenus);
  } catch (error) {
    console.error("Error fetching config.menus:", error);
    res.status(500).json({ error: 'Failed to load config.menus' });
  }
});

// Export the app as middleware for Nuxt
module.exports = {
  path: '/_web/api',
  handler: app
}
