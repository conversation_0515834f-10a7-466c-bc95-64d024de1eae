export const isValidImageUrl = (url) => {
  const imageExtensions = new Set([
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".webp",
    ".svg",
  ]);
  try {
    const { pathname } = new URL(url);
    const extension = pathname.slice(pathname.lastIndexOf(".")).toLowerCase();
    const isValidExtension = imageExtensions.has(extension);
    const isHttpUrl = url.startsWith("http://") || url.startsWith("https://");
    return isValidExtension || isHttpUrl;
  } catch {
    return false;
  }
}
