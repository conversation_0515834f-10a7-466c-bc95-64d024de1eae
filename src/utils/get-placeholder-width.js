export const getPlaceholderWidth = (inputEl) => {
  if (!inputEl?.placeholder) {
    return 0;
  }

  const computedStyles = window.getComputedStyle(inputEl);
  const span = document.createElement('span');

  span.textContent = inputEl.placeholder;
  span.style.font = computedStyles.font;
  span.style.fontSize = computedStyles.fontSize;
  span.style.fontWeight = computedStyles.fontWeight;
  span.style.letterSpacing = computedStyles.letterSpacing;
  span.style.visibility = 'hidden';
  span.style.whiteSpace = 'pre';
  span.style.position = 'absolute';
  span.style.top = '-9999px';

  document.body.appendChild(span);
  const width = span.offsetWidth;
  document.body.removeChild(span);

  return width;
}
