import <PERSON> from "papaparse";
import { convertTimeStamp } from "@/utils/convert-timestamp";

export const processCsvResponse = (response) => {
  const cleanedResponse = response?.replace(/[{}[\]]/g, '');
  const rows = cleanedResponse?.trim()?.split('\n');
  if (!rows?.length) return { processedCsv: '', rowCount: 0 };
  const header = rows?.[0]?.split(',');
  const dataRows = rows?.slice(1);
  const processedRows = dataRows?.filter(row => row?.trim()).map(row => {
    const columns = row?.split(',');
    const timestamp = parseInt(columns?.[0], 10);
    const formattedDate = convertTimeStamp(timestamp);
    if (columns) columns[0] = formattedDate;
    return columns?.join(',');
  });
  return {
    processedCsv: [header?.join(','), ...processedRows].join('\n'),
    rowCount: processedRows.length
  };
};

export const processCsvData = (response) => {
  const parsed = Papa.parse(response, {
    header: true,
    skipEmptyLines: true,
    dynamicTyping: true,
  });
  if (parsed.errors.length) {
    console.error('Parsing errors:', parsed.errors);
    return { rowCount: 0 };
  }
  const { data } = parsed;
  return {
    rowCount: data.length
  };
};
