export const sortLocaleComparator = (sortKey) => {
  return (a, b) => {
    const valA = a[sortKey];
    const valB = b[sortKey];

    if (valA == null && valB != null) return 1;
    if (valA != null && valB == null) return -1;
    if (valA == null && valB == null) return 0;

    if (typeof valA === 'string' && typeof valB === 'string') {
      return valA.localeCompare(valB);
    }

    return valA - valB;
  };
};
