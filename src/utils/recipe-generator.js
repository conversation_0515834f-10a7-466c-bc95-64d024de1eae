export const getMarginSlides = (slides, containerWidth, slideWidth) => {
  const diffWidth = containerWidth - (slideWidth * slides);
  const gap = slides - 1;
  const margin = gap > 1 ? diffWidth / gap : diffWidth;

  if (margin >= 30) {
    return { margin, slides };
  }

  if (slides === 1) {
    return {
      margin: containerWidth - slideWidth,
      slides,
    };
  }

  return getMarginSlides(slides - 1, containerWidth, slideWidth);
};
