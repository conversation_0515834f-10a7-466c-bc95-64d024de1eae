import { CONTENT_GENERATION_TYPE, RECIPE_GENERATION_MESSAGE } from "@/models/recipe-generator.model";
import { compareByAssessment } from "@/utils/compare-by-assessment";

export const setFlagUtil = () => ({ commit }, payload) => {
  if (!payload) {
    return;
  }

  const { flags, flag, value } = payload;

  if (Array.isArray(flags)) {
    flags.forEach(({ flag, value }) => commit('SET_FLAG', { flag, value }))
  } else if (flag) {
    commit('SET_FLAG', { flag, value });
  }
};

export const setImagesReviewDetailsUtil = () => ({ commit, state }, { result, refreshCount }) => {
  const model = result?.model;
  const key = model?.image?.link;

  if (!key) {
    return;
  }

  const imageList = [...state.imageList];
  const imageIndex = imageList?.findIndex((item) => item.key === key);

  if (imageIndex === -1) {
    return;
  }

  const imageObject = imageList[imageIndex];
  const review = model?.review || {};
  const realismRatingStar = review.realism_rating_star || 0;
  const accuracyRatingStar = review.accuracy_rating_star || 0;
  const colorRatingStar = review.color_rating_star || 0;

  Object.assign(imageObject.image, {
    model: model.image?.model || imageObject.image.model,
    prompt: model.image?.prompt || imageObject.image.prompt,
  });

  Object.assign(imageObject.review, {
    color_rating_star: colorRatingStar,
    accuracy_rating_star: accuracyRatingStar,
    realism_rating_star: realismRatingStar,
  });

  const getAverageValue = ({ array, increase }) => {
    const filteredArray = array.filter(Boolean)?.map(Number);

    if (!filteredArray.length) {
      return 0;
    }

    const result = filteredArray.reduce((sum, currentValue) => sum + currentValue, 0) / filteredArray.length;
    return result + increase;
  };

  imageObject.averageValue = getAverageValue({
    array: [realismRatingStar, accuracyRatingStar, colorRatingStar],
    increase: refreshCount ? 10 * refreshCount : 0
  });

  commit('SET_IMAGE_LIST', imageList);
};

export const setGenerationProgressStepsUtil = () => ({ commit, state }, {
  type,
  step,
  result,
  isStreamOutput,
  isHidden,
  isDone = undefined,
  reason = undefined,
}) => {
  if (!type || !step) {
    return;
  }

  const prepareResult = (str) => str?.split(":")?.[0]?.trim() || str;
  const getResultMessage = (message, key) =>  message || RECIPE_GENERATION_MESSAGE[key] || "";
  const steps = [...state.generationProgressSteps];

  // if empty array
  if (!steps.length) {
    steps.push({
      type,
      step,
      result: getResultMessage(prepareResult(result), step),
      isStreamOutput,
      isDone: isDone !== undefined ? isDone : false,
      isHidden,
    });
    commit('SET_GENERATION_PROGRESS_STEPS', steps);
    return;
  }

  const lastStep =  steps[steps.length - 1];

  // if existing step
  if (lastStep.step === step && !lastStep.isDone) {
    lastStep.type = type;

    // if we have a reason value for failure step
    if (type === CONTENT_GENERATION_TYPE.FAILURE && reason) {
      lastStep.isDone = true;

      const message = getResultMessage(prepareResult(reason), step);
      steps.push({
        type,
        step,
        result: state.traceId ? `${message} (Trace ID: ${state.traceId})` : message,
        isStreamOutput,
        isDone: true,
        isHidden,
      });
    }

    commit('SET_GENERATION_PROGRESS_STEPS', steps);
    return;
  }

  // if new step or last step is done
  if (lastStep.step !== step || lastStep.isDone) {
    if (!lastStep.isDone) {
      lastStep.isDone = true;
    }

    let message = getResultMessage(prepareResult(result), step);

    // Add Trace ID to FE failure message if no Trace ID in the previous step massage
    if (
      !isStreamOutput
      && type === CONTENT_GENERATION_TYPE.FAILURE
      && !lastStep.result?.includes("(Trace ID:")
      && state.traceId
    ) {
      message = `${message} (Trace ID: ${state.traceId})`;
    }

    steps.push({
      type,
      step,
      result: message,
      isStreamOutput,
      isDone: isDone !== undefined ? isDone : false,
      isHidden,
    });
    commit('SET_GENERATION_PROGRESS_STEPS', steps);
  }
};

export const updateImageGenerationModelsUtil = () => ({ commit, state }, { id, selected }) => {
  const oldModelList = state.imageGenerationModels;
  const isOne = (key, list) => list.reduce((acc, model) => model[key] ? acc + 1 : acc, 0) <= 1;

  const modelList = oldModelList.map(
    (model) => (model.id === id) ? { ...model, selected } : model
  );

  const isOneSelected = isOne("selected", modelList);
  modelList.forEach((model) => {
    model.disabled = (isOneSelected && model.selected);
  });
  commit("SET_IMAGE_GENERATION_MODEL", modelList)
}

export const setImageSelectedUtil = () => ({ commit, state }, { key, value }) => {
  const maxLimit = 20;
  const imageList = [...state.imageList];
  const imageIndex = imageList.findIndex((item) => item.key === key);

  if (imageIndex === -1) {
    return;
  }

  const item = imageList[imageIndex];

  if ((value && item.isSelected) || (!value && !item.isSelected)) {
    return;
  }

  if (value) {
    // selected limit
    const countOfSelected = imageList.filter((item) => item.isSelected)?.length;
    if (countOfSelected >= maxLimit) {
      return;
    }

    // If the item is being selected
    const maxOrder = Math.max(...imageList.map((i) => i.selectedOrder !== null ? i.selectedOrder : -1));
    item.isSelected = value;
    item.selectedOrder = maxOrder + 1;

    // first selected image mark as main
    if (maxOrder === -1) {
      item.isMainImage = true;
    }
  } else {
    // If the item is being deselected
    const deselectedOrder = item.selectedOrder;
    item.isSelected = false;
    item.selectedOrder = null;
    item.isMainImage = false;

    // Update the selectedOrder of remaining items
    imageList.forEach(i => {
      if (i.selectedOrder !== null && i.selectedOrder > deselectedOrder) {
        i.selectedOrder--;
      }
    });
  }

  const isLimit = imageList.filter((item) => item.isSelected)?.length >= maxLimit;
  commit("SET_IS_IMAGE_SELECTED_LIMIT", isLimit);

  commit('SET_IMAGE_LIST', imageList);
}

export const getGenerationDataGetter = (state, isFirst) => {
  const {
    recipeUUID,
    title,
    imageList,
    servesCount,
    ingredients,
    instructions,
    textGenerationModels,
    promptValue,
    promptValueArchive,
    modifyPromptValue,
    recipeRating,
    assessmentReason,
  } = state;
  const getImageData = (item) => ({
    imageUrl: item.key,
    model: item.image.model,
    assessment: item.assessment
  });
  const recipe = {
    isin: null,
    title,
    image: isFirst ? null : imageList.find((item) => item?.isMainImage)?.url || null,
    servings: servesCount,
    ingredients: ingredients.split("\n"),
    instructions: instructions.split("\n"),
  };
  const result = {
    recipe,
    prompt: promptValue || promptValueArchive[0],
    model: textGenerationModels.filter((item) => item.selected).map((item) => item.id)?.join("|") || null,
    assessment: recipeRating,
    images: imageList.map(getImageData).sort(compareByAssessment),
  };

  if (isFirst) {
    return {
      uuid: recipeUUID,
      ...result,
    };
  }

  return {
    modifyPrompts: [modifyPromptValue],
    assessmentReason: assessmentReason,
    savedImages: imageList.filter(item => item.isSelected).map(getImageData).sort(compareByAssessment),
    ...result,
  };
};
