import { sortLocaleComparator } from "./sort.js";

export const sortDynamicHero = (data, sortKey = '', direction = '') => {
  if (!sortKey || direction === '') {
    return [...data].sort((a, b) => {
      if (a.publishDate === 0 && b.publishDate !== 0) {
        return 1;
      } else if (a.publishDate !== 0 && b.publishDate === 0) {
        return -1;
      } else {
        return a.publishDate - b.publishDate;
      }
    });
  }

  const sorted = [...data].sort(sortLocaleComparator(sortKey));
  return direction === 'desc' ? sorted.reverse() : sorted;
};
