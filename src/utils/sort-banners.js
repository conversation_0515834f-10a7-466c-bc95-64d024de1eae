import { sortLocaleComparator } from "./sort.js";

/**
 * Sorts a list of banners by the provided key and direction,
 * or applies default sorting by state and timestamps.
 *
 * @param {Array<Object>} banners - The list of banners.
 * @param {string} [sortKey] - The key to sort by.
 * @param {"" | "asc" | "desc"} [direction] - The direction of the sort.
 * @returns {Array<Object>} - Sorted list of banners.
 */
export function sortBanners(banners, sortKey = '', direction = '') {
  if (!sortKey || direction === '') {
    const stateOrder = {
      live: 1,
      scheduled: 2,
      draft: 3,
      expired: 4,
    };
    return [...banners].sort((a, b) => {
      const stateComparison = stateOrder[a.state] - stateOrder[b.state];
      if (stateComparison !== 0) return stateComparison;

      if (['live', 'scheduled', 'expired'].includes(a.state)) {
        return a.publishDate - b.publishDate;
      }

      return b.lastUpdate - a.lastUpdate;
    });
  }

  const sorted = [...banners].sort(sortLocaleComparator(sortKey));
  return direction === 'desc' ? sorted.reverse() : sorted;
}
