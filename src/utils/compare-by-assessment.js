import { GENERATED_DATA_RESULT } from "@/models/recipe-generator.model";

const { GOOD_RATING, BAD_RATING } = GENERATED_DATA_RESULT;

/**
 * Compares two objects by their `assessment` value, according to predefined order.
 *
 * @param {Object} a - First object to compare.
 * @param {Object} b - Second object to compare.
 * @returns {number} Negative if `a` should come before `b`, positive if `b` should come before `a`, 0 if equal.
 */
export const compareByAssessment = (a, b) => {
  const assessmentOrder = {
    [GOOD_RATING]: 1,
    [BAD_RATING]: 3,
    null: 2,
  };

  return assessmentOrder[a.assessment] - assessmentOrder[b.assessment];
};
