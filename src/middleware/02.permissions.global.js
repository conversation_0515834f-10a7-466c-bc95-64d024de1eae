import { useContext } from "../composables/useContext.js";
import { useProjectLang } from "../composables/useProjectLang.js";
import { useAuth0 } from "@auth0/auth0-vue";
import { useConfig } from "../composables/useConfig.js";

export default defineNuxtRouteMiddleware( async (to, from) => {
  const { config } = useConfig();

  if (config.value.IS_SERVER) {
    return;
  }

  const auth0 = useAuth0();
  const {
    checkPagePermissionsAsync,
    fetchUserPermissionsAsync,
  } = useContext();
  const {
    projectPermissions,
  } = useProjectLang();

  if (auth0.isLoading.value) {
    return;
  }

  const routePath = to?.path;
  const isDefaultLayout = !to?.meta?.layout;
  const pageMetaAuth = to?.meta?.auth;

  if (pageMetaAuth === false) {
    return;
  }

  if (!auth0.isAuthenticated.value) {
    return navigateTo('/login');
  }

  if (!isDefaultLayout) {
    return;
  }

  if (projectPermissions.value !== null && to?.name !== 'index') {
    await fetchUserPermissionsAsync();
  }

  return checkPagePermissionsAsync(routePath);
});
