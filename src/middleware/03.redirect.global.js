import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";

export default defineNuxtRouteMiddleware(async (to, from) => {
  const buildUrl = (path, query, hash) => {
    const search = query ? `?${new URLSearchParams(query).toString()}` : "";
    const anchor = hash ?? "";
    return `${path}${search}${anchor}`;
  };

  switch (to?.path) {
    case "/add-articles": {
      const articleCreateUrl = buildUrl("/articles/create", to?.query, to?.hash);
      return navigateTo(articleCreateUrl);
    }
    case "/edit-articles": {
      const uuid = to?.query?.[QUERY_PARAM_KEY.UUID];
      const articleEditUrl = buildUrl(`/articles/${uuid}`, to?.query, to?.hash);
      return navigateTo(articleEditUrl);
    }
    case "/banner-details": {
      const uuid = to?.query?.[QUERY_PARAM_KEY.UUID];
      const duplicate = to?.query?.duplicate;

      if (uuid) {
        const query = duplicate ? { duplicate: true } : {};
        const bannerEditUrl = buildUrl(`/banner/${uuid}`, query, to?.hash);
        return navigateTo(bannerEditUrl);
      } else {
        const bannerType = to?.query?.[QUERY_PARAM_KEY.DATA];
        const query = bannerType ? { [QUERY_PARAM_KEY.DATA]: bannerType } : {};
        const bannerCreateUrl = buildUrl("/banner/create", query, to?.hash);
        return navigateTo(bannerCreateUrl);
      }
    }
    case "/categories": {
      const categoryListUrl = buildUrl("/category", to?.query, to?.hash);
      return navigateTo(categoryListUrl, { replace: true });
    }
    case "/add-category": {
      const categoryCreateUrl = buildUrl("/category/create", to?.query, to?.hash);
      return navigateTo(categoryCreateUrl, { replace: true });
    }
    case "/edit-categories": {
      const isin = to?.query?.[QUERY_PARAM_KEY.ISIN];
      const categoryEditUrl = buildUrl(`/category/${isin}`, to?.query, to?.hash);
      return navigateTo(categoryEditUrl, { replace: true });
    }
  }
});
