// plugins/axios.js
import axios from "axios";
import { useNuxtApp } from "#app";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { prepareV2LogoutUrl } from "../utils/prepare-v2-logout-url.js";
import { useConfig } from "../composables/useConfig.js";

export default defineNuxtPlugin(async (nuxtApp) => {
  const { $store } = useNuxtApp();
  const { config: innerConfig } = useConfig();
  const axiosInstance = axios.create({
    baseURL: innerConfig.value.WEB_BACKEND_HOST || window.location.origin,
  });
  const { authorizationToken } = useInnitAuthStore();

  axiosInstance.interceptors.request.use( (config) => {
    if (
      config.baseURL?.includes(innerConfig.value.OAUTH_DOMAIN) ||
      config.url?.includes(innerConfig.value.OAUTH_DOMAIN)
    ) {
      return config;
    }

    if (!config.headers[innerConfig.value.HEADERS.X_INNIT_PROJECT_ID]) {
      config.headers[innerConfig.value.HEADERS.X_INNIT_PROJECT_ID] =
        $store.state?.userData?.project?.id || innerConfig.value.DEFAULT_PROJECT_ID;
    }

    if (authorizationToken.value) {
      config.headers["Authorization"] = authorizationToken.value;
    }

    return config;
  });

  axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
      const code = parseInt(error?.response?.status);

      if (code === 401) {
        if (nuxtApp.$auth) {
          nuxtApp.$auth.logout({
            openUrl(url) {
              const urlWithRedirect = prepareV2LogoutUrl(url);
              window.location.replace(urlWithRedirect);
            },
          }).catch(() => {});
        }
      }

      return Promise.reject(error);
    }
  );

  nuxtApp.provide("axios", axiosInstance);
});
