// plugins/apm.js
import { defineNuxtPlugin } from '#app';
import { ApmVuePlugin } from '@elastic/apm-rum-vue';
import { init as initApm } from '@elastic/apm-rum';

export default defineNuxtPlugin((nuxtApp) => {
  const config = nuxtApp.$config.public;

  if (config.ENVIRONMENT === 'localhost') {
    return;
  }

  // Initialize the APM agent
  const apm = initApm({
    serviceName: 'fims-light-rum',
    serverUrl: 'https://fce369a0f330460783b82ec3c121af06.apm.us-west-2.aws.cloud.es.io:443',
    environment: config.ENVIRONMENT || 'dev',
    breakdownMetrics: true,
    pageLoadTransactionName(location) {
      return location.pathname;
    },
  });

  // Use the APM Vue plugin
  nuxtApp.vueApp.use(ApmVuePlugin, {
    router: nuxtApp.$router,
    apm,
  });
});
