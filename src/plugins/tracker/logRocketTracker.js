import Logrocket from 'logrocket';
import Tracker from "./trackerManager";

export default class LogRocketTracker extends Tracker {

  constructor({ token, enable }) {
    super({
      token: token,
      enable: enable,
      tracker: Logrocket,
      name: "logrocket",
    });
    this.init();
  }

  init() {
    if (!this.isEnabled()) {
      return;
    }

    this.tracker.init(this.token);
  }

  identify(data, config) {
    if (!this.isEnabled(config)) {
      return;
    }
    const { id, email, name } = data;
    this.tracker.identify(id, {
      name,
      email,
    });
  }

  reset() {}

  sendEvent(eventName, eventData, config) {
    if (!this.isEnabled(config)) {
      return;
    }

    this.tracker.track(eventName, eventData);
  }
}
