import mixpanel from "mixpanel-browser";
import Tracker from "./trackerManager";

export default class MixpanelTracker extends Tracker {

  constructor({ token, enable }) {
    super({
      token: token,
      enable: enable,
      tracker: mixpanel,
      name: "mixpanel",
    });
    this.init();
  }

  init() {
    if (!this.isEnabled()) {
      return;
    }

    this.tracker.init(this.token);
  }

  identify(data, config) {
    if (!this.isEnabled(config)) {
      return
    }

    const { id, name } = data;
    this.tracker.identify(id);
    this.tracker.people.set_once({
      name,
    });
  }

  reset(config) {
    if (!this.isEnabled(config)) {
      return;
    }

    this.tracker.reset();
    console.log('Mixpanel reset and user data cleared.');
  }

  sendEvent(eventName, eventData, config) {
    if (!this.isEnabled(config)) {
      return;
    }
    this.tracker.track(eventName, eventData);
  }
}
