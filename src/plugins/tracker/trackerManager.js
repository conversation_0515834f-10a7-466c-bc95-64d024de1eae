export default class Tracker {
  token = undefined;
  tracker = null;
  enable = false;
  name;

  constructor({ token, enable, tracker, name }) {
    this.token = token;
    this.#setTracker(tracker);
    this.enable = enable;
    this.name = name;
  }

  init() {
    throw new Error("init() method must be implemented");
  }

  identify() {
    throw new Error("identify() method must be implemented");
  }

  reset() {
    throw new Error("reset() method must be implemented");
  }

  sendEvent() {
    throw new Error("sendEvent() method must be implemented");
  }

  isEnabled(config = {}) {
    const currentUrl = window.location.href;

    if (!this.enable || !this.tracker) {
      return false;
    }

    if (currentUrl.includes('localhost')) {
      return false;
    }

    if (config[this.name] && !config[this.name].enable) {
      return false;
    }

    return true;
  }

  #setTracker(tracker) {
    this.tracker = typeof tracker !== "undefined" ? tracker : null;
  }
}
