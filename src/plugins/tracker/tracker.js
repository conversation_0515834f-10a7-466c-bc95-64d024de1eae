import MixpanelTracker from "../../plugins/tracker/mixpanelTracker";
import LogRocketTracker from "./logRocketTracker";
import { useConfig } from "../../composables/useConfig.js";

/**
 * @description Sends an event to both Mixpanel and LogRocket trackers.
 * @typedef {function} SendEvent
 * @param {string} eventName - The name of the event to send.
 * @param {Object} [eventData={}] - Additional data to send with the event.
 * @param {Object} [config={}] - Additional configuration for the event.
 */

/**
 * @description Plugin for integrating event tracking
 * @var {{
 *   sendEvent: SendEvent
 *   identify: function
 *   reset: function
 * }} this.$tracker
 *
 */

/**
 * Plugin for integrating event tracking
 */
export default defineNuxtPlugin((nuxtApp) => {
  const { config } = useConfig();

  const mixpanel = new MixpanelTracker({
    token: config.value.MIXPANEL.TOKEN,
    enable: config.value.MIXPANEL.ENABLE,
  });
  const logRocket = new LogRocketTracker({
    token: config.value.LOGROCKET.TOKEN,
    enable: config.value.LOGROCKET.ENABLE,
  });

  const identify = (data, config) => {
    mixpanel.identify(data, config);
    logRocket.identify(data, config);
  };

  const reset = () => {
    mixpanel.reset();
  };

  const sendEvent = (
    eventName,
    eventData = {},
    config = {},
  ) => {
    mixpanel.sendEvent(eventName, eventData, config);
    logRocket.sendEvent(eventName, eventData, config);
  };

  const tracker = {
    reset,
    identify,
    sendEvent,
  };

  nuxtApp.provide('tracker', tracker);
});
