// plugins/auth.js
import { createAuth0 } from "@auth0/auth0-vue";
import { defineNuxtPlugin } from "#app";
import { getCreateAuthOptions } from "../сonstants/auth0-options.js";
import { useConfig } from "../composables/useConfig.js";

export default defineNuxtPlugin((nuxtApp) => {
  const { config } = useConfig();
  const options = getCreateAuthOptions({
    domain: config.value.OAUTH_DOMAIN,
    clientId: config.value.OAUTH_CLIENT_ID,
  });

  const auth0 = createAuth0(options);

  nuxtApp.vueApp.use(auth0);

  nuxtApp.provide("auth", auth0);
});
