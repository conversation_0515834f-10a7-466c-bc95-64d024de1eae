import { createI18n } from 'vue-i18n';
import enMessages from '../../locales/en.json'; // Import your localization file

export default defineNuxtPlugin((nuxtApp) => {
  const languageList = ['en'];
  const defaultLanguage = 'en';
  let language = defaultLanguage;

  // Detect browser language on client-side
  if (process.client) {
    const browserLanguage = navigator.language.split('-')[0];
    language = languageList.includes(browserLanguage) ? browserLanguage : defaultLanguage;
  }

  // Create i18n instance with legacy mode disabled
  const i18n = createI18n({
    legacy: false, // Disable legacy API mode
    locale: language,
    fallbackLocale: defaultLanguage,
    messages: {
      en: enMessages, // Use the imported messages
    },
  });

  // Attach i18n to Nuxt app (nuxtApp)
  nuxtApp.vueApp.use(i18n);

  // Define path method for generating localized paths
  nuxtApp.vueApp.config.globalProperties.$i18nPath = (link) => {
    if (i18n.global.locale.value === defaultLanguage) {
      return `/${link}`;
    }
    return `/${i18n.global.locale.value}/${link}`;
  };

  // Provide the 't' function globally
  nuxtApp.provide('t', i18n.global.t);
});
