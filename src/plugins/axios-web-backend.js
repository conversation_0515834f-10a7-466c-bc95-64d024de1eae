import axios from "axios";

export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig();
  // Create a custom axios instance for web backend API calls
  const api = axios.create({
    baseURL: config.public.webBackendHost,
    withCredentials: false,
    headers: {
      common: {
        Accept: 'application/json',
        'Content-Type': 'application/json'
      }
    },
    timeout: 10000,
  });

  // You can set additional configurations or interceptors here if needed

  // Provide the custom axios instance to the app as $axiosWebBackend
  nuxtApp.provide('axiosWebBackend', api);
});