.recipe-generator-main-container {
  font-family: $font-family-averta;
  .generator-dashboard-main-section {
    padding: 43px 10px 30px 30px;
    .generator-title {
      color: $green-dark;
      font-size: 28px;
      font-weight: bold;
      line-height: 16px;
      margin-bottom: 20px;
    }
    .prompt-container {
      display: flex;
      justify-content: space-between;
      .prompt-text-container {
        display: flex;
        margin: 20px 0px;
        height: 100%;
        width: 84%;
        justify-content: flex-start;
        align-items: center;
        .exit-icon {
          position: relative;
          right: 25px;

          img {
            cursor: pointer;
            height: 13px;
            width: 13px;
          }
        }
        .prompt-text-box {
          display: flex;
          width: 100%;
          height: 44px;
          background-color: $white;
          box-shadow: 1px 2px 2px 0px $box-shadow;
          border-radius: 20px 0 0 20px;
          padding: 5px 0 5px 20px;
          input {
            width: 95%;
            height: 100%;
            font-size: 16px;
            color: $midnight-charcoal;
            caret-color: $green;
            border: none;
            outline: none;
            background: none;
            box-shadow: none;
            font-weight: 400;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &::-webkit-input-placeholder {
              font-family: $font-family-open-sans;
              color: $spanish-gray;
            }

            &::-moz-placeholder {
              font-family: $font-family-open-sans;
              color: $spanish-gray;
            }
          }
        }
        .generate-icon {
          height: 15px;
          width: 15px;
          align-self: center;
          margin-right: 11px;
        }
        .text {
          color: $white;
          font-size: 16px;
          font-weight: 700;
        }
      }
      .line {
        height: 44px;
        width: 1px;
        border: 1px solid $spanish-gray-silver;
        margin-top: 25px;
        opacity: 30%;
      }
      .disable-button {
        opacity: 0.5;
        pointer-events: none;
      }
      .save-button {
        margin: 22px 0px;
        .generate-save-icon {
          height: 20px;
          width: 20px;
          margin-right: 4px;
        }
      }
    }
    .image-refresh-button {
      display: flex;
      font-weight: 700;

      cursor: pointer;
      .generated-images {
        font-size: 16px;
        padding-bottom: 2px;
        color: $green-dark;
        border-bottom: 2px solid $green-dark;
      }
      .refresh-image {
        color: $green;
        font-size: 16px;
        margin-left: 38px;
      }
      .disable-refresh {
        color: $box-shadow;
        pointer-events: none;
        margin-left: 38px;
      }
    }
    .generated-image-container {
      display: flex;
      margin-top: 26px;
      justify-content: space-between;
      .slider-controls {
        display: flex;
        align-items: center;
        .disabled {
          opacity: 0.5;
          pointer-events: none;
          visibility: hidden;
        }
        .slider-control {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          color: $green;
          font-weight: 700;
          background: $white;
          box-shadow: 0 0 10px $box-shadow;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }
      .border-image {
        border: 5px solid $green;
        border-radius: 8px;
        padding: 5px;
      }
      .image-preview {
        border-radius: 8px;
      }
      .generated-image-list {
        background-size: contain;
        height: 293px;
        width: 293px;
        background-color: $white;
        border-radius: 8px;
        display: flex;
        .image-title {
          color: $green;
          font-weight: bold;
          position: absolute;
          width: 33px;
          height: 33px;
          margin-top: 7px;
          margin-left: 6px;
          border: 1px solid $green;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 8px;
          background-color: $white;
        }
        .image-generator {
          height: 50px;
        }
        .loader-section {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 110px;
          .loader-image {
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 80px;
            height: 80px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
          }
        }
        .select-image {
          position: absolute;
          margin-top: 100px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 110px;
          .select-img-section {
            height: 80px !important;
            width: 80px !important;
            cursor: pointer;
          }
        }
        .selected-image {
          position: absolute;
          margin-top: 90px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 98px;
          .selected-img-section {
            height: 92px !important;
            width: 92px !important;
          }
        }
      }
    }
    .generator-output-main-section {
      margin-top: 34px;
      .generator-select-filter-list {
        display: flex;
        justify-content: space-between;
        .generator-selection {
          display: grid;
          width: 100%;
          grid-template-columns: 10% 10% 10% 70%;
          .generator-type {
            .disable-generator-button {
              color: $box-shadow !important;
              pointer-events: none;
            }
            .selector {
              color: $green;
              font-weight: 700;
              font-size: 16px;
              line-height: 1;
              width: max-content;
              cursor: pointer;
              padding-bottom: 4px;
            }
          }
          .selector-click {
            color: $green-dark !important;
            border-bottom: 2px solid $green-dark;
          }
          .last-panel {
            display: flex;
            justify-content: end;
          }
        }
      }
    }
    .generator-output-result-section {
      padding: 30px;
      background-color: $white;
      border-radius: 8px;
      margin-top: 20px;
      height: 400px;
      overflow-y: auto;
      scrollbar-color: $grainsboro $whispering-white-smoke;
      scrollbar-width: thin;

      ::-webkit-scrollbar {
        width: 12px;
        border-radius: 3px;
      }

      ::-webkit-scrollbar-track {
        background: $whispering-white-smoke;
      }

      ::-webkit-scrollbar-thumb {
        background: $grainsboro;
        border: 3px solid $transparent;
        border-radius: 15px;
        background-clip: content-box;
      }
      .generating-recipe-loader-container {
        background-color: $spring-bud;
        padding: 20px;
        border-radius: 8px;
        .generating-recipe-image {
          height: 28px;
          width: 28px;
        }
        .generating-recipe-text {
          font-size: 16px;
          font-weight: 600;
          line-height: 20px;

          color: $graphite-gray;
        }
      }
      .generator-response-checkers {
        display: grid;
        margin-top: 25px;
        .response-checker-text {
          display: flex;
          span {
            color: $graphite-gray;
            font-size: 16px;
            font-weight: 400;
            line-height: 28px;
          }
          img {
            position: relative;
            left: 6px;
            top: 2px;
            height: 19px;
            width: 19px;
          }
        }
      }
      .recipe-result-modify-main {
        border: 1px solid $green;
        border-radius: 8px;
        min-height: 83px;
        margin-bottom: 24px;
        padding: 14px 14px 0px 14px;
        position: relative;
        .recipe-modify-input-field {
          background: $transparent;
          display: block;
          font-size: 16px;
          font-weight: 400;
          color: $graphite-gray;
          line-height: 16px;
          overflow: hidden;
          margin-bottom: 16px;
          padding-bottom: 20px;
          cursor: pointer;
          border: 0;
          width: 100%;
          height: 100%;
        }
        .recipe-modify-button {
          display: flex;
          color: $green;
          font-weight: 700;
          font-size: 16px;
          line-height: 16px;

          position: absolute;
          float: right;
          right: 40px;
          bottom: 14px;
          .cancel-button {
            margin-right: 50px;
            cursor: pointer;
          }
          .confirm-button {
            cursor: pointer;
          }
        }
      }
      .recipe-result-output-section {
        border: 1px solid $green;
        height: 340px;
        border-radius: 8px;
        position: relative;
        padding: 20px 10px 50px 10px;
        overflow-y: auto;
        scrollbar-color: $grainsboro $whispering-white-smoke;
        scrollbar-width: thin;

        ::-webkit-scrollbar {
          width: 12px;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-track {
          background: $whispering-white-smoke;
        }

        ::-webkit-scrollbar-thumb {
          background: $grainsboro;
          border: 3px solid $transparent;
          border-radius: 15px;
          background-clip: content-box;
        }
        .recipe-result-edit-buttons {
          display: flex;
          color: $green;
          font-weight: 700;
          font-size: 16px;
          line-height: 16px;

          float: right;
          position: relative;
          top: 25px;
          right: 30px;
          .cancel-button {
            margin-right: 50px;
            cursor: pointer;
          }
          .confirm-button {
            cursor: pointer;
          }
        }
      }
      .recipe-result-output-details-main {
        display: grid;
        grid-template-columns: 40% 60%;
        .recipe-input {
          border: none;
          background-color: $fresh-green;
          padding: 10px;
          border-radius: 8px;
          width: 100%;
          &:focus {
            border: 1px solid $green;
          }
        }
        .serve-input {
          border: none;
          background-color: $fresh-green;
          padding: 10px 6px;
          border-radius: 8px;
          width: 30px;
          &:focus {
            border: 1px solid $green;
          }
        }
        .recipe-result-left-container {
          .recipe-name-ingredients-details {
            margin-right: 50px;
            .recipe-name {
              font-size: 20px;
              font-weight: 600;
              line-height: 20px;
              color: $graphite-gray;
              width: 100%;
            }
            .recipe-ingredients {
              font-size: 18px;
              font-weight: 400;
              line-height: 20px;
              color: $graphite-gray;
              margin: 20px 0px;
            }
            .recipe-ingredients-list {
              font-size: 14px;
              font-weight: 400;
              line-height: 16px;
              color: $graphite-gray;
            }
            .ingredients-textarea {
              width: 100%;
              padding: 10px;
              min-height: 200px;
              border: none;
              background-color: $fresh-green;
              border-radius: 8px;
              overflow: hidden;

              font-size: 14px;
              font-weight: 400;
              line-height: 16px;
              color: $graphite-gray;
              &:focus {
                border: 1px solid $green;
              }
            }
          }
        }
        .recipe-result-right-container {
          .serve-count-section {
            display: flex;
            align-items: center;
            .serves-count {
              font-size: 14px;
              font-weight: 600;
              line-height: 20px;
              color: $graphite-gray;
              margin-right: 4px;
            }
          }
          .recipe-result-instructions-detials {
            .instructions-text {
              font-size: 18px;
              font-weight: 400;
              line-height: 20px;
              color: $graphite-gray;
              margin: 20px 0px;
            }
            .instructions-list {
              font-size: 14px;
              font-weight: 400;
              line-height: 16px;
              color: $graphite-gray;
            }
            .instruction-textarea {
              width: 100%;
              padding: 10px;
              min-height: 200px;
              border: none;
              background-color: $fresh-green;
              border-radius: 8px;
              overflow: hidden;

              font-size: 14px;
              font-weight: 400;
              line-height: 16px;
              color: $graphite-gray;
              &:focus {
                border: 1px solid $green;
              }
            }
            p:first-child {
              margin-top: 0px;
            }
            p {
              margin-top: 30px;
            }
          }
        }
      }
      .advanced-output-details-main {
        display: grid;
        grid-template-columns: 33% 33% 33%;
        height: 100%;
        .advanced-validation-section {
          padding: 0px 20px;
          overflow-y: auto;
          scrollbar-color: $grainsboro $whispering-white-smoke;
          scrollbar-width: thin;
          white-space: pre-line;

          ::-webkit-scrollbar {
            width: 12px;
            border-radius: 3px;
          }

          ::-webkit-scrollbar-track {
            background: $whispering-white-smoke;
          }

          ::-webkit-scrollbar-thumb {
            background: $grainsboro;
            border: 3px solid $transparent;
            border-radius: 15px;
            background-clip: content-box;
          }
          .validation-text {
            font-size: 14px;
            font-weight: 700;
            line-height: 16px;
            color: $graphite-gray;
          }
          .validation-result {
            font-size: 14px;
            font-weight: 400;
            line-height: 16px;
            color: $graphite-gray;
            margin-top: 18px;
          }
        }
      }
    }
  }
}
