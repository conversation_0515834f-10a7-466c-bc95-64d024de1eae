
  .recipes-main-section {
    font-family: $font-family-averta;
    .back-to-master-list-main-container {
      margin-top: 28px;

      .back-to-master-list-btn {
        cursor: pointer;
        width: max-content;

        .back-to-master-list-text {
          color: $green;
          position: relative;
          top: 2px;
          left: 9px;
        }
      }

      .search-result-container {
        position: relative;
        left: 6px;
        top: 20px;

        .search-result-text {
          color: $black;
        }
      }
    }

    .recipe-info-container {
      margin: 30px 0px;
    }

    .page-head {
      width: 100%;
      height: 44px;
      margin: 15px 5px 25px 2px;
      font-size: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .table-name {
        cursor: default;
        line-height: 16px;
        float: left;
        font-weight: 700;
        color: $graphite-gray;
      }

      .master-list-section {
        display: flex;
        font-size: $font-size-base;
        width: 90%;
        justify-content: end;
        .main-section {
          position: relative;
        }

        .new-recipes-button {
          float: right;
          background-color: $green-light;
          color: $white;
          border-radius: 50px;
          border: none;
          margin-right: 5px;
          margin-left: 37px;
          padding: 12px 20px;
          cursor: pointer;
          box-shadow: 0px 1px 2px 0px $box-shadow;
          font-weight: 700;
        }
      }

      .master-list-searched {
        width: 100%;
        margin-right: 10px;
      }
    }

    .page-head-search {
      height: 12px;
    }

    .recipes-published-pop-up {
      display: flex;
      z-index: 999;
      border: 3px solid $earth-green;
      border-radius: 7px;
      width: 272px;
      height: 47px;
      align-items: center;
      background-color: $green-peppermint;
      justify-content: center;
      position: fixed;
      left: 43%;

      .correct-icon {
        position: relative;
        height: 22px;
        width: 22px;
        left: 18px;
      }

      .recipes-published-pop-up-text {
        text-align: center;
        justify-content: center;
        width: 100%;
        position: relative;
      }
    }

    .recipes-selection-main-container {
      display: flex;
      width: 90%;
      margin-left: -20px;
      padding: 0px 20px;
      align-items: center;
      margin-bottom: 12px;

      .select-all-checbox-section {
        width: 54%;
        margin-left: 14px;
        margin-bottom: 18px;
      }

      .recipes-selection-checkbox-section {
        display: block;
        position: relative;
        font-size: 22px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        input {
          position: absolute;
          opacity: 0;
          height: 0;
          width: 0;

          &:checked {
            ~ {
              .checkmark {
                background-color: $green-light;
                border: 3px solid $green-light;

                &:after {
                  display: block;
                }
              }
            }
          }
        }

        .disabled-selectAll {
          opacity: 0.6;
          cursor: default;
          pointer-events: none;
        }

        .checkmark {
          position: absolute;
          top: -3px;
          left: 0;
          height: 24px;
          width: 24px;
          color: $grainsboro;
          background-color: $white;
          border: 3px solid $grainsboro;
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            border: 3px solid $green-light;
          }

          &:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 2px;
            width: 6px;
            height: 12px;
            border: solid $white;
            border-width: 0 2px 2px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
            font-weight: 800;
          }
        }

        .disable-selectAll {
          pointer-events: none;
          opacity: 0.6;
        }

        .select-all-recipes {
          display: flex;
          color: $black $jet-black;
          position: absolute;
          left: 35px;
          cursor: pointer;
        }
      }

      .total-recipes-selected-section {
        position: relative;
        width: 140%;

        .total-recipes-selected-count {
          margin-right: 10px;
        }

        .total-recipe-selected-cross-icon {
          img {
            height: 12px;
            width: 12px;
            cursor: pointer;
            margin-bottom: 4px;
          }
        }
      }

      .recipe-schedule-container {
        margin-right: 50px;
      }

      .cancel-button {
        width: 35%;
        text-align: center;
        position: relative;
        left: 50px;

        button {
          color: $green;
          font-size: $font-size-base;
          font-weight: 800;
          cursor: pointer;
          border: none;
          background-color: $transparent;
        }
      }
    }

    .select-recipe-section {
      display: flex;
      justify-content: end;
      margin: 10px;

      .select-text {
        color: $green-light;
        border: none;
        background-color: $transparent;
      }
    }

    .recipe-status-section-main {
      font-weight: 700;

      .recipe-status-section {
        display: flex;
        align-items: center;
        color: $slate-gray;
        .filter-text {
          margin-right: 20px;
        }
      }

      .recipe-status-details {
        display: flex;
        border: 1px solid $grainsboro;
        border-radius: 22px;
        width: auto;
        height: 44px;
        justify-content: center;
        align-items: center;
        font-size: $font-size-base;
        padding: 10px 24px;
        cursor: pointer;

        .status-title {
          margin-left: 0px;
          cursor: pointer;
        }

        .recipe-status-selected {
          margin: 0px 4px 0px 12px;
          color: $green-light;
          font-weight: 800;
          text-transform: capitalize;
          cursor: pointer;
        }

        .recipe-status-dropdown-icon {
          transform: rotate(90deg);
          width: 20px;
          height: 20px;
          cursor: pointer;
          margin-right: 7px;
        }

        .line {
          height: 30px;
          width: 1.5px;
          background-color: $bright-gray;
          position: relative;
          margin: 0 11px;
        }

        .remove-status-section {
          .remove-status-button {
            height: 18px;
            width: 18px;
            cursor: pointer;
          }
        }
      }
      .recipe-created-filter-main {
        display: flex;
        justify-content: center;
        align-items: center;
        width: auto;
        height: 44px;
        border: 1px solid $grainsboro;
        border-radius: 22px;
        padding: 10px 24px;
        margin-left: 16px;
        cursor: pointer;

        .created-by-title {
          margin-left: 0px;
          cursor: pointer;
        }

        .created-by-selected {
          margin: 0px 4px 0px 12px;
          color: $green-light;
          font-weight: $font-weight-extra-bold;
          text-transform: capitalize;
          cursor: pointer;
        }

        .created-by-dropdown-icon {
          transform: rotate(90deg);
          width: 20px;
          height: 20px;
          cursor: pointer;
          margin-right: 7px;
        }

        .line {
          height: 30px;
          width: 1.5px;
          background-color: $bright-gray;
          position: relative;
          margin: 0 11px;
        }

        .created-by-section {
          .remove-created-by-button {
            height: 18px;
            width: 18px;
            cursor: pointer;
          }
        }
      }
    }
    .created-by-list-main {
      background-color: $white;
      width: auto;
      height: auto;
      z-index: 9;
      position: absolute;
      left: 30%;
      box-shadow: 0 1px 10px 0 $box-shadow;
      border-radius: 4px;
      margin-top: 10px;

      .created-by-list-data-main {
        .created-by-details-checkbox {
          width: 100%;
          padding-top: 20px;

          .created-by-selected {
            background-color: $aqua-spring;
          }

          .created-by-data {
            display: flex;
            padding: 12px;
            cursor: pointer;
            margin: 0px 36px;
            width: 80%;

            &:hover {
              background: $aqua-spring;
            }

            .created-by-search-list-data {
              display: flex;
              justify-content: space-between;
              width: 77%;
              position: relative;
              top: 2px;
              left: 54px;

              .created-by-sort-name {
                font-weight: $font-weight-normal;
                color: $black;
              }
            }
          }
        }
      }

      .apply-button-container {
        margin-bottom: 20px;

        .status-save-button {
          display: flex;
          justify-content: center;
          position: sticky;
          background-color: $white;
          width: 414px;

          .status-button {
            background: $green-light;
            border-radius: 20px;
            border: none;
            width: 175px;
            height: 35px;
            color: $white;
            cursor: pointer;
            margin-top: 24px;
          }

          .disable-status-button {
            pointer-events: none;
            opacity: 0.7;
          }
        }
      }
    }
    .recipe-status-list-main {
      background-color: $white;
      width: auto;
      height: auto;
      z-index: 9;
      position: absolute;
      right: 20%;
      box-shadow: 0 1px 10px 0 $box-shadow;
      border-radius: 4px;
      margin-top: 10px;

      .recipe-status-list-data-main {
        .recipe-status-details-checkbox {
          width: 100%;
          padding-top: 20px;

          .recipe-status-selected {
            background-color: $aqua-spring;
          }

          .recipe-status-data {
            display: flex;
            padding: 12px;
            cursor: pointer;
            margin: 0px 36px;

            &:hover {
              background: $aqua-spring;
            }

            .round {
              position: relative;
            }

            .round label {
              background-color: $white;
              border: 2px solid $grainsboro;
              border-radius: 100%;
              cursor: pointer;
              height: 24px;
              left: 0px;
              position: absolute;
              width: 24px;

              &:hover {
                border: 1px solid $green-light;
              }
            }

            .round label:after {
              border: 2px solid $white;
              border-top: none;
              border-right: none;
              content: "";
              height: 6px;
              left: 5px;
              opacity: 0;
              position: absolute;
              top: 7px;
              -webkit-transform: rotate(-45deg);
              transform: rotate(-45deg);
              width: 12px;
              cursor: pointer;
            }

            .round input[type="radio"] {
              visibility: hidden;
              display: none;
            }

            .round input[type="radio"] + label {
              background-color: $white;
              border: 2px solid $green-light;
            }

            .round input[type="radio"]+label:after {
              opacity: 1;
              top: 3px;
              left: 2px;
              width: 16px;
              height: 16px;
              border-radius: 70%;
              background: $green-light;
            }

            .recipe-status-search-list-data {
              display: flex;
              justify-content: space-between;
              width: 77%;
              position: relative;
              top: 2px;
              left: 54px;

              .search-recipe-status-sort-name {
                font-weight: $font-weight-normal;
                color: $black;
              }
            }
          }
        }

        .add-ingredients-background {
          background-color: RGB(231, 246, 228);
        }
      }

      .apply-button-container {
        margin-bottom: 20px;

        .status-save-button {
          display: flex;
          justify-content: center;
          position: sticky;
          background-color: $white;
          width: 414px;

          .status-button {
            background: $green-light;
            border-radius: 20px;
            border: none;
            width: 175px;
            height: 35px;
            color: $white;
            cursor: pointer;
            margin-top: 24px;
          }

          .disable-status-button {
            pointer-events: none;
            opacity: 0.7;
          }
        }
      }
    }

    .search-recipe-filter {
      right: 1%;
      left: 0;
    }

    .last-deploy {
      position: relative;
      float: right;
      font-weight: bolder;
      font-size: 13px;
      color: $spanish-gray;
      right: -185px;
      top: 18px;
    }

    .crud-table {
      margin-top: 0px !important;
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
      background: $white;
      border-radius: 8px;
      border: 1px solid $platinum;
      cursor: default;

      .main-table {
        th {
          padding: 8px 15px;
          background: $white-smoke;
          font-weight: 700;
        }

        .recipe-checkbox-header {
          width: 4%;
        }

        td {
          padding: 26px 15px 12px 15px;
          border-bottom: 1px solid $grainsboro-cloud;
        }

        .recipe-table-row {
          &:hover {
            background: $aqua-spring;
          }
        }

        .recipe-selected {
          background: $aqua-spring !important;
        }

        .unpublished-select {
          cursor: pointer;
        }

        .recipe-selected-color {
          &:hover {
            background: $aqua-spring !important;
          }
        }

        .disabled-row {
          cursor: default;

          &:hover {
            background: white !important;
          }
        }

        .recipe-checkbox-column {
          width: 4%;
        }

        .recipe-number {
          font-family: $font-family-averta-regular;
        }

        .recipe-checkbox-section {
          position: relative;
          top: 14px;
        }

        .recipe-image {
          width: 6%;
        }

        @media only screen and (min-width: 1250px) {
          td {
            padding: 26px 8px 12px 8px;
          }

          th {
            padding: 8px 8px;
          }
        }

        .container-promote {
          position: relative;
          right: 17px;
          background-color: $white;
          height: 24px;
          width: 100%;
          margin: 22px;
          border-collapse: collapse;
          overflow: visible;
          cursor: pointer;
          font-size: 0.9em;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;

          input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;

            &:checked {
              ~ {
                .checkmark {
                  background-color: $green-light;
                  border: 3px solid $green-light;

                  &:after {
                    display: block;
                  }
                }
              }
            }
          }

          .disabled-row-checkmark {
            position: absolute;
            top: 0;
            opacity: 0.6;
            left: 0;
            height: 24px;
            width: 24px;
            color: $grainsboro;
            background-color: $white;
            border: 3px solid $grainsboro;
            border-radius: 4px;
            cursor: default;
          }

          .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 24px;
            width: 24px;
            color: $grainsboro;
            background-color: $white;
            border: 3px solid $grainsboro;
            border-radius: 4px;

            &:hover {
              border: 3px solid $green-light;
            }

            &:after {
              content: "";
              position: absolute;
              display: none;
              left: 6px;
              top: 2px;
              width: 6px;
              height: 12px;
              border: solid $white;
              border-width: 0 2px 2px 0;
              -webkit-transform: rotate(45deg);
              -ms-transform: rotate(45deg);
              transform: rotate(45deg);
            }
          }
        }

        .title {
          color: $spanish-gray;
          font-weight: bolder;
          font-size: 12px;
          line-height: 1;
          text-align: left;
        }

        .table-head {
          border-radius: 8px 8px 0px 0px;

          .recipe-variant-head {
            text-align: center;
          }

          @media only screen and (max-width: 1250px) {
            .recipe-variant-head {
              width: 150px;
            }
          }
        }

        th:first-child {
          border-top-left-radius: 8px;
        }

        th:last-child {
          border-top-right-radius: 8px;
        }

        .table-body {
          font-size: 14px;
          font-weight: lighter;
          text-align: left;
          padding: 20px;
          line-height: 1;

          .disabled-row {
            cursor: default;

            &:hover {
              background: none;
            }
          }

          tr:last-child {
            td {
              border-bottom: unset;
            }

            td:first-child {
              border-bottom-left-radius: 8px;
            }

            td:last-child {
              border-bottom-right-radius: 8px;
            }
          }

          .recipe-variant {
            text-align: center;

            .langs-data {
              padding: 2px;
            }
          }

          .table-image {
            object-fit: cover;
            min-width: 50px;
            height: 50px;
          }

          .recipe-name {
            font-weight: 700;
            width: 260px;
            min-width: 250px;
            position: relative;
            .recipe-name-text {
              max-width: 250px;
              height: 16px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .recipe-ai-generated-main-section {
            display: flex;
            background-color: $spring-bud;
            position: absolute;
            bottom: 54px;
            padding: 4px 10px;
            border-radius: 4px;
            .ai-generated-container {
              display: flex;
              align-items: center;
              .ai-generated-text {
                font-family: $font-family-averta;
                font-weight: $font-weight-semi-bold;
                font-size: $font-size-10;
                line-height: $line-height-16;
                color: $graphite-gray;
                margin-left: 6px;
              }
              .ai-generated-image {
                width: 13px;
                height: 13px;
              }
            }
          }

          .recipe-subtitle-name {
            color: $shadow-gray;
            font-weight: 400;
            width: 250px;
            padding-bottom: 1px;
            min-width: 240px;
            margin-top: 8px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            position: absolute;
          }

          .recipe-modified {
            color: $stone-gray;
          }

          .recipe-schedule-status-section {
            display: flex;
            align-items: center;

            .recipe-scheduled-section {
              margin-left: 5px;
              position: relative;

              .recipe-schedule-image {
                background-color: $rose-white;
                border-radius: 4px;
                border: 1px solid $transparent;

                img {
                  height: 22px;
                  width: 22px;
                }

                &:hover {
                  border: 1px solid $copper-rust;
                }
              }

              .error-class-img {
                background-color: $rose-white;
                border-radius: 4px;
                border: 1px solid $transparent;

                img {
                  height: 22px;
                  width: 22px;
                }

                .error-icon {
                  width: 14px;
                  height: 14px;

                  &:hover {
                    border: none;
                  }
                }
              }

              .recipe-schedule-tool-tip {
                visibility: hidden;
                width: 225px;
                background-color: $black;
                color: $white;
                text-align: center;
                border-radius: 6px;
                padding: 10px 0;
                position: absolute;
                z-index: 1;
                bottom: 155%;
                right: -94px;
                opacity: 0.7 !important;
                display: grid;
                gap: 3px;

                &::after {
                  content: "";
                  position: absolute;
                  top: 100%;
                  right: 45%;
                  border-width: 5px;
                  border-style: solid;
                  border-color: $black $transparent $transparent $transparent;
                }
              }

              &:hover .recipe-schedule-tool-tip {
                visibility: visible;
              }

              .recipe-schedule-tool-tip-publish {
                visibility: hidden;
                width: 301px;
                height: 91px;
                background-color: $black;
                color: $white;
                text-align: center;
                border-radius: 6px;
                padding: 10px 0;
                position: absolute;
                z-index: 1;
                bottom: 133%;
                right: -44px;
                opacity: 0.7 !important;
                display: grid;
                gap: 3px;

                .text {
                  line-height: $line-height-18;
                  font-family: $font-family-averta;
                  font-weight: $font-weight-normal;
                  font-size: $font-size-12;
                  opacity: 100%;
                }

                &::after {
                  content: "";
                  position: absolute;
                  top: 100%;
                  right: 16%;
                  border-width: 5px;
                  border-style: solid;
                  border-color: $black $transparent $transparent $transparent;
                }

                button {
                  text-transform: none;
                  color: $black;
                  width: 99px;
                  border-radius: 25px;
                  height: 20px;
                  border: none;
                  font-weight: $font-weight-normal;
                  font-family: $font-family-averta;
                  font-size: $font-size-12;
                }
              }

              &:hover .recipe-schedule-tool-tip-publish {
                visibility: visible;
              }
            }

            .recipe-scheduled-left-section {
              margin-left: 15px !important;
            }
          }

          .menu {
            position: relative;
            padding: 12px 15px;

            .menu-container {
              background-color: $white;
              border-radius: 10px;
              width: 28px;
              height: 20px;
              cursor: pointer;
              display: flex;
              align-items: center;

              .table-edit-btn {
                width: 17px;
                height: 5px;
                padding: 0;
                margin: 0 auto;
                object-fit: cover;
                z-index: 1;
              }

              &:hover {
                background-color: $pure-white;
              }
            }

            .menu-selected {
              background-color: $aqua-spring;

              &:hover {
                background-color: $aqua-spring;
              }
            }

            .menu-box {
              display: block;
              position: absolute;
              right: 22px;
              width: 151px;
              top: 66%;
              z-index: 2;
              box-shadow: 0 4px 10px 0 $shadow-black,
                0 3px 5px 0 $faint-black,
                0 0 0 1px $shadowy-black;
              border-radius: 4px;
              background: $white;

              .menu-list {
                list-style: none;
                background: $white;
                border-radius: 8px;
                margin: 11px 5px;

                li {
                  display: flex;
                  align-items: center;
                  height: 30px;
                  width: 141px;
                  color: $black;
                  padding-left: 10px;
                  font-weight: $font-weight-bold;

                  .list-button {
                    background: none;
                    border: none;
                    padding: 0;
                    margin: 0;
                    font: inherit;
                    color: inherit;
                    cursor: pointer;
                    width: inherit;
                    text-align: left;
                  }

                  &:hover {
                    color: $white;
                    background: $green;
                    cursor: pointer;
                  }
                }

                .disable {
                  filter: opacity(60%);
                  cursor: none;

                  &:hover:before {
                    content: attr(data-tooltip);
                    position: absolute;
                    padding: 6px;
                    margin: 44px -10px -14px -78px;
                    background: $orange;
                    color: $white;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 700;
                    box-shadow: 0 4px 10px 0 $shadow-black,
                      0 3px 5px 0 $faint-black,
                      0 0 0 1px $shadowy-black;
                  }

                  &:hover:after {
                    content: "";
                    position: absolute;
                  }
                }

                .editButton {
                  &:hover:before {
                    content: attr(data-tooltip);
                    position: absolute;
                    padding: 6px;
                    margin: 73px 0px -12px -70px;
                    background: $orange;
                    color: $white;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 700;
                    box-shadow: 0 4px 10px 0 $shadow-black,
                      0 3px 5px 0 $faint-black,
                      0 0 0 1px $shadowy-black;
                  }

                  &:hover:after {
                    content: "";
                    position: absolute;
                  }
                }

                .hide-data {
                  display: none;
                }
              }
            }
          }

          .published {
            width: 112px;
            height: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-transform: capitalize;
            color: $green;
            font-family: $font-family-averta-regular;

            .unpublished-state {
              font-size: 13px;
              width: 112px;
              height: 25px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              text-transform: capitalize;
              color: $silver;
              background-color: $pearl-mist;
              border-radius: 4px;
              font-family: $font-family-arial-serif;

              img {
                position: relative;
                right: 6px;
              }
            }

            .published-state {
              width: 112px;
              height: 25px;
              color: $green;
              background-color: $aqua-spring;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border-radius: 4px;

              img {
                position: relative;
                right: 6px;
                height: 17px;
              }
            }

            .preview-state {
              font-size: 13px;
              width: 112px;
              height: 25px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              text-transform: capitalize;
              color: $amber;
              background-color: $gloden-glow;
              border-radius: 4px;
            }

            .failed-state {
              font-size: 13px;
              width: 112px;
              height: 25px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              text-transform: capitalize;
              color: $radiant-red-orange;
              background-color: $cotton-candy-pink;
              border-radius: 4px;
            }

            .publishing-state {
              font-size: 12px;
              width: 112px;
              height: 25px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              text-transform: capitalize;
              color: $deep-sea-blue;
              background-color: $baby-blue;
              border-radius: 4px;

              img {
                position: relative;
                right: 6px;
              }
            }

            .timeout-state {
              font-size: 13px;
              width: 113px;
              height: 25px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              text-transform: capitalize;
              color: $crusta;
              background-color: $light-coral;
              border-radius: 4px;
            }
          }
        }
      }
    }

    .extra-margin {
      margin-bottom: 90px;
    }

    .product-not-found {
      border-radius: 8px;
      margin-top: 20px;
      height: 100px;
      background-color: $white;
      border: 1px solid $grainsboro;
      color: $shadow-gray;
      text-align: center;
      line-height: 1;

      .no-data {
        margin: auto;
        padding-top: 29px;
        color: $shadow-gray;
        font-weight: 700;
        text-align: center;
        background-color: $white;
        margin-bottom: 1px;

        .no-data-text {
          color: $shadow-gray;
          margin-top: 3px;
        }
      }
    }

    .product-not-found-searched {
      height: 76px !important;
    }
  }

  .open-recipe-publish-popup-modal {
    position: relative;
    padding: 0px 10px;
    min-width: 838px;

    .recipe-publish-main-header {
      width: 100%;
      padding: 0px 24px;

      .recipe-publish-info {
        height: 14px;
        width: 14px;
        margin-left: 10px;
      }

      .recipe-publish-tooltip-section {
        display: flex;
        justify-content: flex-start;
      }

      .tooltip-main-container-for-recipe-publish {
        position: relative;
        display: inline-block;
      }

      .recipe-publish-header {
        width: 30%;
        display: flex;
        line-height: 20px;
        color: $black;
        margin-bottom: 20px;
      }
    }

    .close-publish-recipe-modal {
      position: absolute;
      cursor: pointer;
      top: 0px;
      right: 10px;
    }
  }

  .open-recipe-schedule-popup-modal {
    width: auto !important;
    padding: 0px 10px;
    min-width: 838px;

    .dates-section {
      display: flex;
      justify-content: flex-start;
      margin-top: 20px;
      margin-bottom: 20px;
      width: fit-content;
      position: relative;

      .calendar-zone {
        position: relative;
        top: 46px;
        right: 400px;

        .calendar-range-section-main {
          position: fixed;
          z-index: 9999;
        }
      }

      .select-date-heading {
        line-height: 10px;
        display: flex;
        align-items: center;
        margin-right: 20px;
      }
    }

    .recipe-schedule-for-recipe-list-button-container {
      display: flex;
      justify-content: end;
      margin-top: 15px;
      gap: 20px;
    }

    .recipe-schedule-main-header {
      display: flex;
      justify-content: space-between;

      .recipe-schedule-header {
        font-family: $font-family-averta;
      }

      .close-schedule-recipe-modal {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }

    .recipe-schedule-button {
      position: relative;
      float: right;
      background-color: $white;
      color: $green;
      font-size: 14px;
      border-radius: 30px;
      border: none;
      height: 36px;
      width: 131px;
      cursor: pointer;
      box-shadow: 0px 1px 2px 0px $box-shadow;
      font-weight: 700;
      bottom: 0px;
      text-transform: uppercase;
    }

    .disable-button {
      opacity: 0.6;
      pointer-events: none;
    }
  }

  .updating-recipe-container-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 559px;
    min-height: 293px;
    padding: 0 20px;

    .input-loading {
      height: 60px;
      display: flex;
      justify-content: center;

      .loader-image {
        border: 3px solid $white;
        border-radius: 50%;
        border-top: 3px solid $green;
        border-right: 3px solid $green;
        border-bottom: 3px solid $green;
        width: 20px;
        height: 20px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }

      .publised-icon {
        width: 24px;
        height: 24px;
      }
    }

    .updating-text {
      background-color: $green-peppermint;
      border-radius: 4px;
      border: 1px solid $green-fringy-flower;
      width: 468px;
      height: 57px;
      text-align: center;

      p {
        color: $shadow-gray;
        padding: 18px 0px;
      }
    }
  }

  .deleted-Search-recipe {
    position: relative;
    left: 46%;
    bottom: 86px;
  }

  .deleted-recipe-popup-main {
    display: flex;
    justify-content: center;
    z-index: 999;

    .deleted-recipe-popup {
      display: flex;
      position: fixed;
      background: $light-mint;
      border: 1px solid $green;
      width: 324px;
      height: 46px;
      border-radius: 7px;

      .deleted-recipe-popup-container {
        display: flex;
        padding: 10px 24px;
        width: 100%;
        justify-content: space-between;
      }

      .delete-text-container {
        display: flex;

        .deleted-recipe-popup-image {
          width: 22px;
          height: 22px;
        }

        .deleted-recipe-popup-text {
          font-weight: 400;
          font-size: 16px;
          color: $jet-black;
          margin-left: 8px;
        }
      }

      .close-icon-image {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
    }

    .center-popup {
      left: 45%;
    }
  }

  .wrong-recipe-popup-main {
    display: flex;
    justify-content: center;
    z-index: 999;

    .wrong-recipe-popup {
      display: flex;
      position: fixed;
      top: 74px;
      background: $light-rose;
      border: 1px solid $peachy-pink;
      width: 324px;
      height: 68px;
      border-radius: 7px;

      .wrong-recipe-popup-container {
        display: flex;
        padding: 10px 24px;
        width: 100%;
        justify-content: space-between;
      }

      .wrong-text-container {
        display: flex;

        .wrong-recipe-popup-image {
          width: 22px;
          height: 22px;
          margin-top: 2px;
        }

        .wrong-recipe-popup-text {
          color: $jet-black;
          margin-left: 8px;
        }

        .try-again-text {
          color: $jet-black;
          margin-left: 8px;
        }
      }

      .close-icon-image {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
    }
  }

.delete-loader-section {
  .delete-content {
    width: 100%;
    height: 100%;
    background-color: $white;
    border-radius: 8px;
    position: relative;

    .loading-popup {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      min-height: 255px;
      padding: 0 20px;

      .delete-loading {
        height: 60px;
        display: flex;
        justify-content: center;

        .loader-image {
          border: 3px solid $white;
          border-radius: 50%;
          border-top: 3px solid $persian-red;
          border-right: 3px solid $persian-red;
          border-bottom: 3px solid $persian-red;
          width: 20px;
          height: 20px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }
      }

      .loading-text {
        background-color: $vanilla-ice;
        border-radius: 4px;
        width: 468px;
        height: 57px;

          p {
            color: $jet-black;
            padding: 18px 0px;
            text-align: center;
          }
      }
    }
  }
}
.open-preview-recipe-modal {
  width: 100%;
  text-align: initial;
  position: relative;

  .recipe-main-preview-header {
    width: 100%;
    padding: 0px 24px;

    .recipe-preview-header {
      width: 100%;
      line-height: 40px;
      color: $black;
    }
  }

  .close-preview-recipe-modal {
    position: absolute;
    cursor: pointer;
    top: 0px;
    right: 10px;
  }

  .disableCross {
    pointer-events: none;
    opacity: 0.3;
  }

  .open-preview-recipe-main {
    width: 85vw;
  }
}
