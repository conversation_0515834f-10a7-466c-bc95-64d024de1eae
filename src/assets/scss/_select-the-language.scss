.select-the-language-modal {
  min-width: 560px;
  height: 260px;
  padding: 0 20px 0 20px;
  font-family: $font-family-averta;

  .select-the-language-text {
    display: flex;
    justify-content: space-between;
    padding: 22px 0px;

    span {
      font-size: 24px;
      color: $black;
      font-weight: 700;
    }

    img {
      height: 24px;
      width: 24px;
      cursor: pointer;
    }
  }

  .select-the-language-group-dropdown {
    margin-top: 20px;

    .selected-language-disable-cursor {
      cursor: pointer;
    }

    .select-the-language-group-variant-selected-language {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
      min-height: 50px;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      align-items: center;
      padding: 0px 15px 0px 10px;

      span {
        display: flex;
        align-items: center;

        img {
          height: 20px;
          width: 30px;
        }

        p {
          font-size: 16px;
          font-weight: 400;
          color: $black;
          margin-left: 12px;
        }
      }

      .select-the-language-group-dropdown-icon {
        transform: rotate(90deg);
        width: 6px;
        height: 10px;
      }
    }

    .select-the-language-group-autocomplete-results {
      position: absolute;
      list-style: none;
      left: -5px;
      top: 170px;
      box-shadow: 0 1px 10px 0 $box-shadow;
      background: $white;
      z-index: 1;
      color: $charcoal-light;
      overflow-y: scroll;
      width: 90%;
      border-radius: 8px;
      scrollbar-width: none;
      text-align: left;
      margin-left: 33px;
      height: max-content;
      max-height: 115px;

      &::-webkit-scrollbar {
        display: none;
      }

      .select-the-language-group-language-list {
        span {
          display: flex;

          img {
            height: 20px;
            width: 30px;
          }

          p {
            font-size: 16px;
            font-weight: 400;
            color: $black;
            margin-left: 12px;
          }
        }
      }
    }

    .select-the-language-group-autocomplete-result {
      padding: 8px 10px;
      cursor: pointer;
      margin: 2px;
      border-radius: 4px;

      &.is-active,
      &:hover {
        background: $green;

        p {
          color: $white;
        }
      }
    }
  }

  .select-the-language-group-variant-next-button {
    display: flex;
    justify-content: center;
    margin-top: 50px;

    .next-button {
      background: $green-light;
      padding: 14px 48px;
      border-radius: 50px;
      border: none;
      cursor: pointer;

      p {
        color: $white;
        font-size: 14px;
        font-weight: 900;
      }
    }

    .disable-select-language-button {
      opacity: 0.5;
    }
  }
}
