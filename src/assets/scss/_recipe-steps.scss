  .recipe-container {
    font-family: $font-family-averta;
    .steps-section {
      padding: 20px;
      width: 92%;
      margin-top: 0;

      .recipe-step {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .step-header {
          position: relative;

          .recipe-step-header {
            font-size: $font-size-20;
            font-weight: $font-weight-bold;
            color: $black;
          }

          .compulsory-field {
            position: absolute;
            top: 0;
            left: 127px;
            color: $red;
          }
        }

        .recipe-variant-step-form-text-container {
          display: flex;

          .recipe-variant-flag-section {
            margin-right: 10px;

            .recipe-flag {
              max-width: 32px;
              max-height: 30px;
            }
          }
        }

        .instruction-minimize-view {
          display: flex;
          justify-content: flex-end;
          visibility: visible;
          height: 14px;
          margin-top: 20px;

          .recipe-step-form-dropdown {
            max-width: max-content;
            font-size: $font-size-14;
            font-weight: $font-weight-bold;
            color: $green;
            pointer-events: all;
            cursor: pointer;

            .dropdown-icon {
              width: 14px;
              height: 14px;
              position: relative;
              top: -2px;
              margin-left: 6px;
            }
          }

          .hide-button {
            opacity: 0;
            pointer-events: none;
          }
        }
      }

      .description-section {
        .instruction-hidden-list {
          background: $light-mint;
          opacity: 0.5;
        }

        .instructions-row-data {
          padding-top: 10px;

          .description {
            width: 100%;
            min-height: 100px;
            height: unset;
            padding: 15px 10px;
            margin-bottom: 15px;
            border: 1px solid $ethereal-whisper-gray;
            border-radius: 8px;
            background-color: $white;
            box-shadow: 0px 1px 5px 0px $box-shadow;
            position: relative;

            &:hover {
              background-color: $light-mint;
              border: 1px solid $green;

              .instruction-drag-icon {
                visibility: visible;

                img {
                  cursor: all-scroll;
                }
              }

              .instruction-full-view,
              .instruction-minimize-view {
                width: 100%;
                visibility: visible;
              }
            }

            .instruction-id {
              width: 100%;
              margin-top: 10px;
              margin-bottom: 5px;

              span {
                width: 100%;
                font-size: $font-size-24;
                font-weight: $font-weight-bold;
                color: $black;
                list-style: 20px;
              }
            }

            .empty-instruction {
              position: absolute;
              left: 20px;
              color: $dark-gray;
              cursor: default;
            }

            .instruction-drag-icon {
              position: absolute;
              top: 0;
              left: 50%;
              width: 32px;
              height: 32px;
              text-align: center;
              visibility: hidden;
            }

            .instruction-full-view {
              width: 100%;
              height: 14px;
              margin-top: 10px;
              display: flex;
              justify-content: flex-start;
              visibility: visible;

              .recipe-step-form-dropdown {
                max-width: max-content;
                font-size: $font-size-14;
                font-weight: $font-weight-bold;
                color: $green;
                cursor: pointer;
                pointer-events: all;

                .dropdown-icon {
                  width: 20px;
                  height: 14px;
                  position: relative;
                  top: -2px;
                }
              }
            }

            .instruction-minimize-view {
              width: 100%;
              height: 14px;
              margin-top: 20px;
              display: flex;
              justify-content: flex-end;
              visibility: visible;
              .recipe-step-form-dropdown {
                max-width: max-content;
                font-size: $font-size-14;
                font-weight: $font-weight-bold;
                color: $green;
                cursor: pointer;
                pointer-events: all;

                .dropdown-icon {
                  width: 20px;
                  height: 14px;
                  position: relative;
                  top: -2px;
                  transform: rotate(180deg);
                }
              }

              .hide-button {
                opacity: 0;
                pointer-events: none;
              }
            }

            .instruction-container {
              width: 100%;
              display: flex;

              .instruction-gallery {
                width: 140px;
                min-width: 140px;
                height: 90px;
                margin-left: 18px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                overflow: hidden;
                background-image: url("@/assets/images/recipe-detail-upload.png");
                background-position: center;
                background-size: cover;
                background-repeat: no-repeat;

                .instruction-media {
                  width: 100%;
                  height: 100%;
                  position: absolute;
                  object-fit: cover;
                }

                .play-video-icon-image {
                  width: 28px;
                  height: 28px;
                  position: absolute;
                  left: 8px;
                  bottom: 8px;
                  float: left;
                  z-index: 1;
                  cursor: pointer;
                  pointer-events: all;
                }
              }

              .disable {
                background: none !important;
              }

              .instruction-content {
                width: 100%;
                min-height: 38px;
                margin-left: 23px;

                .step-count-number {
                  height: 38px;
                  font-size: $font-size-base;
                  font-weight: $font-weight-bold;
                  line-height: 20px;
                  color: $spanish-gray;
                  margin-bottom: 5px;
                }
              }

              .buttons-section {
                display: flex;
                align-items: center;

                .edit {
                  width: 39px;
                  height: 41px;
                  padding: 0 10px;
                  margin: 5px;
                  border: 0;
                  border-radius: 4px;
                  background-color: $white;
                  color: $green;
                  font-weight: $font-weight-normal;
                  display: flex;
                  align-items: center;
                  box-shadow: 0 2px 4px 0 $box-shadow;
                  cursor: pointer;
                  img {
                    width: 19px;
                    height: 20px;
                  }
                }

                .delete {
                  width: 39px;
                  height: 41px;
                  padding: 0 10px;
                  margin: 5px;
                  border: 0;
                  border-radius: 4px;
                  background-color: $white;
                  color: $green;
                  font-weight: $font-weight-normal;
                  display: flex;
                  align-items: center;
                  box-shadow: 0 2px 4px 0 $box-shadow;
                  cursor: pointer;
                  img {
                    width: 18px;
                    height: 20px;
                  }
                }
              }

              .hide-button {
                opacity: 0;
                pointer-events: none;
              }
            }

            .instruction-drop-section {
              margin-top: 10px;
              margin-left: 8px;

              .instruction-ingredients-section {
                display: flex;
                justify-content: space-between;

                .instruction-description {
                  width: 55%;
                  min-width: 55%;

                  .instruction {
                    width: 100%;
                    min-height: 45px;
                    padding: 10px;
                    margin-right: 10px;
                    cursor: default;

                    span {
                      display: block;
                      font-size: $font-size-base;
                      padding-top: 0;
                    }

                    .instruction-text {
                      font-size: $font-size-base;
                      font-weight: $font-weight-bold;
                      color: $black;
                      margin-bottom: 10px;
                    }
                  }
                }

                .ingredients-section {
                  width: 35%;
                  min-width: 35%;
                  padding: 0 18px 0 10px;
                  margin-top: 10px;
                  box-shadow: none;

                  .ingredients {
                    margin-bottom: 2px;

                    .ingredients-header {
                      font-size: $font-size-base;
                      font-weight: $font-weight-bold;
                      color: $black;
                    }
                  }

                  .ingredients-and-value {
                    display: flex;
                    position: relative;
                    padding: 5px 0px;

                    .circle-pointer {
                      position: absolute;
                      left: 0;
                      top: 45%;
                      width: 4px;
                      min-width: 4px;
                      height: 4px;
                      border-radius: 2px;
                      background-color: $green-light;
                    }

                    .data {
                      margin-left: 13px;

                      .ingredients {
                        font-size: $font-size-base;
                        font-weight: $font-weight-bold;
                        color: $black;
                        word-break: break-word;

                        .comma {
                          font-weight: $font-weight-normal;
                        }
                      }

                      .modifier {
                        font-size: $font-size-base;
                        font-weight: $font-weight-normal;
                        color: $charcoal-gray;
                      }

                      .value {
                        font-size: $font-size-base;
                        font-weight: $font-weight-normal;
                        color: $charcoal-gray;
                        word-break: break-all;
                      }
                    }
                  }
                }
              }
            }

            .instruction-description {
              width: 100%;
              position: relative;

              .instruction {
                width: 100%;
                padding: 10px;
                min-height: 45px;
                margin-right: 10px;
                cursor: default;
                display: flex;
                flex-direction: column;
                justify-content: center;
                border-radius: 4px;

                span {
                  display: block;
                  font-size: $font-size-base;
                  padding-top: 0;
                }

                .instruction-text {
                  font-size: $font-size-base;
                  font-weight: $font-weight-bold;
                  color: $black;
                  margin-bottom: 10px;
                }
              }
            }
          }

          .description-fims {
            &:hover {
              background-color: $white;
              border: 1px solid $ethereal-whisper-gray;

              .instruction-drag-icon {
                visibility: hidden;
              }
            }
          }
        }

        .add-step-button-container {
          .add-step-button-section {
            display: flex;
          }

          .add-step-text {
            margin-left: 5px;
            font-size: $font-size-12;
            font-weight: $font-weight-normal;
            line-height: 18px;
            color: $gunmetal-grey;
            cursor: pointer;
          }

          .hide-add-text {
            display: none;
          }
        }
      }
    }
  }
