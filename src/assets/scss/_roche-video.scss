    .change-background-color-for-selected {
      background-color: $aqua-spring;
    }
  
    .article-not-found {
      border-radius: 8px;
      margin-top: 20px;
      height: 80px;
      background-color: $white;
      border: 1px solid $grainsboro;
      color: $shadow-gray;
      text-align: center;
      line-height: 1;
  
      .no-data-articles {
        margin: auto;
        padding-top: 29px;
        color: $shadow-gray;
        font-weight: 700;
        text-align: center;
        background-color: $white;
        margin-bottom: 1px;
        .no-data-text {
          font-weight: 400;
          font-size: 16px;
          color: $shadow-gray;
          margin-top: 3px;
        }
      }
    }
  
    .main-inner-section {
      position: relative;
  
      .header-section {
        display: flex;
        justify-content: space-between;
        padding: 20px;
        align-items: center;
  
        .heading-main {
          font-weight: 700;
          font-size: 24px;
        }
  
        .header-button {
          text-align: center;
  
          .new-video-button {
            float: right;
            margin-bottom: 8px;
            width: 190px;
  
            .new-roche-video-button {
              display: flex;
              justify-content: center;
              background-color: $green;
              color: $white;
              font-size: 16px;
              padding: 10px 28px;
              font-weight: 900;
              cursor: pointer;
              border-radius: 50px;
              border: none;
              box-shadow: 0px 1px 2px 0px $box-shadow;
            }
  
            .upload-input {
              display: none;
              visibility: hidden;
            }
          }
  
          .disable-new-video-button {
            opacity: 0.3;
            pointer-events: none;
          }
  
          .video-description-text {
            color: $slate-gray;
            font-size: 14px;
            letter-spacing: 0.5;
            font-weight: 400;
            font-style: italic;
          }
        }
      }
  
      .video-uploading-container {
        padding: 0px 16px;
        position: absolute;
        top: 200px;
        width: 94%;
        z-index: 9;
        left: 2%;
  
        .video-loading-main {
          display: flex;
          text-align: center;
          justify-content: center;
          align-items: center;
          height: 255px;
          border-radius: 8px;
          margin: 20px 0px;
          border: 1px solid $grainsboro;
          background: $white;
          width: 100%;
          min-height: 100%;
          padding: 0 20px;
  
          .input-loading {
            height: 60px;
            display: flex;
            justify-content: center;
  
            .loader-image {
              border: 3px solid $white;
              border-radius: 50%;
              border-top: 3px solid $green;
              border-right: 3px solid $green;
              border-bottom: 3px solid $green;
              width: 20px;
              height: 20px;
              -webkit-animation: spin 2s linear infinite;
              animation: spin 2s linear infinite;
            }
          }
  
          .loading-text {
            background-color: $green-peppermint;
            border-radius: 4px;
            border: 1px solid $green-fringy-flower;
            width: 468px;
            height: 57px;
  
            p {
              font-weight: 400;
              font-size: 16px;
              color: $shadow-gray;
              padding: 18px 0px;
            }
          }
        }
      }
  
      .roche-video-main-table {
        cursor: default;
        margin-bottom: 30px;
  
        .table-section {
          width: 100%;
          height: 100%;
          background-color: $white;
          border-radius: 8px;
  
          .table-container {
            .table-head {
              background-color: $white-smoke;
              border-radius: 8px 8px 0px 0px;
  
              .table-head-title {
                color: $spanish-gray;
                font-size: 12px !important;
                line-height: 1.333 !important;
                text-align: left;
  
                .table-head-row-column {
                  padding: 8px;
                  font-weight: 700;
                  text-transform: capitalize;
                }
              }
            }
  
            .table-body {
              .table-body-row {
                border: 1px solid $grainsboro;
                height: 80px;
              }
  
              .blank-row {
                width: 2%;
              }
  
              .video-name-row {
                width: 22%;
                font-weight: 700;
                line-height: 1.429;
                font-size: 14px;
                color: $jet-black;
                word-break: break-all;
                .video-container {
                  display: flex;
                  align-items: center;
                  .video-name-text {
                    width: 80%;
                    height: auto;
                  }
                  .publisher-form-main-container {
                    position: relative;
                    margin-left: 10px;
  
                    .play-video-icon {
                      width: 25px;
                      height: 25px;
                      cursor: pointer;
                      background: none;
                      color: inherit;
                      border: none;
                      padding: 0;
                      font: inherit;
                      outline: inherit;
                      img {
                        width: 100%;
                      }
                    }
                    .disable {
                      pointer-events: none;
                      opacity: 0.5;
                    }
                  }
                }
              }
  
              .video-cloudfront-row {
                width: 26%;
  
                .disable {
                  opacity: 0.3;
                  pointer-events: none;
                }
  
                .cloudfront-row {
                  width: 90%;
                  display: flex;
                  justify-content: space-between;
  
                  .video-cloudfront-text {
                    width: 79%;
                    word-break: break-all;
  
                    .text {
                      font-weight: 400;
                      line-height: 2;
                      font-size: 12px;
                      color: $navy-blue;
  
                      &:hover {
                        text-decoration: underline;
                        cursor: pointer;
                      }
                    }
                  }
  
                  .video-cloudfront-copy-clipboard {
                    position: relative;
                    display: flex;
                    align-items: center;
                    align-self: center;
  
                    .clipboard-image {
                      position: relative;
                      bottom: 3px;
                      cursor: pointer;
                      height: 20px;
                      width: 18px;
  
                      img {
                        height: 100%;
                        width: 100%;
                      }
                    }
                  }
                }
              }
  
              .video-size-row {
                width: 8%;
                font-weight: 400;
                line-height: 2;
                font-size: 14px;
                color: $gunmetal-grey;
              }
  
              .video-time-row {
                width: 15%;
  
                .text {
                  display: flex;
                  width: 85%;
                  font-weight: 400;
                  line-height: 1.714;
                  font-size: 14px;
                  color: $gunmetal-grey;
                }
              }
  
              .video-delete-row {
                width: 8%;
              }
  
              .video-delete-row-disable {
                opacity: 0.3;
                pointer-events: none;
              }
  
              .table-body-row-column {
                padding-left: 8px;
  
                .delete-video {
                  display: flex;
                  align-items: center;
                  border-radius: 4px;
                  padding: 0 10px;
                  margin: 5px;
                  color: $green;
                  box-shadow: 0 2px 4px 0 $box-shadow;
                  cursor: pointer;
                  height: 41px;
                  width: 39px;
                  border: 0;
                  background-color: $white;
                  font-weight: 400;
  
                  .delete-icon {
                    height: 20px;
                    width: 18px;
                  }
                }
              }
            }
          }
        }
      }
    }