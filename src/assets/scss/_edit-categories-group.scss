.back-to-category-group-list-main-container {
  display: flex;
  justify-content: space-between;
  margin: 28px 0px 0px 28px;

  .back-to-category-group-list-btn {
    cursor: pointer;
    width: max-content;

      .back-to-category-group-list-text {
        color: $green;
        position: relative;
        top: 2px;
        left: 9px;
      }
    }

  .search-result-container {
    margin-top: 20px;

    .search-result-text {
      font-family: $font-family-averta;
      color: $black;
      margin-bottom: 30px;
    }
  }
}

.padding-zero {
  padding: 0;
}

.content {
  .table-header {
    .container {
      display: flex;
      background-color: $white-smoke;
      color: $spanish-gray;
      line-height: $line-height-18;
      padding: 8px 0px;
      padding-left: 110px;
      position: relative;

      @media (min-width: 1180px) and (max-width: 1185px) {
        .category-group-title {
          width: 213px !important;
        }
      }

      @media (min-width: 1040px) and (max-width: 1044px) {
        font-size: 12px !important;

        .category-group-title {
          width: 116px !important;
        }

        .category-group-isin {
          width: 107px !important;
        }
      }

      .category-group-isin {
        width: 167px;
      }

      .category-group-title {
        width: 316px;
      }

      .category-group-count {
        width: 130px;
      }

      .status {
        position: absolute;
        right: 200px;
      }
    }
  }
}

.background-image-categories-group {
  background: $sliver-whisper;
  height: 200px;
  position: relative;
  width: 100%;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  display: flex;

  .background-image {
    position: absolute;
    object-fit: cover;
    width: 100%;
    height: 100%;
    filter: brightness(60%) blur(12px);
  }

  .back-btn {
    top: 40px;
    cursor: pointer;
    position: absolute;
    left: 20px;

    .back-arrow-image {
      position: relative;
      top: -2px;
      width: 18px;
      height: 14px;
      cursor: pointer;
    }

    .back-to-categories {
      margin: 0px 4px;
      color: $green;
      cursor: pointer;
    }
  }

  .head-btn {
    position: absolute;
    right: 60px;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    top: 32px;
    gap: 20px;
  }

  .edit-cat-group-isin {
    color: $white;
    position: absolute;
    top: 82px;
    left: 26px;
  }
}

.input-categories-group-section {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  margin-top: -90px;
  padding-left: 20px;
  padding-right: 60px;
  margin-bottom: -60px;

  .input-section {
    width: 100%;
    background-color: $white;
    border: 1px solid $grainsboro;
    border-radius: 8px;
    justify-content: space-between;
    display: flex;
    flex-direction: column;

    .input-sub-section {
      justify-content: space-between;
      display: flex;
      border-bottom: 1px solid $grainsboro;
    }

    .left-section {
      width: 770px;

      .image-section {
        cursor: pointer;
        width: 120px;
        height: 120px;
        float: left;
        margin: 20px;
        border-radius: 4px;
        overflow: hidden;
        background-image: url("@/assets/images/upload-image-category.png");
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;

        .hover-image {
          opacity: 0;
          width: 100%;
          height: 100%;

          &:hover {
            opacity: 1;
            background-image: url("@/assets/images/edit-image.png") !important;
            background-repeat: no-repeat;
            background-position: center;
            background-color: $translucent-black;
            border-radius: 4px;
            background-size: contain;
          }
        }
      }

      .image-main-div {
        width: 120px;
        height: 120px;
        position: relative;

        .delete-icon-image {
          position: absolute;
          right: 4px;
          top: 6px;
          z-index: 1;
        }

        .display-image-section {
          width: 120px;
          height: 120px;
          object-fit: cover;
          position: absolute;
        }

        .image-inner-container {
          position: absolute;
          height: 120px;
          width: 100%;

          .progress-image {
            height: 120px;
            width: 100%;
            background-color: $jet-black;
            border-radius: 4px;
            position: relative;

            .progress-image-content {
              display: block !important;
              position: absolute;
              width: 100%;
              text-align: center;
              margin-top: 15px;

              .upload-text {
                display: flex;
                flex-direction: column;
                margin-top: 11px;

                .upload-heading {
                  height: 18px;
                  color: $white;
                }

                .upload-media {
                  height: 13px;
                  color: $white;
                }
              }
            }
          }
        }

        .replace-image-tag {
          cursor: pointer;
          position: absolute;
          top: 0px;
          text-align: left;
          color: $white;

          .upload-input {
            width: 120px;
            height: 120px;
            opacity: 0;
          }
        }
      }

      .text-section {
        margin: 34px 1px 1px 1px;
        width: 70%;
        display: flex;
        float: none;
        position: relative;

        .compulsory-field-category-group {
          width: 9px;
          height: 10px;
          cursor: default;
          position: absolute;
          left: 368px;
          top: 4px;
        }

        .title {
          width: 100%;
          border: none;
          border-bottom: 2px dashed $sliver;
          background: $transparent;
          color: $black;
          padding: 5px 0 7px 0;
          text-overflow: ellipsis;
          cursor: text;
        }

        .data {
          border: none;
        }

        ::placeholder {
          color: $sliver;
          font-weight: 700;
        }
      }

      .image-details {
        margin-top: 40px;

        .bold {
          color: $grey;
        }

        .normal {
          color: $grey;
        }
      }
    }

    .right-section-category {
      position: relative;
      width: 250px;

      .published {
        opacity: 0.5;
        pointer-events: none;
      }
      .publish-toggle-section {
        display: contents;
      }

      .publish-btn {
        width: 130px;
        top: 20px;
        right: 25px;
        position: absolute;

        .text {
          position: relative;
          left: 14px;
          top: 7px;
          color: $black;
        }

        .switch {
          position: relative;
          display: inline-block;
          width: 42px;
          height: 26px;
          margin-left: 20px;

          input {
            opacity: 0;
            width: 0;
            height: 0;
          }
        }

        .inactive-publish {
          opacity: 0.5;
        }

        .slider-round {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: $light-white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
          border-radius: 30px;

          &:before {
            position: absolute;
            content: "";
            height: 23px;
            width: 23px;
            left: 2px;
            bottom: 2px;
            background-color: $white;
            -webkit-transition: 0.4s;
            transition: 0.4s;
            border-radius: 50%;
          }
        }
        .slider-round.isSliderActive {
          cursor: default;
        }

        input {
          &:checked {
            + {
              .slider-round {
                background-color: $green;

                &:before {
                  -webkit-transform: translateX(15px);
                  -ms-transform: translateX(15px);
                  transform: translateX(15px);
                }
              }
            }
          }

          &:focus {
            + {
              .slider-round {
                box-shadow: 0 0 1px $green;
              }
            }
          }
        }
      }

      .delete-btn {
        position: absolute;
        bottom: 16px;
        right: 0px;
        min-width: 160px;
        margin-right: 30px;
        cursor: pointer;

        .image {
          width: 14px;
          height: 16px;
        }

        .text {
          position: relative;
          top: 0px;


          margin-left: 6px;
          color: $fiery-red-blaze;
        }

        .disable-delete {
          opacity: 0.5;
          pointer-events: none;
        }

        .table-delete-btn {
          width: auto;
          height: 20px;
          padding: 0;
          cursor: pointer;
          margin: 0 auto;
          object-fit: cover;
          z-index: 1;
          position: relative;
          display: flex;
        }
      }

      .disable-group {
        cursor: default;
      }
    }

    .category-variant-section {
      padding: 25px;
      margin-top: -40px;

      .category-variants-main {
        display: flex;
        justify-content: space-between;
        margin-top: 38px;
        .add-variant-section {
          .add-variant-main {
            display: flex;
            position: relative;
            right: 8px;
            cursor: pointer;

            .add-variant-btn {
              height: 18px;
              width: 18px;
              margin-top: -1px;
            }

            .add-variant-text {
              text-transform: uppercase;
              color: $green-light;
              position: relative;
              top: 2px;
              left: 5px;
            }
            .disable-add-variant-main {
              opacity: 0.5;
            }
          }
        }
        .category-variants {
          font-weight: 400;
          font-size: 20px;
          color: $black;
        }

        .add-variant-main {
          display: flex;
          position: relative;
          right: 8px;
          cursor: pointer;

          .add-variant-btn {
            height: 18px;
            width: 18px;
            margin-top: -1px;
          }

          .add-variant-text {
            text-transform: uppercase;
            color: $green-light;
            position: relative;
            top: 2px;
            left: 5px;
          }
        }

        .disable-add-variant-main {
          opacity: 0.5;
          pointer-events: none;
        }
      }

      .add-category-variant {
        color: $grey;
        padding-top: 12px;
      }

      .category-variant-card-main {
        position: relative;
        width: 100%;
        display: grid;
        grid-template-columns: 25% 25% 25% 25%;
        grid-auto-rows: auto;
        padding-top: 12px;
      }
    }
  }
}

.add-category-recipes-section {
  position: relative;
  padding-left: 20px;
  padding-right: 60px;
  width: 100%;
  height: 386px;
  margin-top: 80px;
  margin-bottom: 20px;

  .content {
    width: 100%;
    height: 100%;
    background-color: $white;
    border: 1px solid $grainsboro;
    border-radius: 8px;
    position: relative;

    .left-section {
      position: absolute;
      top: 32%;
      width: 450px;
      margin-left: 44px;

      .head-text {
        color: $black;
        margin-bottom: 8px;
      }

      .sub-text {
        color: $grey;
        margin-bottom: 30px;
      }
    }

    .right-section {
      position: absolute;
      width: 291px;
      right: 0;
      margin-top: 50px;
      margin-right: 110px;

      .image-content {
        width: 291px;
        height: 291px;

        .image {
          width: 291px;
          height: 291px;
        }
      }
    }
  }
}

.categories-group-section {
  position: relative;
  padding-left: 20px;
  padding-right: 60px;
  width: 100%;
  height: 100%;
  margin-top: 80px;
  margin-bottom: 30px;

  .content {
    width: 100%;
    height: 100%;
    background-color: $white;
    border-radius: 8px;
    position: relative;
    padding: 1px;

    .loading {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      min-height: 255px;
      padding: 0 20px;

      .input-loading {
        height: 60px;
        display: flex;
        justify-content: center;

        .loader-image {
          border: 3px solid $white;
          border-radius: 50%;
          border-top: 3px solid $green;
          border-right: 3px solid $green;
          border-bottom: 3px solid $green;
          width: 20px;
          height: 20px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }
      }

      .loader-text-container {
        display: flex;
        justify-content: center;

      .loading-text {
        background-color: $green-peppermint;
        border-radius: 4px;
        border: 1px solid $green-fringy-flower;
        width: 468px;
        height: 57px;

        p {
          color: $shadow-gray;
          padding: 18px 0px;
          text-align: center;
        }
      }
    }
  }

    .categories-header-section {
      margin: 24px 32px 8px;
      padding-top: 30px;
      display: flex;
      justify-content: space-between;

      .categories-header {
        width: 290px;
        color: $black;
        font-size: 20px;
      }

      .add-categories-btn {
        cursor: pointer;
        position: relative;
        top: 2px;
        max-width: 200px;
        max-height: 20px;

        .add-category-option {
          display: flex;
          align-items: center;

          img {
            margin-right: 5px;
            height: 18px;
            width: 18px;
          }

          p {
            color: $green;
          }
        }
      }
    }

    .categories-table-content {
      margin-bottom: 20px;

      .categories-table {
        .all-content {
          @media (min-width: 1180px) and (max-width: 1185px) {
            .body {
              .category-name {
                width: 213px !important;
              }
            }
          }

          @media (min-width: 1040px) and (max-width: 1044px) {
            .body {
              .category-isin {
                width: 107px !important;
              }

              .category-name {
                width: 116px !important;
              }
            }
          }

          .body {
            border-bottom: 1px solid $grainsboro;
            display: block;
            position: relative;

            .draggable-icon {
              visibility: hidden;
              position: absolute;
              left: 50%;

              img {
                width: 30px;
                height: 30px;
                cursor: move;
              }
            }

            .category-image {
              width: 114px;

              .image-categories {
                margin: 10px 27px 10px 27px;
                width: 60px;
                height: 60px;
                overflow: hidden;
                border-radius: 4px;

                img {
                  width: 100%;
                  height: 60px;
                  object-fit: cover;
                }
              }
            }

            .category-isin {
              width: 167px;
            }

            .category-name {
              width: 316px;
              .category-name-recipe {
                position: relative;
                max-width: 209px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                flex-wrap: nowrap;
                word-break: break-word;
                top: -2px;
              }
            }

            .category-recipes {
              width: 320px;

              .categories-details {
                color: $grey;
                font-size: 14px;
              }
            }

            .category-actions-wrapper {
              display: flex;
              align-items: center;
              gap: 34px;
              padding-right: 22px;

              > div {
                display: flex;
                align-items: center;
                height: 32px;

                .menu {
                  margin-right: 0;
                }
              }
            }

            .category-published {
              width: 300px;
              display: flex;
              justify-content: flex-end;
              position: absolute;
              right: 0px;
              bottom: 22px;

              .categories-btn {
                margin-right: 60px;
                width: 97px;
                height: 25px;
                position: relative;
                top: 4px;

                .published-state {
                  width: 95px;
                  height: 25px;
                  color: $green;
                  background-color: $aqua-spring;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  border-radius: 4px;

                  img {
                    position: relative;
                    right: 6px;
                    width: 16px;
                    height: 16px;
                  }
                }

                .unpublished-state-categories {
                  font-size: 13px;
                  width: 112px;
                  height: 25px;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  text-transform: capitalize;
                  color: $silver;
                  background-color: $pearl-mist;
                  border-radius: 4px;

                  img {
                    width: 16px;
                    height: 16px;
                    position: relative;
                    right: 6px;
                  }
                }

                .preview-state {
                  font-size: 13px;
                  width: 97px;
                  height: 25px;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  text-transform: capitalize;
                  color: $amber;
                  background-color: $gloden-glow;
                  border-radius: 4px;
                }

                .failed-state {
                  font-size: 13px;
                  width: 97px;
                  height: 25px;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  text-transform: capitalize;
                  color: $radiant-red-orange;
                  background-color: $cotton-candy-pink;
                  border-radius: 4px;
                }

                .publishing-state-cat {
                  font-size: 12px;
                  width: 110px;
                  height: 25px;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  text-transform: capitalize;
                  color: $deep-sea-blue;
                  background-color: $baby-blue;
                  border-radius: 4px;

                  img {
                    width: 16px;
                    height: 16px;
                    position: relative;
                    right: 6px;
                  }
                }

                .timeout-state {
                  font-size: 13px;
                  width: 97px;
                  height: 25px;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  text-transform: capitalize;
                  color: $crusta;
                  background-color: $light-coral;
                  border-radius: 4px;
                }
              }

              .edit-btn {
                border-radius: 4px;
                box-shadow: 0px 1px 5px 0px $box-shadow;
                height: 32px;
                width: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: $white;
                cursor: pointer;

                img {
                  width: 16px;
                  height: 16px;
                }
              }

              .disable-category-group-edit-button {
                filter: opacity(60%);
                cursor: pointer;
              }

              .disable-category-group-delete-button {
                filter: opacity(60%);
                cursor: initial;
              }

              .menu {
                position: relative;
                margin-right: 22px;

                .table-edit-btn {
                  width: auto;
                  height: 20px;
                  padding: 0;
                  cursor: pointer;
                  margin: 0 auto;
                  object-fit: cover;
                  z-index: 1;
                }

                .menu-box {
                  display: block;
                  position: absolute;
                  right: -10px;
                  width: 90px;
                  z-index: 2;
                  box-shadow: 0 4px 10px 0 $shadow-black,
                    0 3px 5px 0 $faint-black,
                    0 0 0 1px $shadowy-black;
                  border-radius: 4px;
                  background: $white;

                  .menu-list {
                    list-style: none;
                    border-radius: 4px;
                    margin: 3px;

                    li {
                      padding: 10px 20px;
                      font-size: 15px;
                      color: $charcoal-light;
                      font-weight: 400;

                      &:hover {
                        color: $white;
                        background: $green;
                        cursor: pointer;
                      }
                    }
                  }
                }
              }
            }
          }

          .body:first-child {
            border-top: 1px solid $grainsboro;
          }

          .body:hover {
            background-color: $light-mint;
            border: 1px solid $green;

            .draggable-icon {
              visibility: visible;
              cursor: all-scroll;
            }
          }
        }
      }
    }

    .add-zero-section {
      width: 100%;
      padding: 0 20px;
      margin-bottom: 42px;
      margin-top: 20px;

      .zero-promoted {
        width: 100%;
        text-align: center;
        margin: 0 auto;
        padding: 29px 0;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 4px;

        .bold {
          color: $spanish-gray;
        }

        .normal {
          color: $spanish-gray;
        }
      }
    }
  }
}

.categories-group-section-data {
  margin-top: 20px;
}

.edit-categories-group-delete-category-modal {
  text-align: left;
  display: flex;
  flex-direction: inherit;
  justify-content: space-between;
  align-items: normal;
  margin-top: 30px;
  max-height: 160px;
  padding: 0 14px;
  width: 450px;

  .delete-image {
    img {
      width: 80px;
      margin-bottom: 10px;
    }
  }

  .delete-content {
    width: 310px;

    .delete-title {
      position: relative;
      left: -8px;
      color: $black;

      font-weight: bold;
      font-size: 20px;
    }

    .delete-description {
      position: relative;
      left: -8px;
      color: $grey;

      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
    }

    .button-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 32px;
      margin-bottom: 8px;
      gap: 20px;
    }
  }
}

.update-category-modal {
  width: 660px;
  overflow-y: visible !important;
  scrollbar-color: $grainsboro $whispering-white-smoke;
  scrollbar-width: thin;

  ::-webkit-scrollbar {
    width: 12px;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-track {
    background: $whispering-white-smoke;
  }

  ::-webkit-scrollbar-thumb {
    background: $grainsboro;
    border: 3px solid $transparent;
    border-radius: 15px;
    background-clip: content-box;
  }

  .title-section {
    position: relative;

    .exit {
      cursor: pointer;
      position: absolute;
      right: 18px;
      top: -4px;
      width: 24px;
      height: 24px;
    }

    .title {
      margin-top: 8px;
      font-size: 24px;

      font-weight: 700;
      color: $black;
      min-height: 32px;
      padding: 0px 56px;
    }

    .add-Title {
      margin-top: 8px;
      font-size: 24px;

      font-weight: 700;
      color: $black;
      min-height: 32px;
      padding: 0px 25px;
      text-align: left;
    }

    .search-container-for-category {
      margin: 25px 35px 14px 25px;
      display: flex;
      justify-content: space-between;

      .search-title {
        font-size: 16px;

        padding-top: 8px;
      }

      .search-box {
        position: relative;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 30px;
        padding: 0 12px 0 16px;
        height: 36px;
        width: 300px;

        .search-input-box {
          width: 250px;
          height: 34px;
          margin: 0px 0px 0px 20px;
          padding: 0;
          background: none;
          color: $black;
          border: none;
          font-size: 16px;
          border-radius: 0;
          box-shadow: none;

          ::placeholder {
            font-size: 16px;
            color: $graphite-gray;
            font-weight: 400;
          }
        }

        .align-search-input-box {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 230px;
          display: block;
        }

        .search-icon-green-image {
          position: relative;
          top: -27px;
          left: -5px;
          float: left;
          height: 18px;
          width: 18px;
          cursor: pointer;
        }

        .exit-search-icon {
          width: 12px;
          height: 12px;
          position: relative;
          top: -24px;
          float: right;
          cursor: pointer;
        }
      }
    }
  }

  .add-group-content {
    height: 224px;
    position: relative;
    left: -10px;
    width: 103%;
    overflow-y: scroll;
    background-color: $white-smoke;

    .table-image-loader {
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;
      background-color: $white-smoke;
      margin: 0 auto;
      height: 200px;
      width: 210px;

      .loader {
        border: 3px solid $pristine-white;
        border-radius: 50%;
        border-top: 3px solid $spanish-gray;
        border-right: 3px solid $spanish-gray;
        border-bottom: 3px solid $spanish-gray;
        width: 24px;
        height: 24px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }

      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }

    .container {
      margin: 0 auto;
      width: 630px;
      height: 100%;

      .no-recipe-result {
        position: relative;
        top: 40%;

        font-weight: 700;
        font-size: 20px;
        color: $shadow-gray;
      }

      .card {
        cursor: pointer;
        position: relative;
        float: left;
        margin: 10px;
        width: 190px;
        height: 210px;
        border-radius: 8px;
        background-color: $white;
        border: 1px solid $grainsboro;

        .card-image {
          margin: 10px auto;
          width: 170px;
          height: 140px;
          object-fit: contain;
          overflow: hidden;
          border-radius: 4px;

          .image {
            width: inherit;
            height: inherit;
            object-fit: cover;
          }
        }

        .card-title-main {
          .card-title {
            margin-left: 8px;
            text-align: left;
            font-size: 14px;

            font-weight: 700;
            color: $black;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .total-recipe {
          margin-left: 8px;
          text-align: left;
          font-size: 14px;

          color: $grey;
        }
      }

      .selected-categories {
        border: 1px solid $green;

        .checkmark {
          position: absolute;
          right: 16px;
          bottom: 16px;
          width: 8px;
          height: 14px;
          border: 1.5px solid $green;
          border-width: 0 2px 2px 0;
          -webkit-transform: rotate(45deg);
          -ms-transform: rotate(45deg);
          transform: rotate(45deg);
        }
      }

      .already-added-category {
        opacity: 0.4;
        pointer-events: none;
      }
    }

    .load-button {
      padding: 15px 0px;
      width: 140px;
      margin: 0 auto;
    }
  }

  .add-group-content-space {
    height: 6px;
    position: relative;
    left: -10px;
    width: 103%;
    background-color: $white-smoke;
  }

  .create-section {
    display: flex;
    justify-content: space-between;

    .count-selected {
      text-align: left;
      margin-left: 22px;
      width: 220px;
      margin-top: 30px;
      font-size: 16px;
    }

    .create-btn {
      margin-right: 22px;
      margin-top: 14px;
    }
    .add-button {
      margin-right: 22px;
      margin-top: 14px;
    }
  }
}

