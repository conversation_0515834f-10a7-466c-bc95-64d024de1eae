
  .problem-modal {
    .problem-modal-content {
      padding: 8px 29px;

      .problem-modal-warning {
        font-weight: 700;
        font-size: 20px;
        width: 100%;
        height: 45px;
      }

      .problem-modal-checkbox {
        text-align: initial;

        .issue-type {
          accent-color: green;
          margin-top: 9px;
          cursor: pointer;

          &:first-child {
            margin-top: 0;
          }
        }
      }

      .problem-modal-input {
        margin-top: 11px;
        margin-bottom: 11px;

        .problem-input {
          width: 399px;
          height: 66px;
          background-color: $pearl-mist;
          padding: 0;
          border: 1px solid $grainsboro;
          font-style: italic;
          padding-left: 12px;
          padding-top: 7px;
          padding-right: 2px;
          margin: 12px;
          border-radius: 4px;
        }
      }

      .problem-modal-btn-container {
        display: flex;
        justify-content: flex-end;
        gap: 20px;
      }
    }
  }

  .edit-product-publish-modal {
    .publish-content {
      .slug-warning {
        color: red;
        text-align: left;
        font-size: 11px;
        margin-top: 0px;
      }
    }
  }

  .recipe-container {
    padding: 0 0 50px;

    .view-page-section {
      top: 40px;

      .view-page-header {
        font-size: 16px;
        font-weight: 700;
        color: $black;
      }

      .url-header {
        margin-top: 10px;
        font-size: 14px;
        font-weight: 700;
        color: $black;
      }

      .url {
        margin-top: 4px;
        width: 100%;
        overflow: hidden;
        font-size: 16px;
        font-weight: 400;
        color: $black;
        overflow-wrap: break-word;

        a:hover {
          text-decoration: underline;
        }
      }

      .preview-url {
        cursor: pointer;
        width: max-content;
        margin-top: 30px;
        font-size: 14px;
        font-weight: 700;
        color: $black;

        .image {
          position: relative;
          top: -2px;
          margin-left: 10px;
          width: 16px;
          height: 16px;
        }
      }

      .generate-preview-url {
        cursor: pointer;
        width: max-content;
      }
    }

    .form-container {
      background: white;
      border-radius: 8px;
      width: 69%;
      margin-left: 25px;
      margin-bottom: 20px;
      float: left;

      .unpublish-note-message {
        color: $ruby-red;
        font-size: 15px;
        font-weight: 400;
        text-align: left;
        position: relative;
        bottom: 12px;
      }

      .form-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .form-title-header {
          display: inline-block;
          margin-right: 10px;
          font-size: 20px;
          font-weight: 700;
          color: $black;
        }
      }


      .recipe-variant-form-section {
        display: flex;
        justify-content: space-between;

        .recipe-variant-form {
          display: flex;
          justify-content: unset;
          width: max-content;

          .recipe-variant-section {
            .recipe-flag {
              margin-right: 10px;
              padding-bottom: 5px;

              .recipe-flag {
                max-width: 32px;
                max-height: 30px;
              }
            }
          }

          .hover-recipe-variant-text {
            height: fit-content;
            width: fit-content;

            .hover-image {
              margin-top: 5px;
            }

            &:hover {
              .hover-text {
                visibility: visible;
              }
            }

            .hover-text {
              display: flex;
              position: absolute;
              top: -41px;
              left: 0;
              width: 438px;
              height: 55px;
              background: $black;
              visibility: hidden;
              padding: 10px;
              z-index: 999;
              border-radius: 8px;
              margin-left: 56px;

              &::after {
                content: "";
                position: absolute;
                top: 93%;
                left: 50%;
                margin-left: -12px;
                border-width: 10px;
                border-style: solid;
                border-color: $black $transparent $transparent $transparent;
              }

              p {
                padding-left: 3px;
                color: $white;
                font-size: 12px;
                font-weight: 400;
                text-align: center;
                line-height: 16px;
              }
            }
          }
        }

        .es-icon {
          font-weight: 700;
          font-size: 20px;
          color: $copper-rust;
        }
      }

      .recipe-variant-ingredient-text {
        display: flex;

        .recipe-variant-flag-section {
          margin-right: 10px;

          .recipe-flag {
            max-width: 32px;
            max-height: 30px;
          }
        }

        .compulsory-field {
          color: $red;
        }
      }

      .recipe-variant-notes-text-container {
        display: flex;

        .recipe-variant-flag-section {
          margin-right: 10px;

          .recipe-flag {
            max-width: 32px;
            max-height: 30px;
          }
        }
      }

      .description-section {
        .description {
          width: 100%;
          resize: none;
          background: $pristine-white;
          border-radius: 4px;
          padding: 10px;
          margin: 5px 0;
          border: 1px solid $ethereal-whisper-gray;
          height: 131px;
          color: $charcoal-light;
        }

        .description-notes {
          width: 100%;
          resize: none;
          background: $pristine-white;
          border-radius: 4px;
          padding: 10px;
          margin: 5px 0;
          border: 1px solid $ethereal-whisper-gray;
          height: 96px;
          color: $charcoal-light;
        }
      }

      .form-section-upper-part {
        padding: 20px;
        border-radius: 8px;
      }

      .form-section-lower-part {
        padding: 20px;
        border-radius: 8px;
        background: $white;
      }

      .diet-category-tag-container {
        .flter-show-hide-title {
          margin: 10px 0;
          display: flex;
          justify-content: space-between;

          .filter-heading {
            color: $black;
            font-weight: 700;
            font-size: 20px;
          }

          .recipe-variant-filter-heading {
            display: flex;

            .recipe-variant-flag-section {
              margin-right: 10px;

              .recipe-flag {
                max-width: 32px;
                max-height: 30px;
              }
            }

            .filter-heading {
              color: $black;
              font-weight: 700;
              font-size: 20px;
            }
          }

          .hide-heading {
            color: $green;
            position: relative;
            padding-right: 28px;
            top: 4px;
            cursor: pointer;
          }

          .filter-hide-drop-down {
            position: absolute;
            top: -2px;
            right: 5px;

            .filter-hide-dropdown-icon {
              height: 14px;
              width: 19px;
              margin-top: 8px;
              cursor: pointer;
            }
          }
        }

        .filter-diets-section {
          display: flex;
          justify-content: flex-start;
          padding-bottom: 12px;
          position: relative;

          .filter-section-heading {
            text-transform: uppercase;
            color: $spanish-gray;
            width: 140px;
            margin-top: 13px;
          }

          .filter-diets-container {
            flex-basis: 100%;
            border: 1px solid $ethereal-whisper-gray;
            box-shadow: 0 1px 5px 0 $shadow-black;
            padding: 6px 20px 0px 8px;
            position: relative;
            min-height: 47px;

            .display-filter-diets-section {
              display: flex;
              justify-content: space-between;

              .filter-diets-available {
                display: flex;
                flex-wrap: wrap;
                width: 100%;

                .filter-image-and-diets-name {
                  border-radius: 4px;
                  background-color: $whisper;
                  border: 1px solid $grainsboro;
                  vertical-align: middle;
                  display: flex;
                  position: relative;
                  margin: 0 8px 6px 0;

                  .filter-diets-image img {
                    width: 24px;
                    height: 24px;
                    margin: 4px 6px 4px 7px;
                  }

                  .filter-diets-item-name {
                    margin: auto;
                    padding: 0 12px 0 0px;
                    font-weight: 400;
                    font-size: 16px;
                    color: $black;
                  }

                  &:hover {
                    background: $aqua-spring;
                    border: 1px solid $green-light;
                  }
                }

                .remove-diet-section {
                  margin-right: 10px;

                  .filter-diets-remove-image {
                    cursor: pointer;
                    position: relative;
                    top: 4px;
                    width: 10px;
                    height: 10px;
                  }
                }

                .filter-diets-select-input {
                  margin-top: -5px;
                  border: none;
                  background-color: $white;
                  color: $dark-gray;
                  height: 45px;
                  border-radius: 4px;
                  width: 100%;
                }

                .hide-dropdown {
                  opacity: 0;
                  width: 1px;
                  pointer-events: none;
                }
              }

              .filter-diets-drop-icon {
                width: 20px;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;

                .filter-diets-dropdown-icon {
                  transform: rotate(90deg);
                  cursor: pointer;
                  max-width: 7px;
                  min-width: 7px;
                }
              }
            }

            .filter-select-diets-item {
              overflow-y: visible !important;
              scrollbar-color: $grainsboro $whispering-white-smoke;
              scrollbar-width: thin;

              ::-webkit-scrollbar {
                width: 12px;
                border-radius: 3px;
              }

              ::-webkit-scrollbar-track {
                background: $whispering-white-smoke;
              }

              ::-webkit-scrollbar-thumb {
                background: $grainsboro;
                border: 3px solid $transparent;
                border-radius: 15px;
                background-clip: content-box;
              }

              .filter-diets-no-result-found {
                position: absolute;
                left: -1px;
                box-shadow: 0 1px 10px 0 $box-shadow;
                border: 1px solid $grainsboro;
                background: $white;
                z-index: 1;
                color: $shadow-gray;
                width: 100%;
                border-radius: 4px;
                text-align: center;
                padding: 20px 0;
              }
            }

            .filter-diets-autocomplete-results {
              position: absolute;
              list-style: none;
              left: -1px;
              box-shadow: 0 1px 10px 0 $box-shadow;
              background: $white;
              z-index: 1;
              color: $charcoal-light;
              max-height: 360px;
              overflow-y: scroll;
              width: 100%;
              border-radius: 4px;

              .diets-checkbox-title {
                display: flex;
                justify-content: flex-start;
                flex-basis: 100%;
                cursor: pointer;

                .filter-diets-checkbox {
                  border: 1px solid $grainsboro;
                  height: 22px;
                  width: 22px;
                  min-width: 22px;
                  border-radius: 2px;
                  position: relative;
                  bottom: 0px;
                  margin-right: 12px;
                  margin-top: 3px;
                  cursor: pointer;

                  .filter-diets-round {
                    position: relative;
                  }

                  .filter-diets-round label {
                    background-color: $white;
                    border: 1px solid $transparent;
                    border-radius: 2px;
                    cursor: pointer;
                    height: 20px;
                    left: 0px;
                    position: absolute;
                    top: 0px;
                    width: 20px;
                  }

                  .filter-diets-round label:after {
                    border: 2px solid $white;
                    border-top: none;
                    border-right: none;
                    content: "";
                    height: 5px;
                    left: 3px;
                    opacity: 0;
                    position: absolute;
                    top: 5px;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                    width: 12px;
                  }

                  .filter-diets-round input[type="checkbox"] {
                    visibility: hidden;
                  }

                  .filter-diets-round input[type="checkbox"] + label {
                    background-color: $green-light;
                  }

                  .filter-diets-round input[type="checkbox"] + label:after {
                    opacity: 1;
                  }
                }

                .filter-diets-dropdown-image {
                  height: 28px;
                  width: 28px;
                  margin-right: 6px;

                  img {
                    min-height: 28px;
                    min-width: 28px;
                    max-height: 28px;
                    max-width: 28px;
                  }
                }

                .filter-diets-dropdown-title {
                  margin-top: 3px;
                  color: $black;
                }
              }

              .filter-diets-autocomplete-result {
                padding: 8px 10px;
                margin: 2px;
                border-radius: 4px;

                &.is-active,
                &:hover {
                  background: $aqua-spring;
                }
              }

              .filter-diets-load-more {
                padding: 8px 10px;
                cursor: pointer;
                text-align: center;
                background: $green;
                color: $white;
                width: 150px;
                border-radius: 50px;
                margin: 10px auto;
              }
            }
          }
        }

        .filter-categories-section {
          display: flex;
          justify-content: flex-start;
          padding-bottom: 12px;
          position: relative;

          .filter-section-heading {
            text-transform: uppercase;
            color: $spanish-gray;
            width: 140px;
            margin-top: 13px;
          }

          .filter-categories-container {
            flex-basis: 100%;
            border: 1px solid $ethereal-whisper-gray;
            box-shadow: 0 1px 5px 0 $shadow-black;
            padding: 6px 20px 0px 8px;
            position: relative;
            min-height: 47px;

            .display-filter-categories-section {
              display: flex;
              justify-content: space-between;

              .filter-category-available {
                display: flex;
                flex-wrap: wrap;
                width: 100%;

                .filter-image-and-category-name {
                  border-radius: 4px;
                  background-color: $whisper;
                  border: 1px solid $grainsboro;
                  vertical-align: middle;
                  display: flex;
                  position: relative;
                  margin: 0 8px 6px 0;

                  .category-edit-btn {
                    position: relative;
                    top: 4px;
                    right: 6px;
                    border-radius: 4px;
                    background-color: $white;
                    box-shadow: 0px 1px 5px 0px $box-shadow;
                    height: 24px;
                    width: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    img {
                      width: 12px;
                      height: 12px;
                    }
                  }

                  .filter-categories-image {
                    display: contents;
                  }

                  .filter-categories-image img {
                    width: 24px;
                    height: 24px;
                    margin: 4px 6px 4px 7px;
                    align-self: center;
                    border-radius: 5px;
                    position: absolute;
                  }

                  .filter-categories-item-name {
                    margin: auto;
                    padding: 6px 16px 6px 38px;
                    color: $black;
                  }

                  &:hover {
                    background: $aqua-spring;
                    border: 1px solid $green-light;
                  }
                }

                .remove-category-section {
                  margin-right: 10px;
                  display: contents;

                  .filter-categories-remove-image {
                    cursor: pointer;
                    position: relative;
                    right: 8px;
                    width: 10px;
                    height: 10px;
                    align-self: center;
                  }
                }

                .filter-categories-select-input {
                  margin-top: -5px;
                  border: none;
                  background-color: $white;
                  color: $dark-gray;
                  height: 45px;
                  border-radius: 4px;
                  width: 100%;
                }

                .hide-dropdown {
                  opacity: 0;
                  width: 1px;
                  pointer-events: none;
                }
              }

              .category-filter-tooltip-section {
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .tooltip-main-container-for-category-filter {
                position: relative;
                bottom: 4px;

                .alert-image {
                  height: 16px;
                  width: 16px;
                }
              }

              .filter-categories-drop-icon {
                width: 20px;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;

                .filter-categories-dropdown-icon {
                  transform: rotate(90deg);
                  cursor: pointer;
                  max-width: 7px;
                  min-width: 7px;
                }
              }
            }

            .filter-select-categories-item {
              overflow-y: visible !important;
              scrollbar-color: $grainsboro $whispering-white-smoke;
              scrollbar-width: thin;

              ::-webkit-scrollbar {
                width: 12px;
                border-radius: 3px;
              }

              ::-webkit-scrollbar-track {
                background: $whispering-white-smoke;
              }

              ::-webkit-scrollbar-thumb {
                background: $grainsboro;
                border: 3px solid $transparent;
                border-radius: 15px;
                background-clip: content-box;
              }

              .filter-categories-no-result-found {
                position: absolute;
                left: -1px;
                box-shadow: 0 1px 10px 0 $box-shadow;
                border: 1px solid $grainsboro;
                background: $white;
                z-index: 1;
                color: $shadow-gray;
                width: 100%;
                border-radius: 4px;
                text-align: center;
                padding: 20px 0;
              }
            }

            .filter-categories-autocomplete-results {
              position: absolute;
              list-style: none;
              left: -1px;
              box-shadow: 0 1px 10px 0 $box-shadow;
              background: $white;
              z-index: 1;
              color: $charcoal-light;
              max-height: 360px;
              overflow-y: scroll;
              width: 100%;
              border-radius: 4px;

              .categories-checkbox-title {
                display: flex;
                justify-content: flex-start;
                flex-basis: 100%;

                .filter-categories-checkbox {
                  border: 1px solid $grainsboro;
                  height: 22px;
                  width: 22px;
                  min-width: 22px;
                  border-radius: 2px;
                  position: relative;
                  bottom: 0px;
                  margin-right: 12px;
                  margin-top: 3px;
                  cursor: pointer;

                  .filter-categories-round {
                    position: relative;
                  }

                  .filter-categories-round label {
                    background-color: $white;
                    border: 1px solid $transparent;
                    border-radius: 2px;
                    cursor: pointer;
                    height: 20px;
                    left: 0px;
                    position: absolute;
                    top: 0px;
                    width: 20px;
                  }

                  .filter-categories-round label:after {
                    border: 2px solid $white;
                    border-top: none;
                    border-right: none;
                    content: "";
                    height: 5px;
                    left: 3px;
                    opacity: 0;
                    position: absolute;
                    top: 5px;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                    width: 12px;
                  }

                  .filter-categories-round input[type="checkbox"] {
                    visibility: hidden;
                  }

                  .filter-categories-round input[type="checkbox"] + label {
                    background-color: $green-light;
                  }

                  .filter-categories-round
                    input[type="checkbox"]
                    + label:after {
                    opacity: 1;
                  }
                }

                .filter-categories-dropdown-image {
                  height: 28px;
                  width: 28px;
                  margin-right: 6px;

                  img {
                    min-height: 28px;
                    min-width: 28px;
                    max-height: 28px;
                    max-width: 28px;
                  }
                }

                .filter-categories-dropdown-title {
                  margin-top: 3px;
                  color: $black;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              .filter-categories-autocomplete-result {
                padding: 8px 10px;
                margin: 2px;
                border-radius: 4px;
                cursor: pointer;

                &.is-active,
                &:hover {
                  background: $aqua-spring;
                }
              }

              .filter-category-load-more {
                padding: 8px 10px;
                cursor: pointer;
                text-align: center;
                width: 150px;
                margin: 10px auto;

                .input-loading {
                  height: 30px;
                  text-align: center;
                  text-align: -webkit-center;
                  text-align: -moz-center;

                  .loader-image {
                    border: 3px solid $white;
                    border-radius: 50%;
                    border-top: 3px solid $green;
                    border-right: 3px solid $green;
                    border-bottom: 3px solid $green;
                    width: 20px;
                    height: 20px;
                    -webkit-animation: spin 2s linear infinite;
                    animation: spin 2s linear infinite;
                  }
                }
              }
            }
          }
        }

        .filter-tags-section {
          display: flex;
          justify-content: flex-start;
          position: relative;
          padding-bottom: 12px;

          .filter-section-heading {
            text-transform: uppercase;
            color: $spanish-gray;
            width: 140px;
            margin-top: 13px;
          }

          .filter-tags-container {
            flex-basis: 100%;
            border: 1px solid $ethereal-whisper-gray;
            box-shadow: 0 1px 5px 0 $shadow-black;
            padding: 6px 20px 0px 8px;
            position: relative;
            min-height: 47px;

            .display-filter-tags-section {
              display: flex;
              justify-content: space-between;

              .tag-filter-tooltip-section {
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .tooltip-main-container-for-tag-filter {
                position: relative;
                bottom: 4px;

                .alert-image {
                  height: 16px;
                  width: 16px;
                }
              }

              .filter-tags-available {
                display: flex;
                flex-wrap: wrap;
                width: 100%;

                .filter-image-and-tags-name {
                  border-radius: 4px;
                  background-color: $whisper;
                  border: 1px solid $grainsboro;
                  vertical-align: middle;
                  display: flex;
                  position: relative;
                  margin: 0 8px 6px 0;

                  .filter-tags-item-name {
                    margin: auto;
                    padding: 8px 16px 8px 7px;
                    color: $black;
                  }

                  &:hover {
                    background: $aqua-spring;
                    border: 1px solid $green-light;
                  }
                }

                .tag-edit-btn {
                  position: relative;
                  top: 6px;
                  right: 6px;
                  border-radius: 4px;
                  background-color: $white;
                  box-shadow: 0px 1px 5px 0px $box-shadow;
                  height: 24px;
                  width: 24px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;

                  img {
                    width: 12px;
                    height: 12px;
                  }
                }

                .remove-tag-section {
                  margin-right: 10px;
                  display: contents;

                  .filter-tags-remove-image {
                    cursor: pointer;
                    position: relative;
                    width: 10px;
                    right: 8px;
                    height: 10px;
                    align-self: center;
                  }
                }

                .filter-tags-select-input {
                  margin-top: -5px;
                  border: none;
                  background-color: $white;
                  color: $dark-gray;
                  height: 45px;
                  border-radius: 4px;
                  width: 100%;
                }

                .hide-dropdown {
                  opacity: 0;
                  width: 1px;
                  pointer-events: none;
                }
              }

              .filter-tags-drop-icon {
                width: 20px;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;

                .filter-tags-dropdown-icon {
                  transform: rotate(90deg);
                  cursor: pointer;
                  max-width: 7px;
                  min-width: 7px;
                }
              }
            }

            .filter-select-tags-item {
              overflow-y: visible !important;
              scrollbar-color: $grainsboro $whispering-white-smoke;
              scrollbar-width: thin;

              ::-webkit-scrollbar {
                width: 12px;
                border-radius: 3px;
              }

              ::-webkit-scrollbar-track {
                background: $whispering-white-smoke;
              }

              ::-webkit-scrollbar-thumb {
                background: $grainsboro;
                border: 3px solid $transparent;
                border-radius: 15px;
                background-clip: content-box;
              }

              .filter-tags-no-result-found {
                position: absolute;
                left: -1px;
                box-shadow: 0 1px 10px 0 $box-shadow;
                border: 1px solid $grainsboro;
                background: $white;
                z-index: 1;
                color: $shadow-gray;
                width: 100%;
                border-radius: 4px;
                text-align: center;
                padding: 20px 0;
              }
            }

            .filter-tags-autocomplete-results {
              position: absolute;
              list-style: none;
              left: -1px;
              box-shadow: 0 1px 10px 0 $box-shadow;
              background: $white;
              z-index: 1;
              color: $charcoal-light;
              max-height: 360px;
              overflow-y: scroll;
              width: 100%;
              border-radius: 4px;

              .filter-tags-autocomplete-result {
                padding: 8px 10px;
                margin: 2px;
                border-radius: 4px;

                &.is-active,
                &:hover {
                  background: $aqua-spring;
                }
              }

              .filter-tags-load-more {
                padding: 8px 10px;
                cursor: pointer;
                text-align: center;
                width: 150px;
                margin: 10px auto;

                .input-loading {
                  height: 30px;
                  text-align: center;
                  text-align: -webkit-center;
                  text-align: -moz-center;

                  .loader-image {
                    border: 3px solid $white;
                    border-radius: 50%;
                    border-top: 3px solid $green;
                    border-right: 3px solid $green;
                    border-bottom: 3px solid $green;
                    width: 20px;
                    height: 20px;
                    -webkit-animation: spin 2s linear infinite;
                    animation: spin 2s linear infinite;
                  }
                }
              }

              .checkbox-title {
                display: flex;
                justify-content: flex-start;
                flex-basis: 100%;
                cursor: pointer;

                .filter-tags-checkbox {
                  border: 1px solid $grainsboro;
                  height: 22px;
                  width: 22px;
                  min-width: 22px;
                  border-radius: 2px;
                  position: relative;
                  cursor: pointer;
                  bottom: 0px;
                  margin-right: 12px;

                  .filter-tags-round {
                    position: relative;
                  }

                  .filter-tags-round label {
                    background-color: $white;
                    border: 1px solid $transparent;
                    border-radius: 2px;
                    cursor: pointer;
                    height: 20px;
                    left: 0px;
                    position: absolute;
                    top: 0px;
                    width: 20px;
                  }

                  .filter-tags-round label:after {
                    border: 2px solid $white;
                    border-top: none;
                    border-right: none;
                    content: "";
                    height: 5px;
                    left: 3px;
                    opacity: 0;
                    position: absolute;
                    top: 5px;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                    width: 12px;
                  }

                  .filter-tags-round input[type="checkbox"] {
                    visibility: hidden;
                  }

                  .filter-tags-round input[type="checkbox"] + label {
                    background-color: $green-light;
                  }

                  .filter-tags-round input[type="checkbox"] + label:after {
                    opacity: 1;
                  }
                }

                .filter-tags-dropdown-title {
                  color: $black;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
          }
        }

        .filter-allergens-section {
          display: flex;
          justify-content: flex-start;
          padding-bottom: 12px;
          position: relative;

          .filter-section-heading {
            text-transform: uppercase;
            color: $spanish-gray;
            width: 140px;
            margin-top: 13px;
          }

          .filter-allergens-container {
            flex-basis: 100%;
            border: 1px solid $ethereal-whisper-gray;
            box-shadow: 0 1px 5px 0 $shadow-black;
            padding: 6px 20px 0px 8px;
            position: relative;
            min-height: 47px;

            .display-filter-allergens-section {
              display: flex;
              justify-content: space-between;

              .filter-allergens-available {
                display: flex;
                flex-wrap: wrap;
                width: 100%;

                .filter-image-and-allergens-name {
                  border-radius: 4px;
                  background-color: $whisper;
                  border: 1px solid $grainsboro;
                  vertical-align: middle;
                  display: flex;
                  position: relative;
                  margin: 0 8px 6px 0;

                  .filter-allergens-image img {
                    width: 24px;
                    height: 24px;
                    margin: 4px 6px 4px 7px;
                  }

                  .filter-allergens-item-name {
                    margin: auto;
                    padding: 0 12px 0 0px;
                    color: $black;
                  }

                  &:hover {
                    background: $aqua-spring;
                    border: 1px solid $green-light;
                  }
                }

                .remove-allergens-section {
                  margin-right: 10px;

                  .filter-allergens-remove-image {
                    cursor: pointer;
                    position: relative;
                    top: 4px;
                    width: 10px;
                    height: 10px;
                  }
                }

                .filter-allergens-select-input {
                  margin-top: -5px;
                  border: none;
                  background-color: $white;
                  color: $dark-gray;
                  height: 45px;
                  border-radius: 4px;
                  width: 100%;
                }

                .hide-dropdown {
                  opacity: 0;
                  width: 1px;
                  pointer-events: none;
                }
              }

              .filter-allergens-drop-icon {
                width: 20px;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;

                .filter-allergens-dropdown-icon {
                  transform: rotate(90deg);
                  cursor: pointer;
                  max-width: 7px;
                  min-width: 7px;
                }
              }
            }

            .filter-select-allergens-item {
              overflow-y: visible !important;
              scrollbar-color: $grainsboro $whispering-white-smoke;
              scrollbar-width: thin;

              ::-webkit-scrollbar {
                width: 12px;
                border-radius: 3px;
              }

              ::-webkit-scrollbar-track {
                background: $whispering-white-smoke;
              }

              ::-webkit-scrollbar-thumb {
                background: $grainsboro;
                border: 3px solid $transparent;
                border-radius: 15px;
                background-clip: content-box;
              }

              .filter-allergens-no-result-found {
                position: absolute;
                left: -1px;
                box-shadow: 0 1px 10px 0 $box-shadow;
                border: 1px solid $grainsboro;
                background: $white;
                z-index: 1;
                color: $shadow-gray;
                width: 100%;
                border-radius: 4px;
                text-align: center;
                padding: 20px 0;
              }
            }

            .filter-allergens-autocomplete-results {
              position: absolute;
              list-style: none;
              left: -1px;
              box-shadow: 0 1px 10px 0 $box-shadow;
              background: $white;
              z-index: 1;
              color: $charcoal-light;
              max-height: 360px;
              overflow-y: scroll;
              width: 100%;
              border-radius: 4px;

              .allergens-checkbox-title {
                display: flex;
                justify-content: flex-start;
                flex-basis: 100%;
                cursor: pointer;

                .filter-allergens-checkbox {
                  border: 1px solid $grainsboro;
                  height: 22px;
                  width: 22px;
                  min-width: 22px;
                  border-radius: 2px;
                  position: relative;
                  bottom: 0px;
                  margin-right: 12px;
                  margin-top: 3px;
                  cursor: pointer;

                  .filter-allergens-round {
                    position: relative;
                  }

                  .filter-allergens-round label {
                    background-color: $white;
                    border: 1px solid $transparent;
                    border-radius: 2px;
                    cursor: pointer;
                    height: 20px;
                    left: 0px;
                    position: absolute;
                    top: 0px;
                    width: 20px;
                  }

                  .filter-allergens-round label:after {
                    border: 2px solid $white;
                    border-top: none;
                    border-right: none;
                    content: "";
                    height: 5px;
                    left: 3px;
                    opacity: 0;
                    position: absolute;
                    top: 5px;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                    width: 12px;
                  }

                  .filter-allergens-round input[type="checkbox"] {
                    visibility: hidden;
                  }

                  .filter-allergens-round input[type="checkbox"] + label {
                    background-color: $green-light;
                  }

                  .filter-allergens-round input[type="checkbox"] + label:after {
                    opacity: 1;
                  }
                }

                .filter-allergens-dropdown-image {
                  height: 28px;
                  width: 28px;
                  margin-right: 6px;

                  img {
                    min-height: 28px;
                    min-width: 28px;
                    max-height: 28px;
                    max-width: 28px;
                  }
                }

                .filter-allergens-dropdown-title {
                  margin-top: 3px;
                  color: $black;
                }
              }

              .filter-allergens-autocomplete-result {
                padding: 8px 10px;
                margin: 2px;
                border-radius: 4px;

                &.is-active,
                &:hover {
                  background: $aqua-spring;
                }
              }

              .filter-diets-load-more {
                padding: 8px 10px;
                cursor: pointer;
                text-align: center;
                background: $green;
                color: $white;
                width: 150px;
                border-radius: 50px;
                margin: 10px auto;
              }
            }
          }
        }
      }
    }

    .recipe-content-section {
      display: flex;
      gap: 20px;

      .left-recipe-section {
        display: flex;
        flex-direction: column;
        position: relative;
        width: 69%;

        &.disabled-section {
          pointer-events: none;
        }

        .new-form-section {
          width: 100%;
          background: $white;
          border-radius: 8px;
          padding: 20px;
          margin: 0 25px 20px 25px;
        }
      }
  
      .right-recipe-section {
        display: flex;
        flex-direction: column;
        position: relative;
        width: 25%;

        .new-form-section {
          width: 100%;
          background: $white;
          border-radius: 8px;
          padding: 20px;
        }
      }
    }

    .form-section {
      position: relative;
    }

    .recipe-variant-main-container {
      border: 1px solid $grainsboro-gray;
      margin-bottom: 18px;

      .recipe-variant {
        span {
          font-size: 16px;
          font-weight: 700;
          color: $black;
        }
      }

      .recipe-variant-language-container {
        border-radius: 4px;
        border: 1px solid $white;
        padding: 10px 0px;
        width: 100%;
        margin-top: 10px;

        .recipe-variant-language {
          display: flex;

          img {
            height: 24px;
            width: 30px;
          }

          .default-lang-title {
            position: relative;
            top: 3px;
            margin-left: 15px;
            color: $black;
            line-height: 20px;
          }
        }

        .recipe-variant-drop-down {
          cursor: pointer;
          display: flex;
          position: relative;
          border: 1px solid $grainsboro;
          background: $white;
          width: 100%;
          height: 45px;
          padding: 10px;

          .recipe-variant-drop-down-container {
            display: flex;

            .recipe-variant-container {
              display: flex;

              .recipe-variant-drop-down-image {
                width: 23px;
                height: 22px;
              }

              .recipe-variant-drop-down-data {
                color: $jet-black;
                margin-top: 3px;
                margin-left: 3px;
              }
            }
          }

          .recipe-variant-dropdown-icon {
            position: absolute;
            transform: rotate(90deg);
            width: 8px;
            height: 12px;
            cursor: pointer;
            right: 12px;
            top: 15px;
          }
        }

        .recipe-variant-autocomplete-results {
          margin-top: 25px;
          top: 25px;
          left: 0px;
          width: 100%;
          position: absolute;
          list-style: none;
          box-shadow: 0 1px 10px 0 $box-shadow;
          background: $white;
          z-index: 1;
          color: $charcoal-light;
          overflow-y: scroll;
          border-radius: 4px;
          scrollbar-width: none;

          &::-webkit-scrollbar {
            display: none;
          }
        }

        .recipe-variant-drop-down-image-title {
          .drop-down-recipe-variant-drop-down-image {
            width: 23px;
            height: 22px;
          }

          .drop-down-recipe-variant-drop-down-data {
            color: $jet-black;
            margin-top: 3px;
            margin-left: 3px;
          }
        }

        .recipe-variant-autocomplete-result {
          padding: 8px 10px;
          color: $jet-black;
          cursor: pointer;
          margin: 2px;
          border-radius: 4px;

          &.is-active,
          &:hover {
            background: $green;
            color: $white;
          }
        }

        .dropdown-icon {
          transform: rotate(90deg);
          height: 12px;
          width: 6px;
          position: relative;
          cursor: pointer;
          top: 8px;
        }
      }

      .delete-recipe-variant-container {
        margin-top: 12px;

        .delete-recipe-variant {
          width: max-content;
          display: flex;
          cursor: pointer;

          p {
            text-transform: uppercase;
            font-size: 14px;
            color: $fiery-red-blaze;
            font-weight: 700;
            margin-left: 5px;
            position: relative;
            top: 2px;
          }
        }
      }

      .add-recipe-variant-button {
        margin-top: 10px;
        width: 70%;

        .add-recipe-variant {
          display: flex;
          cursor: pointer;
          width: max-content;

          img {
            height: 18px;
            width: 18px;
            margin-left: 2px;
          }

          p {
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
            color: $green-light;
            position: relative;
            top: 1px;
            margin-left: 5px;
          }
        }
      }

      .disable-variant-button {
        display: none;
      }
    }

    .filter-background {
      background: none;
    }

    .publish-section {
      position: relative;
      padding: 20px;
      .add-schedule-main-section {
        margin-bottom: 12px;
        width: max-content;
        .disable {
          opacity: 0.6;
          pointer-events: none;
        }

        .add-image {
          height: 18px;
          width: 18px;
          cursor: pointer;
        }

        .add-sechedule-container {
          line-height: 18px;
          color: $green-light;
          margin-left: 4px;
          cursor: pointer;
        }

        .publish-hover-text {
          visibility: hidden;
          width: 272px;
          background-color: $jet-black;
          color: $white;
          text-align: center;
          border-radius: 6px;
          position: absolute;
          z-index: 1;
          height: -moz-fit-content;
          height: fit-content;
          padding: 5px 10px;
          bottom: 92%;
          left: -50px;
          opacity: 0.8;
          font-size: 12px;
          font-weight: 400;
          font-style: normal;

          &::after {
            content: "";
            position: absolute;
            top: 26px;
            border-width: 10px;
            border-style: solid;
            border-color: $jet-black $transparent $transparent $transparent;
          }
        }

        &:hover .publish-hover-text {
          visibility: visible;
        }
      }

      .schedule-publish-main-section {
        box-shadow: 0 0 13px $box-shadow;
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 12px;

        .schedule-publish-container {
          display: flex;
          justify-content: space-between;
          margin-bottom: 14px;

          .schedule-image-title {
            display: flex;

            .schedule-publish-image {
              height: 26px;
              width: 26px;
            }

            .schedule-publish-text-container {
              font-weight: 400;
              font-size: 16px;
              color: $black;
              align-self: center;
              margin-left: 11px;
            }
          }

          .schedule-publish-cross {
            height: 16px;
            width: 16px;
            align-self: center;
            cursor: pointer;
          }
        }
      }

      #publishDisabledInVariant {
        width: 100%;
        background-color: $jet-black;
        color: $white;
        height: fit-content;
        border-radius: 8px;
        padding: 5px 10px;
        position: absolute;
        z-index: 1;
        font-size: 12px;
        font-weight: 300;
        text-align: center;
        bottom: 403px;

        &::after {
          content: "";
          position: absolute;
          bottom: -16px;
          left: 80%;
          margin-left: -11px;
          border-width: 10px;
          border-style: solid;
          border-color: $jet-black $transparent $transparent $transparent;
        }
      }

      .disable-new {
        .switch,
        .form-title-header {
          pointer-events: none;
          opacity: 0.5;
        }
      }

      .publish-toggle-section {
        position: relative;

        .publish-hover-text {
          position: absolute;
          bottom: 35px;
          right: 0px;
          visibility: hidden;
          width: 220px;
          height: fit-content;
          background-color: $jet-black;
          color: $white;
          border-radius: 8px;
          padding: 7px 9px;
          opacity: 0.8;
          z-index: 1;
          font-size: 12px;
          font-weight: 300;
          text-align: center;

          &::after {
            content: "";
            position: absolute;
            top: 40px;
            right: 18px;
            border-width: 10px;
            border-style: solid;
            border-color: $jet-black $transparent $transparent $transparent;
          }
        }

        &:hover .publish-hover-text {
          visibility: visible;
        }
      }

      .calendar-icon {
        pointer-events: none;
      }
      .publish-toggle-section-end {
        position: relative;
        .calendar-icon-end {
          position: relative;
          float: right;
          right: 12px;
          top: 8px;
        }
        .publish-hover-text-end {
          position: absolute;
          bottom: 0;
          right: -30px;
          width: 311px;
          height: 91px;
          visibility: hidden;
          background-color: $jet-black;
          color: $white;
          border-radius: $border-radius;
          padding: 7px 9px;
          opacity: 0.8;
          z-index: 1;
          font-family: $font-family-averta;
          font-size: $font-size-12;
          font-weight: $font-weight-light;
          text-align: center;

          &::after {
            content: "";
            position: absolute;
            top: 91px;
            right: 39px;
            border-width: 10px;
            border-style: solid;
            border-color: $jet-black $transparent $transparent $transparent;
          }
        }

        &:hover .publish-hover-text-end {
          visibility: visible;
        }
        .tool-tip-text {
          line-height: $line-height-18;
          font-family: $font-family-averta;
          font-weight: $font-weight-normal;
          font-size: $font-size-12;
          opacity: 100%;
        }
        button {
          text-transform: none;
          color: $black;
          width: 99px;
          border-radius: 25px;
          height: 20px;
          border: none;
          font-weight: $font-weight-normal;
          font-family: $font-family-averta;
          font-size: $font-size-12;
        }
      }
      .description-section {
        margin-bottom: 0;
        padding-top: 18px;

        .disable-delete-btn {
          opacity: 0.6;
          pointer-events: none;
        }

        .delete {
          cursor: pointer;
          display: flex;
          align-items: center;
          font-weight: 700;
          font-size: 14px;
          width: max-content;

          span {
            margin-left: 5px;
            color: $ruby-red ;
          }
        }

        .delete-recipe-variant-container {
          margin-top: 20px;

          .delete-recipe-variant {
            width: max-content;
            display: flex;
            cursor: pointer;

            p {
              position: relative;
              top: 2px;
              margin-left: 5px;
              font-size: $font-size-14;
              font-weight: $font-weight-bold;
              color: $fiery-red-blaze;
            }
          }
        }
      }

      .recipe-id {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        border-top: 1px solid $grainsboro-gray;

        .form-title-header {
          font-size: 14px;
          margin-top: 15px;
        }

        span {
          margin-top: 10px;
          font-weight: 300;
          font-size: 16px;
          color: $black;
        }
      }

      .slug-id {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: relative;
        border-top: 1px solid $grainsboro-gray;
        padding-top: 10px;

      .slug-main-container {
        width: 100%;
      }

        .form-slug-input-text {
          border: 1px solid $grainsboro-gray;
          width: 100%;
          background: $pearl-mist;
          padding: 10px 4px;
          height: 40px;
          border-radius: 4px;
          margin-top: 7px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .slug-title-header {
          font-size: 14px;
          font-weight: 700;
          color: $black;
        }
      }

      .disable-section {
        opacity: 0.6;
        pointer-events: none;
      }

      .form-title-schedule-section {
        .disable {
          opacity: 0.6;
          pointer-events: none;
        }

        .add-schedule-main-section {
          margin-bottom: 12px;
          width: max-content;

          .add-image {
            height: 18px;
            width: 18px;
            cursor: pointer;
          }

          .add-sechedule-container {
            line-height: 18px;
            color: $green-light;
            margin-left: 4px;
            cursor: pointer;
          }

          .publish-hover-text {
            visibility: hidden;
            width: 272px;
            background-color: $jet-black;
            color: $white;
            text-align: center;
            border-radius: 6px;
            position: absolute;
            z-index: 1;
            height: -moz-fit-content;
            height: fit-content;
            padding: 5px 10px;
            bottom: 82%;
            left: -50px;
            opacity: 0.8;
            font-size: 12px;
            font-weight: 400;
            font-style: normal;

            &::after {
              content: "";
              position: absolute;
              top: 40px;
              border-width: 10px;
              border-style: solid;
              border-color: $jet-black $transparent $transparent $transparent;
            }
          }

          &:hover .publish-hover-text {
            visibility: visible;
          }
        }

        .schedule-publish-main-section {
          box-shadow: 0 0 13px $box-shadow;
          border-radius: 4px;
          padding: 12px;
          margin-bottom: 12px;

          .schedule-publish-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 14px;

            .schedule-image-title {
              display: flex;

              .schedule-publish-image {
                height: 26px;
                width: 26px;
              }

              .schedule-publish-text-container {
                font-weight: 400;
                font-size: 16px;
                color: $black;
                align-self: center;
                margin-left: 11px;
              }
            }

            .schedule-publish-cross {
              height: 16px;
              width: 16px;
              align-self: center;
              cursor: pointer;
            }
          }
        }
      }

      .form-title {
        padding: 10px 0 12px;

        .form-title-header {
          font-size: 14px;
        }

        .disable {
          opacity: 0.6;
        }

        .publish-button-header {
          font-size: 16px;
        }

        .switch {
          position: relative;
          display: inline-block;
          width: 50px;
          height: 26px;
        }

        .disable-switch {
          opacity: 0.5;
          pointer-events: none;
        }

        .published {
          opacity: 0.5;
          pointer-events: none;
        }

        .switch input {
          opacity: 0;
          width: 0;
          height: 0;
        }

        .slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: $light-white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
        }

        .slider:before {
          position: absolute;
          content: "";
          height: 20px;
          width: 20px;
          left: 2px;
          bottom: 3px;
          background-color: $white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
        }

        input:checked + .slider {
          background-color: $green;
        }

        input:focus + .slider {
          box-shadow: 0 0 1px $green;
        }

        input:checked + .slider:before {
          -webkit-transform: translateX(26px);
          -ms-transform: translateX(26px);
          transform: translateX(26px);
        }
        .slider.round {
          border-radius: 34px;
        }

        .slider.round:before {
          border-radius: 50%;
        }
      }
    }

    .author-publisher-form-container {
      background: $white;
      border-radius: 8px;
      padding: 20px;
      width: 25%;
      margin-bottom: 20px;
      float: left;
      margin-left: 25px;
      position: relative;

      .author-publisher-section {
        width: 25%;
        position: relative;

        .form-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;
        }
      }

      .author-form-title-header {
        color: $black;
      }
      .author-section-main {
        width: 100%;
      }

      .author-section {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .form-author-input-text {
        border: 1px solid $grainsboro-gray;
        width: 100%;
        height: 42px;
        background: $pearl-mist;
        padding: 10px 4px;
        border-radius: 4px;
        margin-top: 7px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: $black;
      }

      .publisher-form-main-container {
        display: flex;

        .publisher-form-info-icon-main {
          position: relative;

          .info-icon {
            position: relative;
            bottom: 1px;
            left: 6px;
          }
        }

        .publisher-form-title-header {
          font-weight: 700;
          color: $black;
          font-size: 14px;
        }
      }

      .none-publisher {
        color: $stone-gray;
        margin-top: 7px;
      }

      .publisher-drop-down-main-section {
        position: relative;
        width: 100%;

        ::-webkit-scrollbar {
          width: 12px;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-track {
          background: $whispering-white-smoke;
        }

        ::-webkit-scrollbar-thumb {
          background: $grainsboro;
          border: 3px solid $transparent;
          border-radius: 15px;
          background-clip: content-box;
        }
      }

      .publisher-drop-down {
        cursor: pointer;
        display: flex;
        position: relative;
        margin-top: 5px;
        border: 1px solid $ethereal-whisper-gray;
        box-shadow: 0 1px 2px 0 $shadow-black;
        background-color: $white;
        color: $dark-gray;
        padding: 4px 38px 4px 9px;
        transition: box-shadow 180ms, border 180ms;
        width: 100% !important;
        min-height: 45px;
        border-radius: 4px;

        .publisher-drop-down-container {
          display: flex;

          .publisher-container {
            display: flex;

            .publisher-drop-down-data {
              display: flex;

              .publisher-image-container {
                background: white;
                margin-left: 12px;
                align-self: center;
              }

              .publisher-text-container {
                color: $black;
                margin-left: 2px;
                align-self: center;
                word-break: break-all;
              }
            }

            .publisher-image {
              position: relative;
              right: 8px;
              width: 30px;
              height: 30px;
              border-radius: 4px;
              object-fit: contain;
            }
          }
        }

        .publisher-dropdown-icon {
          position: absolute;
          transform: rotate(90deg);
          width: 8px;
          height: 12px;
          cursor: pointer;
          right: 20px;
          top: 40%;
        }
      }

      .select-one-text {
        padding: 8px 20px;
        color: $jet-black;
        margin: 2px;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background: $green;
          color: $white;
        }
      }

      .publisher-autocomplete-results {
        right: 0px;
        width: 100%;
        position: absolute;
        list-style: none;
        box-shadow: 0 1px 10px 0 $box-shadow;
        background: $white;
        z-index: 1;
        color: $charcoal-light;
        overflow-y: scroll;
        border-radius: 4px;
        max-height: 185px;
      }

      .publisher-autocomplete-result {
        padding: 8px 20px;
        color: $jet-black;
        cursor: pointer;
        margin: 2px;
        display: flex;
        border-radius: 4px;

        &.is-active,
        &:hover {
          background: $green;
          color: $white;
        }
      }

      .publisher-image {
        min-width: 30px;
        margin: auto 0;

        .publisher-result-icon {
          height: 30px;
          width: 30px;
          border-radius: 4px;
          object-fit: contain;
          vertical-align: middle;
        }
      }

      .publisher-result-name {
        margin-left: 10px;
        align-self: center;
        word-break: break-all;
      }
    }

    .recipe-media-form-section {
      min-height: 200px;
      padding: 20px;
    }

    .ingredients-section {
      padding: 20px;
      width: 92%;
      margin-top: 0;

      .compulsory-field {
        color: $red;
      }

      .loading {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 0 20px;

        .input-loading {
          height: 60px;
          display: flex;
          justify-content: center;

          .loader-image {
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 20px;
            height: 20px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
          }
        }
      }

      .description-section {
        padding-top: 0px;
        border-radius: 4px;
        cursor: default;

        .ingredients-table {
          overflow: visible;

          thead {
            border: none;
            display: block;

            th {
              margin-left: 15px;
              padding: 12px 0px;
              text-align: left;
              border: none;
              font-size: 14px;
              color: $steel-gray;
              font-weight: 700;

              &:first-child {
                border-left: none;
                border-top-left-radius: 4px;
              }

              &:last-child {
                border-top-right-radius: 4px;
              }

              .compulsory-field {
                position: relative;
                left: 5px;
                color: $red;
              }
            }
          }

          .tbody {
            .table-group-data {
              .group-content {
                .group-name-section {
                  display: flex;
                  margin-top: 10px;
                  height: 48px;
                  background-color: $bright-gray;
                  border: 1px solid $grainsboro;
                  border-radius: 4px 4px 0 0;
                  justify-content: space-between;

                  .left-side-group {
                    display: flex;
                    width: 100%;

                    .filter-group-input {
                      background-color: $transparent;
                      width: 93%;
                      height: 32px;
                      margin-top: 6px;
                      padding-left: 16px;
                      font-weight: 700;
                      border: none;
                    }
                  }

                  .delete-group {
                    width: 40px;
                    visibility: hidden;
                    padding: 0px;

                    img {
                      cursor: pointer;
                      position: relative;
                      top: 11px;
                    }
                  }

                  .disable-delete-group {
                    display: none;
                  }

                  &:hover {
                    background-color: $light-mint;

                    .left-side-group {
                      .filter-group-input {
                        border-radius: 4px;
                        margin-left: 16px;
                        padding-left: 8px;
                        border: 1px solid $grainsboro;
                        background-color: $white;
                      }
                    }

                    .delete-group {
                      visibility: visible;
                      padding: 0px;
                      height: 36px;
                    }

                    .disable-delete-group {
                      display: none;
                    }
                  }
                }

                .empty-group-ingredient {
                  box-shadow: 0 0 0 1px $grainsboro;
                  border-radius: 4px;
                  text-align: center;

                  .text-ingredient {
                    color: $spanish-gray;
                    font-size: 16px;
                    display: block;
                    padding: 12px;
                  }
                }

                .hidden-list {
                  opacity: 0.5;
                  background: $light-mint;
                }

                .table-row-data {
                  box-shadow: 0 0 0 1px $grainsboro;
                  border-radius: 4px;

                  tr {
                    display: block;
                    border: 1px solid $grainsboro;

                    &:hover {
                      background-color: $light-mint;
                      border: 1px solid $green;

                      .draggable-icon {
                        visibility: visible;
                        padding: 0px;
                        width: 36px;
                        height: 36px;

                        img {
                          cursor: all-scroll;
                        }
                      }

                      .ingredient-full-view-container {
                        .ingredient-keywords-container {
                          .ingredient-border-line {
                            .border {
                              background-color: $green;
                            }
                          }
                        }
                        .internal-notes-section {
                          .internal-notes-input {
                            background-color: $light-mint;
                          }
                        }
                      }
                    }

                    .draggable-icon {
                      visibility: hidden;
                      padding: 0px;

                      img {
                        cursor: all-scroll;
                      }
                    }

                    .disabled-draggable-icon {
                      opacity: 0;
                      pointer-events: none;
                    }

                    td {
                      padding: 5px 0px;
                      position: relative;

                      .ing-tool {
                        position: relative;
                      }

                      .ing-note {
                        position: relative;
                      }

                      .filter-select-input {
                        border: none;
                        background-color: $transparent;
                        color: $black;
                        width: 90% !important;
                        height: 37px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        position: relative;
                      }

                      .no-ingredients-data {
                        pointer-events: none;
                      }

                      .uom-input {
                        width: 82% !important;
                      }

                      ::-webkit-input-placeholder {
                        font-style: italic;
                      }

                      :-moz-placeholder {
                        font-style: italic;
                      }

                      ::-moz-placeholder {
                        font-style: italic;
                      }

                      :-ms-input-placeholder {
                        font-style: italic;
                      }

                      .filter-icon-box {
                        position: absolute;
                        top: 2px;
                        left: 0px;
                        height: 45px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .dropdown-icon {
                          transform: rotate(90deg);
                          width: 8px;
                          cursor: pointer;
                        }

                        .dropdown-icon-hide {
                          display: none;
                        }
                      }

                      .autocomplete-results-nouom {
                        position: absolute;
                        list-style: none;
                        top: 40px;
                        left: 5px;
                        right: 5px;
                        box-shadow: 0 1px 10px 0 $box-shadow;
                        background: $white;
                        z-index: 1;
                        color: $charcoal-light;
                        max-height: 360px;
                        overflow-y: scroll;
                        width: 96%;
                        scrollbar-width: none;
                        padding: 8px 10px;
                        cursor: pointer;
                        margin: 2px;
                        border-radius: 4px;

                        &::-webkit-scrollbar {
                          display: none;
                        }
                      }

                      .autocomplete-results {
                        position: absolute;
                        list-style: none;
                        top: 40px;
                        left: 5px;
                        right: 5px;
                        box-shadow: 0 1px 10px 0 $box-shadow;
                        background: $white;
                        z-index: 1;
                        color: $charcoal-light;
                        max-height: 360px;
                        overflow-y: scroll;
                        width: 96%;
                        border-radius: 4px;

                        &::-webkit-scrollbar {
                          display: block;
                          width: 6px;
                        }
                        &::-webkit-scrollbar-track {
                          background: $whispering-white-smoke;
                        }

                        &::-webkit-scrollbar-thumb {
                          background: $grainsboro;
                          background-clip: content-box;
                        }
                      }

                      .autocomplete-result {
                        padding: 8px 10px;
                        cursor: pointer;
                        margin: 2px;
                        border-radius: 4px;

                        &.is-active,
                        &:hover {
                          background: $green;
                          color: $white;
                        }
                      }

                      .shoppable-container {
                        display: flex;
                        justify-content: space-between;

                        .shoppable-sub-container {
                          .dropdown-shoppable-data {
                            transform: rotate(90deg);
                            width: 8px;
                            cursor: pointer;
                            position: absolute;
                            left: 0px;
                            top: 18px;
                          }

                          .autocomplete-results-shoppable {
                            position: absolute;
                            list-style: none;
                            top: 45px;
                            left: 0px;
                            right: 5px;
                            box-shadow: 0 1px 10px 0 $box-shadow;
                            background: $white;
                            z-index: 1;
                            color: $charcoal-light;
                            max-height: 360px;
                            overflow-y: scroll;
                            width: 80%;
                            border-radius: 4px;
                            scrollbar-width: none;

                            &::-webkit-scrollbar {
                              display: none;
                            }
                          }

                          .autocomplete-result-shoppable {
                            padding: 8px 20px;
                            cursor: pointer;
                            margin: 2px;
                            border-radius: 4px;

                            &.is-active,
                            &:hover {
                              background: $green;
                              color: $white;
                            }
                          }

                          .shoppable-text-area {
                            margin-left: 20px;
                            display: flex;

                            .shoppable-name-text {
                              color: $jet-black;
                            }

                            .shoppable-sub-name-text {
                              color: $gunmetal-grey;
                            }
                          }
                        }

                        .shoppable-icon {
                          cursor: pointer;
                          float: right;
                          position: relative;
                          right: 35px;
                        }
                      }

                      .ingredient-load-more {
                        padding: 8px 10px;
                        cursor: pointer;
                        text-align: center;
                        background: $green;
                        color: $white;
                        width: 150px;
                        border-radius: 50px;
                        margin: 10px auto;
                      }
                    }

                    .modifier-cart-menu {
                      .modifier-cart-menu-box {
                        .filter-select-input {
                          width: 91% !important;
                        }

                        .cart-icon {
                          max-width: unset;
                        }

                        .pointer {
                          cursor: pointer !important;
                          max-width: unset;
                        }
                      }
                    }

                    .menu {
                      .menu-box {
                        img {
                          cursor: pointer;
                          max-width: unset;
                        }

                        .parse-button {
                          background-color: $white;
                          color: $green;
                          font-size: 14px;
                          padding: 3px 16px;
                          font-weight: bold;
                          cursor: pointer;
                          position: relative;
                          top: 1px;
                          margin-right: 20px;
                          border-radius: 50px;
                          border: 1px solid $green;
                          box-shadow: 0px 1px 5px 0px $shadow-black;
                        }

                        .noDataParse {
                          opacity: 0.5;
                          pointer-events: none;
                        }
                      }

                      .disable-menu {
                        display: none;
                        pointer-events: none;
                        opacity: 0.5;
                      }
                    }
                  }
                }

                .ingredient-keyword-main-container {
                  .ingredient-drop-down {
                    margin-bottom: 3px;
                    text-align: center;

                    .dropdown-icon {
                      height: 14px;
                      width: 19px;
                      cursor: pointer;
                    }
                  }

                  .ingredient-full-view-section {
                    .ingredient-keywords-container {
                      .ingredient-border-line {
                        padding: 3px 25px;

                        .border {
                          height: 1px;
                          width: 100%;
                          background-color: $grainsboro;
                        }
                      }

                      .ingredient {
                        position: relative;
                        display: flex;

                        .ingredient-keywords {
                          padding: 8px 0px 0px 38px;
                          display: flex;
                          width: 100%;

                          .keywords {
                            min-width: 330px;
                            border: none;
                            background: $transparent;
                            font-size: 14px;
                            position: relative;
                            height: 26px;
                            font-weight: 400;
                            color: $black;
                            padding: 0px 0px 0px 9px;
                            bottom: 4px;
                          }

                          .keywordsPosition {
                            bottom: 13px;
                          }

                          ::placeholder {
                            color: $gunmetal-grey;
                            font-style: italic;
                            font-weight: 700;
                            font-size: 14px;
                          }

                          .ingredient-keyword-container {
                            margin-left: 67px;
                            position: relative;

                            .selected-ingredient-keyword {
                              background-color: $whisper;
                              border: 1px solid $grainsboro;
                              border-radius: 4px;
                              max-width: 25%;
                              padding: 12px 12px;
                              height: 19px;
                              display: inline-flex;
                              align-items: center;
                              position: relative;
                              top: -12px;
                              margin-left: 8px;
                              margin-top: 8px;
                              color: $black;

                              .ingredient-keyword-name {
                                max-width: 120px;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                word-break: break-all;
                              }

                              .ingredient-keyword-remove {
                                .remove-ingredient-keyword-image {
                                  margin-left: 2px;
                                  cursor: pointer;
                                  position: relative;
                                  right: -6px;
                                  width: 8px;
                                  height: 8px;
                                }
                              }
                            }

                            .over-ride {
                              color: $green;
                              cursor: pointer;
                              text-align: left;
                              width: auto;
                              text-transform: uppercase;
                              font-size: 14px;
                              font-weight: 700;
                              margin-right: 37px;
                            }
                          }

                          .head-container-breadcrumb {
                            position: relative;

                            .main-container-breadcrumb {
                              flex-wrap: wrap;
                              width: auto;
                              text-overflow: ellipsis;
                              max-width: 100%;
                              display: flex;

                              .breadcrumb {
                                background-color: RGB(235, 235, 235);
                                border: 1px solid $grainsboro;
                                box-shadow: 0px 1px 0px 0px RGB(218, 218, 218);
                                border-radius: 4px;
                                font-size: 16px;
                                line-height: 1.3;
                                color: $black;
                                padding: 12px 5px;
                                top: -11px;
                                position: relative;
                                height: 19px;
                                display: flex;
                                align-items: center;
                                margin-left: 8px;
                                margin-top: 8px;
                                width: max-content;

                                .data-name {
                                  overflow: hidden;
                                  text-overflow: ellipsis;
                                  white-space: nowrap;
                                  max-width: 108px;
                                }
                              }

                              .edit-breadcrumb {
                                height: 18px;
                                margin-left: 12px;
                                margin-top: 3px;
                                color: $green;
                                cursor: pointer;
                                text-align: left;
                                width: auto;
                                text-transform: uppercase;
                              }

                              .add-breadcrumb {
                                margin-left: 12px;
                                color: $green;
                                cursor: pointer;
                                text-align: left;
                                width: auto;
                                text-transform: uppercase;
                                font-size: 14px;
                                font-weight: 700;
                              }
                            }

                            .main-container-breadcrumb-global {
                              flex-wrap: wrap;
                              width: auto;
                              text-overflow: ellipsis;
                              max-width: 100%;
                              display: flex;

                              .breadcrumb {
                                background-color: $white;
                                border: 1px solid $grainsboro;
                                box-shadow: 0px 0px 0px 0px RGB(218, 218, 218);
                                border-radius: 4px;
                                font-size: 16px;
                                line-height: 1.3;
                                color: $spanish-gray-slate;
                                font-weight: 400;
                                padding: 12px 5px;
                                top: -11px;
                                position: relative;
                                height: 19px;
                                display: flex;
                                align-items: center;
                                margin-left: 8px;
                                margin-top: 8px;
                                width: max-content;

                                .data-name {
                                  overflow: hidden;
                                  text-overflow: ellipsis;
                                  white-space: nowrap;
                                  max-width: 108px;
                                }
                              }

                              .edit-breadcrumb {
                                width: auto;
                                height: 18px;
                                margin-left: 12px;
                                margin-top: 3px;
                                color: $green;
                                cursor: pointer;
                                text-align: left;
                                text-transform: uppercase;
                              }

                              .add-breadcrumb {
                                width: auto;
                                margin-left: 12px;
                                color: $green;
                                cursor: pointer;
                                text-align: left;
                                text-transform: uppercase;
                                font-size: 14px;
                                font-weight: 700;
                              }
                            }
                          }
                        }

                        .ingredient-size {
                          display: flex;
                          justify-content: flex-end;
                          margin-right: 2px;
                          position: relative;
                          right: 8px;
                          top: 6px;

                          .ingredient-size-main-no-externalId {
                            margin-right: 16px;
                          }

                          .ingredient-size-main-container {
                            display: flex;

                            .ingredient-add-size-container {
                              color: $green-light;
                              cursor: pointer;
                              line-height: 18px;
                              margin-left: 4px;
                              font-style: normal;
                              margin-right: 60px;
                            }

                            .ingredient-add-size-no-externalId {
                              color: $green-light;
                              font-size: 14px;
                              text-transform: uppercase;
                              line-height: 18px;
                              font-weight: 700;
                              margin-left: 4px;
                              font-style: normal;

                              span {
                                cursor: pointer;
                              }
                            }
                          }
                        }

                        .ingredient-numeric-size-no-externalId {
                          justify-content: flex-end !important;
                        }

                        .ingredient-numeric-size {
                          width: 35%;
                          height: auto;
                          position: relative;
                          right: 24px;
                          display: flex;
                          justify-content: flex-end;
                          padding-top: 6px;

                          .ingredient-numeric-size-no-externalId {
                            margin-right: 0px !important;
                          }

                          .ingredient-numeric-size-container {
                            display: flex;
                            margin-right: 30px;

                            .ingredient-numeric-heading {
                              margin-top: 4px;
                              margin-right: 11px;
                            }

                            .numeric-text {
                              background-color: $grainsboro;
                              box-shadow: 0px 1px 5px 0px $shadow-black;
                              padding: 3px 7px;
                              border-radius: 4px;
                              display: flex;
                              height: 25px;
                              max-width: 156px;

                              .ing-count {
                                margin-right: 3px;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                max-width: 65px;
                              }
                            }

                            .close-icon {
                              margin-left: 8px;
                              cursor: pointer;
                            }
                          }
                        }

                        .ingredient-external-id-main-no-g-ml {
                          margin-right: -12px;
                        }

                        .ingredient-external-id-main {
                          margin-top: 2px;

                          .ingredient-external-id-text {
                            font-weight: 400;
                            font-size: 12px;
                            color: $gunmetal-grey;
                            width: 95px;
                            padding-right: 6px;
                            word-break: break-all;
                          }
                        }
                      }

                      .internal-notes-section {
                        display: flex;
                        position: relative;
                        padding-right: 28px;
                        margin-top: 4px;
                        margin-bottom: 8px;

                        .internal-notes {
                          display: flex;
                          margin-left: 37px;
                          width: 112px;
                          margin-top: 10px;

                          .internal-text {
                            font-size: 14px;
                            font-weight: 700;
                            color: $jet-black;
                          }
                        }

                        .internal-notes-input {
                          width: 100%;
                          font-size: 14px;
                          margin-top: 11px;
                          padding-bottom: 2px;
                          color: $jet-black;
                          border: 1px solid $whisper;
                          margin-left: 4px;
                          border-left: none;
                          border-right: none;
                          font-weight: 500;
                          border-top: none;
                        }
                        ::placeholder {
                          color: $slate-gray;
                        }
                      }

                      .admin-weight {
                        position: relative;
                        display: flex;

                        .ingredient-size {
                          width: 100%;
                          display: flex;
                          justify-content: space-between;
                          position: relative;
                          padding-top: 6px;

                          .ingredient-size-main-container {
                            display: flex;
                            margin-left: 25px;

                            .ingredient-add-size-container {
                              color: $green-light;
                              cursor: pointer;
                              line-height: 18px;
                              margin-left: 4px;
                              font-style: normal;
                            }

                            .ingredient-add-size-no-externalId {
                              color: $green-light;
                              font-size: 14px;
                              text-transform: uppercase;
                              line-height: 18px;
                              font-weight: 700;
                              margin-left: 4px;
                              font-style: normal;

                              span {
                                cursor: pointer;
                              }
                            }
                          }
                        }

                        .ingredient-numeric-size {
                          width: 100%;
                          height: auto;
                          position: relative;
                          display: flex;
                          justify-content: space-between;
                          padding-top: 6px;

                          .ingredient-numeric-size-container {
                            display: flex;
                            margin-left: 25px;

                            .ingredient-numeric-heading {
                              margin-top: 4px;
                              margin-right: 11px;
                            }

                            .numeric-text {
                              background-color: $grainsboro;
                              box-shadow: 0px 1px 5px 0px $shadow-black;
                              padding: 3px 7px;
                              border-radius: 4px;
                              display: flex;
                              height: 25px;
                              max-width: 156px;

                              .ing-count {
                                margin-right: 3px;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                max-width: 65px;
                              }
                            }

                            .close-icon {
                              margin-left: 8px;
                              cursor: pointer;
                            }
                          }
                        }

                        .ingredient-external-id-main {
                          margin-top: 2px;

                          .ingredient-external-id-text {
                            font-weight: 400;
                            font-size: 12px;
                            color: $gunmetal-grey;
                            width: 95px;
                            margin-right: 14px;
                            word-break: break-all;
                          }
                        }
                      }
                    }

                    .ingredient-minimize-view {
                      margin-bottom: 3px;
                      text-align: center;

                      .dropdown-icon {
                        transform: rotate(180deg);
                        width: 20px;
                        height: 14px;
                        cursor: pointer;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        .add-ingredient-add-group-shoppable-review-main-container {
          display: flex;
          justify-content: space-between;
        }

        .add-ingredient-button {
          margin: 20px 0;
          float: left;
        }

        .hide-button {
          opacity: 0;
          pointer-events: none;
        }

        .add-group-button {
          font-weight: 800;
          top: 10px;
          float: left;
          background-color: $white;
          color: $green;
          border: none;
          margin: 18px auto;
          cursor: pointer;
          position: relative;
          text-align: center;
          white-space: nowrap;
          outline: none;
          border-radius: 50px;
          left: 24px;
          font-size: 14px;

          .add-image {
            top: -2px;
            position: relative;
            width: 18px;
            height: 18px;
            margin-right: 6px;
          }
        }

        .shoppable-review-button {
          pointer-events: all;
          width: max-content;
          margin-top: 33px;
          margin-top: 30px;
          font-size: 14px;
          color: $green;
          font-weight: 700;
          cursor: pointer;
          float: right;

          .add-image {
            width: 29px;
            height: 15px;
            position: relative;
            top: -1px;
            margin-right: 6px;
          }
        }

        .add-button-text {
          margin-left: 5px;
          font-weight: 400;
          color: $gunmetal-grey;
          font-size: 12px;
          line-height: 18px;
        }

        .hide-add-button {
          display: none;
        }
      }

      .form-title {
        .nutrition-info {
          font-size: 14px;
          color: $green;
          align-items: flex-end;
          display: flex;
          cursor: pointer;
          padding: 2px;

          img {
            margin-right: 7px;
          }

          &:active {
            background-color: $semi-transparent-gray;
          }
        }
      }
    }


    .notes-section {
      padding: 20px;
      width: 92%;
      margin-top: 0;

      .description-section {
        margin-bottom: 0;
        padding-top: 0px;
      }
    }

    .nutrition-section {
      padding: 20px;
      width: 92%;
      margin-top: 0;

      .recipe-variant-flag-section {
        margin-right: 10px;

        .recipe-flag {
          max-width: 32px;
          max-height: 30px;
        }
      }

      .nutrition-section-container {
        display: flex;
        justify-content: space-between;

        .nutrition-minimize-view {
          margin-bottom: 8px;
          text-align: center;
          align-self: center;

          .nutrition-form-dropDown {
            cursor: pointer;

            .info-text {
              font-size: 14px;
              font-weight: 700;
              color: $green-light;
              line-height: 1.143;
            }

            .dropdown-icon {
              width: 20px;
              height: 14px;
              cursor: pointer;
            }
          }
        }
      }

      .nutrtion-maximize-view {
        .nutrition-toogle-serving-view-container {
          display: flex;
          margin-top: 10px;

          .nutrition-selection-container {
            .nutrition-drop-down-container {
              .selected-nutrition {
                text-transform: lowercase;
                margin-left: 5px;
                font-size: 16px;
                font-weight: 400;
                color: $shadow-gray;
              }

              .nutrition-dropdown-icon {
                transform: rotate(90deg);
                width: 8px;
                height: 12px;
                cursor: pointer;
              }
            }

            .box-container {
              display: flex;
              justify-content: space-between;
              position: relative;
              align-items: center;
              border: 1px solid $grainsboro;
              background: $white;
              width: 139px;
              padding: 10px;
              border-radius: 4px;
              cursor: pointer;
            }

            .disable-drop-down-box {
              pointer-events: none;
              cursor: default;
            }

            .nutrition-autocomplete-results {
              margin-top: 3px;
              width: 140px;
              position: absolute;
              list-style: none;
              box-shadow: 0 1px 10px 0 $box-shadow;
              background: $white;
              z-index: 1;
              color: $charcoal-light;
              border-radius: 4px;
              max-height: 334px;
            }

            .nutrition-autocomplete-result {
              padding: 8px 20px;
              color: $jet-black;
              cursor: pointer;
              margin: 2px;
              border-radius: 4px;
              height: 42px;

              p {
                position: relative;
                right: 6px;
                top: 2px;
                font-size: 16px;
                font-weight: 400;
                text-transform: lowercase;
              }

              &.is-active,
              &:hover {
                background: $green;
                color: $white;
              }
            }
          }

          .nutrition-yield-serving-main-section {
            display: flex;
            justify-content: space-around;
            width: 50%;
            margin-left: 20px;

            .serving-size-section {
              display: flex;

              .serving-text-section {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 14px;
                color: $jet-black;
              }

              .serving-input-section {
                display: flex;
                align-self: center;

                .input-section {
                  font-size: 14px;
                  color: $black;
                  width: 64px;
                  height: 36px;
                  resize: none;
                  background: $pearl-mist;
                  border-radius: 4px;
                  border: 1px solid $grainsboro;
                  text-align: center;
                  padding: 10px;
                  margin-right: 10px;
                  margin-left: 12px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }

            .serving-size-per-container-section {
              display: flex;

              .serving-text-section {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 14px;
                color: $jet-black;
              }

              .serving-input-section {
                display: flex;
                align-self: center;

                .input-section {
                  font-size: 14px;
                  color: $black;
                  width: 64px;
                  height: 36px;
                  resize: none;
                  background: $pearl-mist;
                  border-radius: 4px;
                  border: 1px solid $grainsboro;
                  text-align: center;
                  padding: 10px;
                  margin-right: 10px;
                  margin-left: 12px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
          }
        }

        .nutrition-table-container {
          .nutrition-table {
            width: 100% !important;
            overflow: visible;

            thead {
              border: none;

              th {
                padding: 12px 10px;
                border: none;
                font-size: 12px;
                line-height: 18px;
                color: $steel-gray;
                text-transform: uppercase;
                font-weight: 700;

                &:first-child {
                  border-left: none;
                  border-top-left-radius: 4px;
                }

                &:last-child {
                  border-top-right-radius: 4px;
                }
              }

              .nutrition-name-head {
                width: 300px;
                max-width: 25%;
                text-align: left;
              }

              .nutrition-sub-name-head {
                width: 450px;
                max-width: 25%;
                text-align: left;
              }

              .nutrition-value-head {
                width: 300px;
                max-width: 15%;
                text-align: center;
              }

              .nutrition-value-unit-head {
                width: 300px;
                max-width: 15%;
                text-align: center;
              }

              .nutrition-percentage-value-unit-head {
                width: 180px;
                max-width: 13%;
                text-align: center;
              }
            }

            tbody {
              .nutrition-table-body-row {
                box-shadow: 0 0 0 1px $grainsboro;
                padding: 5px 0;
                border: 1px solid $grainsboro;

                .nutrition-name-column {
                  width: 300px;
                  max-width: 25%;
                  text-align: left;
                  font-weight: 700;
                }

                .nutrition-sub-name-column {
                  width: 450px;
                  max-width: 25%;
                  text-align: left;
                }

                .nutrition-value-column {
                  width: 300px;
                  max-width: 15%;
                  text-align: center;

                  .nutrition-value-area-section {
                    .nutrition-value-area {
                      width: 90px;
                      text-align: center;
                      padding: 10px 15px;
                      border-radius: 4px;
                      background: $pearl-mist;
                      border: 1px solid $grainsboro;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    }
                  }
                }

                .nutrition-value-unit-column {
                  width: 300px;
                  max-width: 15%;
                  text-align: center;
                }

                .nutrition-percentage-value-unit-column {
                  width: 180px;
                  max-width: 13%;
                  text-align: center;
                  color: $gunmetal-grey;
                }

                td {
                  padding: 6px 10px;
                  color: $jet-black;
                  font-size: 14px;
                  font-weight: 400;
                }
              }
            }
          }
        }

        .nutrition-notes-text-section {
          margin-top: 10px;

          .nutrition-text {
            color: $jet-black;
          }
        }
      }
    }
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type="number"] {
    -moz-appearance: textfield;
  }

  .ingredient-modal {
    text-align: left;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;
    align-items: normal;
    margin-top: 30px;
    max-height: 160px;
    padding: 0 14px;
    width: 469px;

    .ingredient-image {
      img {
        width: 80px;
        margin-bottom: 10px;
      }
    }

    .ingredient-content {
      width: 310px;

      .ingredient-title {
        position: relative;
        left: -8px;
        color: $black;
        font-weight: bold;
        font-size: 20px;
        margin-bottom: 15px;
      }

      .ingredient-description {
        position: relative;
        left: -8px;
        color: $fiery-red-blaze;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        margin-top: 10px;
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 32px;
        margin-bottom: 12px;
        gap: 20px;
      }
    }
  }

  .recipe-variant-modal {
    text-align: left;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;
    align-items: normal;
    margin-top: 30px;
    max-height: 160px;
    padding: 0 14px;
    width: 450px;

    .recipe-variant-image {
      img {
        width: 80px;
        margin-bottom: 10px;
      }
    }

    .recipe-variant-content {
      width: 310px;

      .recipe-variant-title {
        position: relative;
        left: -8px;
        color: $black;
        font-weight: 700;
        font-size: 20px;
      }

      .recipe-variant-desc {
        position: relative;
        left: -8px;
        color: $fiery-red-blaze;
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        margin-top: 5px;
      }

      .recipe-variant-button-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 32px;
        margin-bottom: 12px;
        gap: 20px;
      }
    }
  }

  .recipe-variant-modal-container {
    min-width: 560px;
    height: 200px;
    padding: 0 22px;

    .recipe-variant {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;

      span {
        font-size: 24px;
        color: $black;
        font-weight: 700;
      }

      img {
        height: 24px;
        width: 24px;
        cursor: pointer;
      }
    }

    .recipe-variant-language-dropdown {
      margin-top: 20px;

      .recipe-variant-selected-language {
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        padding: 10px 18px;
        border: 1px solid $grainsboro;
        border-radius: 8px;

        span {
          display: flex;

          img {
            height: 20px;
            width: 30px;
          }

          p {
            font-size: 16px;
            font-weight: 400;
            color: $black;
            margin-left: 5px;
          }
        }

        .dropdown-icon {
          transform: rotate(90deg);
          width: 6px;
          height: 10px;
          margin-top: 4px;
          cursor: pointer;
        }
      }

      .dropdown-disabled {
        cursor: default;
        pointer-events: none;
      }

      .autocomplete-results {
        position: absolute;
        list-style: none;
        left: 10px;
        right: 0px;
        top: 137px;
        box-shadow: 0 1px 10px 0 $box-shadow;
        border: 1px solid $grainsboro;
        background: $white;
        z-index: 1;
        color: $charcoal-light;
        max-height: 360px;
        overflow-y: scroll;
        width: 89%;
        border-radius: 4px;
        scrollbar-width: none;
        text-align: left;
        margin-left: 22px;

        &::-webkit-scrollbar {
          display: none;
        }

        .recipe-language-list {
          display: flex;

          img {
            height: 20px;
            width: 30px;
            margin-left: 2px;
          }

          p {
            font-size: 16px;
            font-weight: 400;
            color: $black;
            margin-left: 5px;
          }
        }
      }

      .autocomplete-result {
        padding: 8px 10px;
        cursor: pointer;
        margin: 3px 6px;
        border-radius: 4px;

        &.is-active,
        &:hover {
          background: $green;
          color: $white;
        }
      }
    }

    .recipe-variant-add-button {
      display: flex;
      justify-content: center;
      margin-top: 38px;

      .add-button {
        background: $green-light;
        padding: 10px 48px;
        border-radius: 50px;
        border: none;
        cursor: pointer;

        p {
          color: $white;
          font-size: 14px;
          font-weight: 9000;
        }
      }
    }
  }

  .edit-variant-tag-name {
    min-width: 560px;
    height: 260px;

    .top-section-recipe-variant {
      display: flex;
      justify-content: space-between;
      padding: 10px 16px;

      .add-variant-text {
        color: $black;
      }

      .close-icon {
        cursor: pointer;
        height: 24px;
        width: 24px;
        position: relative;
        top: 4px;
      }
    }

    .middle-section-recipe-variant {
      display: grid;
      margin-top: 4px;

      .variant-tag-select {
        text-align: left;
        padding: 14px;
        margin-left: 5px;
        font-size: 16px;
        font-weight: 400;
        color: $grey;
      }

      .variant-name-input {
        padding: 10px;
        margin: 8px 20px;
        border-radius: 5px;
        border: 1px solid $grainsboro;
        color: $black;
      }
    }

    .bottom-section-recipe-variant {
      margin-top: 25px;

      .add-button {
        border: none;
        background: $green-light;
        padding: 14px 48px;
        border-radius: 50px;
        color: $white;
        font-size: 14px;
        font-weight: 900;
      }
    }
  }

  .create-tag-modal {
    min-width: 260px;
    padding: 16px 10px 0px 10px;

    .tag-title {
      font-size: 16px;
      margin-bottom: 12px;
      font-weight: bolder;
    }

    .tag-input-button {
      color: $charcoal-light;

      .tag-name {
        width: 100%;
        background-color: $pristine-white;
        border: 1px solid $ethereal-whisper-gray;
        border-radius: 4px;
        padding: 5px 0 5px 10px;
        height: 50px;
        margin-bottom: 16px;
      }
    }

    .button-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      gap: 20px;
    }
  }

  .open-preview-recipe-modal {
    width: 100%;
    text-align: initial;
    position: relative;

    .recipe-main-preview-header {
      width: 100%;
      padding: 0px 24px;

      .recipe-preview-header {
        width: 100%;
        font-size: 24px;
        line-height: 40px;
        font-weight: 700;
        color: $black;
      }
    }

    .close-preview-recipe-modal {
      position: absolute;
      cursor: pointer;
      top: 0px;
      right: 10px;
    }

    .open-preview-recipe-main {
      width: 85vw;
    }
  }

  .nutrition-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 470px;
    min-height: 200px;
    padding: 0 10px;

    .nutrition-info-modal {
      width: 100%;

      .nutrition-info-popup {
        .Nutrition-title {
          display: flex;
          justify-content: space-between;

          .title {
            font-size: 24px;
            font-weight: bold;
            margin: 6px 0px 14px 0px;
          }

          .close-nutrition-popup {
            cursor: pointer;
            width: 24px;
            height: 24px;
          }
        }

        .nutrition-content {
          .title {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid $bright-gray;
            padding: 10px 0;

            .name {
              width: 200px;
              text-align: left;
            }

            .dvp {
              width: 80px;
              text-align: center;
            }

            .quantity {
              width: 180px;
              text-align: right;
            }
          }

          .sub-title {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid $bright-gray;
            color: $grey;
            padding: 10px 0 10px 20px;
          }
        }

        .nutrition-bottom {
          text-align: left;
          margin-top: 30px;

          .allergy-title {
            color: $black;
            font-size: 16px;
            font-weight: bold;
          }

          .allergy-content {
            color: $crimson-blaze;
            font-size: 16px;
            font-weight: 400;
          }
        }
      }
    }

    .nutrition-image-loader {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;
      background-color: $white;
      height: 200px;
      width: 210px;

      .loader {
        border: 3px solid $pristine-white;
        border-radius: 50%;
        border-top: 3px solid $spanish-gray;
        border-right: 3px solid $spanish-gray;
        border-bottom: 3px solid $spanish-gray;
        width: 24px;
        height: 24px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }

      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }

    .nutrition-info-popup-container {
      display: flex;
      width: 100%;
      align-items: normal;
      justify-content: space-between;
      padding: 20px 6px 4px 16px;

      .nutrition-image {
        img {
          height: 80px;
          margin-bottom: 10px;
        }
      }

      .nutrition-content {
        width: 310px;
        text-align: left;

        .nutrition-head {
          font-size: 20px;
          margin-bottom: 10px;
          color: $black;
          font-weight: 700;
          position: relative;
          left: -8px;
        }

        .nutrition-description {
          color: $grey;
          font-size: 16px;
          line-height: 24px;
          font-weight: 400;
          position: relative;
          left: -8px;
        }

        .button-container {
          justify-content: flex-end;
          display: flex;
          align-items: center;
          margin-top: 10px;
        }
      }
    }
  }

  .no-product-matches-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 460px;
    min-height: 200px;
    padding: 0 20px;

    .product-image {
      width: 60px;
      height: 60px;
      margin-bottom: 20px;
      margin-top: 20px;
    }

    .product-content {
      font-size: 16px;
      color: $charcoal-light;

      .product-description {
        margin-bottom: 16px;
        text-align: center;
        color: $dim-gray;
      }

      .product-button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }

  .error-save-recipe-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 334px;
    min-height: 118px;
    padding: 0 20px;

    .publish-content {
      font-size: 16px;
      color: $charcoal-light;

      .publish-head {
        font-size: 16px;
        margin-bottom: 10px;
        color: $dim-gray;
        font-weight: bolder;

        .unable-to-save {
          color: $red;
          font-weight: 700;
        }

        .unable-to-save-title {
          color: $charcoal-light;
          font-weight: 300;
        }

        .error-list {
          margin-top: 6px;

          .unable-to-save-list {
            font-weight: 300;
            text-align: left;
            margin-left: 22px;
          }
        }

        .unable-to-save-and-publish {
          color: $red;

          .error-resolution-text {
            color: $charcoal-light;
          }
        }
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
        gap: 20px;
      }
    }
  }

  .edit-recipe-deleted-recipe-popup {
    display: flex;
    background: $light-mint;
    border: 1px solid $green;
    width: 324px;
    height: 46px;
    border-radius: 7px;
    position: fixed;
    top: 72px;
    left: 44%;
    z-index: 999;

    .deleted-recipe-popup-container {
      display: flex;
      padding: 10px 24px;
      width: 100%;
      justify-content: space-between;
    }

    .delete-text-container {
      display: flex;

      .deleted-recipe-popup-image {
        width: 22px;
        height: 22px;
      }

      .deleted-recipe-popup-text {
        font-weight: 400;
        font-size: 16px;
        color: $jet-black;
        margin-left: 8px;
      }
    }

    .close-icon-image {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }

  .edit-recipe-wrong-recipe-popup {
    display: flex;
    background: $light-rose;
    border: 1px solid $peachy-pink;
    width: 324px;
    height: 68px;
    border-radius: 7px;
    position: fixed;
    top: 72px;
    left: 44%;
    z-index: 999;

    .wrong-recipe-popup-container {
      display: flex;
      padding: 10px 24px;
      width: 100%;
      justify-content: space-between;
    }

    .wrong-text-container {
      display: flex;

      .wrong-recipe-popup-image {
        width: 22px;
        height: 22px;
        margin-top: 2px;
      }

      .wrong-recipe-popup-text {
        color: $jet-black;
        margin-left: 8px;
      }

      .try-again-text {
        color: $jet-black;
        margin-left: 8px;
      }
    }

    .close-icon-image {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }

  .edit-product-save-modal {
    width: 400px;
    min-height: 160px;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;

    .nutrition-info-popup-container {
      width: 25%;

      .nutrition-image {
        margin-bottom: 0;
        margin-top: 0;
      }
    }

    .publish-content {
      width: 70%;

      .publish-head {
        text-align: left;
        color: $black;
      }

      .button-container {
        justify-content: flex-end;
        margin-top: 30px;
        font-weight: 700;
        gap: 20px;
      }
    }
  }

  .publish-recipe-variant-modal {
    width: 469px;
    min-height: 253px;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;

    .publish-info-popup-container {
      width: 25%;

      .publish-image {
        margin-bottom: 0;
        margin-top: 30px;

        .save-image-container {
          img {
            height: 80px;
            width: 80px;
          }
        }
      }
    }

    .publish-content {
      width: 70%;
      position: relative;
      top: 30px;
      height: max-content;

      .publish-head {
        font-size: 20px;
        color: $black;
        font-weight: 700;
        text-align: left;
      }

      .note-message {
        color: $ruby-red;
        font-size: 15px;
        font-weight: 400;
        text-align: left;
        position: relative;
        top: 15px;
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        top: 80px;
        gap: 20px;
      }
    }
  }

  .recipe-matches-modal {
    width: 400px;
    margin: 10px 25px 5px 25px;

    .recipe-matches-header-section {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;

      .recipe-matches-title {
        .recipe-matches-ingredient-link {
          color: $shade-of-orange;
          font-weight: bold;
        }
      }
    }

    .recipe-matches-content {
      .main-recipe-matches-section {
        .main-body-recipe-matches {
          .recipe-matches-ingredient-details {
            width: 100%;
            min-width: 380px;
            padding: 10px 15px;
            margin-bottom: 10px;
            border: 1px solid $ethereal-whisper-gray;
            border-radius: 6px;
            box-shadow: 0 2px 3px 0 $diluted-black;
            text-align: left;
          }

          .main-content-recipe-matches {
            color: $charcoal-light;
            line-height: 20px;
            display: inline-block;
            vertical-align: middle;
            margin-left: 3px;
            max-width: 250px;
            font-size: 14px;

            .position-text-recipe-matches {
              font-weight: 700;
              color: $spanish-gray;
            }

            .recipe-matches-ingredient-name {
              margin-bottom: 5px;
            }

            .recipe-matches-ingredient-gtin {
              font-size: 12px;
              color: $trolley-grey;
            }
          }

          .image-div {
            width: 70px;
            text-align: center;
            margin-right: 10px;
          }

          .ingredient-image-recipe-matches {
            max-height: 70px;
            max-width: 70px;
            padding-right: 5px;
          }
        }
      }
    }

    .recipe-matches-button-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
    }
  }

  .edit-product-matches-modal {
    padding: 10px 20px;
    text-align: left;
    width: 430px;

    .add-ingredient-modal-content {
      .close-ingredient-modal {
        float: right;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }

      .add-ingredient-text {
        width: 425px;
        text-align: left !important;
        font-size: 24px !important;
        color: $black;
        font-weight: 700;
      }

      .input-setion-text {
        margin-bottom: 4px;
        color: $gunmetal-grey;
        font-size: 16px;

        .red-text {
          color: $fiery-red-blaze;
        }
      }

      .input-section-ingredient {
        border: 1px solid $grainsboro;
        border-radius: 8px;
        width: 100%;
        height: 50px;
        margin-bottom: 22px;
        padding: 15px;
        caret-color: $green-light;
      }

      .input-ingredient {
        position: relative;

        .input-loading-image-url {
          height: 60px;
          display: flex;
          justify-content: center;
          position: absolute;
          right: 16px;
          top: 14px;

          .loader-image-url {
            border: 3px solid $white;
            border-radius: 50%;
            border-top: 3px solid $green;
            border-right: 3px solid $green;
            border-bottom: 3px solid $green;
            width: 20px;
            height: 20px;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
          }
        }

        .correct-icon-image-url {
          position: absolute;
          top: 12px;
          right: 16px;
        }

        .input-section-upload-link {
          border: 1px solid $grainsboro;
          border-radius: 8px;
          width: 100%;
          height: 50px;
          margin-bottom: 32px;
          padding: 15px 40px 15px 40px;
          caret-color: $green-light;
        }

        .link-image {
          position: absolute;
          top: 12px;
          left: 10px;
        }

        .error-image {
          position: absolute;
          top: 16px;
          right: 12px;
        }

        .error-message {
          position: absolute;
          top: 52px;
          left: 42px;
          color: $red-orange;
          font-size: $font-size-14;
          background: $white;
        }
      }

      ::placeholder {
        color: $spanish-gray;
        font-size: 16px;
        font-weight: 400;
      }
    }

    .edit-product-modal-content {
      .edit-description {
        font-size: 18px;
        text-align: center;
        margin-bottom: 20px;
        font-weight: 700;

        .edit-description-subtitle {
          font-weight: 400;
          font-size: 16px;
          color: $red;
          margin-top: 10px;
        }
      }

      .edit-product-matches-button-container {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .add-variant-category-name {
    min-width: 560px;
    height: 260px;

    .top-section-recipe-variant {
      display: flex;
      justify-content: space-between;
      padding: 10px 16px;

      .add-variant-text {
        color: $black;
      }

      .close-icon {
        cursor: pointer;
        height: 24px;
        width: 24px;
        position: relative;
        top: 4px;
      }
    }

    .middle-section-recipe-variant {
      display: grid;
      margin-top: 4px;

      .variant-category-select {
        display: flex;
        text-align: left;
        padding: 14px;
        margin-left: 5px;
        font-size: 16px;
        font-weight: 400;
        color: $grey;

        .category-name {
          max-width: 300px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .variant-name-input {
        padding: 10px;
        margin: 8px 20px;
        border-radius: 5px;
        border: 1px solid $grainsboro;
        color: $black;
      }
    }

    .bottom-section-recipe-variant {
      margin-top: 25px;

      .add-button {
        border: none;
        background: $green-light;
        padding: 14px 48px;
        border-radius: 50px;
        color: $white;
        font-size: 14px;
        font-weight: 900;
      }

      .disable-add-button {
        opacity: 0.3;
        cursor: default !important;
        pointer-events: none;
      }
    }
  }
  .add-ingredient-modal {
    width: 539px;
  }


  .unable-to-leave-recipe-varients {
    width: 420px;

    .popup {
      padding: 22px 30px 2px 30px;
      display: flex;
      align-items: center;

      .cross-image-div {
        margin-top: 6px;

        .cross-image {
          width: 66px;
          display: inline-block;
        }
      }

      .text-heading {
        word-spacing: 1px;
        display: inline-block;
        width: 300px;
        margin-left: 15px;
        text-align: left;
        line-height: 28px;
      }
    }

    .button-container {
      margin-top: 30px;
      margin-right: 30px;
      float: right;
    }

    .unable-to-leave-empty-field {
      font-size: 12px;

      .error-list {
        font-weight: normal;
        text-align: left;
        margin-left: 29%;
        display: block;
        color: $gray;
        line-height: 20px;
      }
    }
  }

  .add-ingredient-weights-popup {
    padding: 10px 20px;
    width: 515px;
    height: 240px;
    overflow-y: auto;

    .add-ingredient-headin-text-and-cross-logo {
      display: flex;
      align-items: center;
      padding: 10px 0px;

      .add-ingredient-heading-text {
        color: $obsidian-black;
        font-size: 20px;
        font-weight: 700;
        font-style: normal;
        letter-spacing: 0px;
        text-align: left;
        width: 424px;
        display: inline-block;
      }

      .add-ingredient-cross-image {
        width: 24px;
        height: 24px;
        cursor: pointer;
        margin-left: 30px;
      }
    }

    .sub-text-container {
      .sub-text {
        color: $graphite;
        font-style: normal;
        letter-spacing: 0px;
        margin: 10px 0px;
        text-align: left;

        .sub-text-bold-text {
          color: $graphite;
          font-weight: 700;
          font-style: normal;
          letter-spacing: 0px;
          text-align: left;
        }
      }
    }

    .input-container {
      display: flex;
      align-items: center;

      .input-box-container {
        .input-box {
          width: 192px;
          border-radius: 4px;
          border: 1px solid $moon-stone;
          background-color: $frost-white;
          padding: 10px;
          color: $gunmetal-grey;
        }
      }

      .radio-button-container-one {
        margin: 0px 0px 0px 65px;

        .round {
          position: relative;
        }

        .round label {
          background-color: $white;
          border: 2px solid $grainsboro;
          border-radius: 100%;
          cursor: pointer;
          height: 24px;
          left: 0px;
          position: absolute;
          width: 24px;
        }

        .round label:after {
          border: 2px solid $white;
          border-top: none;
          border-right: none;
          content: "";
          height: 6px;
          left: 5px;
          opacity: 0;
          position: absolute;
          top: 7px;
          -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
          width: 12px;
          cursor: pointer;
        }

        .round input[type="radio"] {
          visibility: hidden;
          display: none;
        }

        .round input[type="radio"] + label {
          background-color: $white;
          border: 3px solid $green-light;
        }

        .round input[type="radio"] + label:after {
          opacity: 1;
          top: 3px;
          left: 2px;
          width: 14px;
          height: 14px;
          border-radius: 70%;
          background: $green-light;
        }

        .radio-btn-text-container {
          position: relative;
          top: 2px;
          left: 34px;

          .radio-btn-text {
            cursor: pointer;
            color: $black;
          }
        }
      }
    }

    .btn-div {
      position: absolute;
      right: 30px;
      bottom: 30px;
      gap: 20px;
      display: flex;
    }
  }

.main-ingredient-keywords-popup-modal {
  width: 727px;
  font-family: $font-family-averta;

  .main-edit-keyword-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .edit-keyword-section {
      font-size: 20px;
      font-weight: 700;
      color: $black;
      margin-left: 28px;
    }

    .close-image-icon {
      height: fit-content;
      width: fit-content;
      cursor: pointer;
      margin-right: 28px;
    }
  }

  .ingredients-quantity-section {
    display: block;
    color: $black;
    margin-left: 28px;
    margin-bottom: 5px;
    height: auto;
    word-break: break-all;
    text-align: left;
    margin-right: 31px;
  }

  .ingredients-text-section {
    color: $black;
    margin-left: 4px;
  }

  .input-container {
    display: flex;
    align-items: center;

    .ingredient-keyword-container {
      width: 92%;
      border-radius: 4px;
      border: 1px solid $moon-stone;
      background-color: $frost-white;
      padding: 23px;
      color: $gunmetal-grey;
      margin-left: 28px;
      margin-bottom: 5px;

      .cross-icon-off {
        display: none;
      }

      .keywords-full-version {
        width: 667px;
        position: absolute;
        right: 41px;
        padding: 13px;
        margin-top: -24px;
        color: $black;
        background: $transparent;
        border: none;
      }
    }

    ::placeholder {
      font-style: italic;
      font-weight: 400;
      font-size: 14px;
      color: $gunmetal-grey;
    }

    .ingredient-keyword-after-edit::-webkit-scrollbar {
      width: 12px;
      border-radius: 3px;
    }

    .ingredient-keyword-after-edit::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
    }

    .ingredient-keyword-after-edit::-webkit-scrollbar-thumb {
      background: $grainsboro;
      border: 3px solid $transparent;
      border-radius: 15px;
      background-clip: content-box;
    }

    .ingredient-keyword-after-edit {
      width: 92%;
      border-radius: 4px;
      border: 1px solid $moon-stone;
      background-color: $frost-white;
      padding: 10px;
      overflow: auto;
      max-height: 300px;
      scrollbar-color: $grainsboro $whispering-white-smoke;
      scrollbar-width: thin;
      color: $gunmetal-grey;
      margin-left: 28px;
      margin-bottom: 5px;
      align-items: flex-start;
      display: flex;
      flex-wrap: wrap;
      position: relative;
      box-shadow: 0 0px 0px 0px RGB(218, 218, 218);

      .keywords {
        min-width: 330px;
        border: none;
        background: $transparent;
        font-size: 14px;
        position: relative;
        height: 26px;
        font-weight: 400;
        color: $black;
        padding: 0px 0px 0px 9px;
      }

      .cross-icon-edit {
        position: absolute;
        right: 15px;
        width: 16px;
        height: 16px;
        align-self: center;
      }

      .cross-icon-edit:hover {
        cursor: pointer;
      }

      .selected-ingredient-keyword-main {
        max-width: 612px;
        flex-wrap: wrap;
        display: flex;
        align-items: center;

        .selected-ingredient-keyword-popup {
          background-color: $whisper;
          border: 1px solid $grainsboro;
          border-radius: 4px;
          max-width: -moz-fit-content;
          max-width: 96%;
          height: fit-content;
          padding-left: 12px;
          padding-top: 3px;
          padding-bottom: 3px;
          padding-right: 12px;
          display: flex;
          align-items: center;
          position: relative;
          top: -4px;
          margin-left: 8px;
          margin-top: 8px;

          .ingredient-data-keyword {
            display: flex;
            align-items: center;
            width: -moz-max-content;
            width: 96%;

            .ingredient-data-keyword-section {
              width: 100%;

              .data-truncated {
                max-width: 100%;
                word-wrap: break-word;
                line-height: 1.6;
                text-align: initial;
              }
            }

            .remove-ingredient-keyword-image {
              margin-left: 2px;
              top: 1px;
              cursor: pointer;
              position: relative;
              right: -6px;
              width: 9px;
              height: 10px;
            }
          }
        }
      }
    }
  }

  .keyword-article {
    display: flex;
    font-size: 14px;
    color: RGB(114, 119, 113);
    margin-bottom: 12px;
    margin-left: 28px;
    font-weight: 400;
  }

  .global-keywords {
    display: flex;
    font-size: 14px;
    font-weight: 700;
    color: $jet-black;
    line-height: 1.2;
    margin-left: 28px;
    margin-bottom: 10px;
  }

  .main-section-breadcrumb-global-keywords {
    display: flex;
    margin-left: 19px;
    align-items: center;
    flex-wrap: wrap;

    .breadcrumb-global-keywords {
      background-color: $white;
      border: 1px solid $grainsboro;
      border-radius: 4px;
      max-width: 152px;
      padding: 12px 12px;
      height: 20px;
      display: flex;
      align-items: center;
      position: relative;
      top: -11px;
      margin-left: 8px;
      margin-top: 8px;
      font-size: 14px;
      color: $gunmetal-grey;
      font-weight: 400 !important;

      .display-keyword {
        max-width: 152px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }

  .global-keywords-empty {
    display: flex;
    font-size: 14px;
    color: $gunmetal-grey;
    margin-left: 28px;
  }
}

.recipe-preview {
  border-top: 1px solid $grainsboro-gray;
  padding-top: 16px;
  font-family: $font-family-averta;

  img {
    width: 17px;
    height: 19px;
    margin-top: -8px;
  }

  span {
    cursor: pointer;
    font-weight: 700;
    font-size: 14px;
    color: $green-light;
  }
}

.disable-add-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 60px;
  border-radius: 50px;
  border: 0;
  background-color: $green;
  text-shadow: 0 -1px 0 $faint-black;
  color: $white;
  font-family: $font-family-averta;
  font-weight: 700;
  box-shadow: 0 2px 4px 0 $box-shadow;
  margin: 5px;
  text-align: center;
  opacity: 0.6;
  pointer-events: none;
  height: 45px;
  font-size: 16px;
}

.disable-recipe-detail-content {
  pointer-events: none;

  .ing-tool {
    pointer-events: all;

    .filter-select-input {
      pointer-events: none;
    }
  }
}

.disable-nutrition-detail-content {
  background: none !important;
  width: -webkit-fill-available !important;
  border: none !important;
  pointer-events: none;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.rotate {
  transform: rotate(270deg) !important;
}
.rotated {
  transform: rotate(180deg) !important;
}

.disable-recipe-detail {
  opacity: 0.5;
  pointer-events: none;
}

.recipe-schedule-form-modal-container {
  width: 530px;
  height: 295px;
  overflow: visible;
  font-family: $font-family-averta;

  .recipe-schedule-sub-container {
    .recipe-schedule-top-container {
      display: flex;
      justify-content: space-between;
      padding: 10px 30px;

      .schedule-text {
        font-size: 20px;
        font-weight: 700;
        color: $black;
      }

      .close-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;

        .close-icon-image {
          height: 100%;
          width: 100%;
        }
      }
    }

    .publish-text {
      padding: 10px 30px;
      font-size: 16px;
      line-height: 10px;
      font-weight: 700;
      margin: 20px 0px;
      display: flex;
      text-align: start;
    }

    .recipe-schedule-date-picker-container {
      display: flex;
      margin-top: 15px;
      margin-left: 30px;
    }

    .recipe-schedule-bottom-container {
      position: relative;
      top: 75px;
      right: 20px;

      .recipe-schedule-button-section {
        float: right;
        gap: 20px;
        display: flex;
      }
    }
  }
}
