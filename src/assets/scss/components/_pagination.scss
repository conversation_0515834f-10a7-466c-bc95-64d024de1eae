.pagination {
  margin: 0;
  padding: 30px 0px;
  display: flex;
  list-style: none;
  justify-content: center;
  align-items: center;
  border: none;
}

.pagination .disabled-pagination {
  display: none;
}

.pagination li:first-child,
li:last-child {
  .page-link {
    margin: 0 !important;
    border-radius: 50% !important;
    background: $white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px !important;
    height: 36px !important;
  
  }

  .page-link:hover {
    color: $green;
  }
}

.next,
.prev {
  margin: 0 !important;
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  color: $grey;
  align-items: center;
  justify-content: center;
  width: 36px !important;
  height: 36px !important;
  box-shadow: 0px 0px 4px 0px $box-shadow;
}

.next a:focus,
.prev a:focus {
  outline: none;
}

.next:hover,
.prev:hover {
  color: $green;
}

.next a,
.prev a {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.prev .page-link {
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.next .page-link {
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-item {
  margin: 0 5px;
}

.page-item .page-link {
  color: $grey;
  background: $ink-wash-black;
  border: none;
  line-height: 1 !important;
  font-family: $font-family-averta;
  height: 36px;
  width: 36px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-link:focus {
  box-shadow: unset;
  outline: 0;
}

.page-item .page-link:hover {
  background: $tinted-green;
  color: $green;
}

.page-item.active .page-link {
  background-color: $green;
  color: $white;
  border-color: unset;
}

// SRT pagination //

.pagination-unpromoted {
  margin: 0;
  padding: 20px 0px;
  display: flex;
  list-style: none;
  justify-content: center;
  align-items: center;
  border: none;
}

.pagination-unpromoted .disabled-pagination {
  display: none;
}

.pagination-unpromoted li:first-child,
li:last-child {
  .page-link-unpromoted {
    margin: 0 !important;
    border-radius: 50% !important;
    background: $white !important;
    display: flex;
    color: $grey;
    align-items: center;
    justify-content: center;
    width: 40px !important;
    height: 36px !important;
    box-shadow: 0px 0px 4px 0px $box-shadow;
  }

  .page-link-unpromoted:hover {
    color: $white;
  }
}

.next-unpromoted,
.prev-unpromoted {
  margin: 0 !important;
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 25px;
  font-weight: 548;
  color: $green-light;
  width: 21px !important;
  height: 21px !important;
}

.next-promoted,
.prev-promoted {
  margin: 0 !important;
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 25px;
  font-weight: 548;
  color: $dark-turquoise;
  width: 21px !important;
  height: 21px !important;
}

.next-unpromoted a:focus,
.prev-unpromoted a:focus {
  outline: none;
}

.next-promoted a:focus,
.prev-promoted a:focus {
  outline: none;
}

.next-unpromoted a,
.prev-unpromoted a {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.next-promoted a,
.prev-promoted a {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.prev-unpromoted .page-link-unpromoted {
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.prev-promoted .page-link-unpromoted {
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.next-unpromoted .page-link-unpromoted {
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.next-promoted .page-link-unpromoted {
  border-radius: 50% !important;
  background: $white !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-item-unpromoted {
  margin: 0 5px;
  color: $grey;
}

.page-item-unpromoted .page-link-unpromoted {
  border: none;
  line-height: 1 !important;
  height: 26px;
  width: 26px;
  font-size: 14px;
  cursor: pointer;
  font-family: $font-family-averta;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-link-unpromoted:focus {
  box-shadow: unset;
  outline: 0;
}

.page-item-unpromoted .page-link-unpromoted:hover {
  background: $tinted-green;
}

.page-item-unpromoted.active .page-link-unpromoted,
.active-unpromoted {
  background-color: $green;
  color: $white !important;
  width: 26px;
  height: 26px;
  border-radius: 4px;
  font-family: $font-family-averta;
  font-weight: 400;
  font-size: 14px;
  border-color: unset;
}

.page-item-unpromoted.active .page-link-unpromoted,
.active-promoted {
  background-color: $dark-turquoise;
  color: $white !important;
  width: 26px;
  height: 26px;
  border-radius: 4px;
  font-family: $font-family-averta;
  font-weight: 400;
  font-size: 14px;
  border-color: unset;
}