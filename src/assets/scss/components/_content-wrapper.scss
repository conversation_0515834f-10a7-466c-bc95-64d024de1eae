.content-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  width: auto;
  max-width: calc(100% - $content-wrapper-left-shift);
  height: 100%;
  min-height: calc(100dvh - $content-wrapper-top-shift);
  margin: $content-wrapper-top-shift 15px 0 $content-wrapper-left-shift;
  padding: $content-wrapper-padding-top $content-wrapper-padding-right $content-wrapper-padding-bottom $content-wrapper-padding-left;

  &-head {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    margin-bottom: 20px;
  }

  &-head &-title {
    margin-bottom: 0;
  }

  &-body {
    width: 100%;
  }

  &-head-actions {
    flex: 1 1 auto;
    display: flex;
    justify-content: flex-end;
    max-width: 100%;
  }

  &.__loading-body &-head-actions {
    opacity: .6;
    pointer-events: none;
    user-select: none;
  }
}
