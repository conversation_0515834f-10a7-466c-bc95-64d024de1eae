.import-recipe-main-section {
  font-family: $font-family-averta;
  width: 530px;
  height: 215px;
  padding: 8px 24px;
  &.content {
    height: 240px;
  }

  .import-recipe-content {
    position: relative;

    .import-recipe-top-section {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .import-recipe-header {
        color: $black;
      }

      .close-button {
        border: none;
        background: none;
        .close-icon {
          height: 26px;
          width: 26px;
          cursor: pointer;

          img {
            height: 100%;
            width: 100%;
          }
        }
      }
    }

    .import-recipe-successful-section {
      display: flex;
      margin-top: 20px;
      padding: 14px;
      background-color: $green-peppermint;
      border: 1px solid $green-light;
      border-radius: 4px;

      .import-recipe-successful-text {
        font-family: $font-family-averta;
        color: $black;
        margin-left: 14px;
      }

      .green-correct-icon {
        width: 22px;
        height: 22px;
      }
    }

    .import-recipe-error-section {
      display: flex;
      margin-top: 20px;
      padding: 14px;
      background: $light-rose;
      border: 1px solid $peachy-pink;
      border-radius: 4px;

      .import-recipe-error-container {
        display: flex;

        .import-recipe-error-image {
          width: 22px;
          height: 22px;
          margin-top: 2px;
        }

        .import-recipe-error-access-text {
          margin-left: 12px;
          margin-bottom: 4px;
          color: $jet-black;
          text-align: left;
        }

        .import-recipe-error-text {
          display: flow;
          margin-left: 12px;
          color: $jet-black;
          text-align: left;
        }
      }
    }

    .input-box-section {
      margin-top: 25px;

      .input-box {
        margin-bottom: 10px;

        .input-url {
          background: $pearl-mist;
          width: 100%;
          padding: 10px 12px;
          border: 1px solid $grainsboro;
          border-radius: 4px;
          &.error {
            border-color: $red;
          }
          &.error::placeholder {
            color: $red;
          }
        }
      }
    }

    .import-recipe-warning {
      display: flex;
      justify-content: flex-start;
      gap: 10px;
      font-size: $font-size-base;
      font-weight: $font-weight-semi-bold;
      color: $red;
    }

    .button-section {
      display: flex;
      justify-content: flex-end;
      position: absolute;
      top: 150px;
      right: 10px;
      &.content {
        top: 185px;
      }

      .cancel-and-import-recipe-button-section {
        display: flex;
        gap: 20px;
      }
    }
  }
}
