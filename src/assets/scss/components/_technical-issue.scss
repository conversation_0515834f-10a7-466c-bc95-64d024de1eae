.technical-issue-modal {
  width: 595px;
  background-color: $white;
  border-radius: $border-radius;
  display: flex;
  flex-direction: column;
  padding: 8px 16px 10px 16px;

  .technical-issue-modal-content {
    color: $silver;
    width: 24px;
    height: 24px;
    position: absolute;
    right: 26px;
    cursor: pointer;
  }

  .technical-issue-header {
    font-family: $font-family-averta;
    font-weight: $font-weight-bold;
    font-size: $font-size-20;
    color: $graphite-gray;
    text-align: left;
    margin-top: 11px;
  }

  .technical-issue-details {
    font-size: $font-size-base;
    font-weight: $font-weight-normal;
    color: $graphite-gray;
    font-family: $font-family-averta;
    text-align: left;

    .technical-issue-description {
      text-align: left;
      margin-top: 17px;
    }

    .issue-resolve-list {
      counter-reset: list-counter;
      list-style: none;
      text-align: left;
    }

    .issue-resolve-list li {
      counter-increment: list-counter;
      line-height: 17px;
    }

    .issue-resolve-list li:before {
      content: counter(list-counter) ")";
      margin-right: 5px;
    }
  }

  .technical-issue-email-text {
    font-weight: $font-weight-bold;
  }

  .button-section {
    margin-top: 21px;
    margin-left: auto;
  }
}
