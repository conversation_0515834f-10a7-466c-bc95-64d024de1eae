.process-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  width: 100%;
  max-width: 530px;
  height: auto;
  min-height: 255px;

  @media (min-width: 600px) {
    width: 530px;
  }

  &-loader {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid $white;
    border-top: 3px solid $green;
    border-right: 3px solid $green;
    border-bottom: 3px solid $green;
  }

  &-description {
    width: 100%;
    max-width: 468px;
    height: 57px;
    padding: 18px 0;
    border-radius: 4px;
    background-color: $green-peppermint;
    border: 1px solid $green-fringy-flower;
    text-align: center;
    color: $shadow-gray;
  }

  &-saving {}
  &-publishing {}
  &-unpublishing {}
  &-scheduling {}
  &-unscheduling {}
  &-deleting &-loader {
    border-top-color: $persian-red;
    border-right-color: $persian-red;
    border-bottom-color: $persian-red;
  }
  &-deleting &-description {
    background-color: $vanilla-ice;
    border-color: $vanilla-ice;
    color: $jet-black;
  }
}
