.batch-generator-header-container {
  margin-bottom: 40px;
  font-size: $font-size-14;
  font-family: $font-family-averta;
  .button-container {
    display: flex;
    align-items: baseline;
    width: 100%;
    gap: 40px;
    margin-bottom: 16px;
  }
  .input-container {
    .iq-r-g-prompt {
      .input-field {
        width: 100%;
        border: none;
        border-radius: 20px 0 0 20px;
        padding: 0 20px;
        font-size: $font-size-base;
        font-weight: $font-weight-normal;
      }
    }
  }
}
.batch-prompt-data-table-main {
  font-family: $font-family-averta;
  font-size: $font-size-14;
  .batch-prompt-table-container {
    border-radius: 8px;
    background-color: $white;    
    .batch-loader {
      align-items: normal;
    }
    .batch-prompt-table {
      .table-head-row {
        border-bottom: 1px solid $light-gray;
        border-radius: 8px 8px 0px 0px;
        .table-head {
          margin: 0 4px;
          color: $jet-black;
          line-height: 1;
          text-align: left;
        
          th {
            padding: 24px 6px 14px 6px;
            font-weight: $font-weight-semi-bold;
          }
        }        
      }
      .batch-table-body {
        .table-body {
          &:hover {
            background-color: $aqua-spring;
            .remove-container {
              .basket {
                visibility: visible;
              }
            }
          }
          td {
            padding: 14px 6px 14px 6px;
          }
          .status {
            width: 20%;
            font-weight: $font-weight-normal;
            color: $jet-black;
          }
          .serial-no {
            width: 3%;
            font-weight: $font-weight-semi-bold;
            text-align: center;
            color: $jet-black;
          }
          .prompt {
            width: 40%;
            font-weight: $font-weight-normal;
            color: $jet-black;
          }
          .status-failed {
            color: $apple-red;
          }
          .status-running {
            font-weight: $font-weight-semi-bold;
          }
          .status-pending {
            color: $semi-gray;
          }
          .ready-to-review {
            width: 22%;
            font-weight: $font-weight-normal;            
            color: $vivid-green;
            .not-generated {
              color: $jet-black;
            }
          }
          .expand-width {
            width: 50%;
          }
          .text-underline {
            text-align: left;
            text-decoration: underline;
          }
          .remove-container {
            position: relative;
            height: 65px;
            text-align: end;
            .basket {
              visibility: hidden;
              margin-right: 10px;
            }
          }
        }
      }
    }
  }

  .button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  
  .batch-delete-button {
    position: absolute;
    top: 18px;
    right: 10px;
  
    .button-height {
      height: 30px;
      min-width: 81px;
    }
  }  
  .limit-prompt-section {
    padding: 20px 0;
    font-weight: $font-weight-normal;
    color: $charcoal-gray;
  }  
  .batch-loader-icon {
    align-items: normal;
  }
  .new-batch-button {
    align-self: center;
    margin-left: auto;
  }
  .batch-generation-heading {
    margin-bottom: 10px;
  }
  .redirect-recipes-page {
    font-size: $font-size-base;
    font-weight: $font-weight-semi-bold;
    color: $green-dark;
    text-decoration: underline;
  }
}
.batch-generated {
  background-color: $transparent !important;
  border: 1px solid $green-light;
}