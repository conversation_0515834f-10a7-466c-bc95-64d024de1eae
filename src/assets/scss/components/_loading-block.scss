.loading-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  width: 90%;
  padding: 40px 30px;
  margin: 50px;
  background-color: $white;
  border-radius: $border-radius;

  &-spinner {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid $white;
    border-top: 3px solid $green;
    border-right: 3px solid $green;
    border-bottom: 3px solid $green;
  }

  &-info {
    width: min(425px, 100%);
    padding: 20px;
    background-color: $green-peppermint;
    border-radius: 4px;
    border: 1px solid $green-fringy-flower;

    p {
      margin-bottom: 0 !important;
      font-weight: 400 !important;
      text-align: center;
    }
  }
}
