.export-main-section {
    font-family: $font-family-averta;
    width: 356px;
    min-height: 296px;
    height: auto;
    padding: 8px 24px;

    .export-top-section {
        display: flex;
        justify-content: space-between;
        padding: 4px 0px 40px 0px;

        .export-header {
            color: $black;
        }

        .close-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
        }
    }

    .export-successful-section {
        background-color: $green-peppermint;
        border: 1px solid $green-light;
        display: flex;
        padding: 14px;
        border-radius: 4px;

        .export-successful-text {
            font-family: $font-family-averta;
            color: $black;
            margin-left: 14px;
        }

        .green-correct-icon {
            width: 22px;
            height: 22px;
        }
    }
    .export-error-section {
        background: $light-rose;
        border: 1px solid $peachy-pink;
        display: flex;
        padding: 14px;
        border-radius: 4px;

        .export-error-container {
            display: flex;

            .export-error-image {
                width: 22px;
                height: 22px;
                margin-top: 2px;
            }

            .export-error-access-text {
                color: $jet-black;
                margin-left: 12px;
                margin-bottom: 4px;
                text-align: left;
            }

            .export-error-text {
                color: $jet-black;
                margin-left: 12px;
                display: flow;
                text-align: left;
            }
        }
    }

    .export-checkbox-section {
        .export-category {
            text-align: left;
            font-weight: $font-weight-semi-bold;
            padding-bottom: 16px;
        }

        .export-data-list {
            padding: 0 0 20px 12px;
            display: flex;

            .round {
                position: relative;
                bottom: 10px;
            }

            .round label {
                background-color: $white;
                border: 2px solid $grainsboro;
                border-radius: 100%;
                cursor: pointer;
                height: 24px;
                left: 0px;
                position: absolute;
                width: 24px;
            }

            .round label:after {
                border: 2px solid $white;
                border-top: none;
                border-right: none;
                content: "";
                height: 6px;
                left: 5px;
                opacity: 0;
                position: absolute;
                top: 7px;
                -webkit-transform: rotate(-45deg);
                transform: rotate(-45deg);
                width: 12px;
                cursor: pointer;
            }

            .round input[type="radio"] {
                visibility: hidden;
                display: none;
            }

            .round input[type="radio"]+label {
                background-color: $white;
                border: 3px solid $green-light;
            }

            .round input[type="radio"]+label:after {
                opacity: 1;
                top: 3px;
                left: 2px;
                width: 14px;
                height: 14px;
                border-radius: 70%;
                background: $green-light;
            }

            .export-recipe-list-data {
                position: relative;
                left: 10px;

                .export-recipe-name {
                    cursor: pointer;
                    width: fit-content;
                    color: $black;
                }
            }
        }
    }

    .export-initial-button-section {
        display: flex;
        justify-content: space-around;
        padding-top: $font-size-base;
    }

    .export-successful-button-section {
        display: flex;
        justify-content: end;
        position: absolute;
        bottom: 24px;
        right: 24px;
    }
}