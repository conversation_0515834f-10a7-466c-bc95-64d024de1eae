.recipe-info-overview-section {
  display: grid;
  grid-template-columns: 50% 25% 25%;
  gap: 3px;
  margin: 30px 6px 0 0;
  &.content {
    display: grid;
    grid-template-columns: 40% 20% 20% 20%;
    gap: 3px;
    margin: 30px 6px 0 0;
  }

  .border-box {
    background: $white;
    padding: 35px 0px;
  }

  .last-box {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .recipe-head-count-section {
    display: flex;
    justify-content: center;
    gap: 15px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;

    .image {
      height: 32px;
      width: 30px;

      img {
        height: 100%;
        width: 100%;
      }
    }

    .recipe-count {
      display: flex;
      gap: 10px;
      font-size: $font-size-28;
      align-items: baseline;

      .head-count {
        color: $green;
        font-weight: $font-weight-bold;
      }

      .text {
        font-weight: $font-weight-normal;
        color: $black;
      }
    }
  }

  .count-section {
    display: flex;
    justify-content: center;

    .recipe-count {
      display: flex;
      gap: 10px;
      align-items: baseline;

      .count {
        color: $green;
        font-weight: $font-weight-normal;
        font-size: $font-size-28;
      }

      .text {
        font-weight: $font-weight-normal;
        color: $black;
        font-size: $font-size-14;
      }
    }
  }
}
