.recipe-image-generator-main-container {
    .recipe-image-inner-section {
        position: relative;
        max-height: 290px;
        width: 100%;
        overflow-y: scroll;

        &::-webkit-scrollbar {
            width: 8px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: $grainsboro;
            background-clip: content-box;
            border-radius: 3px;
        }

        .image-selector {
            &-loading {
                z-index: 4;
                position: absolute;
                top: 0;
                left: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;

                &-body {
                    width: 100%;
                    max-width: 474px;
                    padding: 4px 16px;
                    border: 1px solid $grainsboro;
                    border-radius: $border-radius;
                    background-color: $white;
                    text-align: center;
                }

                &-icon {
                    display: inline-block;
                    width: 30px;
                    height: 30px;
                    background-image: url("@/assets/images/icons/loader.svg?skipsvgo=true");
                    background-repeat: no-repeat;
                    background-position: center center;
                    background-size: contain;
                    animation: rotation 1s linear infinite;
                    transform-origin: center center;
                }

                span {
                    margin: 8px;
                    vertical-align: middle;
                }
            }

            &-wrapper {
                &-main-box {
                    position: relative;
                    width: 100%;
                    overflow: hidden;
                }

                &-inner-box {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 21px 23px;
                    width: 100%;
                    height: auto;
                    transition: transform 0.5s ease;
                }
            }
        }
    }
}
