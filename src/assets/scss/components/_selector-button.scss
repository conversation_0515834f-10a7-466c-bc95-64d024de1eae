.selector-button {
  width: 82px;
  height: 82px;
  padding-top: 23px;
  border-radius: $border-radius;
  font-size: $font-size-12;

  &-absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }

  &-bg {
    z-index: 1;
    opacity: 0.8;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: $green;
    border-radius: $border-radius;
  }

  &-container {
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
    position: relative;
    width: 100%;
    height: 100%;

    span:first-child {
      display: inline-block;
      width: 22px;
      height: 19px;
      background-image: url("@/assets/images/icons/check-select.svg?skipsvgo=true");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: contain;
    }
  }

  &-circular {
    position: relative;
    bottom: 20px;
    z-index: 2;
    display: flex;
    justify-content: center;
    padding: 20px;
    border-radius: 50%;
    background-color: $green;

    span {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid $white;
      border-radius: 50%;
      padding: 10px;

      img {
        width: 22px;
        height: 19px;
      }
    }
  }
}
