.filter-select {
  position: relative;

  &-toggle {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    width: auto;
    height: 44px;
    padding: 10px 20px 10px 24px;
    border-radius: 22px;
    border: 1px solid $grainsboro;

    img {
      transform: rotate(90deg);
    }

    &-hr {
      width: 1.5px;
      height: 100%;
      background-color: $bright-gray;
    }
  }

  &-expanded &-toggle img {
    transform: rotate(-90deg);
  }

  &-dropdown {
    min-width: 300px;
    height: auto;
    max-height: 420px;
    padding: 20px;
    background-color: $white;
    border-radius: 4px;
    box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.2);

    &-body {
      overflow-y: auto;
      height: auto;
      max-height: calc(420px - (50px + 40px));

      &::-webkit-scrollbar {
        width: 12px;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: $whispering-white-smoke;
      }

      &::-webkit-scrollbar-thumb {
        background: $grainsboro;
        border: 3px solid $transparent;
        border-radius: 15px;
        background-clip: content-box;
      }
    }

    &-actions {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      height: 50px;
    }
  }

  &-option {
    padding: 12px;
    background-color: transparent;

    &:has(label:hover),
    &-selected {
      background-color: $aqua-spring;
    }
  }
}
