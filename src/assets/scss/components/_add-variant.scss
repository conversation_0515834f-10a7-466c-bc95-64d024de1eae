.add-variant-name-container {
  min-width: 560px;
  height: 260px;
  font-family: $font-family-averta;

  .top-section-recipe-variant {
    display: flex;
    justify-content: space-between;
    padding: 10px 16px;

    .back {
      width: 14px;
      height: 9px;
      transform: rotate(90deg);
      cursor: pointer;
      position: relative;
      top: 10px;
    }

    .add-variant-text {
      font-size: 24px;
      font-weight: 700;
      color: $black;
    }

    .close-icon {
      cursor: pointer;
      height: 24px;
      width: 24px;
      position: relative;
      top: 4px;
    }
  }

  .middle-section-recipe-variant {
    display: grid;
    margin-top: 4px;

    .variant-category-select {
      display: flex;
      text-align: left;
      padding: 14px;
      margin-left: 5px;
      font-size: 16px;
      font-weight: 400;
      color: $grey;

      .item-name {
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .variant-name-input {
      padding: 10px;
      margin: 8px 20px;
      border-radius: 5px;
      border: 1px solid $grainsboro;
      font-size: 16px;
      color: $black;
      font-weight: 400;
    }
  }

  .bottom-section-recipe-variant {
    margin-top: 25px;

    .add-button {
      border: none;
      background: $green-light;
      padding: 14px 48px;
      border-radius: 50px;
      color: $white;
      font-size: 14px;
      font-weight: 900;
    }

    .disable-add-button {
      opacity: 0.3;
      cursor: default !important;
      pointer-events: none;
    }
  }
}