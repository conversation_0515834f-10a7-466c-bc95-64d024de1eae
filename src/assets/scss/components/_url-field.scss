.url-field {
  position: relative;
  top: 8px;
  width: 56%;

  .cta-url-section {
    display: flex;
    border-radius: 4px;
    background: $form-control-background-color;
    border: 1px solid $form-control-border-color;

    .input-section {
      display: flex;
      width: 93%;
      position: relative;
      .url-input {
        width: 98%;
        height: 45px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        line-height: 16px;
        border: transparent;
      }
    }

    .cta-link-input-verify-section {
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
      align-items: center;
      .cta-link-progress-check {
        .loader-image {
          width: 20px;
          height: 20px;
          border: 3px solid $white;
          border-top: 3px solid $green;
          border-right: 3px solid $green;
          border-bottom: 3px solid $green;
          border-radius: 50%;
          animation: spin 2s linear infinite;
        }
      }
      .cta-link-correct-check {
        .correct-icon {
          width: 16.8px;
          height: 14px;
        }
      }
      .cta-link-wrong-check {
        .wrong-icon {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
  .cta-broken-link-validation-section {
    position: absolute;
    .cta-link-broken-message {
      font-family: $font-family-arial-serif;
      color: $red-orange;
    }
  }
}
