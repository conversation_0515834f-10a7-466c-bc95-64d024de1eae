.shoppable-review-ingredient {
  position: relative;
  display: flex;

  .shoppable-review-ingredient-keywords {
    display: flex;
    width: 60%;
    padding: 9px 0 0 0;

    .ingredient-global-keyword-container {
      position: relative;
      width: 75%;
      margin-right: 10px;

      .selected-ingredient-keyword {
        position: relative;
        float: left;
        bottom: 10px;
        display: inline-flex;
        align-items: center;
        padding: 12px;
        height: 20px;
        max-width: 25%;
        margin: 8px 0 0 8px;
        border: 1px solid $grainsboro;
        border-radius: 4px;
        background-color: $whisper;

        .tool-tip {
          position: absolute;
          z-index: 9;
          bottom: 35px;
          width: 90%;
          height: auto;
          padding: 5px 10px;
          display: flex;
          text-align: center;
          visibility: hidden;
          opacity: 1;
          border-radius: 8px;
          font-size: $font-size-12;
          font-weight: $font-weight-semi-bold;
          color: $feather-gray;
          background-color: $jet-black;
          word-break: break-all;

          &::after {
            content: "";
            position: absolute;
            top: 20px;
            right: 40%;
            border-width: 10px;
            border-style: solid;
            border-color: $jet-black $transparent $transparent $transparent;
          }
        }

        .ingredient-keyword-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 95%;
        }

        .ingredient-keyword-remove {
          .remove-ingredient-keyword-image {
            position: relative;
            width: 8px;
            height: 8px;
            margin-left: 2px;
            cursor: pointer;

            &.remove-disabled {
              cursor: default;
              pointer-events: none;
            }

            &.remove-close-icon-disabled {
              visibility: hidden;
            }
          }
        }
      }
    }

    .keywords {
      position: relative;
      bottom: 4px;
      min-width: 330px;
      height: 26px;
      padding: 0 0 0 9px;
      border: none;
      background: $transparent;
      font-size: $font-size-14;
      font-weight: $font-weight-normal;
      color: $black;

      ::placeholder {
        color: $gunmetal-grey;
        font-style: italic;
        font-weight: $font-weight-bold;
        font-size: $font-size-14;
      }
    }

    .keywordsPosition {
      bottom: 13px;
    }

    .ingredient-keyword-container {
      width: 75%;
      margin-left: 67px;

      .selected-ingredient-keyword {
        position: relative;
        top: -12px;
        display: inline-flex;
        align-items: center;
        max-width: 25%;
        height: 19px;
        padding: 12px;
        margin-left: 8px;
        margin-top: 8px;
        border: 1px solid $grainsboro;
        border-radius: 4px;
        background-color: $whisper;

        .ingredient-keyword-name {
          max-width: 95%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .ingredient-keyword-remove {
          .remove-ingredient-keyword-image {
            position: relative;
            right: -6px;
            width: 8px;
            height: 8px;
            margin-left: 2px;
            cursor: pointer;

            &.remove-disabled {
              cursor: default;
              pointer-events: none;
            }

            &.remove-close-icon-disabled {
              visibility: hidden;
            }
          }
        }
      }

      .over-ride {
        width: auto;
        margin-right: 37px;
        color: $green;
        cursor: pointer;
        text-align: left;
        text-transform: uppercase;
        font-size: $font-size-14;
        font-weight: $font-weight-bold;
      }
    }

    .head-container-breadcrumb {
      position: relative;

      .main-container-breadcrumb {
        display: flex;
        flex-wrap: wrap;
        max-width: 524px;
        width: auto;
        text-overflow: ellipsis;

        .breadcrumb {
          position: relative;
          top: -11px;
          display: flex;
          align-items: center;
          width: max-content;
          height: 19px;
          padding: 12px 5px;
          margin-left: 8px;
          margin-top: 8px;
          border: 1px solid $grainsboro;
          border-radius: 4px;
          box-shadow: 0 1px 0 0 RGB(218, 218, 218);
          background-color: RGB(235, 235, 235);
          color: $black;
          font-size: $font-size-base;
          line-height: 1.3;
          text-overflow: ellipsis;

          .tool-tip-text {
            position: absolute;
            bottom: 29px;
            left: -4px;
            z-index: 1;
            width: 100%;
            height: fit-content;
            padding: 5px 10px;
            border-radius: 8px;
            visibility: hidden;
            background-color: $jet-black;
            color: $white;
            text-align: left;
            word-break: break-word;
            font-size: $font-size-12;
            font-weight: $font-weight-light;

            &::after {
              position: absolute;
              bottom: -16px;
              left: 50%;
              border-width: 10px;
              border-style: solid;
              margin-left: -11px;
              content: "";
              border-color: $jet-black $transparent $transparent $transparent;
            }
          }

          &:hover .tool-tip-text {
            visibility: visible;
          }

          .data-name {
            max-width: 108px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .edit-breadcrumb {
          width: auto;
          margin-left: 12px;
          color: $green;
          cursor: pointer;
          text-align: left;
          text-transform: uppercase;
        }
      }
    }
  }

  .ingredient-size {
    position: relative;
    top: 9px;
    width: 14%;
    margin-left: 46px;

    .ingredient-size-text {
      position: absolute;
    }

    .ingredient-add-size-container {
      margin-left: 34px;
      color: $green-light;
      line-height: 18px;
      font-style: normal;

      span {
        cursor: pointer;
      }
    }
  }

  .ingredient-numeric-size {
    position: relative;
    top: 5px;
    display: flex;
    width: 16%;
    margin-left: 46px;
    margin-right: 10px;

    .ingredient-numeric-heading {
      margin-top: 4px;
      margin-right: 11px;
    }

    .numeric-text {
      display: flex;
      height: 25px;
      padding: 3px 7px;
      border-radius: 4px;
      max-width: 139px;
      box-shadow: 0 1px 5px 0 $shadow-black;
      background-color: $grainsboro;
      overflow: hidden;
      text-overflow: ellipsis;

      .ing-count {
        max-width: 85px;
        margin-right: 3px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .close-icon {
      width: 14px;
      height: 14px;
      margin-left: 8px;
      cursor: pointer;
      img {
        width: 65%;
      }
    }
  }
}

.shoppable-admin-weight {
  position: relative;

  .ingredient-size {
    margin-top: 10px;

    .ingredient-size-text {
      position: absolute;
    }

    .ingredient-add-size-container {
      line-height: 18px;
      margin-left: 34px;
      font-style: normal;
      color: $green-light;

      span {
        cursor: pointer;
      }
    }
  }

  .ingredient-numeric-size {
    display: flex;
    justify-content: flex-start;
    width: auto;
    height: auto;
    margin-top: 10px;

    .ingredient-numeric-heading {
      margin-top: 4px;
      margin-right: 10px;
    }

    .numeric-text {
      display: flex;
      max-width: 140px;
      height: 25px;
      padding: 3px 7px;
      border-radius: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      box-shadow: 0px 1px 5px 0px $shadow-black;
      background-color: $grainsboro;

      .ing-count {
        max-width: 85px;
        margin-right: 3px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .close-icon {
      width: 11px;
      height: 10px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
}