.ai-generated-main-section,
.recipe-ai-generated-main-section {
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 4px;
  background-color: $spring-bud;
}

.recipe-ai-generated-main-section {
  position: absolute;
  bottom: 54px;
  display: flex;
  padding: 4px 10px;
  background-color: $spring-bud;
  border-radius: 4px;

  .ai-generated-container {
    display: flex;
    align-items: center;

    .ai-generated-text {
      margin-left: 6px;
      font-family: $font-family-averta;
      font-weight: $font-weight-semi-bold;
      font-size: $font-size-10;
      line-height: $line-height-16;
      color: $graphite-gray;
    }

    .ai-generated-image {
      width: 13px;
      height: 13px;
    }
  }
}

.ai-generated-main-section {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 34px;
  padding: 9px 15px;
  border-radius: 4px;
  background-color: $spring-bud;

  .ai-generated-text {
    margin-left: 6px;
    font-family: $font-family-averta;
    font-weight: $font-weight-semi-bold;
    font-size: $font-size-14;
    line-height: $line-height-16;
    color: $graphite-gray;
  }

  .ai-generated-icon {
    width: 14px;
    height: 14px;
  }
}

.batch-generated-icon-height {
  height: 36px;
}

.recipe-type-badge-is-new-format {
  position: relative;
  bottom: initial;
  width: fit-content;
}
