.image-selected-container {
  position: relative;
  display: inline-block;

  .hover-overlay {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    inset: 2px;
    background-color: $transparent-green;
    transition: opacity 0.3s ease;
    border-radius: $border-radius;

      &.hovered {
        opacity: 1;
      }
  }

  .miniature-image {
    width: 100%;
    height: auto;
  }

  .check-image {
    width: 16px;
    height: 16px;
  }
}
