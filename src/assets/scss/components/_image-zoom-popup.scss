.iq-r-g-slider {
  &-dialog {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;

    &-content {
      position: relative;
      width: auto;
      min-width: 300px;
      height: calc(100dvh - 200px);
      background-color: $white;
      border-radius: $border-radius;
      border: 6px solid $white;

      &-close {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 24px;
        height: 24px;

        > img {
          width: 100%;
          height: 100%;
        }
      }

      > img {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: $border-radius;
        object-fit: contain;
        aspect-ratio: auto;
      }
    }
    button {
      &:disabled {
        opacity: 0.6;
        box-shadow: none;
        pointer-events: none;
      }
    }
  }
}

