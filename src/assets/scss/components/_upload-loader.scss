.circular-loader {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circular-progress {
  width: 100%;
  height: 100%;
}

.circle-bg {
  fill: none;
  stroke: $white;
  stroke-width: 3.8;
}

.circle {
  fill: none;
  stroke: $translucent-black;
  stroke-width: 2.8;
  stroke-linecap: round;
  transition: stroke-dasharray 0.4s ease;
}

.percentage {
  fill: $translucent-black;
  font-family: Arial, sans-serif;
  font-size: 0.5em;
  text-anchor: middle;
}

.progress-loader-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  height: 279px;
  width: 279px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.7);
}

.upload-loader-context {
  margin-top: 25px;
  color: $white;
}