.recipe-video-main-container {
    font-family: $font-family-averta;

    .video-inner-container,
    img,
    video {
        height: 279px;
        width: 279px;
        border-radius: $border-radius;
    }

    .empty-video-section {
        position: absolute;
    }

    .video-section {
        position: relative;

        .video-top-section {
            position: absolute;
            padding: 4px;
            width: 100%;
            display: flex;
            justify-content: flex-end;

            .delete-video-button {
                z-index: 9;

                .delete-button-image {
                    height: 32px;
                    width: 32px;
                    filter: invert(1);
                }
            }
        }

        .video-player {
            position: absolute;
            top: 50%;
            width: 100%;
            display: flex;
            justify-content: center;
            z-index: 1;

            img {
                height: 36px;
                width: 36px;
            }

        }

        .recipe-video {
            border-radius: $border-radius;
            object-fit: cover;
        }

        .video-bottom-section {
            position: absolute;
            top: 225px;
            left: 0;
            text-align: left;
            height: 54px;
            width: 100%;
            border-radius: 8px;
            background-image: linear-gradient($veiled-gray,
                    $veiled-gray );

            .context {
                color: $white;
                font-size: $font-size-12;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .upper-info-text {
                padding: 8px;
            }

            .lower-info-text {
                padding: 0 8px;
            }

        }
    }
}