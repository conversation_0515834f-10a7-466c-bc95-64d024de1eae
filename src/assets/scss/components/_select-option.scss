.select-option,
.select-option-dropdown {
  input {
    background-color: transparent;
    border: none;
    box-shadow: none;
    outline: none;
  }
}

.select-option {

  &-container {
    position: relative;
    min-height: 44px;
    padding: 12px 44px 12px 19px;
    background-color: $white;
    box-shadow: 0px 1px 5px 0px $box-shadow;
  }

  &-btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    right: 18px;
    width: 24px;
    height: 24px;
    transform: translateY(-50%);

    img {
      width: auto;
      height: 15px;
      transform: rotateZ(90deg);
      transform-origin: center;
    }
  }

  &-open &-btn-icon img {
    transform: rotateZ(-90deg);
  }

  &-dropdown {
    height: auto;
    max-height: calc(44px + 276px + 60px); // 380px
    background-color: $white;
    border: 1px solid $grainsboro;
    border-radius: 4px;
    box-shadow: 0px 1px 5px 0px $box-shadow;


    &-search {
      width: 100%;
      height: 44px;
      padding: 12px 19px;
    }

    &-list {
      overflow-y: scroll;
      max-height: 276px;
      padding: 8px 0;

      &::-webkit-scrollbar {
        width: 12px;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: $whispering-white-smoke;
      }

      &::-webkit-scrollbar-thumb {
        background: $grainsboro;
        border: 3px solid $transparent;
        border-radius: 15px;
        background-clip: content-box;
      }
    }

    &-confirm {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
    }
  }

  &-option {
    max-height: 48px;
    padding: 11px 16px;

    &:hover,
    &-selected,
    &-focused {
      background-color: $aqua-spring;
    }

    &-add {
      display: inline-flex;
      align-items: center;

      &-icon {
        width: 24px;
        margin: -4px 8px 0 0;
        text-align: center;
      }
    }
  }
}


