.recent-activity-list-container {
  display: flex;
  flex-wrap: wrap;
  height: auto;
  gap: 35px;
  margin-top: 26px;

  .recent-activity-list-main {
    position: relative;
    z-index: 0;
    width: 167px;
    height: 160px;
    background-color: $white;
    border-radius: $border-radius;

    .btn-container {
      position: absolute;
      right: 0;
      z-index: 1;
      width: fit-content;

      .delete-icon {
        width: 28px;
        height: 32px;
        background: $semi-gray;
        border-top-right-radius: 8px;
        visibility: hidden;
      }

      .delete-flag {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 84px;
        height: 32px;
        padding: 0 10px;
        background-color: $gunmetal-grey;
        color: $white;
        border-top-right-radius: $border-radius;
        font-size: $font-size-14;
        font-weight: $font-weight-bold;

        span {
          text-decoration: underline;
        }
      }
    }
    .btn-container:hover {
      cursor: pointer;
    }

    .recent-activity-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      height: 100%;
      padding: 10px;
      position: relative;

      .recent-activity {
        position: absolute;
        left: 10px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        width: 100%;
      }

      .recent-activity-image {
        width: 18px;
        height: 18px;
        margin-right: auto;
      }

      .total-recipe-count {
        font-size: $font-size-28;
        font-weight: $font-weight-normal;
        color: $green;
        margin-top: 26px;
      }

      .line {
        width: 67px;
        height: 1px;
        margin: 10px 50px;
        background-color: $spanish-gray-silver;
      }

      .recipe-image {
        width: 66px;
        height: 66px;
        margin: 13px 0 5px;
        border-radius: $border-radius;
      }

      .recent-activity-recipe {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: $font-size-14;
        font-weight: $font-weight-normal;
        color: $graphite-gray;
        line-height: 1.4em;
        height: 2.8em;
        padding: 0 5px;
      }

      .recent-activity-time {
        font-size: $font-size-12;
        color: $grey;
        font-weight: $font-weight-normal;
        margin-top: 2px;
      }

      .recent-activity-in-progress {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 27px;
        background-color: $green-light;
        border-bottom-right-radius: $border-radius;
        border-bottom-left-radius: $border-radius;
        position: absolute;
        bottom: 0;
        font-size: $font-size-12;
        font-weight: $font-weight-normal;
        color: $white;
      }
    }
  }

  .recent-activity-list-main:hover {
    .delete-icon {
      visibility: visible;
    }
  }
}
