.profile-menu {

  &-button {
    width: 37px;
    height: 37px;
    background-color: lightgreen;
    border-radius: 50px;
    text-transform: uppercase;
  }

  &-dropdown {
    width: 300px;
    margin: 5px 0 0;
    padding: 4px;
    background-color: $white;
    border: 1px solid $grainsboro;
    border-radius: 4px;
    box-shadow: 0 1px 10px 0 $box-shadow;

    hr {
      width: 100%;
      height: 1px;
      margin: 2px 0;
      background-color: $subtle-whisper-grey;
      border: none;
    }

    &-item {
      margin: 2px 0;
      padding: 8px 6px;
      text-align: left;
      border-radius: 4px;

      img {
        width: 24px;
        margin-right: 12px;
      }
    }

    &-action {
      &:hover {
        background-color: $green;
        color: $white;
      }
    }
  }

}
