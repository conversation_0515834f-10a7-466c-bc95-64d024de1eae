.header {
  display: grid;
  grid-template-columns: 1fr 230px;
  align-items: center;
  gap: 10px;
  position: fixed;
  width: 100%;
  height: $header-height;
  top: 0;
  left: 0;
  right: 0;
  padding: 16px $header-padding-right 10px $header-padding-left;
  background: $pristine-white;

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 250px;
    gap: 170px;
  }

  &-block-actions {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
  }
}

.modal {
  font-family: $font-family-averta;
  .button-container {
    .create-btn {
      padding: 10px 25px;
      border-radius: 30px;
      color: $white;
      text-transform: uppercase;
      margin: 40px 5px 0px 5px;
      box-shadow: 0 0 13px $box-shadow;
      background: $crimson-blaze;
      border: none;
      font-weight: 700;
      font-size: 14px;
    }

    .cancel-btn {
      padding: 10px 25px;
      border-radius: 30px;
      color: $green;
      text-transform: uppercase;
      margin: 40px 5px 0px 5px;
      box-shadow: 0 0 13px $box-shadow;
      background: $white;
      border: 1px solid $subtle-whisper-grey;
      font-weight: 700;
      font-size: 14px;
    }
  }
}
