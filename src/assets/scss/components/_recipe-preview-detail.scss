.recipe-preview-page::-webkit-scrollbar {
  width: 12px;
  border-radius: 3px;
}

.recipe-preview-page::-webkit-scrollbar-track {
  background: $whispering-white-smoke;
}

.recipe-preview-page::-webkit-scrollbar-thumb {
  background: $grainsboro;
  border: 3px solid $transparent;
  border-radius: 15px;
  background-clip: content-box;
}

.recipe-preview-page {
  overflow-y: scroll;
  height: 72vh;
  width: 101%;
  scrollbar-color: $grainsboro $whispering-white-smoke;
  scrollbar-width: thin;
  font-family: $font-family-averta;

  @media screen and (min-width: 1600px) {
    width: 100.5%;
  }

  .outer-section {
    .recipe-preview-loading-main {
      display: flex;
      text-align: center;
      justify-content: center;
      align-items: center;
      width: 100%;
      min-height: 100%;
      padding: 0 20px;
      margin-top: 150px;

      .input-loading {
        height: 60px;
        display: flex;
        justify-content: center;

        .loader-image {
          border: 3px solid $white;
          border-radius: 50%;
          border-top: 3px solid $green;
          border-right: 3px solid $green;
          border-bottom: 3px solid $green;
          width: 20px;
          height: 20px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }
      }

      .loading-text {
        background-color: $green-peppermint;
        border-radius: 4px;
        border: 1px solid $green-fringy-flower;
        width: 468px;
        height: 57px;

        p {
          font-weight: 400;
          font-size: 16px;
          color: $shadow-gray;
          padding: 18px 0px;
        }
      }
    }

    .main-section {
      display: flex;
      flex-direction: column;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      background-color: $white;
      margin: 0 20px;
      z-index: -1;

      .recipe-details-video-image-container {
        display: flex;
        flex-direction: column;
        margin-right: 36px;
        margin-top: 24px;

        .recipe-basic-details {
          display: flex;
          justify-content: space-between;
          margin-left: 21px;

          .recipe-video-image {
            display: flex;
            margin-bottom: 70px;
            width: 100%;
          }
        }
      }

      .recipe-steps-at-recipe-preview {
        margin: 8px 25px 28px 21px;
        display: flex;

        .recipe-steps-notes-container {
          width: 68%;
          display: flex;
          flex-direction: column;
          padding-right: 20px;

          .notes-section {
            margin-top: 16px;
            padding: 14px 16px 16px 16px;
            border: 1px solid $grainsboro;
            border-radius: 8px;
            display: flex;
            flex-direction: column;

            .notes-heading {
              font-weight: 700;
              font-size: 20px;
              color: $black;
            }

            .notes-available {
              font-weight: 400;
              font-size: 16px;
              color: $grey;
              word-break: break-word;
            }

            .no-notes-available {
              margin-top: 13px;
              font-weight: 400;
              font-size: 16px;
              color: $grey;
            }
          }
        }

        .ingredients-categories {
          display: flex;
          flex-direction: column;
          width: 32%;

          .recipe-preview-filter-slug-main-section {
            width: 100%;
            border: 1px solid $grainsboro;
            border-radius: 4px;
            background-color: $pearl-mist;
            padding: 16px 20px;
            margin-bottom: 16px;

          }
        }
      }
    }
  }
}