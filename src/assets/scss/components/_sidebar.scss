.disabled {
  pointer-events: none;
  opacity: 0.6;
}

.sidebar-logo a {
  text-decoration: none;
}

.sidebar-section {
  position: fixed;
  z-index: 99999;
  left: 0;
  top: 0;
  width: 240px;
  height: 90vh;
}

.sidebar-container {
  height: 80vh;
  margin-top: 10px;
  margin-bottom: 20px;
  padding: 30px 0;
  background-color: $green;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  transition: width 0.5s;
  overflow: auto;
  font-family: $font-family-averta;
  scrollbar-color: $grainsboro $whispering-white-smoke;
  scrollbar-width: none;
}

.sidebar-container::-webkit-scrollbar {
  width: 0px;
}

.sidebar-container::-webkit-scrollbar-thumb {
  background: $grainsboro;
  border-radius: 3px;
}

.sidebar-container::-webkit-scrollbar-track {
  background-color: $bright-white-smoke;
}

.sidebar-menu {
  &-item {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-item-divider {
    padding-top: 10px;
    padding-bottom: 10px;

    hr {
      opacity: .3;
      width: 100%;
      border: none;
      border-bottom: 1px solid $white;
    }
  }

  &-item-link {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    width: 84%;
    height: 42px;
    padding-left: 30px;
    border-top-right-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
    border: none;
    background-color: transparent;
    text-decoration: none;
    box-shadow: none;

    &:hover,
    &.__active {
      background-color: $emerald-green;
    }
  }
}
