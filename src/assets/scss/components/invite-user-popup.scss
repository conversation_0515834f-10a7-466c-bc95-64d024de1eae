.invite-popup {
  &-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
    width: 500px;
    height: 210px;
  }

  &-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  &-input-box {
    margin-bottom: 10px;
    border: 1px solid $grainsboro-gray;
    background: $pearl-mist;
    border-radius: 4px;
  }

  &-input-email {
    width: 100%;
    padding: 10px 12px;
    border: none;

    &[disabled],
    &:disabled {
      opacity: 0.5;
    }
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
  }
}
