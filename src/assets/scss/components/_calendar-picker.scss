.dp__input_icon {
  width: 100% !important;
  color: $jet-black !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}
.input-slot-image {
  float: right !important;
  margin-right: 18px !important;
  width: 16px !important;
  height: 16px !important;
  margin-left: 8px !important;
}
.dp__active_date {
  color: $black !important;
  border-radius: 3px !important;
  border: 1px solid $islamic-green !important;
  background: none !important;
}
.dp__cell_offset {
  display: none !important;
}
.dp__calendar_item {
  min-width: 35px !important;
}
.dp__today {
  border: none !important;
}
.dp__arrow_bottom {
  display: none !important;
}
.dp__arrow_top {
  display: none !important;
}
.dp__inner_nav {
  border-radius: 4px !important;
}
.dp__month_year_wrap {
  font-weight: 600 !important;
}
.dp__cell_inner {
  font-size: 14px !important;
}
.dp__calendar_header_item {
  height: auto !important;
  font-size: 12px !important;
  color: $slate-gray !important;
  font-weight: 400 !important;
}
.dp__cell_disabled {
  cursor: default !important;
}
.dp__main * {
  font-family: $font-family-averta !important;
}
.dp__tooltip_mark {
  display: none !important;
}

.dp__marker_tooltip,
.dp__arrow_bottom_tp {
  background-color: $black !important;
}
.dp__tooltip_text {
  color: $white !important;
}
.dp__range_start,
.dp__range_end {
  color: $black !important;
  border-radius: 3px !important;
  border: 1px solid $islamic-green !important;
  background: none !important;
}

.dp__input {
  visibility: hidden !important;
}

.dp__input_wrap {
  width: 264px !important;
  border-radius: 4px;
  box-shadow: 0 0 4px 1px $box-shadow;
}
.select-date-text span {
  font-weight: 400 !important;
  font-size: 16px !important;
}
.custom-datepicker-range .dp__outer_menu_wrap.dp--menu-wrapper {
  left: 0 !important;
}
.dp__outer_menu_wrap.dp--menu-wrapper {
  left: 0 !important;
}

.custom-datepicker-range .dp__menu_inner {
  padding: 0 76px !important;
}
.hero-datepicker-popup .dp__menu.dp__menu_index.dp__theme_light,
.banner-datepicker-popup .dp__menu.dp__menu_index.dp__theme_light {
  position: fixed !important;
}

.range-mode {
  width: 400px !important;
}
.range-mode .dp__input_wrap {
  width: 100% !important;
}
.date-selection-container {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-around;
}
.selected-dates,
.select-date-text {
  display: flex;
  align-items: center;
  padding: 5px;
}
.banner-live {
  cursor: default;
  opacity: 0.4;
}

.vertical-line {
  display: inline-block;
  height: 28px;
  border-left: 2px solid $light-white;
}

.selected-start-date,
.selected-end-date {
  font-weight: bold;
  font-size: 14px;
}
.select-date-text {
  margin-left: 24px;
  min-width: 104px;
}
.date-select-text {
  position: relative;
  right: 8px;
  font-weight: 700;
  width: 42px;
}
.enter-date-text {
  display: flex;
}
.asterisk-input {
  width: 6px;
  margin-bottom: 12px;
}
.selected-dates-container {
  display: flex;
  padding: 8px 14px;
  align-items: center;
}
.recipe-details-range-calendar {
  width: 100% !important;
}
.recipe-details-range-calendar .dp__input_icon {
  position: relative;
  top: 0;
  inset-inline-start: 0;
  transform: translateY(0);
}

.recipe-details-range-calendar .dp__input {
  display: none;
}
.recipe-details-date-selection-container {
  width: 100%;
}
.recipe-details-range-calendar .selected-dates-container {
  border-bottom: 1px solid $grainsboro;
  justify-content: space-between;
}
.date-value-select {
  display: flex;
  align-items: center;
}
.recipe-details-range-calendar .dp__menu_inner {
  padding: 0 22px !important;
}
.range-mode .dp__range_between {
  background: $green-peppermint !important;
  border: none !important;
}
.range-mode .dp__date_hover,
.range-mode .dp__range_end,
.range-mode .dp__range_start {
  background: $vivid-green !important;
  color: $white !important;
}
.publish-toggle-section-start {
  position: relative;
  pointer-events: visible;
  .calendar-icon-start {
    top: -28px;
    left: 173px;
  }
  .publish-hover-text-start {
    position: absolute;
    bottom: 28px;
    right: -51px;
    width: 311px;
    height: 91px;
    visibility: hidden;
    background-color: $jet-black;
    color: $white;
    border-radius: $border-radius;
    padding: 7px 9px;
    z-index: 1;
    font-family: $font-family-averta;
    font-size: $font-size-12;
    font-weight: $font-weight-light;
    text-align: center;

    &::after {
      content: "";
      position: absolute;
      top: 91px;
      right: 49px;
      border-width: 10px;
      border-style: solid;
      border-color: $jet-black $transparent $transparent $transparent;
    }
  }

  &:hover .publish-hover-text-start {
    visibility: visible;
  }
  .tool-tip-text {
    line-height: $line-height-18;
    font-family: $font-family-averta;
    font-weight: $font-weight-normal;
    font-size: $font-size-12;
    opacity: 100%;
  }
  button {
    text-transform: none;
    color: $black;
    width: 99px;
    border-radius: 25px;
    height: 20px;
    border: none;
    font-weight: $font-weight-normal;
    font-family: $font-family-averta;
    font-size: $font-size-12;
    background-color: $white;
  }
}
.dp__cell_inner.dp__cell_disabled.dp__date_hover {
  pointer-events: none !important;
  background: none !important;
  color: $silver-sand !important;
}
.dp__cell_inner.dp__cell_disabled.dp__range_between {
  pointer-events: none !important;
  background: none !important;
  color: $silver-sand !important;
}
