.image-box {
  position: relative;
  width: 120px;
  height: 120px;
  background-image: url("@/assets/images/upload-image-category.png");
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 4px;
  overflow: hidden;

  &-image,
  &-progress,
  &-hover,
  &-upload-input {
    width: 100%;
    height: 100%;
  }

  &-image,
  &-hover,
  &-progress {
    position: absolute;
    top: 0;
    left: 0;
  }

  &-hover,
  &-upload-input {
    opacity: 0;
  }

  &-image {
    object-fit: cover;
  }

  &-progress {
    padding: 15px 0 0;
    text-align: center;
    background-color: $jet-black;
    border-radius: 4px;

    &-text {
      margin-top: 10px;
      line-height: $line-height-18;
    }
  }

  &-hover {
    &:hover {
      opacity: 1;
      background-image: url("@/assets/images/edit-image.png");
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
      background-color: $translucent-black;
      border-radius: 4px;
    }
  }
}
