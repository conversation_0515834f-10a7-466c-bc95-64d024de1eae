.recipe-servings-description {
  .servings-time-section {
    display: flex;
    justify-content: space-between;
    font-size: $font-size-14;

    .servings-section {
      display: flex;
      align-items: center;

      .compulsory-field-available {
        position: relative;
        color: $red;
      }

      .main-title {
        width: 66px;
        font-weight: $font-weight-semi-bold;
        color: $black;
      }

      .yield-title {
        margin-top: 10px;
      }

      .input-section {
        width: 32px;
        height: 42px;
        resize: none;
        border-radius: 4px;
        margin-left: 20px;
        border: 1px solid $grainsboro;
        text-align: center;
        background: $pearl-mist;
        color: $black;
      }

      .yield-input,
      .price-input {
        text-align: left;
        padding: 10px;
        margin-top: 10px;
        border: 1px solid $grainsboro;
        border-radius: 4px;
        background: $pearl-mist;
        color: $black;

        &.yield-input {
          width: 94px;
        }

        &.price-input {
          width: auto;
        }
      }

      .currency-input-section {
        position: relative;

        .currency-dropdown {
          display: flex;
          width: 114px;
          align-items: center;
          margin-top: 10px;
          padding: 10px;
          background: $white;
          cursor: pointer;
          text-align: left;
        }

        .currency-text {
          line-height: 24px;
          font-family: $font-family-arial-serif;
          color: $jet-black;
        }

        .currency-dropdown-icon {
          position: absolute;
          width: 8px;
          height: 12px;
          right: 28px;
          transform: rotate(90deg);
          cursor: pointer;
        }

        .currency-autocomplete-results {
          position: absolute;
          z-index: 1;
          width: 112px;
          max-height: 185px;
          top: 62px;
          list-style: none;
          box-shadow: 0 1px 10px 0 $box-shadow;
          background: $white;
          border-radius: 4px;
          margin-left: 22px;
          padding: 2px;
          color: $charcoal-light;

          .currency-autocomplete-result {
            color: $black;
            cursor: pointer;
            margin: 2px;
            display: flex;
            border-radius: 4px;
            padding: 14px 6px;

            &.is-active,
            &:hover {
              background: $green;
              color: $white;
            }

            .currency-result-name {
              word-break: break-all;
            }
          }
        }
      }
    }

    .time-section {
      margin-top: 10px;
      align-items: flex-start;

      .time-data {
        display: block;

        .time-hr-min {
          display: flex;
          align-items: center;

          .servings-value {
            margin-left: 5px;
          }

          .time-input {
            width: 48px;
            margin-right: 10px;
          }
        }

        .time-sec {
          display: block;
          margin-top: 10px;

          .second-label {
            position: relative;
            left: -4px;
          }

          .time-input {
            width: 80px;
            height: 50px;
            resize: none;
            background: $pearl-mist;
            border-radius: 4px;
            padding: 10px;
            margin-left: 20px;
            border: 1px solid $grainsboro;
            text-align: center;
            margin-right: 10px;
            color: $black;
          }
        }
      }
    }
  }

  .description-section {

    .description {
      width: 100%;
      height: 131px;
      resize: none;
      border-radius: 4px;
      padding: 10px;
      margin: 5px 0;
      border: 1px solid $ethereal-whisper-gray;
      background: $pristine-white;
      color: $charcoal-light;
      overflow-y: scroll;
      scrollbar-color: $grainsboro $whispering-white-smoke;
      scrollbar-width: thin;
      &::-webkit-scrollbar {
        display: block;
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: $whispering-white-smoke;
      }
      &::-webkit-scrollbar-thumb {
        background: $grainsboro;
        background-clip: content-box;
      }
    }
  }
}