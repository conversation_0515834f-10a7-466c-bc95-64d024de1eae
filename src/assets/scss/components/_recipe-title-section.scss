.recipe-header-section {
  padding: 25px;
  background-color: transparent;
  font-family: $font-family-averta;

  .button-section-container {
    display: flex;
    justify-content: space-between;

    .back-button-section {
      display: flex;
      align-items: center;
      gap: 10px;
      width: max-content;
      cursor: pointer;

      .back-button {
        display: flex;
        align-items: center;
        margin: 0px 4px;
        color: $green;
        font-size: $font-size-base;
        font-weight: $font-weight-bold;

        img {
          width: 14px;
          height: 14px;
          transform: rotate(90deg);
          margin-right: 8px;
        }
      }
    }

    .button-section {
      display: flex;
      gap: 10px;
    }
  }
}

.recipe-title-form-section {
  float: left;
  width: 100%;

  .ai-generated-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;

    .recipe-detail-label {
      font-size: $font-size-24;
      font-weight: bold;
      color: $green-dark;
    }
    .recipe-detail-label-section {
      display: flex;
      align-items: center;
      gap: 10px;
      .recipe-variant-flag-section {
        margin-bottom: 3px;
        .recipe-flag {
          max-width: 32px;
          max-height: 30px;
        }
      }
    }
  }

  .recipe-main-section {
    .recipe-title-section {
      background: $white;
      min-height: 200px;
      border-radius: 8px;

      .label {
        margin-bottom: 10px;
        font-size: $font-size-14;
        font-weight: $font-weight-semi-bold;
        color: $graphite-gray;

        .astrisk {
          margin-left: 5px;
          color: $red;
        }
      }

      .input {
        width: 100%;
        margin-bottom: 20px;
        padding: 10px;
        height: 50px;
        background: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 4px;
        color: $charcoal-gray;
        font-size: $font-size-20;
        font-weight: $font-weight-semi-bold;
      }

      .subtitle {
        font-size: $font-size-base;
        font-weight: $font-weight-normal;
      }
    }
  }
}
