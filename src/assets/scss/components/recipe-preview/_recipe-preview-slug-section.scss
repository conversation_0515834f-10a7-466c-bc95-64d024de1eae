.slug-sections {
  .slug-available {
    margin-top: 14px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    border-radius: 4px;
    border: 1px solid $grainsboro;
    padding: 10px;
    background-color: $white;

    .slug-container-for-preview {
      margin: 9px;

      .slug-title {
        font-weight: $font-weight-normal;
        font-size: $font-size-14;
        color: $black;
        word-break: break-all;
      }
    }
  }

  .no-slug-available {
    margin-top: 13px;
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}

.author-sections {
  .author-heading {
    margin-top: 12px;
  }

  .author-available {
    margin-top: 14px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    border-radius: 4px;
    border: 1px solid $grainsboro;
    background-color: $white;

    .author-container-for-preview {
      margin: 9px;

      .author-title {
        font-weight: $font-weight-normal;
        font-size: $font-size-14;
        color: $black;
        word-break: break-all;
      }
    }
  }

  .no-author-available {
    margin-top: 13px;
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}

.publisher-sections {
  .publisher-heading {
    margin-top: 12px;
  }

  .publisher-available {
    margin-top: 14px;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    border-radius: 4px;
    border: 1px solid $grainsboro;
    background-color: $white;

    .publisher-container-for-preview {
      margin: 9px;
      word-break: break-all;

      .publisher-data {
        display: flex;

        img {
          height: 30px;
          width: 30px;
          border-radius: 4px;
          object-fit: contain;
          align-self: center;
        }

        p {
          margin-left: 12px;
          margin-top: 5px;
          font-weight: $font-weight-normal;
          font-size: $font-size-base;
          color: $black;
        }
      }
    }
  }

  .no-publisher-available {
    margin-top: 13px;
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}