.recipe-preview-variant-main-container {
    display: flex;
    align-items: center;
    gap: 20px;
    height: 66px;
    margin: 12px 0;

    .recipe-preview-variant-title {
        color: $spanish-gray;
        line-height: 1;
        text-transform: uppercase;
    }

    .recipe-preview-variant-dropdown {
        position: relative;
        width: 246px;
        height: 44px;
        cursor: pointer;

        .recipe-preview-variant-selected {
            position: relative;
            width: 100%;
            height: 100%;
            margin-left: 4px;
            border-radius: 4px;
            box-shadow: 0 1px 5px 0 $box-shadow;
            background-color: $white;
            line-height: 1.25;
            cursor: pointer;

            .recipe-preview-variant-flag-image {
                position: absolute;
                left: 23px;
                margin-right: 6px;

                img {
                    width: 24px;
                    height: 24px;
                }
            }

            .dropdown-image {
                position: absolute;
                right: 23px;
                cursor: pointer;

                .recipe-variant-dropdown-icon {
                    transform: rotate(90deg);
                }

                .recipe-variant-dropdown-icon-open {
                    transform: rotate(270deg);
                }

                img {
                    width: 6px;
                    height: 12px;
                }
            }

            .default-mark {
                margin-left: 3px;
            }
        }

        .dropdown-list {
            position: absolute;
            top: 55px;
            width: 246px;
            padding-bottom: 4px;
            margin-left: 4px;
            background-color: $white;
            box-shadow: 0 1px 5px 0 $box-shadow;
            border-radius: 4px;
            line-height: 1.25;
            z-index: 2;

            .dropdown-list-item {
                display: flex;
                width: 100%;
                height: 42px;
                padding-left: 17px;
                padding-top: 11px;
                margin: 2px 0 1px 0;
                background-color: $white;
                border-radius: 4px;
                line-height: 1.25;
                cursor: pointer;

                .recipe-preview-variant-flag-image {
                    margin-right: 4px;
                    width: 30px;
                    height: 30px;
                }

                .default-mark {
                    margin-left: 3px;
                }

                &:hover {
                    background-color: $green-light;
                    color: $white;
                }
            }
        }
    }
}
