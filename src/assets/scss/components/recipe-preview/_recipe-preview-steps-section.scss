.recipe-preview-steps-main-container {
    .recipe-preview-steps-inner-container {
        display: flex;
        flex-direction: column;
        border: 1px solid $grainsboro;
        border-radius: 8px;

        .recipe-preview-steps-title {
            margin: 16px 0 0 16px;
            color: $jet-black;
            font-weight: $font-weight-bold;
        }

        .recipe-preview-steps-loading-main {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            min-height: 200px;
            padding: 0 20px;
            text-align: center;

            .input-loading {
                display: flex;
                justify-content: center;

                .loader-image {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    border: 3px solid $white;
                    border-top-color: $green;
                    border-right-color: $green;
                    border-bottom-color: $green;
                    animation: spin 2s linear infinite;
                }
            }
        }

        .recipe-preview-steps-content {
            display: flex;
            flex-direction: column;

            .recipe-preview-steps {
                display: flex;
                width: 100%;
                padding-top: 15px;
                border-bottom: 1px solid $grainsboro;

                &:last-child {
                    border-bottom: none;
                }

                .instruction-gallery {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: relative;
                    width: 140px;
                    height: 90px;
                    margin: 8px 14px 17px 15px;
                    border-radius: 4px;
                    background: url("@/assets/images/recipe-detail-upload.png") center/cover no-repeat;
                    overflow: hidden;

                    &.disable {
                        background: none !important;
                    }

                    .instruction-media,
                    .image {
                        height: 100%;
                        object-fit: cover;
                    }

                    .play-video-icon-image {
                        position: absolute;
                        bottom: 8px;
                        left: 8px;
                        width: 28px;
                        height: 28px;
                        z-index: 1;
                        cursor: pointer;
                    }

                    .video-image-counting {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        background-color: $black;

                        .counting {
                            margin-top: 3px;
                            color: $white;
                            text-align: center;
                        }
                    }
                }

                .recipe-counting {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 24px;
                    height: 24px;
                    margin: 0 12px 0 17px;
                    border-radius: 10px;
                    background-color: $light-mint;

                    span {
                        color: $green-light;
                        margin-top: 3px;
                    }
                }

                .recipe-details {
                    display: flex;
                    flex-direction: column;
                    padding-bottom: 17px;

                    .recipe-title {
                        margin-bottom: 5px;
                        color: $black;
                    }

                    .recipe-description {
                        width: 447px;
                        color: $grey;
                    }

                    .recipe-ingredients {
                        display: flex;
                        flex-direction: column;
                        margin-top: 10px;
                        word-break: break-all;

                        ul {
                            list-style: none;

                            li {
                                color: $charcoal-gray;

                                span {
                                    font-weight: $font-weight-normal;
                                    margin-right: 4px;
                                }

                                &:before {
                                    content: "\2022";
                                    display: inline-block;
                                    margin-right: 11px;
                                    width: 4px;
                                    height: 4px;
                                    color: $green-light;
                                }
                            }
                        }
                    }
                }
            }
        }

        .no-recipe-steps-available {
            margin: 13px 0 17px 16px;
            color: $grey;
        }
    }
}