.diets-sections {
  .diets-heading {
    margin-bottom: 13px;
  }

  .diet-available {
    display: flex;
    flex-wrap: wrap;

    .image-and-diet-name {
      display: flex;
      border-radius: 4px;
      border: 1px solid $grainsboro;
      margin: 0 6px 6px 0;
      padding: 5px 4px;
      background-color: $white;
      vertical-align: middle;

      .image img {
        width: 24px;
        height: 24px;
      }

      .diet-item-name {
        margin: auto;
        padding: 0 7px;
        font-weight: $font-weight-normal;
        font-size: $font-size-14;
        color: $black;
      }
    }
  }

  .no-diet-available {
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}

.categories-sections {
  .categories-heading {
    margin: 13px 0;
  }

  .category-available {
    display: flex;
    flex-wrap: wrap;

    .image-and-category-name {
      position: relative;
      margin: 0 6px 6px 0;
      display: flex;
      border-radius: 4px;
      border: 1px solid $grainsboro;
      padding: 5px 4px;
      background-color: $white;
      vertical-align: middle;

      .image {
        display: contents;
      }

      .image img {
        position: absolute;
        align-self: center;
        width: 24px;
        height: 24px;
        border-radius: 5px;
      }

      .categories-item-name {
        margin: auto;
        padding: 4px 7px 4px 32px;
        font-weight: $font-weight-normal;
        font-size: $font-size-14;
        color: $black;
      }
    }
  }

  .no-category-available {
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}

.tags-sections {
  margin-top: 19px;

  .tags-heading {
    padding-bottom: 8px;
  }

  .tag-available {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 8px;

    .tag-container-for-preview {
      margin-top: 6px;
      margin-right: 6px;
      border: 1px solid $grainsboro;
    }

    .tags-title {
      display: flex;
      border-radius: 4px;
      padding: 9px;
      background-color: $white;
      font-weight: $font-weight-normal;
      font-size: $font-size-14;
      color: $black;
    }
  }

  .no-tag-available {
    margin-top: 13px;
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}

.allergens-sections {
  margin-top: 19px;

  .allergens-heading {
    margin-bottom: 13px;
  }

  .allergens-available {
    display: flex;
    flex-wrap: wrap;

    .image-and-allergens-name {
      display: flex;
      border-radius: 4px;
      border: 1px solid $grainsboro;
      margin: 0 6px 6px 0;
      padding: 5px 4px;
      background-color: $white;
      vertical-align: middle;

      .image img {
        width: 24px;
        height: 24px;
      }

      .allergens-item-name {
        margin: auto;
        padding: 0 7px;
        font-weight: $font-weight-normal;
        font-size: $font-size-14;
        color: $black;
      }
    }
  }

  .no-allergens-available {
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}