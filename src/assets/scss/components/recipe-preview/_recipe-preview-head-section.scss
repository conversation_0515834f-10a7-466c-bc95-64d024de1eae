.recipe-preview-head-main-container {
    width: 100%;

    .recipe-preview-head-inner-container {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-left: 24px;
        padding-bottom: 28px;
        border-bottom: 1px solid $grainsboro;

        .recipe-name {
            .recipe-title {
                margin-bottom: 12px;
                font-size: $font-size-28;
                font-weight: $font-weight-bold;
                color: $black;
            }

            .recipe-subtitle {
                font-size: $font-size-20;
                font-weight: $font-weight-normal;
                color: $charcoal-gray;
            }
        }

        .time-container {
            display: flex;
            align-items: baseline;

            .clock {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                width: 92px;
                height: 70px;
                margin-left: 20px;

                img {
                    width: 20px;
                    height: 20px;
                }

                .time-title,
                .time-value,
                .time-value-not-available {
                    padding-top: 5px;
                    text-align: center;
                    color: $charcoal-gray;
                }

                .time-value-not-available {
                    color: $slate-gray;
                }
            }
        }
    }

    .recipe-description {
        margin-top: 12px;
        margin-left: 24px;
        color: $grey;
    }

    .price-section {
        margin-top: 50px;
        margin-left: 24px;

        .price-description {
            color: $charcoal-gray;
        }
    }
}