.recipe-preview-nutrition-main-section {
  width: 100%;
  border: 1px solid $grainsboro;
  border-radius: 4px;
  background-color: $pearl-mist;
  padding: 16px 20px;
  margin: 16px 0px;

  .nutrition-main-section {
    .nutrition-heading-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: $font-weight-bold;
      color: $jet-black;

      .nutrition-heading-text {
        font-size: $font-size-20;
        line-height: 1.5;
      }

      .nutrition-serving-drop-menu {
        position: relative;
        left: 11px;

        .nutrition-toogle-serving-view-container {
          display: flex;

          .nutrition-selection-container {
            position: relative;

            .nutrition-drop-down-container {
              .selected-nutrition {
                text-transform: lowercase;
                font-size: $font-size-base;
                font-weight: $font-weight-normal;
                color: $shadow-gray;
                margin-left: 5px;
              }

              .nutrition-dropdown-icon {
                transform: rotate(90deg);
                width: 8px;
                height: 12px;
                cursor: pointer;
              }
            }

            .box-container {
              display: flex;
              justify-content: space-between;
              align-items: center;
              border: 1px solid $grainsboro;
              background: $white;
              width: 139px;
              padding: 10px;
              border-radius: 4px;
              cursor: pointer;
            }

            .disable-drop-down-box {
              pointer-events: none;
              cursor: default;
            }

            .nutrition-autocomplete-results {
              position: absolute;
              margin-top: 3px;
              width: 140px;
              list-style: none;
              box-shadow: 0 1px 10px 0 $box-shadow;
              border-radius: 4px;
              max-height: 334px;
              background: $white;
              z-index: 1;
              color: $charcoal-light;
            }

            .nutrition-autocomplete-result {
              margin: 2px;
              padding: 8px 20px;
              cursor: pointer;
              border-radius: 4px;
              height: 42px;
              color: $jet-black;

              p {
                position: relative;
                right: 6px;
                top: 2px;
                font-size: $font-size-base;
                font-weight: $font-weight-normal;
                text-transform: lowercase;
              }

              &.is-active,
              &:hover {
                background: $green;
                color: $white;
              }
            }
          }
        }
      }
    }

    .nutrition-content-section {
      margin-top: 10px;

      .nutrition-serving-content-area {
        .nutrition-serving-text {
          display: flex;
          justify-content: space-between;
          width: 100%;
          margin: 6px 0;
          font-size: $font-size-base;
          font-weight: $font-weight-normal;
          line-height: 1.375;
          color: $jet-black;

          .serving-text {
            width: 70%;
          }

          .serving-unit {
            width: 30%;
            word-break: break-all;
            text-align: end;
            display: flex;
            justify-content: flex-end;
          }
        }

        .horizontal-line {
          margin: 5px 0;
          height: 1.5px;
          width: 100%;
          background-color: $jet-black;
        }
      }

      .nutrition-content-row {
        margin-top: 10px;
        padding-bottom: 5px;
        text-align: right;
        display: flex;
        justify-content: space-between;
        width: 100%;
        border-bottom: 1px solid $grainsboro;
        font-size: $font-size-base;
        line-height: 1.375;

        .nutrition-name-heading {
          font-weight: $font-weight-bold;
          width: 50%;
          align-self: center;
          text-align: left;
        }

        .nutrition-sub-name-heading {
          padding-left: 24px;
          font-weight: $font-weight-normal;
          align-self: center;
          text-align: left;
        }

        .nutrition-unit-heading {
          width: 20%;
          word-break: break-all;
          font-weight: $font-weight-normal;
          color: $shadow-gray;
          align-self: center;
          text-align: left;
        }

        .nutrition-percentage-unit-heading {
          width: 20%;
          display: flex;
          justify-content: flex-end;
          align-self: center;

          .nutrition-unit {
            font-weight: $font-weight-bold;
            word-break: break-all;
            align-self: center;
          }
        }
      }
    }
  }

  .nutrition-notes-section {
    margin-top: 10px;
    background-color: $white;
    border-radius: 4px;
    padding: 3px 10px;

    .nutrition-text {
      width: 95%;
      padding-bottom: 10px;
      color: $jet-black;
      line-height: $line-height-16;
    }
  }
}