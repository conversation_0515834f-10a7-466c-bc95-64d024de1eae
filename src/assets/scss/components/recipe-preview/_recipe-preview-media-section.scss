.recipe-preview-media-main-container {
    .media-container {
        position: relative;
        width: 286px;
        height: 200px;
        min-width: 286px;
        min-height: 200px;

        .image-section {
            height: 100%;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .video-section {
            height: 100%;
            .video-inner-section {
                height: 100%;
            }
            video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .video-loader {
                width: 262px !important;
                height: 168px !important;
                object-fit: contain !important;
                mix-blend-mode: hard-light !important;
            }

            .video-icon-play {
                position: absolute;
                top: 151px;
                left: 0px;
                width: 40px;
                height: 40px;
                margin: 1.5px;
                cursor: pointer;
            }
        }

        .link-section {
            width: 100%;

            .link-main-container {
                .image-container {
                    position: relative;
                    width: 100%;
                    height: 200px;
                    border-radius: 4px;
                    overflow: hidden;

                    .image {
                        object-fit: contain;
                        border-radius: 4px;
                    }

                    .text-container {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        display: flex;
                        flex-direction: column;
                        width: 100%;
                        height: 54px;
                        border-radius: 4px;
                        background-image: linear-gradient($dim-charcoal-gray, $dim-charcoal-gray);
                        text-align: left;
                        cursor: pointer;

                        .text {
                            color: $white;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .upper-text-container {
                            padding: 8px;
                            font-size: $font-size-10;
                        }

                        .lower-text-container {
                            padding: 0 8px;
                        }
                    }
                }
            }
        }
    }

    .image-video-toggle-section {
        display: flex;
        justify-content: center;
        margin-top: 30px;

        .toggle {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 8px 24px;
            font-size: $font-size-18;
            font-weight: $font-weight-bold;
            border-radius: 50px;
            box-shadow: 0 1px 2px 0 $box-shadow;
            cursor: pointer;
        }

        .image-toggle {
            width: 125px;
            height: 36px;
            background-color: $green;
            color: $white;
            z-index: 10;
        }

        .video-toggle {
            width: 105px;
            height: 36px;
            color: $slate-gray;
            margin-left: -18px;
            z-index: 5;
        }
    }
}