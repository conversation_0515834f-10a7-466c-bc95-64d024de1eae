.recipe-preview-ingredients-main-section {
  width: 100%;
  padding: 16px 10px 12px 10px;
  border: 1px solid $grainsboro;
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: $pearl-mist;

  .ingredient-top-section {
    display: flex;
    justify-content: space-between;

    .ingredients-heading {
      margin-bottom: 10px;
      padding: 0px 10px;
      font-weight: $font-weight-bold;
      font-size: $font-size-20;
      color: $black;
    }

    .ingredient-serving-container {
      position: relative;
      .ingredients-per-serving {
        margin-top: 4px;
        text-align: end;
        font-weight: $font-weight-normal;
        font-size: $font-size-base;
        color: $black;

        img {
          margin-top: -5px;
          width: 20px;
          height: 19px;
        }

        .no-serving-available {
          color: $slate-gray !important;
        }
      }

      .servings-drop-down {
        position: relative;
        bottom: 8px;
        left: 0px;
        cursor: pointer;
        display: flex;
        width: 170px;
        height: 44px;
        padding: 10px;
        border: 1px solid $grainsboro;
        background: $white;

        ::-webkit-scrollbar {
          width: 12px;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-track {
          background: $whispering-white-smoke;
        }

        ::-webkit-scrollbar-thumb {
          border-radius: 15px;
          background: $grainsboro;
          border: 3px solid $transparent;
          background-clip: content-box;
        }

        .servings-container {
          display: flex;

          .serving-spoon-image {
            width: 20px;
            height: 19px;
            margin-top: 0px;
          }

          .serving-count {
            margin: 0px 5px;
            font-weight: $font-weight-normal;
            font-size: $font-size-base;
            color: $jet-black;
          }

          .servings-dropdown-icon {
            position: absolute;
            top: 15px;
            right: 20px;
            transform: rotate(90deg);
            cursor: pointer;
            width: 8px;
            height: 12px;
          }
        }

        .disable-dropdown {
          pointer-events: none;
        }
      }

      .disable-serving-dropdown {
        pointer-events: none;
        opacity: 0.5;
      }

      .autocomplete-results {
        position: absolute;
        right: -1px;
        width: 170px;
        list-style: none;
        box-shadow: 0 1px 10px 0 $box-shadow;
        border-radius: 4px;
        max-height: 134px;
        overflow-y: scroll;
        background: $white;
        z-index: 1;
        color: $charcoal-light;
        scrollbar-width: none;
      }

      .autocomplete-result {
        margin: 2px;
        cursor: pointer;
        border-radius: 4px;
        height: 42px;
        color: $jet-black;

        p {
          position: relative;
          right: 0px;
          top: 2px;
        }

        &.is-active,
        &:hover {
          background: $green;
          color: $white;
        }
      }

      .selected-servings {
        margin: 2px;
        pointer-events: none;
        border-radius: 4px;
        height: 42px;
        background: $green;
        color: $white;

        p {
          position: relative;
          right: 0px;
          top: 2px;
        }
      }

      .serving-button {
        width: 100%;
        padding: 8px 20px;
      }
    }
  }

  .recipe-preview-ingredients-loading-main {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 200px;
    padding: 0 20px;

    .input-loading {
      display: flex;
      justify-content: center;

      .loader-image {
        border-radius: 50%;
        width: 20px;
        height: 20px;
        border: 3px solid $white;
        border-top: 3px solid $green;
        border-right: 3px solid $green;
        border-bottom: 3px solid $green;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }
    }
  }

  .ingredients-available {
    list-style: none;

    .ingredients-available-group {
      border: 1px solid $soft-sliver-grey;
      border-radius: 5px;
      margin-bottom: 6px;

      .ingredients-group {
        background-color: $gentle-gainsboro-gray;
        font-size: $font-size-base;
        line-height: 26px;
        color: $jet-black;

        .name-span {
          padding: 0px 10px;
        }
      }

      .ingredients-space {
        padding: 0px 10px;

        .ingredients-and-value {
          position: relative;
          padding: 5px 0px;
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid $grainsboro;

          .circle-pointer {
            position: absolute;
            left: 0px;
            top: 50%;
            width: 4px;
            height: 4px;
            border-radius: 2px;
            background-color: $green-light;
          }

          .ingredients-main {
            display: flex;
            align-items: center;
            width: 50%;

            .ingredients {
              margin-left: 13px;
              font-weight: $font-weight-bold;
              font-size: $font-size-base;
              color: $black;
              word-break: break-word;

              .ingredients-note {
                font-weight: $font-weight-normal;
                font-size: $font-size-14;
                color: $shadow-gray;
              }
            }
          }

          .value {
            display: flex;
            justify-content: center;
            align-items: center;
            text-transform: capitalize;
            font-weight: $font-weight-normal;
            font-size: $font-size-base;
            color: $charcoal-gray;
            text-align: right;
          }
        }
      }

      .no-ingredient {
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 30px;
        font-weight: $font-weight-normal;
        font-size: $font-size-base;
        color: $charcoal-gray;
      }
    }

    .ingredients-available-no-group {
      border: 1px solid $white;
    }
  }

  .no-ingredients-available {
    padding: 0px 10px;
    font-weight: $font-weight-normal;
    font-size: $font-size-base;
    color: $grey;
  }
}