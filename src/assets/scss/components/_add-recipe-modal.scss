.add-category-recipe-matches-modal {
  font-family: $font-family-averta;
  padding: 0;
  width: 838px;
  height: 100%;
  text-align: left;
  overflow-y: visible !important;
  scrollbar-color: $grainsboro $whispering-white-smoke;
  scrollbar-width: thin;

  ::-webkit-scrollbar {
    width: 12px;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-track {
    background: $whispering-white-smoke;
  }

  ::-webkit-scrollbar-thumb {
    background: $grainsboro;
    border: 3px solid $transparent;
    border-radius: 15px;
    background-clip: content-box;
  }
  .header-section {
    display: flex;
    width: 100%;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 6px;
    padding: 0px 15px;
  }

  .title-section {
    display: flex;
    justify-content: space-between;
    width: 90%;

    .title {
      font-size: 24px;
      color: $black;
      font-weight: 700;
    }

    .search-box-pop {
      position: relative;
      background-color: $pearl-mist;
      border: 1px solid $grainsboro;
      border-radius: 30px;
      padding: 0 12px 0 16px;
      height: 36px;
      width: 300px;

      .search-input-box {
        width: 250px;
        height: 34px;
        margin: 0px 0px 0px 20px;
        padding: 0;
        background: none;
        color: $black;
        border: none;
        font-size: 16px;
        border-radius: 0;
        box-shadow: none;

        ::placeholder {
          font-size: 16px;
          color: $graphite-gray;
          font-weight: 400;
        }
      }

      .align-search-input-box {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 230px;
        display: block;
      }

      .search-icon-green-image {
        position: relative;
        bottom: 25px;
        right: 5px;
        width: 18px;
        height: 18px;
        cursor: pointer;

        img {
          position: relative;
          bottom: 3px;
        }
      }

      .exit-search-icon {
        position: relative;
        bottom: 23px;
        float: right;
        width: 12px;
        height: 12px;
        cursor: pointer;

        img {
          position: relative;
          bottom: 6px;
        }
      }
    }
  }
  .close-icon-section {
    width: 10%;
    display: flex;
    justify-content: flex-end;
    .close-icon {
      height: 24px;
      width: 24px;
      cursor: pointer;
      img {
        height: 100%;
        width: 100%;
      }
    }
  }

  .add-table-content {
    position: relative;
    left: 10px;
    max-height: 243px;
    min-height: 243px;
    overflow-y: scroll;
    padding-left: 6px;
    padding-right: 22px;

    .table-image-loader {
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;
      background-color: $white;
      margin: 0 auto;
      height: 200px;
      width: 210px;

      .loader {
        border: 3px solid $pristine-white;
        border-radius: 50%;
        border-top: 3px solid $spanish-gray;
        border-right: 3px solid $spanish-gray;
        border-bottom: 3px solid $spanish-gray;
        width: 24px;
        height: 24px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }

      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }

    .add-table {
      .no-recipe-result {
        position: relative;
        top: 90px;
        display: flex;
        justify-content: center;
        font-weight: 700;
        font-size: 20px;
        color: $shadow-gray;
      }

      .change-background-color-for-selected {
        background-color: $aqua-spring;
      }

      .add-recipe-body {
        border-bottom: 1px solid $grainsboro;

        .recipe-image-wrapper {
          width: 60px;
          height: 60px;
          margin-left: 10px;
          margin-top: 10px;
          margin-bottom: 10px;
          margin-right: 20px;
          overflow: hidden;

          .recipe-image {
            width: 100%;
            height: 60px;
            object-fit: cover;
          }
        }

        .table-image-recipe {
          width: 70px;
        }

        .table-recipe-code {
          width: 70px;
        }

        .recipe-code {
          font-size: 12px;
          font-weight: 400;
          color: $stone-gray;
        }

        .recipe-name-tooltip {
          position: relative;
          display: inline-block;
          display: -webkit-box;
          margin: 10px 0px;
          top: -1px;
          font-size: 14px;
          font-weight: 700;
          max-width: 328px;
          color: $black;
        }

        .recipe-subtitle {
          margin-top: 3px;
          color: $shadow-gray;
          font-weight: 400;
          font-size: 14px;
        }

        .recipe-details {
          .details {
            padding-left: 4px;
            width: 150px;
            font-size: 14px;
            font-weight: 400;
            color: $black;
          }

          .image {
            position: relative;
            top: -1px;
            width: 12px;
            height: 12px;
          }
        }
      }

      .add-recipe-body:first-child {
        border-top: 1px solid $grainsboro;

        .recipe-name-tooltip {
          position: relative;
          display: inline-block;
          display: -webkit-box;
          margin: 10px 0px;
          top: -1px;
          font-size: 14px;
          font-weight: 700;
          max-width: 328px;
          color: $black;
        }
      }
    }

    .load-button {
      padding: 15px 0px;
      width: 140px;
      margin: 0 auto;
    }
  }

  .done-section {
    text-align: center;
    border-top: 1px solid $grainsboro;
    padding-top: 14px;
  }
}
