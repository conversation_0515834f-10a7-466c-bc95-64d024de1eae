.recipe-image-and-video-container {
    font-family: $font-family-averta;

    .recipe-image-and-video-inner-section {
        position: relative;
        margin-right: 20px;
        display: flex;
        gap: 30px;
        width: max-content;

        .container-part {
            width: 280px;

            .top-section {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .heading-text {
                    font-weight: $font-weight-semi-bold;
                }

                .button-section {
                    .dropdown-icon {
                        height: 16px;
                        width: 16px;
                        transform: rotate(90deg);
                    }
                }

                .add-recipe-image-section {
                    position: relative;
                }

                .recipe-image-dropdown-section {
                    position: absolute;
                    top: 48px;
                    right: 0;
                    z-index: 3;
                    width: 98%;
                    list-style: none;
                    border-radius: 4px;
                    background: $white;
                    color: $charcoal-light;
                    box-shadow: 0 1px 10px 0 $box-shadow;

                    .result-section {
                        display: flex;
                        justify-content: center;
                        width: 98%;
                        padding: 8px;
                        margin: 2px;
                        border-radius: 4px;
                        color: $jet-black;
                        cursor: pointer;
                        font-size: $font-size-base;
                        font-weight: $font-weight-semi-bold;

                        &.is-active,
                        &:hover {
                            background: $green;
                            color: $white;
                        }
                    }

                    .result-disable-section {

                        &.is-active,
                        &:hover {
                            background: none;
                            color: $jet-black;
                        }
                    }

                    .disable-text {
                        opacity: 0.5;
                    }
                }
            }

            .media-part {
                margin-top: 20px;
            }
        }

        .media-error-section {
            position: absolute;
            top: 50px;
            right: -11px;
            width: 104%;
            height: 375px;
            border: 2px solid #ff0000;
            border-radius: 8px;
        }
    }

    .recipe-media-error-section {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-top: 20px;

        .info-icon {
            width: 20px;
            height: 20px;
        }

        .text {
            font-size: $font-size-base;
            color: $crimson-red;
        }
    }
}