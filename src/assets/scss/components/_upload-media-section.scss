.media-upload-main-container {
    padding: 15px;

    .top-section {
        display: flex;
        justify-content: space-between;

        .heading-text {
            color: $graphite-gray;
            font-size: $font-size-base;
            font-weight: $font-weight-semi-bold;
        }

        .close-button-section {
            .close-icon {
                height: 20px;
                width: 20px;
            }
        }
    }

    .middle-section {
        margin-top: 15px;
        display: flex;
        align-items: center;
        gap: 30px;

        .input-link-section {
            display: flex;
            align-items: center;
            width: 90%;
            gap: 15px;

            .link-text {
                font-size: $font-size-base;
                font-weight: $font-weight-bold;
                color: $green-dark;
            }

            .input-zone {
                width: 100%;
                display: flex;
                align-items: center;
                gap: 10px;

                .input-field {
                    width: 90%;
                    padding: 12px 20px;
                    border: 1px solid $grainsboro;
                    border-radius: 20px;

                    &::placeholder {
                        color: $grainsboro;
                    }
                }

                .invalid-url-message {
                    color: $red;
                    font-size: $font-size-14;
                }
            }
        }

        .add-button-section {
            button {
                width: 76px;
            }
        }
    }

    .bottom-section {
        margin-top: 30px;

        .inner-section {
            padding: 25px 0;
            border: 2px dashed $slate-gray;
            background-color: $pearl-mist;
            border-radius: 8px;
            text-align: center;

            .text-section {
                color: $green-dark;

                .text {
                    margin: 10px 0;
                }
            }

            .upload-file-button {
                margin-top: 10px;

                button {
                    width: 160px;
                }

                .upload-input {
                    display: none;
                }
            }
        }
    }
}