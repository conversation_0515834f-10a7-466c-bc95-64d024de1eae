.block-wrapper {
  position: relative;

  &-container {
    position: relative;
    width: 100%;
    padding: 28px 30px;
    background-color: $white;
    border-radius: $border-radius;

    &-transparent {
      padding: 0;
      background-color: transparent;
    }
  }

  &-title {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
  }
}

.block-wrapper:has(+ .block-wrapper) {
  margin-bottom: 30px;
}
