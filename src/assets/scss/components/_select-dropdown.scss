.select-dropdown {
  width: 185px;
  height: 44px;
  background-color: $white;
  border-radius: 8px;
  box-shadow: 1px 1px 0 0 $box-shadow;

  &-button {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    gap: 5px;
    padding: 0 15px;
  }

  &-panel {
    z-index: 100;
    margin: 1px 0 0;
    padding: 2px;
    background-color: $white;
    border: 1px solid $grainsboro;
    border-radius: 4px;
    box-shadow: 0 1px 10px 0 $box-shadow;

    &-search {
      height: 40px;
      padding: 10px 6px;
      border-bottom: 1px solid $grainsboro;

      img {
        width: 16px;
      }

      input {
        margin: 0 5px;
        max-width: calc(100% - (32px + 10px));
        overflow: hidden;
        text-overflow: ellipsis;
        border: none;
      }
    }

    &-list {
      max-height: 150px;

      &-item {
        margin: 2px 0;
        padding: 14px 6px;
        text-align: left;
        border-radius: 4px;

        &-active,
        &:hover {
          background-color: $green;
          color: $white;
        }
      }
    }
  }

  // open state
  &-open &-button-icon {
    transform: rotate(180deg);
  }
}

