.recipe-preview-page::-webkit-scrollbar {
  width: 12px;
  border-radius: 3px;
}

.recipe-preview-page::-webkit-scrollbar-track {
  background: $whispering-white-smoke;
}

.recipe-preview-page::-webkit-scrollbar-thumb {
  background: $grainsboro;
  border: 3px solid $transparent;
  border-radius: 15px;
  background-clip: content-box;
}

.recipe-preview-page {
  overflow-y: scroll;
  height: 72vh;
  width: 100%;
  scrollbar-color: $grainsboro $whispering-white-smoke;
  scrollbar-width: thin;
  font-family: $font-family-averta;

  @media screen and (min-width: 1600px) {
    width: 100%;
  }

  .recipe-preview-loading-main {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 100%;
    padding: 0 20px;

    .input-loading {
      height: 60px;
      display: flex;
      justify-content: center;

      .loader-image {
        border: 3px solid $white;
        border-radius: 50%;
        border-top: 3px solid $green;
        border-right: 3px solid $green;
        border-bottom: 3px solid $green;
        width: 20px;
        height: 20px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }
    }

    .loading-text {
      background-color: $green-peppermint;
      border-radius: 4px;
      border: 1px solid $green-fringy-flower;
      width: 468px;
      height: 57px;

      p {
        font-weight: 400;
        font-size: 16px;
        color: $shadow-gray;
        padding: 18px 0px;
      }
    }
  }

  .preview-main-container {
    .variant-dropdown-section {
      display: flex;
      height: 66px;

      .variant-dropdown-title {
        margin: 7px 0px;
        margin-left: 27px;
        margin-right: 8px;
        color: $spanish-gray;
        line-height: 1;
        padding-top: 15px;
        text-transform: uppercase;
      }

      .recipe-preview-variant-dropdown {
        position: relative;
        width: 246px;
        height: 44px;
        cursor: pointer;

        .recipe-preview-variant-selected {
          display: flex;
          margin: 5px 0px;
          width: 100%;
          height: 100%;
          margin-left: 4px;
          background-color: $white;
          border-radius: 4px;
          box-shadow: 0px 1px 5px 0px $box-shadow;
          padding-top: 13px;
          padding-left: 15px;
          line-height: 1.25;
          position: relative;
          cursor: pointer;

          .flag-image {
            margin-right: 4px;
          }

          .dropdown-image {
            position: absolute;
            right: 23px;

            .recipe-variant-dropdown-icon {
              transform: rotate(90deg);
            }

            .recipe-variant-dropdown-icon-open {
              transform: rotate(270deg);
            }
          }

          .dropdown-image :hover {
            cursor: pointer;
          }
        }

        .dropdown-list {
          width: 246px;
          height: auto;
          position: absolute;
          box-shadow: 0px 1px 5px 0px $box-shadow;
          margin-left: 4px;
          font-size: 16px;
          line-height: 1.25;
          top: 48px;
          right: 2px;
          background-color: $white;
          z-index: 2;
          padding-bottom: 4px;
          border-radius: 4px;

          .dropdown-list-item {
            display: flex;
            width: 100%;
            height: 42px;
            padding: 1px 1px;
            margin-top: 2px;
            margin-bottom: 1px;
            background-color: $white;
            padding-left: 17px;
            padding-top: 11px;
            border-radius: 4px;
            line-height: 1.25;

            .flag-image {
              margin-right: 4px;
            }
          }

          .dropdown-list-item:hover {
            display: flex;
            width: 100%;
            background-color: $green-light;
            color: $white;
            cursor: pointer;
          }
        }
      }
    }

    .main-section {
      display: flex;
      flex-direction: column;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      background-color: $white;
      margin: 0 20px;

      .recipe-details-video-image-container {
        display: flex;
        flex-direction: column;
        margin-right: 36px;
        margin-top: 24px;

        .recipe-basic-details {
          display: flex;
          justify-content: space-between;
          margin-left: 21px;

          .recipe-video-image {
            display: flex;
            margin-bottom: 70px;
            width: 100%;
          }
        }
      }

      .recipe-steps-at-recipe-preview {
        margin: 8px 25px 28px 21px;
        display: flex;

        .recipe-steps-notes-container {
          width: 68%;
          display: flex;
          flex-direction: column;
          padding-right: 20px;

          .notes-section {
            margin-top: 16px;
            padding: 14px 16px 16px 16px;
            border: 1px solid $grainsboro;
            border-radius: 8px;
            display: flex;
            flex-direction: column;

            .notes-heading {
              font-weight: 700;
              font-size: 20px;
              color: $black;
            }

            .notes-available {
              font-weight: 400;
              font-size: 16px;
              color: $grey;
              word-break: break-word;
            }

            .no-notes-available {
              margin-top: 13px;
              font-weight: 400;
              font-size: 16px;
              color: $grey;
            }
          }
        }

        .ingredients-categories {
          display: flex;
          flex-direction: column;
          width: 32%;

          .recipe-preview-filter-slug-main-section {
            width: 100%;
            border: 1px solid $grainsboro;
            border-radius: 4px;
            background-color: $pearl-mist;
            padding: 16px 20px;
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}
