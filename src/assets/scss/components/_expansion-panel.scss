.expansion-panel {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, .2);

  &:last-child {
    margin-bottom: 0;
  }

  &-expanded {}

  &-drag {
    visibility: hidden;
    cursor: move;
    pointer-events: none;
    position: absolute;
    top: -1px;
    left: 50%;
    width: fit-content;
    transform: translateX(-50%);
  }


  &-head {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 10px;
    position: relative;
    min-height: 54px;
    padding: 7px 16px;
    border: 1px solid transparent;
    border-radius: 8px;

    &:hover {
      border-color: $green-light;
      background-color: $aqua-spring;
    }

    &-content {
      flex: 1 1 auto;
      max-width: 100%;
    }

    &-action {
      width: auto;

      button {
        min-width: auto;
        padding-inline: 0;
        font-size: 14px;
        font-weight: 700;
        color: $green;

        img {
          width: 19px;
          height: auto;
          rotate: 90deg;
        }
      }
    }
  }

  &-head:hover &-drag {
    visibility: visible;
    pointer-events: auto;
  }

  &-expanded &-head-action button img {
    rotate: -90deg;
  }

  &-body {
    display: grid;
    grid-template-rows: 0fr;
    transition: grid-template-rows 200ms ease-in-out;

    > div {
      overflow: hidden;
    }
  }

  &-expanded &-body {
    grid-template-rows: 1fr;
  }
}
