    .privacy-policy-container {
        max-width: 1280px;
        margin-top: 10px;
        width: 100%;
        margin-bottom: 50px;
        font-family: $font-family-averta;
        .row {
            margin-right: 2rem;
        }
        .expand-click {
            border-bottom: 1px dotted $grainsboro-cloud;
            margin-bottom: 10px;
            -webkit-transition: 0.2s linear all;
            transition: 0.2s linear all;
        }
        ul {
            list-style-type: none;
            padding: 0;
        }

        li {
            margin: 0;
        }

        .description-box {
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 15px;
            h3 {
                padding-top: 0px !important;
                margin-top: 0px !important;
            }

            .description-image {
                margin-right: 5px;
            }

            .description-image-legal {
                margin-right: 5px;
                margin-top: -12px;
            }
        }
        #innit-policy .expand-click {
            margin: -10px;
            padding: 12px 25px 13px 10px;
        }
        #innit-policy .innit-header p {
            margin: 0;
            padding: 0;
        }
        #innit-policy .box-primary {
            border: 1px solid $light-silver;
            border-bottom-color: $medium-silver;
            box-shadow: 0 1px 0 $light-alabaster;
            -webkit-box-shadow: 0 1px 0 $shadow-black;
            background: transparent;
        }
        #init-policy .innit-content {
            position: relative;
            padding: 25px 30px;
            margin: 0 auto;
            border-radius: 3px 3px 0 0;
        }
        #innit-policy h3 + p {
            padding-top: 0;
        }
        #innit-policy p {
            font-size: $font-size-14;
            font-weight: $font-weight-normal;
            line-height: $line-height-18;
            margin-bottom: 9px;
            color: $black;
            padding-top: 11px;
        }
        #innit-policy h6 {
            text-transform: none;
            padding-top: 19px;
            color: $black;
            font-size: $font-size-base;
            font-weight: $font-weight-semi-bold;
        }
        #innit-policy h5 {
            font-size: $font-size-base;
            padding-top: 19px;
            font-weight: $font-weight-semi-bold;
        }
        #innit-policy h4 {
            font-size: $font-size-base;
            font-weight: $font-weight-semi-bold;
            padding-top: 19px;
            margin-bottom: 0;
        }
        #innit-policy h3 {
            font-size: $font-size-base;
            line-height: $line-height-19;
            font-weight: $font-weight-semi-bold;
            padding-top: 24px;
        }
        #innit-policy h2 {
            font-size: $font-size-base;
            font-weight: $font-weight-semi-bold;
            line-height: $line-height-21;
            padding-top: 21px;
        }
        #innit-policy h1 {
            font-size: $font-size-base;
            font-weight: $font-weight-semi-bold;
            line-height: $line-height-21;
            margin-bottom: 5px;
        }
        #innit-policy .innit-header {
            border-bottom: 1px dotted $gray-tint;
            padding-bottom: 25px;
            position: relative;
        }
        #innit-policy ul li {
            list-style: none;
            line-height: $line-height-19;
            font-weight: $font-weight-normal;
            font-size: $font-size-14;
            margin-top: 2px;
        }
        #innit-policy .legal-pp .one-line-col > ul.for_boxes {
            margin-top: 21px;
        }
        .one-line-col,
        #innit-policy.init-fluid-policy .half-col {
            width: 100%;
        }
    }

