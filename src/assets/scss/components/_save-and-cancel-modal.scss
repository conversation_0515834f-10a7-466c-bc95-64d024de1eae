.save-recipe-variant-modal {
  width: 454px;
  display: flex;
  flex-direction: inherit;
  justify-content: space-between;
  font-family: $font-family-averta;

  .save-info-popup-container {
    width: 25%;

    .save-image {
      margin-top: 23px;

      .save-image-container {
        img {
          height: 80px;
          width: 80px;
        }
      }
    }
  }

  .publish-content {
    width: 72%;
    padding-top: 22px;

    .publish-head {
      font-size: 20px;
      color: $black;
      font-weight: 700;
      text-align: left;
      padding-right: 10px;
    }

    .note-message {
      color: $ruby-red;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      padding-top: 15px;
      padding-right: 30px;
    }

    .slug-warnings {
      color: $ruby-red;
      font-size: 12px;
      text-align: left;
      font-weight: 400;
      margin-top: 10px;
      padding-right: 30px;
    }

    .button-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-right: 16px;
      padding-bottom: 4px;
      margin-top: 30px;
      gap: 20px;
    }
  }
}
