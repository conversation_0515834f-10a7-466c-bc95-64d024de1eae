.image-selector-slide {
    position: relative;
    width: 134px;
    height: 134px;
    border: 1px solid $dark-gray;
    border-radius: $border-radius;

    >img {
        width: 100%;
        height: auto;
        border-radius: $border-radius;
        object-fit: contain;
    }

    &-zoom:hover {
        cursor: zoom-in;
    }

    &.__loading {
        border-color: transparent;
        background-color: $grainsboro-gray;

        &:nth-child(2n),
        &:nth-child(5n) {
            opacity: 0.6;
        }

        &:nth-child(3n),
        &:nth-child(4n) {
            opacity: 0.4;
        }

        &:nth-child(6n) {
            opacity: 1;
        }
    }

    &.__selected {
        border: 3px solid $green;
        border-radius: 13px;
        padding: 5px;
    }

    &-select {
        display: none;
        pointer-events: none;
    }

    &-select:hover,
    &:hover:not(.__loading) &-select {
        display: inline-block;
        pointer-events: auto;
    }

    .max-limit-reached {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.56);
        border-radius: 8px;
        line-height: $line-height-20;
        color: $white;
        visibility: hidden;
    }

    &:hover {
        .max-limit-reached {
            visibility: visible;
        }
    }
}