  .main-container {
    font-family: $font-family-averta;
    color: $black;
    padding-bottom: 44px;

    h1 {
      font-size: $font-size-base;
      font-weight: $font-weight-semi-bold;
      margin-bottom: 20px;
    }

    h2,
    h3,
    h4,
    h5,
    h6 {
      font-size: $font-size-14;
      font-weight: $font-weight-semi-bold;
      margin-top: 30px;
    }

    ul,
    ol {
      list-style-type: none;
      padding-left: 0;
      margin-left: 0;
    }

    ul.terms-list {
      margin-top: 10px;
    }

    p {
      margin-top: 10px;
      font-size: $font-size-14;
      font-weight: $font-weight-normal;
    }

    li {
      font-size: $font-size-14;
      font-weight: $font-weight-normal;
    }

    .table {
      margin-top: 30px;

      .terms-policy-table-main {
        border: 1px solid $black;
        border-radius: 8px;
      }

      .terms-heading {
        font-weight: $font-weight-semi-bold;
        font-size: $font-size-base;
        border-bottom: 1px dotted $black;
        padding: 20px 20px;
      }

      .terms-details-container {
        padding: 20px;

        .terms-details-main {
          margin-bottom: 40px;

          .terms-title {
            font-weight: $font-weight-semi-bold;
            font-size: $font-size-base;
            margin-bottom: 8px;
          }

          .terms-paragraph {
            font-weight: $font-weight-normal;
            font-size: $font-size-14;
          }
        }

        .terms-details-main:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
