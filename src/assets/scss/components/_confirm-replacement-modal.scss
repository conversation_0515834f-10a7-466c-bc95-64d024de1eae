.confirm-replacement-modal {
  font-family: $font-family-averta;

  .error-delete-hero-modal-content {
    padding: 12px 30px;
    width: 471px;

    .error-cross-image-div {
      margin-right: 20px;
    }

    .error-cross-image,
    .replacement-hero-image {
      height: 80px;
      min-width: 80px;
    }

    .replacement-hero-image {
      transform: rotate(90deg);
    }

    .select-schedule-date-main-container {
      display: flex;

      .select-schedule-date-hero-modal-heading {
        text-align: left;
        padding: 12px 0px;
        font-weight: $font-weight-bold;
        font-size: $font-size-20;
        color: $black;
        margin-bottom: 12px;

        .capitalize-the-word {
          text-transform: capitalize;
        }

        .unable-schedule-text,
        .delete-hero-text {
          color: $black;
          margin-bottom: 12px;
        }

        .error-delete-schedule-date-hero-modal-sub {
          font-weight: $font-weight-light;
          font-size: $font-size-base;

          .error-schedule-sub-heading {
            color: $gunmetal-grey;
          }

          .delete-schedule-sub-heading {
            color: $grey;
          }

          .quiz-draft-sub-heading {
            color: $ruby-red;
            font-weight: $font-weight-medium;
            font-size: $font-size-12;
            line-height: $line-height-18;
          }
        }
      }
    }

    .replacement-hero-modal-btn-container,
    .error-delete-hero-modal-btn-container,
    .delete-hero-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      font-weight: $font-weight-bold;
      font-size: $font-size-base;

      .select-schedule-date-hero-confirm-btn,
      .select-schedule-date-hero-cancel-btn,
      .select-schedule-date-hero-okay-btn,
      .delete-date-hero-cancel-btn,
      .delete-date-hero-delete-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        text-shadow: 0 -1px 0 $faint-black;
        text-align: center;
        cursor: pointer;
        height: 44px;
        min-width: 121px;
        background-color: $green;
        color: $white;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;

        &.select-schedule-date-hero-cancel-btn,
        &.delete-date-hero-cancel-btn {
          background-color: $white;
          color: $green;
          box-shadow: 0 1px 4px 1px $box-shadow;
        }

        &.select-schedule-date-hero-okay-btn,
        &.delete-date-hero-delete-btn {
          background-color: $fiery-red-blaze;
          color: $white;
        }

        &.delete-date-hero-delete-btn {
          width: 121px;
        }
      }
    }

    .error-delete-hero-modal-btn-container {
      margin-top: 36px;

      .select-schedule-date-hero-okay-btn {
        width: 121px;
        font-size: 16px;
        margin-left: 10px;
        font-weight: $font-weight-bold;
      }
    }

    .delete-hero-modal-btn-container {
      margin-top: 36px;
      align-items: center;

      .delete-date-hero-cancel-btn {
        min-width: 109px;
        font-weight: bolder;
      }

      .delete-date-hero-delete-btn {
        width: 121px;
        font-size: $font-size-base;
        font-weight: $font-weight-semi-bold;
        margin-left: 10px;
      }
    }
  }

  .select-schedule-date-hero-modal-content {
    padding: 12px 30px;
    width: 500px;
    height: 340px;

    .select-schedule-date-picker-container {
      display: flex;
      margin-top: 14px;
    }

    .select-schedule-date-main-container {
      display: flex;

      .select-schedule-date-hero-modal-heading {
        font-weight: $font-weight-bold;
        font-size: $font-size-20;
        color: $black;
        margin-bottom: 12px;
      }

      .select-schedule-date-close-icon {
        position: absolute;
        right: 30px;

        img {
          cursor: pointer;
        }
      }
    }

    .select-schedule-date-hero-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      position: absolute;
      right: 32px;
      bottom: 36px;
      font-size: $font-size-base;
      font-weight: $font-weight-bold;

      .select-schedule-date-hero-confirm-btn,
      .select-schedule-date-hero-cancel-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        text-shadow: 0 -1px 0 $faint-black;
        text-align: center;
        cursor: pointer;
        height: 44px;
        min-width: 121px;

        &.select-schedule-date-hero-cancel-btn {
          background-color: $white;
          color: $green;
          box-shadow: 0 1px 4px 1px $box-shadow;
        }
      }
    }
  }
}