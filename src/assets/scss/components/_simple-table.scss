
$simple-table-head-column-color: $spanish-gray;
$simple-table-column-x-gap: 10px;
$simple-table-border-color: $grainsboro;

.simple-table-wrapper {
  width: 100%;
  height: 100%;
  background-color: $white;
  border: 1px solid $simple-table-border-color;
  border-radius: $border-radius;
}

.simple-table {

  &-head {
    background-color: $white-smoke;
    border-radius: $border-radius $border-radius 0 0;

    &-row {
      margin: 0;
      padding: 0;
    }

    &-column {
      width: 110px;
      margin: 0;
      padding-top: 6px;
      padding-bottom: 6px;
      border: none;
      font-size: 12px;
      font-weight: 700;
      line-height: 1.333;
      color: $simple-table-head-column-color;
      text-align: left;
      text-transform: uppercase;

      &:first-child {
        border-top-left-radius: $border-radius;
      }
      &:last-child {
        border-top-right-radius: $border-radius;
      }

      &-sortable {

        &-active {
          color: $vivid-green;
        }

        &-icon {
          visibility: hidden;

          &-asc {
            transform: rotate(180deg);
          }
        }

        &:hover &-icon,
        &-active &-icon {
          visibility: visible;
        }
      }
    }
  }

  &-body {
    margin: 0;
    padding: 0;
  }

  &-row {
    height: 80px;
    border-top: 1px solid $simple-table-border-color;

    &-expired-toggle {
      height: 52px;
    }

    &:has(input[type="checkbox"]:not(:disabled)) {
      cursor: pointer;
    }

    &:has(input[type="checkbox"]:not(:disabled)):hover,
    &:has(input[type="checkbox"]:checked),
    &:has([data-selected-highlight="highlight"]) {
      background-color: $aqua-spring;
    }
  }

  &-column {
    word-break: break-word;

    &-expired-toggle {
      text-align: center;
    }
  }

  &-head-column,
  &-column {
    padding-left: 5px;
    padding-right: 5px;
    font-family: $font-family-averta;
  }

  &-head-column:first-child,
  &-column:first-child {
    padding-left: $simple-table-column-x-gap;
  }

  &-head-column:last-child,
  &-column:last-child {
    padding-right: $simple-table-column-x-gap;
  }

  &-img {
    height: 60px;
    width: 60px;
    object-fit: cover;
    border-radius: 4px;
  }
}
