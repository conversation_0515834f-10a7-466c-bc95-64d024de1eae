.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
  position: relative;
  width: auto;
  min-width: 82px;
  height: 25px;
  padding: 0 10px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  text-transform: capitalize;
  vertical-align: middle;

  img {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  &-only-icon {
    width: 25px;
    min-width: 25px;
    padding: 0;
  }

  &-small {
    width: fit-content;
    min-width: fit-content;
    height: auto;
    padding: 1px 13px;
  }

  &:has(+ .badge) {
    margin-right: 5px;
  }

  &-light-green {
    color: $green;
    background-color: $mint-cream;
  }
  &-light-green#{&}-outline {
    border-color: $green;
  }

  &-silver {
    color: $silver;
    background-color: $pearl-mist
  }
  &-silver#{&}-outline {
    border-color: $silver;
  }

  &-blue {
    color: $deep-sea-blue;
    background-color: $baby-blue;
  }
  &-blue#{&}-outline {
    border-color: $deep-sea-blue;
  }

  &-yellow {
    color: $lightning-yellow;
    background-color: $alabaster
  }
  &-yellow#{&}-outline {
    border-color: $lightning-yellow;
  }


  &-orange {
    color: $copper-rust;
    background-color: $rose-white;
  }
  &-orange#{&}-outline {
    border-color: $copper-rust;
  }

  &-red {
    color: $red;
    background-color: $light-rose;
  }
  &-red#{&}-outline {
    border-color: $red;
  }

  &-purple {
    color: $royal-purple;
    background-color: $lavender-mist;
  }
  &-purple#{&}-outline {
    border-color: $royal-purple;
  }

  &-outline {
    background-color: transparent;
  }
}
