.button-with-switcher {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  position: relative;
  font-family: $font-family-averta;

  &-slider-round {
    position: relative;
    width: 42px;
    height: 26px;
    background-color: $light-white;
    border-radius: 30px;
    transition: 0.4s;

    &:before {
      content: "";
      position: absolute;
      height: 23px;
      width: 23px;
      left: 2px;
      bottom: 2px;
      background-color: $white;
      transition: 0.4s;
      border-radius: 50%;
    }
  }

  &-checked &-slider-round {
    background-color: $green;
  }

  &-checked &-slider-round:before {
    transform: translateX(15px);
  }

  &:focus &-slider-round {
    box-shadow: 0 0 1px $green;
  }
}
