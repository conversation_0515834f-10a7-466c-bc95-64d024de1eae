.variant-card-field {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 58px;
  padding-inline: 20px;
  border: 1px solid $grainsboro;
  border-radius: $border-radius;
  background-color: $white;

  &:hover {
    background-color: $light-mint;
    border-color: $green;
  }

  &-prefix {
    font-size: 16px;
    font-weight: 700;
    color: $copper-rust;
  }

  &-actions {
    display: flex;
    gap: 10px;
  }

  &-action {
    width: 16px;
    height: auto;

    &:disabled,
    &[disabled] {
      img {
        opacity: 0.4;
      }
    }

    &-delete {
      visibility: hidden;
      pointer-events: none;
    }
  }

  &:hover &-action-delete {
    visibility: visible;
    pointer-events: auto;
  }

  &-input {
    width: 100%;
    font-size: 14px;
    font-weight: 700;
    color: $jet-black;
    border: none;
    background-color: transparent;
    outline: none;
    box-shadow: none;

    &:read-only,
    &[readonly] {
      cursor: default;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis ellipsis;
    }
  }

  &-input:not(:read-only):not([readonly]):focus + &-actions,
  &-input:not(:read-only):not([readonly]):active + &-actions {
    display: none;
    pointer-events: none;
  }
}
