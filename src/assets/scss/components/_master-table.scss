.table-content {
    font-family: $font-family-averta;
    width: 100%;

    .table {
        .table-head-row {
            background-color: $white-smoke;
            border-radius: 8px 8px 0px 0px;

            .table-head {
                color: $spanish-gray;
                font-size: 14px;
                line-height: 1;
                text-align: left;
                margin: 0 4px;
                text-transform: uppercase;

                th {
                    padding: 8px 0px;
                    font-weight: 700;
                    width: 110px;

                    &:first-child {
                        padding-left: 20px;
                    }
                }

                .table-head-title {
                    width: 170px;
                }
                .table-head-ingredient-name {
                    width: 30%;
                }
                .table-head-ingredient-product {
                    width: 45%;
                }
                .table-head-ingredient-edit {
                    width: 25%;
                }
            }
        }

        .table-non-shoppable-ingredient-row {
            background-color: $feather-gray;
        }

        .table-body {
            border: 1px solid $grainsboro;
            height: 79px;

            td:first-child {
                padding-left: 20px;
            }

            .table-image {
                padding-left: 10px;

                img {
                    height: 60px;
                    width: 60px;
                    object-fit: cover;
                    border-radius: 4px;
                }
            }

            .table-name {
                word-break: break-word;

                .table-title {
                    font-weight: bolder;
                }
            }

            .table-content-name-section {
                word-break: break-word;

                .content-name-section {
                    display: flex;
                    align-items: center;

                    .table-title {
                        font-weight: bolder;
                        max-width: 85%;
                        font-size: $font-size-14;
                    }

                    .table-tooltip-info {
                        position: relative;
                        width: 15%;
                        padding-left: 10px;

                        .symbol-icon {
                            cursor: pointer;
                        }

                        .tool-tip-locked-campaign {
                            visibility: hidden;
                            word-break: break-word;
                            width: 320px;
                            height: auto;
                            background-color: $jet-black;
                            opacity: 0.8;
                            color: $white;
                            border-radius: 8px;
                            padding: 8px 10px;
                            position: absolute;
                            z-index: 1;
                            margin-top: -62px;
                            margin-left: -77px;
                            font-size: 12px;
                            font-weight: 600;
                            text-align: center;
                            white-space: normal;

                            &::after {
                                content: "";
                                position: absolute;
                                top: 100%;
                                left: 26%;
                                border-width: 10px;
                                border-style: solid;
                                border-color: $jet-black $transparent $transparent $transparent;
                            }
                        }

                        .tool-tip-available-language {
                            width: max-content;
                            position: absolute;
                            margin-top: -40px;
                            margin-left: -30px;
                            font-size: 12px;
                        }

                        .alert-tooltip-info {
                            margin-left: -88px;
                        }

                        &:hover .tool-tip-locked-campaign {
                            visibility: visible;
                        }

                    }
                }
            }

            .table-count {
                .table-recipe-count {
                    color: $grey;
                }
            }

            .table-category-and-recipe-count {
                color: $grey;
                font-size: $font-size-14;

                .table-count {
                    display: flex;
                    gap: 3px;
                    font-weight: bolder;
                }

                .table-category-name {
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .table-ingredient-count {
                .table-count {
                    display: flex;
                    gap: 4px;
                    color: $charcoal-gray;

                    .product-matches {
                        display: flex;
                        align-items: center;

                        .gray-faded-divider {
                            color: $gray;
                        }

                        .matches-text {
                            &.common-matches-text{
                                padding-left: 2px;
                            }

                            &.gray-faded-text {
                              color: $gray;
                            }
                        }

                        .info-icon-container {
                            display: flex;
                            align-items: center;
                            margin-left: 8px;
                            width: 16px;
                            height: 15px;
                            img {
                                width: 100%;
                                height: 100%;
                            }
                        }

                        .tooltip {
                            visibility: hidden;
                            opacity: 0.8;
                            word-break: break-word;
                            color: $white;
                            font-family: $font-family-averta;
                            text-align: center;
                            width: 320px;
                            height: auto;
                            background-color: $black;
                            border-radius: 8px;
                            padding: 8px 10px;
                            margin-bottom: 74px;
                            margin-left: -85px;
                            position: absolute;
                            z-index: 1;
                        }

                        .tooltip::after {
                            content: "";
                            position: absolute;
                            top: 100%;
                            left: 26%;
                            border: 10px solid transparent;
                            border-top-color: $black;
                        }

                        .info-icon-container:hover .tooltip {
                            visibility: visible;
                          }
                        }
                    }
                    .bold-text {
                        font-weight: $font-weight-bold;
                    }
                }

            .table-published {
                .published-state {
                    width: 95px;
                    height: 25px;
                    font-weight: 400;
                    font-size: 12px;
                    color: $green;
                    background-color: $aqua-spring;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;

                    img {
                        position: relative;
                        right: 6px;
                        width: 16px;
                        height: 16px;
                    }
                }

                .timeout-state {
                    font-size: 13px;
                    width: 97px;
                    height: 25px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    text-transform: capitalize;
                    color: $orange;
                    background-color: $amber-blush;
                    border-radius: 4px;
                }

                .publishing-state {
                    font-size: 12px;
                    width: 110px;
                    height: 25px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    text-transform: capitalize;
                    color: $deep-sea-blue;
                    background-color: $baby-blue;
                    border-radius: 4px;

                    img {
                        width: 16px;
                        height: 16px;
                        position: relative;
                        right: 6px;
                    }
                }

                .unpublished-state {
                    font-size: 13px;
                    width: 112px;
                    min-width: 112px;
                    height: 25px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    text-transform: capitalize;
                    color: $silver;
                    background-color: $pearl-mist;
                    border-radius: 4px;

                    img {
                        position: relative;
                        right: 6px;
                    }
                }
            }

            td {
                padding: 0px 10px 0px 0px;
            }

            .buttons-section {
                .button-zone {
                    position: relative;
                    display: flex;
                    gap: 10px;

                    .edit,
                    .delete {
                        display: flex;
                        align-items: center;
                        border-radius: 4px;
                        padding: 0 10px;
                        color: $green;
                        box-shadow: 0 2px 4px 0 $box-shadow;
                        cursor: pointer;
                        height: 41px;
                        width: 39px;
                        background-color: $white;
                        position: relative;
                        border: none;

                        &:hover {

                            .tooltip-wrapper,
                            .updating-tooltip-wrapper {
                                visibility: visible;
                            }
                        }
                    }

                    .disabled-image {
                        opacity: 0.6;
                    }

                    .tooltip-wrapper {
                        visibility: hidden;
                        position: absolute;
                        top: 50px;
                        left: -200px;
                        background: $rose-white;
                        width: 265px;
                        z-index: 9999;
                        padding: 10px;
                        border-radius: 8px;

                        .tooltip-image {
                            width: 14px;
                            height: 14px;
                        }

                        .tooltip-text {
                            color: $graphite-gray;
                            width: 90%;
                            font-size: 13px;
                            display: -webkit-inline-box;
                            text-align: left;
                        }
                    }

                    .updating-tooltip-wrapper {
                        visibility: hidden;
                        position: absolute;
                        top: 50px;
                        left: -50px;
                        width: max-content;
                        z-index: 9999;
                        padding: 5px;
                        border-radius: 4px;
                        background: $sunset-orange;
                        box-shadow: 0 4px 10px 0 $shadow-black,
                            0 3px 5px 0 $faint-black, 0 0 0 1px $shadowy-black;

                        .tooltip-text {
                            font-size: 10px;
                            color: $white;
                        }
                    }
                }
            }
        }
    }
}