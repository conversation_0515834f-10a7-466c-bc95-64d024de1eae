.main-edit-search {
  font-family: $font-family-averta;
  .loader-main-container {
    height: 255px;
    background: $white;
    display: flex;
    justify-content: center;
    margin: 24px 30px;
    border-radius: 8px;
  }

  .edit-search-header {
    display: flex;
    justify-content: flex-end;
    height: 67px;
    width: 100%;

    .right-side {
      display: flex;
      margin: 13px 50px 13px 0px;
    }
  }

  .filter-container-search {
    width: 93%;
    margin: 24px 45px;
    border-radius: 8px;
    border: 1px solid $grainsboro;
    background-color: $white;

    .filter-container {
      width: 100%;
      padding: 24px 30px;

      .filter-head {
        color: $black;
        display: flex;
        justify-content: space-between;
      }

      .filter-box {
        width: 100%;
        padding: 23px 22px;
        border-radius: 8px;
        margin-top: 14px;
        border: 1px solid $grainsboro;

        .head-section {
          height: 32px;
          display: flex;
          justify-content: space-between;

          .left-side {
            display: flex;
            overflow: hidden;

            .filter-name {
              position: relative;
              top: 4px;
              font-size: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .edit-btn {
              border-radius: 4px;
              box-shadow: 0px 1px 5px 0px $box-shadow;
              height: 32px;
              margin: 0px 14px;
              width: 32px;
              min-width: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;

              img {
                width: 16px;
                height: 16px;
              }
            }
          }

          .right-side {
            min-width: 65px;

            .edit-button {
              color: $green;
              border: none;
              background-color: $transparent;
              position: relative;
              top: 6px;
              margin-bottom: 0;

              .arrow-down-up {
                position: relative;
                margin-left: 12px;
                left: 0px;
                top: -3px;
                border: solid $green;
                border-width: 0 2px 2px 0;
                display: inline-block;
                padding: 4px;
                transform: rotate(45deg);
                -webkit-transform: rotate(45deg);
              }
            }
          }
        }

        .content-section {
          .hr-tag {
            border: 1px solid $bright-gray;
            margin-top: 23px;
          }

          .text-button-section {
            display: flex;
            justify-content: space-between;
            margin: 20px auto;

            .text {
              color: $black;
              margin-bottom: 0;
            }

            .disable {
              opacity: 0.2;
              pointer-events: none;
            }

            .add-category-button {
              color: $green;
              border: none;
              background-color: $transparent;

              .add-image {
                position: relative;
                top: -1px;
                width: 18px;
                height: 18px;
                margin-right: 4px;
              }
            }
          }

          .list-group {
            display: inline-block;
            position: relative;
            left: -11px;
            width: 104%;

            .category-card {
              cursor: move;
              position: relative;
              float: left;
              margin: 11px;
              width: 190px;
              height: 210px;
              border-radius: 8px;
              background-color: $white;
              border: 1px solid $grainsboro;

              .card-image {
                margin: 10px auto;
                width: 170px;
                height: 140px;
                border-radius: 4px;
                overflow: hidden;

                .image {
                  width: 100%;
                  height: 100%;
                  border-right: 4px;
                  object-fit: cover;
                }
              }

              .card-title {
                margin-left: 8px;
                text-align: left;
                color: $black;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 137px;
              }

              .total-recipe {
                margin-left: 8px;
                text-align: left;
                font-size: 14px;
                color: $grey;
              }

              .delete-icon {
                display: none;
              }
            }

            .category-card:hover {
              background-color: $green-light;

              .card-image {
                opacity: 0.4;
              }

              .card-title {
                color: $white;
              }

              .total-recipe {
                color: $white;
              }

              .delete-icon {
                display: block;
                cursor: pointer;
                position: absolute;
                right: 11px;
                bottom: 16px;
              }
            }

            .tag-card {
              position: relative;
              float: left;
              margin: 11px;
              width: 190px;
              height: 58px;
              border-radius: 8px;
              border: 1px solid $grainsboro;
              background-color: $white;
              cursor: move;

              .black-tag-image {
                position: absolute;
                top: 21px;
                left: 12px;
                width: 14px;
                height: 14px;
                display: block;
              }

              .white-tag-image {
                display: none;
              }

              .card-title {
                margin-top: 9px;
                padding: 0px 38px;
                text-align: left;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: $jet-black;
              }

              .total-recipe {
                margin-left: 38px;
                text-align: left;
                font-size: 14px;
                color: $grey;
              }

              .delete-icon {
                display: none;
              }
            }

            .tag-card:hover {
              background-color: $green-light;

              .black-tag-image {
                display: none;
              }

              .white-tag-image {
                position: absolute;
                top: 21px;
                left: 12px;
                width: 14px;
                height: 14px;
                display: block;
              }

              .card-title {
                color: $white;
              }

              .total-recipe {
                color: $white;
              }

              .delete-icon {
                display: block;
                cursor: pointer;
                position: absolute;
                right: 11px;
                bottom: 18px;
              }
            }
          }

          .hidden-list {
            opacity: 0.5;
          }

          .delete-flex {
            display: flex;
            justify-content: flex-end;

            .delete-btn {
              min-width: 135px;
              text-align: right;
              cursor: pointer;

              .image {
                width: 14px;
                height: 16px;
              }

              .text {
                position: relative;
                top: 1px;
                margin-left: 6px;
                color: $fiery-red-blaze;
              }
            }
          }
        }
      }
    }
  }

  .filter-container-other-search {
    width: 93%;
    margin: 24px 45px;
    border-radius: 8px;
    border: 1px solid $grainsboro;
    background-color: $white;

    .filter-container {
      width: 100%;
      padding: 24px 30px;

      .filter-head {
        font-weight: 700;
        font-size: 24px;
        color: $black;
        display: flex;
        justify-content: space-between;

        .filter-tag-head {
          color: $black;
        }
      }

      .filter-box {
        width: 100%;
        border-radius: 8px;
        margin-top: 14px;
        position: relative;

        .head-section {
          padding: 23px 22px;
          border-radius: 8px;
          display: flex;
          justify-content: space-between;
          border: 1px solid $grainsboro;

          &:hover {
            border: 1px solid $green-light;
            background-color: $aqua-spring;

            .draggable-icon {
              visibility: visible;
            }
          }

          .draggable-icon {
            transform: translate(-50%, 0%);
            top: 0%;
            position: absolute;
            left: 50%;
            width: 28px;
            cursor: move;
            visibility: hidden;
          }

          .left-side {
            display: flex;
            margin-left: 38px;
            overflow: hidden;

            .filter-icon-tags {
              position: absolute;
              width: 44px;
              height: 78px;
              top: 1px;
              left: 1px;
              background: $baby-blue;
              border-radius: 8px 0px 0px 8px;

              .filter-image {
                transform: translate(-50%, -50%);
                top: 50%;
                position: relative;
                left: 50%;
              }
            }

            .filter-icon-diets {
              position: absolute;
              width: 44px;
              height: 78px;
              top: 1px;
              left: 1px;
              background: $alabaster;
              border-radius: 8px 0px 0px 8px;

              .filter-image {
                transform: translate(-50%, -50%);
                top: 50%;
                position: relative;
                left: 50%;
              }
            }

            .filter-name {
              position: relative;
              top: 4px;
              font-size: 20px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .edit-btn {
              border-radius: 4px;
              box-shadow: 0px 1px 5px 0px $box-shadow;
              height: 32px;
              margin-right: 4px;
              margin-left: 12px;
              width: 32px;
              background-color: $white;
              min-width: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;

              img {
                width: 16px;
                height: 16px;
              }
            }
          }

          .right-side {
            min-width: 200px;
            display: flex;
            .edit-btn {
              border-radius: 4px;
              box-shadow: 0px 1px 5px 0px $box-shadow;
              height: 32px;
              margin-right: 4px;
              margin-left: 12px;
              width: 32px;
              background-color: $white;
              min-width: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;

              img {
                width: 16px;
                height: 16px;
              }
            }
            .edit-button {
              color: $green;
              border: none;
              background-color: $transparent;
              position: relative;
              top: 6px;
              left: 32px;

              .arrow-down-up {
                position: relative;
                margin-left: 12px;
                left: 0px;
                top: -3px;
                border: solid $green;
                border-width: 0 2px 2px 0;
                display: inline-block;
                padding: 4px;
                transform: rotate(45deg);
                -webkit-transform: rotate(45deg);
              }
            }
          }
        }

        .content-section-other {
          padding: 0px 22px 23px 22px;
          border-radius: 8px;
          border-bottom: 1px solid $grainsboro;
          border-left: 1px solid $grainsboro;
          border-right: 1px solid $grainsboro;

          .hr-tag {
            border: 1px solid $bright-gray;
          }

          .text-button-section {
            display: flex;
            justify-content: space-between;
            margin: 20px auto;

            .text {
              color: $black;
              margin-bottom: 0;
            }

            .add-category-button {
              color: $green;
              border: none;
              background-color: $transparent;

              .add-image {
                position: relative;
                top: -1px;
                width: 18px;
                height: 18px;
                margin-right: 4px;
              }
            }

            .disable {
              opacity: 0.5;
              pointer-events: none;
            }
          }

          .list-group {
            display: inline-block;
            position: relative;
            left: -11px;
            width: 104%;

            .category-card {
              cursor: move;
              position: relative;
              float: left;
              margin: 11px;
              width: 190px;
              height: 210px;
              border-radius: 8px;
              background-color: $white;
              border: 1px solid $grainsboro;

              .card-image {
                margin: 10px auto;
                width: 170px;
                height: 140px;
                border-radius: 4px;
                object-fit: contain;
                overflow: hidden;

                .image {
                  width: 100%;
                  height: 100%;
                  border-right: 4px;
                }
              }

              .card-title {
                margin-left: 8px;
                text-align: left;
                color: $black;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .total-recipe {
                margin-left: 8px;
                text-align: left;
                font-size: 14px;
                color: $grey;
              }

              .delete-icon {
                display: none;
              }
            }

            .category-card:hover {
              background-color: $green-light;

              .card-image {
                opacity: 0.4;
              }

              .card-title {
                color: $white;
              }

              .total-recipe {
                color: $white;
              }

              .delete-icon {
                display: block;
                cursor: pointer;
                position: absolute;
                right: 11px;
                bottom: 16px;
              }
            }

            .tag-card {
              cursor: move;
              position: relative;
              float: left;
              margin: 11px;
              width: 190px;
              height: 58px;
              border-radius: 8px;
              background-color: $white;
              border: 1px solid $grainsboro;

              .diet-image {
                position: absolute;
                top: 15px;
                left: 8px;
                width: 23px;
                height: 23px;
                display: block;
              }

              .black-tag-image {
                position: absolute;
                top: 21px;
                left: 12px;
                width: 14px;
                height: 14px;
                display: block;
              }

              .white-tag-image {
                display: none;
              }

              .card-title {
                max-width: 180px;
                margin-top: 9px;
                padding: 0px 38px;
                text-align: left;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: $jet-black;
              }

              .total-recipe {
                margin-left: 38px;
                text-align: left;
                font-size: 14px;
                color: $grey;
              }

              .delete-icon {
                display: none;
              }
            }

            .tag-card:hover {
              background-color: $green-light;

              .black-tag-image {
                display: none;
              }

              .white-tag-image {
                position: absolute;
                top: 21px;
                left: 12px;
                width: 14px;
                height: 14px;
                display: block;
              }

              .card-title {
                color: $white;
              }

              .total-recipe {
                color: $white;
              }

              .delete-icon {
                display: block;
                cursor: pointer;
                position: absolute;
                right: 11px;
                bottom: 18px;
              }
            }
          }

          .hidden-list {
            opacity: 0.5;
          }

          .delete-flex {
            display: flex;
            justify-content: flex-end;

            .delete-btn {
              min-width: 135px;
              text-align: right;
              cursor: pointer;

              .image {
                width: 14px;
                height: 16px;
              }

              .text {
                position: relative;
                top: 1px;
                font-weight: 700;
                font-size: 14px;
                margin-left: 6px;
                color: $fiery-red-blaze;
              }
            }
          }
        }

        .border-hide {
          border: unset;
          border-bottom: none;
          border-left: 1px solid $grainsboro;
          border-right: 1px solid $grainsboro;
          border-top: 1px solid $grainsboro;
        }
      }
    }
  }
  .back {
    cursor: pointer;
    position: absolute;
    left: 22px;
    top: 12px;
    width: 14px;
    height: 8px;
    transform: rotate(90deg);
  }

  .create-tag-group {
    margin: 0 10px;
    position: relative;

    .close-image-icon {
      position: absolute;
      right: 0px;
      top: -15px;

      img {
        cursor: pointer;
      }
    }

    .disable {
      opacity: 0.2;
      pointer-events: none;
    }

    .close-image-icon-modal-image {
      position: absolute;
      right: 0px;
      top: -4px;

      img {
        cursor: pointer;
      }
    }

    .previous-modal {
      position: absolute;
      left: 0px;
      top: 0px;

      img {
        width: 14px;
        height: 8px;
        transform: rotate(90deg);
        cursor: pointer;
      }
    }

    .folder-image-icon {
      margin-top: 14px;

      img {
        width: 24px;
      }
    }

    .create-tag-group-modal {
      width: 390px;
      margin: 0 22px;
      padding: 20px 10px 20px 10px;

      .create-tag-heading {
        font-weight: 700;
        height: 72px;
        font-size: 24px;
        width: 100%;
        max-width: 250px;
        margin: 0 auto;
        cursor: default;
      }

      .rename-tag-heading {
        height: 30px;
        max-width: unset;
      }

      .create-tag-desc {
        font-family: $font-family-averta;
        color: $grey;
        font-size: 16px;
        height: 54px;
        margin-top: 10px;
        cursor: default;
      }

      .create-tag-group-button {
        color: $charcoal-light;
        position: relative;

        .create-tag-group-name {
          width: 100%;
          background-color: $pristine-white;
          border: 1px solid $ethereal-whisper-gray;
          border-radius: 4px;
          padding: 5px 30px 5px 10px;
          height: 50px;
          margin-bottom: 20px;
          font-size: 16px;
        }

        ::placeholder {
          color: $sliver;
        }

        .error-image {
          position: absolute;
          top: 16px;
          right: 12px;
        }

        .check-image {
          position: absolute;
          top: 20px;
          right: 12px;
        }

        .error-message {
          position: absolute;
          top: 52px;
          left: 12px;
          color: $red-orange;
          font-size: $font-size-14;
          background: $white;
        }
      }

      .create-tag-group-button-container {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .add-tag-modal {
    width: 660px;
    overflow-y: visible !important;
    scrollbar-color: $grainsboro $whispering-white-smoke;
    scrollbar-width: thin;

    ::-webkit-scrollbar {
      width: 12px;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
    }

    ::-webkit-scrollbar-thumb {
      background: $grainsboro;
      border: 3px solid $transparent;
      border-radius: 15px;
      background-clip: content-box;
    }

    .title-section {
      position: relative;

      .disable {
        opacity: 0.2;
        pointer-events: none;
      }

      .back {
        cursor: pointer;
        position: absolute;
        left: 22px;
        top: 12px;
        width: 14px;
        height: 8px;
        transform: rotate(90deg);
      }

      .exit {
        cursor: pointer;
        position: absolute;
        right: 18px;
        top: -4px;
        width: 24px;
        height: 24px;
      }

      .title {
        cursor: default;
        padding: 0 66px;
        margin-top: 8px;
        font-size: 24px;
        font-weight: 700;
        color: $black;
        margin-bottom: 14px;
      }
    }

    .search-container {
      margin: 0 35px 14px 25px;
      display: flex;
      justify-content: space-between;

      .search-title {
        font-size: 16px;
        padding-top: 8px;
      }

      .search-box-tag {
        position: relative;
        display: flex;
        align-items: center;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 30px;
        padding: 0 12px 0 16px;
        height: 36px;
        width: 300px;

        .search-input-box {
          width: 228px;
          height: 34px;
          padding: 0;
          background: none;
          color: $black;
          border: none;
          font-size: 16px;
          border-radius: 0;
          opacity: 0.6;
          box-shadow: none;

          ::placeholder {
            font-size: 16px;
            color: $graphite-gray;
            font-weight: 400;
          }
        }

        .align-search-input-box {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 230px;
          display: block;
          position: relative;
          left: 10px;
        }
        .disabled-search-icon {
          cursor: default !important;
          opacity: 0.4;
        }
        .search-icon-green-image {
          float: left;
          height: 18px;
          width: 18px;
          cursor: pointer;
        }

        .exit-search-icon {
          width: 12px;
          height: 12px;
          position: relative;
          float: right;
          cursor: pointer;
        }
      }
    }

    .add-group-content {
      height: 224px;
      position: relative;
      left: -10px;
      width: 103%;
      overflow-y: scroll;
      background-color: $white-smoke;

      .table-image-loader {
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9;
        background-color: $white-smoke;
        margin: 0 auto;
        height: 200px;
        width: 210px;

        .loader {
          border: 3px solid $pristine-white;
          border-radius: 50%;
          border-top: 3px solid $spanish-gray;
          border-right: 3px solid $spanish-gray;
          border-bottom: 3px solid $spanish-gray;
          width: 24px;
          height: 24px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }

        @-webkit-keyframes spin {
          0% {
            -webkit-transform: rotate(0deg);
          }

          100% {
            -webkit-transform: rotate(360deg);
          }
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }
      .tag-popup {
        height: 100% !important;
      }
      .diet-popup {
        height: 100% !important;
      }
      .container {
        margin: 0 auto;
        width: 630px;
        height: auto;
        .no-recipe-result {
          position: relative;
          top: 40%;
          font-weight: 700;
          font-size: 20px;
          color: $shadow-gray;
        }
        .tag-card {
          cursor: pointer;
          position: relative;
          float: left;
          margin: 10px;
          width: 190px;
          height: 58px;
          border-radius: 8px;
          background-color: $white;
          border: 1px solid $grainsboro;

          .black-tag-image {
            position: absolute;
            top: 21px;
            left: 12px;
            width: 14px;
            height: 14px;
            display: block;
          }

          .white-tag-image {
            display: none;
          }

          .card-title {
            margin-top: 9px;
            padding: 0px 38px;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: $jet-black;
          }

          .total-recipe {
            margin-left: 38px;
            text-align: left;
            font-size: 14px;
            color: $grey;
          }
        }

        .selected-tags {
          border: 1px solid $green;

          .checkmark {
            position: absolute;
            right: 16px;
            bottom: 22px;
            width: 7px;
            height: 14px;
            border: 1.5px solid $green;
            border-width: 0 2px 2px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
          }
        }

        .already-added-category {
          opacity: 0.4;
          pointer-events: none;
        }
      }

      .load-button {
        padding: 15px 0px;
        width: 140px;
        margin: 0 auto;
      }
    }

    .add-group-content-space {
      height: 6px;
      position: relative;
      left: -10px;
      width: 103%;
      background-color: $white-smoke;
    }

    .create-section {
      display: flex;
      justify-content: space-between;

      .count-selected {
        cursor: default;
        text-align: left;
        margin-left: 22px;
        width: 220px;
        margin-top: 30px;
        font-size: 16px;
      }

      .create-btn {
        margin-right: 22px;
        margin-top: 14px;
      }
    }
  }

  .add-category-modal {
    width: 660px;
    overflow-y: visible !important;
    scrollbar-color: $grainsboro $whispering-white-smoke;
    scrollbar-width: thin;

    ::-webkit-scrollbar {
      width: 12px;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
    }

    ::-webkit-scrollbar-thumb {
      background: $grainsboro;
      border: 3px solid $transparent;
      border-radius: 15px;
      background-clip: content-box;
    }

    .title-section {
      position: relative;

      .disable {
        opacity: 0.2;
        pointer-events: none;
      }

      .exit {
        cursor: pointer;
        position: absolute;
        right: 18px;
        top: -4px;
        width: 24px;
        height: 24px;
      }

      .title {
        cursor: default;
        padding: 0 10px;
        margin-top: 8px;
        font-size: 24px;
        font-weight: 700;
        color: $black;
        margin-bottom: 14px;
      }
    }

    .search-container-for-category {
      margin: 0 35px 14px 25px;
      display: flex;
      justify-content: space-between;

      .search-title {
        font-size: 16px;
        padding-top: 8px;
      }

      .search-box {
        display: flex;
        position: relative;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 30px;
        padding: 0 12px 0 16px;
        height: 36px;
        width: 300px;

        .search-input-box {
          width: 250px;
          height: 34px;
          margin: 0px 0px 0px 20px;
          padding: 0;
          background: none;
          color: $black;
          border: none;
          font-size: 16px;
          border-radius: 0;
          box-shadow: none;

          ::placeholder {
            font-size: 16px;
            color: $graphite-gray;
            font-weight: 400;
          }
        }

        .align-search-input-box {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 230px;
          display: block;
        }

        .disabled-search-icon {
          cursor: default !important;
          opacity: 0.4;
        }
        .search-icon-green-image {
          height: 18px;
          width: 18px;
          cursor: pointer;
        }

        .exit-search-icon {
          width: 12px;
          height: 12px;
          cursor: pointer;
        }
      }
    }

    .add-group-content {
      height: 224px;
      position: relative;
      left: -10px;
      width: 103%;
      overflow-y: scroll;
      background-color: $white-smoke;

      .table-image-loader {
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9;
        background-color: $white-smoke;
        margin: 0 auto;
        height: 200px;
        width: 210px;

        .loader {
          border: 3px solid $pristine-white;
          border-radius: 50%;
          border-top: 3px solid $spanish-gray;
          border-right: 3px solid $spanish-gray;
          border-bottom: 3px solid $spanish-gray;
          width: 24px;
          height: 24px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }

        @-webkit-keyframes spin {
          0% {
            -webkit-transform: rotate(0deg);
          }

          100% {
            -webkit-transform: rotate(360deg);
          }
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }

      .tag-popup {
        height: 100% !important;
      }
      .diet-popup {
        height: 100% !important;
      }
      .container {
        margin: 0 auto;
        width: 630px;
        height: auto;
        .no-recipe-result {
          position: relative;
          top: 40%;
          font-weight: 700;
          font-size: 20px;
          color: $shadow-gray;
        }

        .card {
          cursor: pointer;
          position: relative;
          float: left;
          margin: 10px;
          width: 190px;
          height: 210px;
          border-radius: 8px;
          background-color: $white;
          border: 1px solid $grainsboro;

          .card-image {
            margin: 10px auto;
            width: 170px;
            height: 140px;
            border-radius: 4px;
            object-fit: contain;
            overflow: hidden;

            .image {
              width: inherit;
              height: inherit;
              object-fit: cover;
            }
          }

          .card-title {
            margin-left: 8px;
            text-align: left;
            color: $black;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .total-recipe {
            margin-left: 8px;
            text-align: left;
            font-size: 14px;
            color: $grey;
          }
        }

        .selected-categories {
          border: 1px solid $green;

          .checkmark {
            position: absolute;
            right: 16px;
            bottom: 16px;
            width: 8px;
            height: 14px;
            border: 1.5px solid $green;
            border-width: 0 2px 2px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
          }
        }

        .already-added-category {
          opacity: 0.4;
          pointer-events: none;
        }
      }

      .load-button {
        padding: 15px 0px;
        width: 140px;
        margin: 0 auto;
      }
    }

    .add-group-content-space {
      height: 6px;
      position: relative;
      left: -10px;
      width: 103%;
      background-color: $white-smoke;
    }

    .create-section {
      display: flex;
      justify-content: space-between;

      .count-selected {
        cursor: default;
        text-align: left;
        margin-left: 22px;
        width: 220px;
        margin-top: 30px;
        font-size: 16px;
      }

      .create-btn {
        margin-right: 22px;
        margin-top: 14px;
      }
    }
  }

  .edit-product-publish-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 334px;
    min-height: 118px;
    padding: 0 20px;

    .publish-content {
      font-size: 16px;
      color: $charcoal-light;

      .publish-head {
        color: $dim-gray;
        font-weight: bolder;
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }

  .edit-product-save-modal {
    width: 400px;
    min-height: 160px;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;

    .nutrition-info-popup-container {
      width: 25%;

      .nutrition-image {
        margin-bottom: 0;
        margin-top: 0;
      }
    }

    .publish-content {
      width: 70%;

      .publish-head {
        text-align: left;
        color: $black;
      }

      .button-container {
        justify-content: flex-end;
        margin-top: 30px;
        font-weight: 700;
      }
    }
  }

  .select-group-modal {
    .select-group-modal-content {
      padding: 12px 30px;
      width: 471px;

      .select-group-modal-heading {
        color: $black;
      }

      .select-group-modal-checkbox {
        text-align: initial;
        padding: 12px;
      }

      .select-group-modal-btn-container {
        display: flex;
        justify-content: flex-end;
        gap: 20px;
      }
    }
  }
}
