.users {
  font-family: $font-family-averta;
  &-block-wrapper .block-wrapper-container {
    padding-left: 0;
    padding-right: 0;
  }
  .user-invite-main-section{
    display: flex;
    justify-content: space-between;
 }
  .users-details-container {
    position: relative;
    padding-bottom: 10px;

    .table-head,
    .user-content {
      display: flex;
      padding: 3px 52px;
    }

    .table-head {
      .name-head,
      .permission-head {
        width: 50%;
        line-height: 45px;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .line-break {
      height: 1px;
      width: 100%;
      background: $light-gray;
    }

    .user-content {
      margin-top: 20px;

      .name-part {
        display: flex;
        flex-direction: column;
        align-items: baseline;
        width: 50%;
        margin-top: 40px;

        .name-coloumn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: auto;

          .name-symbol,
          .name {
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .name-symbol {
            height: 37px;
            width: 37px;
            background-color: red;
            border-radius: 50%;
            color: $white;
            font-weight: 600;
            line-height: 45px;
            font-size: $font-size-19;
          }

          .name {
            margin-left: 23px;
            line-height: 56px;
            margin-bottom: 0px;
          }
        }
      }

      .controls-section {
        width: 38%;

        .controls-header,
        .controls {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
        }

        .controls-header {
          margin-bottom: 15px;

          div {
            font-weight: 400;
            line-height: 45px;
            font-size: 14px;
            color: $black;
          }
        }

        .controls {
          margin-bottom: 32px;

          .checkbox {
            margin-left: 8px;
            width: max-content;
          }
        }
      }
    }

    .invite-section {
      margin: 15px 0 15px 50px;
    }
    .no-users-found{
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  // TMP solution for green-button
  &-green-button:disabled {
    opacity: 1;
    background-color: rgb(78 186 54 / 60%);
  }
}
