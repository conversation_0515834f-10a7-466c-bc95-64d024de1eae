.setting {
  &-main {
    padding-inline: 23px 8px;
  }

  &-container {

    &-title {
      margin-bottom: 16px;
      font-size: 14px;
      font-weight: 600;
      font-family: $font-family-averta;
      line-height: 22px;
    }

    &-field {
      width: min(425px, 100%);
    }

    &-block {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 20px 30px;
      margin-bottom: 25px;

      &-column {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    &-create {
      padding-top: 15px;
      margin-bottom: 30px;
    }
  }

  &-project {

    &-actions {
      display: flex;
      flex-wrap: wrap;
      gap: 25px;
    }
  }
}
