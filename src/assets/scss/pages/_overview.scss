  .dashboard-main-section {
    font-family: $font-family-averta;
    padding: 0 8px;

    .quick-links-header {
      color: $graphite-gray;
    }

    .quick-link-list-container {
      display: flex;
      margin-top: 26px;
      gap: 35px;

      .quick-link-list-main {
        height: 160px;
        width: 167px;
        background-color: $white;
        border-radius: 8px;
        cursor: pointer;

        .quick-link-list {
          height: 100%;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          text-align: center;

          .quick-link-image {
            width: 28px;
            height: 28px;
          }

          .quick-link-title {
            padding: 14px 30px;
          }
        }
      }
    }

    .recent-activity-main-section {
      margin-top: 40px;
      color: $graphite-gray;
    }

    .recent-activity-container {
      padding: 35px 0;
      margin-top: 26px;
      background-color: $white;
      border-radius: $border-radius;

      .recent-activity-content {
        display: flex;
        justify-content: center;
        color: $shadow-gray;
      }
    }

    .recipes-main-section {
      margin-top: 30px;
      color: $graphite-gray;
    }

    .getting-started-main-section {
      margin-top: 40px;
      color: $graphite-gray;
    }

    .getting-started-main-container {
      background-color: $white;
      padding: 22px 22px;
      border-radius: 8px;
      display: flex;
      margin-top: 20px;
      align-items: center;
      min-height: 360px;

      .getting-started-left-container {
        width: 50%;
      }

      .getting-started-left-section {
        display: flex;
        margin-bottom: 14px;
        align-items: center;

        .radio-button-container {
          width: 10%;
        }

        .getting-started-radio-button {
          display: block;
          position: relative;
          height: 28px;
          width: 28px;
          border-radius: 50%;
          cursor: pointer;
          border: 1.5px solid $green;
          -webkit-user-select: none;
          -moz-user-select: none;
          user-select: none;

          input {
            position: absolute;
            opacity: 0;
            left: 0px;
            cursor: pointer;
            height: 28px;
            width: 28px;
            z-index: 9;
          }

          .checkmark {
            position: absolute;
            top: 0px;
            left: 0px;
            height: 26px;
            width: 26px;
            background-color: $gentle-whisper-gray;
            border-radius: 50%;
          }
        }

        .getting-started-radio-button input:checked ~ .checkmark {
          background-color: $green;
        }

        .checkmark:after {
          content: "";
          position: absolute;
          display: none;
        }

        .getting-started-radio-button input:checked ~ .checkmark:after {
          display: block;
        }

        .title-and-time-section {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 90%;
          margin-left: 20px;
        }

        .getting-started-title-container {
          width: 70%;

          .getting-started-title {
            font-size: 14px;
            font-weight: 400;
            color: $black;
            cursor: pointer;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }

        .getting-started-total-time {
          display: flex;
          justify-content: flex-end;
          width: 30%;
          color: $stone-gray;
          padding-left: 30px;
          text-align: end;
        }
      }

      .getting-started-right-section {
        width: 50%;
        padding: 38px;

        .getting-started-video-container {
          border-radius: 8px;
          height: 216px;
          width: fit-content;
          background-color: $grainsboro-gray;

          @media only screen and (max-width: 1200px) {
            height: auto;
          }
        }
      }

      .video-section {
        text-align: center;
      }

      #getting-started-video-controls {
        left: 50px;
        margin-top: 68px;
        position: relative;
      }

      video::-webkit-media-controls-mute-button,
      video::-webkit-media-controls-volume-slider {
        display: none !important;
      }

      video::-webkit-media-controls-volume-slider {
        display: none !important;
      }

      .getting-started-full-screen-image {
        width: 16px;
        height: 16px;
      }

      .getting-started-full-screen-icon {
        position: absolute;
        bottom: 88px;
        right: 105px;
        background: none;
        color: inherit;
        border: none;
        padding: 0;
        font: inherit;
        cursor: pointer;
        outline: inherit;
      }

      .play-button-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: auto;
        pointer-events: none;
      }

      #circle-play-b {
        cursor: pointer;
        pointer-events: auto;
      }

      #getting-started-play-video-icon-image {
        height: 54px;
      }

      #getting-started-play-pause {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: none;
        border: none;
        padding: 0;
        font: inherit;
        cursor: pointer;
        background: $transparent;
      }

      .getting-started-seek {
        position: absolute;
        top: -35px;
        left: 6px;
        width: 73%;
        accent-color: $white;
        border: none;
      }

      #getting-started-seek-bar {
        width: 100%;
        height: 5px;
        cursor: pointer;
      }

      #getting-started-mute {
        position: absolute;
        background: none;
        bottom: 89px;
        right: 64px;
        color: none;
        border: none;
        padding: 0;
        font: inherit;
        cursor: pointer;
      }

      #getting-started-mute img {
        border-radius: 45%;
        width: 16px;
        height: 16px;
        background: $transparent;
        z-index: 999;
      }

      input[type="range"]::-moz-range-progress {
        background-color: $white;
      }

      input[type="range"]::-moz-range-track {
        background-color: $slate-gray;
      }

      input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        height: 3px;
        width: 7px;
        border-radius: 0px;
        cursor: pointer;
        margin-top: -3px;
      }

      @media screen and (-webkit-min-device-pixel-ratio: 0) {
        input[type="range"] {
          background-color: $slate-gray;
        }

        input[type="range"]::-webkit-slider-runnable-track {
          height: 10px;
          -webkit-appearance: none;
          color: $white;
        }

        input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          height: 3px;
          width: 7px;
          border-radius: 0px;
          margin-bottom: 8px;
        }
      }

      input[type="range"]:focus {
        outline: none;
      }

      .getting-started-oval {
        outline: inherit;
        background-color: $misty-black;
        padding: 8px;
        border-radius: 50%;
        margin-bottom: 2px;
      }

      #getting-started-video-player {
        position: relative;
        cursor: pointer;
      }

      .getting-started-seek-container {
        position: relative;
        text-align: center;
        margin: 0px 10px;
        padding: 0px 10px;
        width: calc(100% - 20px);
        background-color: $slate-gray;
      }

      #getting-started-video-container-popup {
        border-radius: 8px;
        height: 216px;

        @media only screen and (max-width: 1200px) {
          height: auto;
        }
      }
    }
  }