.create-page-main-container {
  font-family: $font-family-averta;

  .create-page-section {
    display: flex;

    .left-section {
      width: 30%;
      height: 100vh;
        img {
          height: 100%;
        }
    }

    .right-section {
      width: 70%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .create-project-section {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .innit-iq-logo {
          max-width: 140px;
          max-height: 120px;
          padding-bottom: 20px;

          img {
            height: 100%;
            width: 100%;
          }
        }

        .create-project-zone {
          background: $white;
          box-shadow: 0 0 13px $box-shadow;
          padding: 40px 50px;
          margin-top: 40px;

          .heading-text {
            color: $black;
            font-weight: 700;
            font-size: 24px;
          }

          .project-name {
            margin-top: 20px;

            .project-name-heading {
              color: $gunmetal-grey;
              font-size: 16px;
              font-weight: 400;
            }

            .project-name-input {
              margin-top: 10px;
              font-weight: 400;
              font-size: 16px;
              color: $jet-black;
              width: 400px;
              height: 50px;
              border-radius: 4px;
              background-color: $pearl-mist;
              border: 1px solid $grainsboro;
              padding: 1px 14px;
            }
          }

          .create-project-button-container {
            margin-top: 40px;

            .create-project-button {
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 0 21px;
              border-radius: 50px;
              border: 0;
              background-color: $green;
              text-shadow: 0 -1px 0 $faint-black;
              color: $white;
              font-weight: 700;
              box-shadow: 0 2px 4px 0 $box-shadow;
              margin: 5px;
              text-align: center;
              cursor: pointer;
              min-width: 141px;
              height: 45px;
              font-size: $font-size-base;
            }

            .disable-button {
              opacity: 0.6;
              pointer-events: none;
            }
          }
        }
      }
    }
  }
}
