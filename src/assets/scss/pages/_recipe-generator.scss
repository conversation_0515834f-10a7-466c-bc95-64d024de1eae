.iq-r-g {
  &-container {
    width: 100%;
    margin-bottom: 34px;

    &-header {
      display: flex;
      align-items: baseline;
      gap: 40px;
      width: 100%;
      margin-bottom: 16px;

      &-title {
        display: inline-block;
        margin-bottom: 0 !important;
        padding-inline: 0 !important;
        padding-bottom: 2px;
        border-bottom: 2px solid $green-dark;
        cursor: default;
      }

      // TODO: [Button] It must be a text button component
      &-btn {
        width: auto;
        height: auto;
        margin-bottom: 0 !important;
        padding-inline: 0 !important;
        padding-bottom: 2px;
        transition: none;
        border-bottom: 2px solid transparent;

        &-active {
          color: $green-dark;
          border-color: $green-dark;
        }

        &-right {
          margin-left: auto;
        }

        &:disabled {
          color: $box-shadow;
        }
      }
    }

    &-body {
      width: 100%;
      height: 415px;
      padding: 20px 10px;
      background-color: $white;
      border-radius: 8px;
    }

    &-prompt-group {
      display: flex;
      gap: 45px;

      &-wrapper {
        width: 100%;
        position: relative;
      }
    }

    &-foodlm-prompt-group {
      width: 86%;
    }
  }

  // prompt history dropdown

  &-history-dropdown-container {
    position: absolute;
    top: 0;
    z-index: 19;
    width: 613px;
    padding: 10px 13px;
    border-radius: 13px;
    background-color: $white;
    box-shadow: 0px 1px 10px 0 $box-shadow;

    &-inner-box {
      margin-top: 12px;
      overflow-y: scroll;
      max-height: 410px;

      &-name,
      &-prompt-name {
        display: flex;
        width: 98%;
        padding: 0px 10px;
        border-radius: 6px;
        font-family: $font-family-averta;

        &-details {
          width: inherit;
          height: auto;
          text-align: left;
          margin: 10px 0px;

          .image-icon-container {
            img {
              width: 14px;
              height: 15px;
              margin-left: 7px;
              margin-bottom: 5px;
            }
          }
        }

      }

      &-name {
        font-weight: $font-weight-semi-bold;
        font-size: $font-size-14;
        color: $almost-black;
        margin: 10px 0px;
      }

      &-prompt-name {
        font-size: $font-size-base;
        font-weight: $font-weight-normal;
        color: $graphite-gray;

        &:hover {
          cursor: pointer;
          background-color: $mint-cream;
        }
      }

      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: $grainsboro;
        background-clip: content-box;
        border-radius: 3px;
      }
    }
  }

  // recipe-prompt ---
  &-prompt {
    z-index: 2;
    display: flex;
    height: 44px;
    width: 100%;
    border-radius: 20px;
    border: 1px solid $link-water-haze;

    &-field {
      flex: 1 1 100%;
      max-width: calc(100% - 161px);
      height: 100%;
      padding: 0 22px;
      background-color: $white;
      border-radius: 20px 0 0 20px;
      border: none;
      box-shadow: 1px 1px 1px $ink-wash-black;
      color: $midnight-charcoal;
      font-family: $font-family-averta;
      font-size: 16px;
      font-weight: 400;
      font-style: normal;
      letter-spacing: 0;
      caret-color: $midnight-charcoal;

      &::-webkit-input-placeholder {
        color: $spanish-gray;
      }

      &::-moz-placeholder {
        color: $spanish-gray;
      }
    }

    &-btn {
      flex: 1 1 161px;
      max-width: 161px;
      height: 100%;
      display: flex;
      justify-content: center;
      place-items: center;
      gap: 10px;
      padding: 0 8px;
      background-color: $gable-green;
      border-radius: 0 20px 20px 0;
      border: none;
      box-shadow: 0px 1px 5px $shadow-black;
      color: $white;
      font-size: 16px;
      font-family: $font-family-averta;
      font-weight: 700;
      letter-spacing: 0;
      text-align: center;

      >img {
        display: inline-block;
        width: 16px;
        height: 16px;
      }
    }
  }

  // recipe models ---
  &-recipe-models {
    z-index: 1;
    width: 100%;
    height: auto;
    min-height: 40px;
    margin-top: -20px;
    padding-top: 20px;
    border-radius: 0 0 20px 20px;
    border: 1px solid $link-water-haze;
    border-top: none;

    &-wrapper {
      padding: 0 22px;
    }

    &-list {
      display: flex;
      gap: 35px;

      h3 {
        margin-bottom: 0;
        padding-top: 16px;
        padding-bottom: 16px;

        // depends on control-radio size
        line-height: 20px;
      }

      &-group {

        &-text-models,
        &-image-models {
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          gap: 16px 22px;
          width: 100%;
          padding-top: 16px;
          padding-bottom: 16px;
        }

        &-text-models {
          flex: 1 1 100%;
          max-width: max-content;
        }

        &-image-models {
          flex: 1 1 100%;
          max-width: 100%;

          &-holder {
            display: flex;
            justify-content: space-between;
            width: 100%;

            &-dynamic-checkbox {
              display: flex;
              flex-wrap: wrap;

              &-row {
                display: flex;
                flex-wrap: wrap;
                gap: 16px 22px;

                &-firstrow {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 14px 22px;

                  @media only screen and (max-width: 1370px) {
                    width: 100%;
                    gap: 14px;
                  }
                }
              }
            }

            &-dropdown-btn {
              pointer-events: all;
              height: 18px;
              cursor: pointer;
              min-width: 45px;

              .icon {
                height: 100%;
                cursor: pointer;
              }

              .icon-down {
                transform: rotate(180deg);
              }
            }
          }
        }

        &-divider {
          width: auto;
          height: auto;
          min-height: 100%;
          border-right: 1px solid $link-water-haze;
        }
      }
    }
  }

  // recipe output ---
  &-recipe-output-panel {
    height: 100%;
    padding: 10px 20px;
    overflow-y: auto;
    scrollbar-color: $grainsboro $whispering-white-smoke;
    scrollbar-width: thin;
  }

  &-recipe-output-loader {
    display: flex;
    align-items: center;
    gap: 15px;
    height: auto;
    min-height: 67px;
    margin-bottom: 30px;
    padding: 12px 26px;
    background-color: $spring-bud;
    border-radius: $border-radius;

    >img {
      width: 28px;
      height: 28px;
    }

    >p {
      font-family: $font-family-averta;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      line-height: 20px;
    }
  }

  &-recipe-output-loader-text {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: $font-family-averta;
    font-size: 16px;
    font-weight: 400;
    text-align: left;
    line-height: 28px;

    >img {
      width: 19px;
      height: 19px;
    }
  }

  &-recipe-output-default {
    &-row {
      display: flex;
      gap: 15px;
    }

    &-column {
      &-1 {
        flex: 1 1 40%;
        max-width: 40%;
      }

      &-2 {
        flex: 1 1 60%;
        max-width: 60%;
      }
    }

    &-head {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 40px;
      height: 44px;

      &-sub-title {
        color: $graphite-gray;
        font-family: $font-family-averta;
        font-size: 18px;
        font-weight: 400;
        line-height: 20px;
      }

      &-second-title {
        color: $graphite-gray;
        font-family: $font-family-averta;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
      }
    }

    &-body {
      color: $graphite-gray;
      font-family: $font-family-averta;
      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
    }

    p {
      white-space: pre-line;
    }
  }

  &-recipe-output-modify {
    &-field {
      display: flex;
      flex-direction: column;
      min-height: 83px;
      margin-bottom: 10px;
      padding: 16px;
      border-radius: $border-radius;
      border: 1px solid $green;

      >textarea {
        width: 100%;
        height: 100%;
        color: $graphite-gray;
        font-family: $font-family-averta;
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0;
        text-align: left;
        line-height: 16px;
        border: none;
        background-color: $transparent;
        box-shadow: none;
      }
    }

    &-actions {
      display: flex;
      gap: 40px;
      margin-left: auto;

      >button {
        width: auto;
        height: auto;
        margin-bottom: 0 !important;
        padding-inline: 0 !important;
        transition: none;
      }
    }
  }

  &-recipe-output-edit {
    height: 100%;
    max-height: 100%;
    padding: 8px;
    border-radius: $border-radius;
    border: 1px solid $green;
    overflow-y: auto;

    &-row {
      display: flex;
      gap: 40px;
    }

    &-column {
      &-1 {
        flex: 1 1 35%;
        max-width: 35%;
      }

      &-2 {
        flex: 1 1 65%;
        max-width: 65%;
      }
    }

    &-title {
      color: $graphite-gray;
      font-family: $font-family-averta;
      font-size: 18px;
      font-weight: 400;
      line-height: 44px;
    }

    &-field {
      width: 100%;
      padding: 8px 10px;
      border-radius: $border-radius;
      background-color: $shade-of-green;
      border: 1px solid $transparent;
      opacity: 1;
      color: $graphite-gray;
      font-family: $font-family-averta;
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      line-height: 16px;

      &:focus {
        border-color: $green;
      }
    }

    textarea {
      min-height: 239px;
    }

    &-title-input {
      width: 100%;
      height: 42px;
      font-size: 20px;
      font-weight: 600;
      line-height: 20px;
    }

    &-serves-input {
      width: 42px;
      height: 42px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      text-align: center;

      // necessary styling for Firefox and others
      -moz-appearance: textfield !important;
      appearance: none;
    }

    &-actions {
      display: flex;
      gap: 40px;
      justify-content: flex-end;
      padding-top: 16px;

      >button {
        width: auto;
        height: auto;
        margin-bottom: 0 !important;
        padding-inline: 0 !important;
        transition: none;
      }
    }
  }

  &-recipe-output-advance {
    font-size: 14px;
    font-family: $font-family-averta;
    font-weight: 400;
    letter-spacing: 0;
    text-align: left;
    line-height: 16px;
    white-space: pre-line;
  }

  // recipe rating ---


  &-rating {

    &-calculating {
      margin-bottom: 22px;
    }

    &-rate {
      display: flex;
      align-items: center;
      gap: 32px;
      margin-bottom: 22px;
    }

    &-question {
      display: flex;
      align-items: center;
      gap: 22px;
      margin-bottom: 24px;
    }

    &-feedback {
      width: 100%;
      padding: 30px;
      background-color: $white;
      border-radius: 8px;

      &-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        &-radio {
          display: flex;
          gap: 24px 40px;
        }
      }
    }
  }

  &-rating-group-button {
    display: flex;
    gap: 12px;
  }

  &-rating-image-group-button {
    gap: 8px;
  }

  // recipe images ---
  $recipe-images-height: 351px;

  &-recipe-images {
    display: flex;
    gap: 45px;
    flex-wrap: nowrap;
    width: 100%;
    max-height: $recipe-images-height;

    hr {
      opacity: 0.3;
      border: none;
      border-right: 1px solid $spanish-gray-silver;
      border-radius: 0;
    }

    &-camera-roll {
      width: 279px;
      min-width: 279px;
    }

    &-selector {
      flex: 1 1 100%;
      max-width: calc(100% - (279px + 91px));

      &-full-width {
        max-width: 100%;
      }
    }
  }

  // recipe image selector ---
  &-image-selector {
    width: 100%;
    max-height: $recipe-images-height;

    &-container {
      position: relative;
      width: 100%;
      max-height: $recipe-images-height;
    }

    &-loading {
      z-index: 4;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      &-body {
        width: 100%;
        max-width: 474px;
        padding: 4px 16px;
        border-radius: $border-radius;
        border: 1px solid $grainsboro;
        background-color: $white;
        text-align: center;
      }

      &-icon {
        display: inline-block;
        width: 30px;
        height: 30px;
        background-image: url("@/assets/images/icons/loader.svg?skipsvgo=true");
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
        animation: rotation 1s linear infinite;
        transform-origin: center center;
      }

      span {
        margin: 8px;
        vertical-align: middle;
      }
    }

    &-slider-wrapper {
      position: relative;
      width: 100%;
      max-height: $recipe-images-height;
      overflow: hidden;
    }

    &-slider {
      z-index: 1;
      width: auto;
      height: $recipe-images-height;
      transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);

      &-group {
        display: inline-flex;
        flex-direction: row;
        flex-wrap: wrap;
        width: auto;
        min-width: 165px;
        height: $recipe-images-height;
        margin-right: 23px;

        &:last-child {
          margin-right: 0;
        }
      }

      &-nav-btn {
        z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 50%;
        width: 42px;
        height: 42px;
        border-radius: 50%;
        border: 2px solid $white;
        background-color: $green;
        transform: translateY(-50%);

        span {
          display: inline-block;
          width: 17px;
          height: 9px;
          background-image: url("@/assets/images/icons/arrow-down.svg?skipsvgo=true");
          background-repeat: no-repeat;
          background-position: center center;
          background-size: contain;
        }

        &-prev {
          left: -12px;

          span {
            transform: rotateZ(90deg);
          }
        }

        &-next {
          right: -12px;

          span {
            transform: rotateZ(-90deg);
          }
        }
      }
    }
  }

  // recipe image selector slide ---

  &-image-selector-slide {
    position: relative;
    width: 165px;
    height: 165px;
    border: 1px solid $dark-gray;
    border-radius: $border-radius;

    > img {
      width: 100%;
      height: auto;
      border-radius: $border-radius;
      object-fit: contain;
    }

    &-zoom:hover {
      cursor: zoom-in;
    }

    &.__loading {
      border-color: transparent;
      background-color: $grainsboro-gray;

      &:nth-child(2n),
      &:nth-child(5n) {
        opacity: 0.6;
      }

      &:nth-child(3n),
      &:nth-child(4n) {
        opacity: 0.4;
      }

      &:nth-child(6n) {
        opacity: 1;
      }
    }

    &-rating {
      z-index: 3;
      cursor: default;
      display: none;
      pointer-events: none;
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px;
      border-radius: $border-radius;
    }

    &-select,
    &-limit {
      display: none;
      pointer-events: none;
    }

    &-limit {
      z-index: 2;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      border-radius: $border-radius;
      background-color: rgba(0, 0, 0, 0.56);
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      white-space: pre-line;
    }

    &-rating:hover,
    &:hover:not(.__loading) &-rating,
    &-select:hover,
    &:hover:not(.__loading) &-select {
      display: inline-block;
      pointer-events: auto;
    }

    &-limit:hover,
    &:hover:not(.__loading) &-limit {
      display: flex;
      pointer-events: auto;
    }
  }


  $recipe-images-slide-width: 305px;
  $recipe-images-slide-height: 305px;

  // recipe images slider ---
  &-slider {
    display: flex;
    gap: 26px;
    width: 100%;
    height: $recipe-images-slide-height;

    &-container {
      width: 100%;
      height: $recipe-images-slide-height;
      max-height: auto;
      overflow-y: hidden;
      overflow-x: hidden !important;
      transform: scroll(calc(var(--i, 0) / var(--n) * -100%));
      scroll-behavior: smooth;

      &::-webkit-scrollbar {
        display: none;
        height: 1px;
        width: 1px;
      }

      &::-webkit-scrollbar-track {
        display: none;
        background: $transparent;
      }

      &::-webkit-scrollbar-thumb {
        display: none;
        background: $mid-grey;
      }

      &::-webkit-scrollbar-thumb:hover {
        display: none;
        background: $midnight-grey;
      }
    }

    &-carousel {
      display: inline-flex;

      &.__slides-centered {
        justify-content: space-around;
        width: 100%;
      }
    }

    &-slide {
      width: $recipe-images-slide-width;
      height: $recipe-images-slide-height;
      margin-right: 30px;
      background-color: $transparent;
      border-radius: $border-radius;
      border: 6px solid $transparent;

      &:last-child {
        margin-right: 0 !important;
      }

      &-image {
        border-radius: $border-radius;
      }

      &-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        background-color: $white;
        border-radius: $border-radius;
        border: 6px solid $white;

        &:hover {
          cursor: zoom-in;
        }
      }

      &-img {
        width: 100%;
        height: auto;
        border-radius: $border-radius;
        object-fit: contain;
      }

      &-number {
        z-index: 1;
        cursor: default;
        position: absolute;
        top: 0;
        left: 0;
        width: 33px;
        height: 33px;
        background-color: $white;
        border-radius: 8px;
        border: 1px solid $green;
        color: $green;
        font-size: 18px;
        font-weight: 700;
        letter-spacing: 0;
        line-height: 32px;
        text-align: center;
      }

      &-rating {
        z-index: 3;
        cursor: default;
        display: none;
        pointer-events: none;
        position: absolute;
        top: 0;
        right: 0;
        padding: 2px;
        border-radius: $border-radius;
      }

      &-loader {
        z-index: 3;
        cursor: default;
        position: absolute;
        top: calc(50% - (94px / 2));
        left: calc(50% - (94px / 2));
        width: 94px;
        height: 94px;
        border-radius: 50%;
        border: 3px solid $white;
        border-top: 3px solid $green;
        border-right: 3px solid $green;
        border-bottom: 3px solid $green;
        will-change: transform;
        animation: iq-r-g-recipe-images-slide-spin 2s linear infinite;
      }

      &-btn,
      &-checked {
        display: none;
        pointer-events: none;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 94px;
        height: 94px;
        transform: translateX(-50%) translateY(-50%);

        >img {
          width: 100%;
          height: 100%;
        }
      }

      &-btn {
        z-index: 3;
      }

      &-checked {
        z-index: 4;
        cursor: default;
      }

      &.__selected {
        border-color: $green;
      }

      &.__selected &-wrapper {
        border-width: 7px
      }

      &:hover:not(.__selected) &-btn,
      &.__selected &-checked,
      &:hover &-rating,
      &-rating.__rated {
        display: inline-block;
        pointer-events: auto;
      }
    }

    &-control {
      display: flex;
      width: 44px;
      height: 100%;

      button {
        width: 40px;
        height: 40px;
        margin: auto;
        border-radius: 50%;
        color: $green;
        font-weight: 700;
        background-color: $white;
        box-shadow: 0 0 10px $box-shadow;
        cursor: pointer;
        transition: none;
      }
    }

    &-skeleton {
      justify-content: space-between;
      gap: 10px;

      &.__with-one-slide {
        justify-content: center;
      }

      &-slide {
        margin-right: 0 !important;
      }
    }

    &-skeleton &-slide-wrapper {
      cursor: default;
    }
  }

  @keyframes iq-r-g-recipe-images-slide-spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  // button ---
  &-save-btn {
    min-width: 100px;

    >img {
      width: 20px;
      height: 20px;
    }
  }

  &-rating-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    background-color: $white;
    border-radius: $border-radius;
    background-repeat: no-repeat;
    background-position: center;

    &-small {
      width: 24px;
      height: 24px;
    }

    &:hover {
      background-color: $pearl-mist;
    }

    &.__active {
      background-color: $white;
    }

    &.__thumbs-up {
      background-image: url("@/assets/images/icons/thumbs-up.svg?skipsvgo=true");

      &.__active {
        background-image: url("@/assets/images/icons/thumbs-up-active.svg?skipsvgo=true");
      }
    }

    &.__thumbs-down {
      background-image: url("@/assets/images/icons/thumbs-down.svg?skipsvgo=true");

      &.__active {
        background-image: url("@/assets/images/icons/thumbs-down-active.svg?skipsvgo=true");
      }
    }
  }

  // utils ---
  button {
    &:disabled {
      opacity: 0.6;
      box-shadow: none;
      pointer-events: none;
    }
  }
}
