.articles {

  &-expansion-panel-toggle-btn {
    min-width: auto;
    margin-right: auto;
    padding-inline: 0;
    font-size: 18px;
    font-weight: 700;
    color: $green;

    img {
      width: 19px;
      height: auto;
      rotate: 90deg;
    }

    &.__expanded {
      img {
        rotate: -90deg;
      }
    }
  }
}

// component articles-list-head
.articles-list-head {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  width: 100%;
  height: 100%;
  padding: 5px 0;

  &-title,
  &-description,
  &-actions {
    flex: 1 1 100%;
  }

  &-title,
  &-description {
    p {
      width: 100%;
      font-size: 16px;
      font-weight: 700;
    }
  }

  &-title {
    max-width: 45%;
    color: $jet-black;
  }

  &-description {
    max-width: 30%;
    color: $spanish-gray;
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    max-width: 25%;
  }

  @media ($breakpoint-desktop) {
    &-title {
      max-width: 60%;
    }
    &-description {
      max-width: 25%;
    }
    &-actions {
      max-width: 15%;
    }
  }
}

// component articles-list-content-table
.articles-list-content {

  &-table {
    &-wrapper {
      width: 100%;
    }

    &-row {
      display: grid;
      grid-template-columns: 35px minmax(72px, 10%) 11% minmax(120px, 38%) 14% minmax(120px, 20%) 70px;
      overflow: hidden;
      position: relative;
      border: 1px solid transparent;

      &-drag {
        visibility: hidden;
        cursor: move;
        pointer-events: none;
        position: absolute;
        top: -1px;
        left: 50%;
        width: fit-content;
        transform: translateX(-50%);
      }

      &:hover &-drag {
        visibility: visible;
        pointer-events: auto;
      }
    }

    &-cell {
      display: inline-flex;
      align-items: center;
      padding-left: 5px;
      padding-right: 5px;

      &:first-child {
        padding-left: 10px;
      }
      &:last-child {
        padding-right: 10px;
      }
    }

    &-head {
      background-color: $white-smoke;
      border-color: $white-smoke;
    }

    &-head &-cell {
      padding: 6px 5px;
      font-size: $font-size-12;
      font-weight: 700;
      line-height: 1.333;
      color: $spanish-gray;
      text-align: left;
      text-transform: uppercase;
    }

    &-body {
      height: 74px;
      border-top: 1px solid $simple-table-border-color;

      &:hover {
        border-radius: $border-radius;
        border-color: $green-light;
        background-color: $aqua-spring;
      }

      &:hover + & {
        border-top-color: transparent;
      }
    }

    &-body &-cell {}
  }

  &-image {
    position: relative;
    width: 60px;
    height: 60px;

    .__icon-new,
    .__icon-video {
      position: absolute;
      width: 24px;
      height: 24px;
      object-fit: cover;
    }

    .__icon-new {
      left: -8px;
      top: -6px;
    }

    .__main-image {
      width: auto;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }

    .__icon-video {
      left: 20px;
      top: 16px;
    }
  }

  &-uuid {
    font-size: $font-size-12;
    color: $gunmetal-grey;
    word-break: break-all;
  }

  &-title {
    font-size: $font-size-14;
    color: $jet-black;
    font-weight: $font-weight-bold;
  }

  &-last-update {
    font-size: $font-size-14;
    color: $stone-gray;
  }
}

.article-item {

  &-head {
    display: flex;
    align-items: stretch;
    gap: 20px;
  }

  &-details {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-top {
      display: inline-flex;
      padding: 8px 8px 0 8px;
      border-bottom: 2px dashed $sliver;
    }

    &-publish-btn {
      align-self: flex-start;
    }

    &-body {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
    }

    &-text-mark {
      vertical-align: super;
    }
  }

  &-hr {
    margin: 20px 0;
    border: 1px solid $grainsboro;
  }

  &-settings {
    &-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
      padding: 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &-name {
      flex: 1 1 130px;
      max-width: 130px;
    }

    &-actions {
      flex: 1 1 auto;
      display: inline-flex;
      gap: 100px;

      &-select {
        max-width: 450px;
      }

      &-checkbox {
        display: inline-flex;

        img {
          margin: 0 4px;
        }
      }
    }

  }

  &-pages {
    &-wrapper {
      margin-top: 20px;
    }

    &-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    &-body {
      height: auto;
      min-height: 320px;
    }

    &-drop {
      height: 100%;

      &-details {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 320px;
        padding: 5px 100px 0 40px;
        transition: all 0.3s ease;

        &-title {
          margin-bottom: 8px;
        }

        &-sub-title {
          margin-bottom: 30px;
        }

        &-img {
          width: auto;
          max-height: 281px;
        }

        &-zip-upload-btn {
          label {
            width: 170px;
          }

          input {
            width: 0;
          }
        }
      }

      &-over {
        height: 320px;
        padding: 20px;
        background-color: $aqua-spring;
        border-radius: 4px;
        transition: all 0.3s ease;

        &-body {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          height: 100%;
          border: 2px dashed $green-light;
          border-radius: 2px;

          img {
            max-height: 32px;
          }
        }
      }

    }

    &-zip-upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 40px;
      height: 320px;
      border: 1px solid $grainsboro;
      border-radius: 8px;

      &-loader {
        width: 20px;
        height: 20px;
        border: 3px solid $white;
        border-top: 3px solid $green;
        border-right: 3px solid $green;
        border-bottom: 3px solid $green;
        border-radius: 50%;
        animation: spin 2s linear infinite;
      }

      &-alert {
        width: 468px;
        padding: 18px;
        background-color: $green-peppermint;
        border: 1px solid $green-fringy-flower;
        border-radius: 4px;
        text-align: center;
      }
    }

    &-zip-content {
      display: inline-block;

      &-body {
        display: inline-block;
      }

      &-action {
        display: flex;
        gap: 10px;
        padding-inline: 4px;
      }
    }

    &-single-article {
      pointer-events: none;
      float: left;
      position: relative;
      width: 285px;
      height: 514px;
      margin: 0 24px 22px 0;
      border-radius: 4px;

      &-index {
        z-index: 1;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: calc((285px / 2) - 21px);
        bottom: 24px;
        width: 42px;
        height: 42px;
        transform: translateX(-50%);

        span {
          z-index: 3;
        }

        &:before {
          content: "";
          z-index: 2;
          opacity: 0.7;
          position: absolute;
          width: 100%;
          height: 100%;
          background-color: $black;
          border-radius: 50%;
        }
      }
    }

    &-iframe-wrapper {
      position: relative;
      width: 100%;
      height: 500px;
      overflow: hidden;

      iframe {
        overflow: hidden;
        position: absolute;
        height: 514px;
        margin-right: 24px;
        border: none;
      }
    }

    &-footer {
      margin-top: 20px;
    }
  }

  &-replace-zip-modal {
    display: grid;
    grid-template-columns: 80px auto;
    gap: 30px;
    width: 100%;
    max-width: 420px;
    padding: 10px 0;

    @media (min-width: 600px) {
      width: 420px;
    }

    &-title {
      margin-bottom: 10px;
    }

    &-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 30px;
    }

    &-file-btn {
      display: inline-flex;

      input {
        width: 0;
      }
    }
  }

  &-preview-modal {
    width: 100%;
    max-width: 650px;
    padding-bottom: 16px;

    @media (min-width: 600px) {
      width: 650px;
      max-width: 100%;
    }

    &-body {
      display: grid;
      grid-template-columns: auto 50%;
      gap: 20px;
      margin-top: 35px;
    }

    &-article-details {
      display: flex;
      gap: 14px;
      margin-bottom: 30px;

      img {
        height: 74px;
        width: 74px;
        border-radius: 5px;
        object-fit: cover;
      }
    }

    &-devices {
      &-actions {
        margin-top: 7px;

        > div {
          padding: 12px 0;
        }
      }
    }

    &-preview {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-btn-prev,
      &-btn-next {
        width: 20px;
        height: 24px;

        img {
          height: 100%;
        }
      }

      &-btn-prev {
        img {
          transform: rotate(180deg);
        }
      }

      &-container {
        overflow: hidden;
        display: flex;
        justify-content: center;
        position: relative;
        width: 246px;
        margin: 0 auto;
      }

      &-screen {
        overflow: hidden;
        position: relative;
        width: 180px;
        border-radius: 12px;
        border: 4px solid #000000;

        &-small {
          height: 284px;
        }

        &-large {
          height: 355px;
        }
      }

      &-iframe-container {
        overflow: hidden;
        position: absolute;
        top: 1px;
        left: 1px;
        width: 200%;
        height: 160%;

        iframe {
          user-select: none;
          position: relative;
          width: 81%;
          height: 100%;
          border: none;
          background-color: transparent;
          transform: scale(0.6);
          transform-origin: top left;
        }
      }

      &-iframe-hide-scroll {
        z-index: 1;
        position: absolute;
        top: 0;
        right: 0;
        width: 15px;
        height: 100%;
        background-color: $white;
      }

      &-btn-refresh {
        z-index: 99999;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 10px;
        left: 5px;
        height: 22px;
        width: 22px;
        background-color: $black;
        border-radius: 50%;

        img {
          height: 9px;
          width: 12px;
          transform-origin: center;
          transform: rotateZ(90deg);
        }
      }
    }

    &-actions {
      display: flex;
      justify-content: flex-end;
      gap: 20px;
      padding-top: 32px;
    }
  }
}

.article-new-category-modal {
  width: 100%;
  max-width: 542px;
  padding-bottom: 16px;

  @media (min-width: 600px) {
    width: 542px;
    max-width: 100%;
  }

  .loading-block {
    margin: 0 auto;
  }

  &-body {
    position: relative;
    padding: 24px 0 32px 0;
  }

  input {
    margin-top: 14px;
  }

  &-form-control-suffix {
    position: absolute;
    right: 13px;
    bottom: calc(32px + ((50px / 2) - (16px / 2)));
    width: 16px;
    height: 16px;
  }

  &-form-control-error {
    position: absolute;
    bottom: 10px;
    left: 0;
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
  }
}
