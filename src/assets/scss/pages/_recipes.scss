.recipes-page {
  .recipe-info-overview-section.content {
    margin-top: 10px;
  }

  .recipes-block-wrapper-with-select-all {
    margin-bottom: 0;
  }
}

.recipes-filter-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  min-height: 44px;

  .recipes-sub-title {
    margin-bottom: 0;
  }

  &-body {
    display: inline-flex;
    gap: 35px;

    &-filters {
      display: inline-flex;
      align-items: center;
      gap: 20px;
    }
  }
}

.recipes-block-select {
  display: flex;
  justify-content: flex-end;
  padding: 0 10px 10px;

  button {
    min-width: min-content;
    height: auto;
    padding: 0;
  }
}

.recipes-select-all-block {
  display: flex;
  justify-content: space-between;
  align-items: center;

  > div:first-child {
    display: inline-flex;
    gap: 120px;
  }

  > div:last-child {
    display: inline-flex;
    gap: 50px;
  }
}

.recipes-select-count-block {
  display: inline-flex;
  align-items: center;
  gap: 5px;

  button {
    width: 24px;
    height: 24px;

    img {
      vertical-align: baseline;
    }
  }
}

.recipes-table {
  .simple-table-head-column {
    width: auto;
  }

  .simple-table-column-select {
    width: 60px;

    > div {
      width: 24px;
      height: 24px;
    }
  }

  .simple-table-head-column-recipe-title,
  .simple-table-column-title {
    width: 260px;
  }

  .simple-table-column-title {
    .recipes-title-wrapper {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .recipes-title {
      width: 250px;
    }

    p {
      max-width: 250px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .simple-table-column-variants {
    span {
      padding-left: 25px;
    }
  }

  .simple-table-column-actions {
    width: 60px;
  }
}

.recipe-technical-issue-modal {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 580px;

  @media (min-width: 600px) {
    width: 580px;
  }

  a {
    text-decoration: none;
  }

  ol {
    counter-reset: list-counter;
    list-style: none;
    text-align: left;

    li {
      counter-increment: list-counter;
      line-height: 20px;

      &:before {
        content: counter(list-counter) ")";
        margin-right: 5px;
      }
    }
  }

  button {
    margin-left: auto;
  }
}

.recipe-preview-detail-modal-wrapper {
  padding-left: 10px;
  padding-right: 10px;
}

.recipe-preview-detail-modal {
  width: 85vw;
  max-width: 85vw;

  &-title {
    margin: 0 20px;
  }

  &-body {
    margin-top: 10px;
  }
}

.recipes-schedule-publish-modal {
  width: 100%;
  max-width: 840px;

  @media (min-width: 900px) {
    width: 840px;
  }

  &-title {
    margin-bottom: 20px;

    img {
      margin-left: 10px;
    }
  }

  &-body {
    margin-bottom: 20px;
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
  }

  &-dates-section {
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
    height: 50px;
    margin-bottom: 20px;
  }

  &-table-section {
    height: auto;
    max-height: 47vh;
  }

  &-table {
    .simple-table-head-column {
      width: auto;
    }

    .simple-table-column-title {
      width: 360px;
    }
  }
}
