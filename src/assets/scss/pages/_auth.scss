.auth {

  &-text-btn {
    line-height: inherit;
    vertical-align: baseline;

    &:hover {
      text-decoration: underline;
    }
  }

  // login
  &-login,
  &-redirecting {
    &-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 50px 32px;
      text-align: center;

      p > a {
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    &-logo {
      margin-bottom: 44px;

      > img {
        width: auto;
        height: 47px;
      }
    }
  }

  // signup
  &-signup {

    &-video-wrapper {
      width: 100%;
      max-width: 800px;
      height: auto;
      margin-left: auto;
      border-radius: 8px;
      border: 12px solid transparent;
      background-color: rgba(148, 206, 210, 0.26);
    }

    &-video-mobile-wrapper {
      width: 100%;
      min-width: 260px;
      max-width: 960px;
      height: auto;
      min-height: 100px;
      margin: 38px auto 42px;
      border-radius: 8px;
      border: 5px solid transparent;
      background-color: rgba(148, 206, 210, 0.26);
    }

    &-image {
      height: 33px;
      margin-right: 8px;
    }

    &-container {
      width: 100%;
      padding: 0 32px;
    }

    &-head {
      position: relative;
      background-image: url("@/assets/images/auth-signup-bg-image.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &-logo {
        display: flex;
        padding-top: 48px;

        > img {
          width: auto;
          height: 39px;
          margin: auto;
        }
      }

      &-body {
        display: flex;
        max-width: 1440px;
        margin-left: auto;
        margin-right: auto;
        padding: 62px 0;

        @media (min-width: $responsive-sm) {
          gap: 50px;
        }

        @media (min-width: $responsive-md) {
          padding-left: 60px;
          padding-right: 60px;
        }
      }
    }

    &-left-section {
      flex: 1 1 100%;
      max-width: 100%;
    }
    &-right-section {
      display: none;

    }


    @media (min-width: $responsive-sm) {
      &-left-section,
      &-right-section {
        flex: 1 1 100%;
      }

      &-left-section {
        max-width: 431px;
      }

      &-right-section {
        display: block;
        align-self: center;
        max-width: calc(100% - 431px);

      }

    }

    // food-lm-not-available component
    &-top-section,
    &-food-LM-text {
      display: flex;
      align-items: center;
    }
    &-top-section {
      gap: 30px;
    }
    &-food-LM-text {
      font-family: $font-family-averta;
    }
    &-now-available-section {
      height: 27px;
      padding: 0 20px;
      border-radius: $border-radius;
      background-color: rgba(77, 185, 53, 0.17);
      font-family: $font-family-averta;
      line-height: 27px;
      white-space: nowrap;
    }


    &-description,
    &-sub-description {
      &-text {
        font-family: $font-family-averta;
        text-align: center;
      }
    }

    &-description {
      margin-top: 46px;

      &-text {
        font-size: 48px;
        font-size: clamp(32px, 4vw, 48px);
        line-height: 42px;
      }
    }
    &-sub-description {
      margin-top: 32px;

      &-text {
        line-height: 32px;
      }
    }

    @media (min-width: $responsive-sm) {
      &-description {
        &-text {
          line-height: 64px;
        }
      }

      &-description,
      &-sub-description {
        &-text {
          text-align: left;
        }
      }
    }


    &-wrapper {
      width: 100%;
      max-width: 1200px;
      margin-top: 62px;
      margin-left: auto;
      margin-right: auto;

      &-mt-0 {
        margin-top: 0;
      }

      &-head {
        margin-bottom: 60px;
        text-align: center;
      }

      &-body {
        width: 100%;
      }
    }


    &-title-2 {
      display: inline-block;
      font-size: clamp(32px, 4vw, 42px);
      line-height: 44px;

      > img {
        margin-right: 9px;

        &.__icon-1 {
          width: 34px;
          height: 44px;
          margin-left: 9px;
        }

        &.__icon-unlock {
          width: 39px;
          height: 39px;
        }

        &.__icon-2 {
          width: 32px;
          height: 40px;
        }
      }

      @media (min-width: $responsive-xs) {
        display: inline-flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 5px;
      }

      .__featuring {
        color: #262729;
        font-family: $font-family-averta;
      }

      .__food {
        color: $forest-green;
        font-family: $font-family-averta;
        font-weight: 900;
        letter-spacing: -1.35px;
      }

      .__generator {
        color: $green;
        font-family: $font-family-averta;
        font-weight: 700;
      }

      .__unlock {
        color: $green;
        font-family: $font-family-averta;
        font-weight: 900;
      }

      .__food-content {
        color: #333333;
        font-family: $font-family-averta;
      }

      .__slash {
        color: #333333;
        font-family: $font-family-averta;
      }

      .__p {
        color: $green;
        font-family: $font-family-averta;
      }
    }


    // details component
    &-details {
      display: grid;
      grid-template-rows: auto auto auto;
      gap: 70px;
      width: 100%;
      padding-top: 45px;

      @media (min-width: $responsive-sm) {
        grid-template-columns: auto auto auto;
        gap: 16px;
      }
    }
    &-detail {
      position: relative;
      padding: 45px 35px;
      border-radius: 8px;
      background-color: rgba(246, 250, 250, 1);

      &-index {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: -45px;
        left: 50%;
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background-color: $forest-green;
        transform: translateX(-50%);
        color: $white;
        font-family: $font-family-averta;
        font-size: 48px;
        font-weight: 900;
        text-align: center;
      }

      &-title {
        margin: 20px 0;
        color: $green;
        font-family: $font-family-averta;
        font-size: 24px;
        font-weight: 900;
        line-height: 32px;
        text-align: center;
      }

      &-text {
        color: #666666;
        font-family: $font-family-averta;
        font-size: 20px;
        line-height: 29px;
        text-align: center;
      }
    }


    // signup-unlock component
    &-unlock {
      display: flex;
      flex-direction: column-reverse;
      gap: 32px;
      width: 100%;
      margin-bottom: 100px;

      &-col-left {
        flex: 1 1 100%;
        max-width: 100%;
      }

      &-col-right {
        display: flex;
        justify-content: center;
        flex: 1 1 100%;
        max-width: 100%;
      }

      @media (min-width: $responsive-sm) {
        flex-direction: row;
        gap: 0;

        &-col-left {
          max-width: 416px;
        }
        &-col-right {
          justify-content: flex-end;
          max-width: calc(100% - 416px);
        }
      }

      &-text {
        color: $black;
        font-family: $font-family-averta;
        font-size: 20px;
        font-weight: 400;
        text-align: left;
        line-height: 32px;
      }

      &-img {
        width: 100%;
        max-width: 550px;
        height: auto;
        object-fit: contain;
      }
    }


    // signup-costs component
    &-costs {
      padding-top: 62px;
      padding-bottom: 100px;
      background-color: rgba(246, 250, 250, 1);

      &-wrapper {
        display: flex;
        flex-direction: column-reverse;
        gap: 32px;
        width: 100%;

        &-col-left {
          flex: 1 1 100%;
          max-width: 100%;
        }

        &-col-right {
          display: flex;
          justify-content: center;
          flex: 1 1 100%;
          max-width: 100%;
        }

        @media (min-width: $responsive-sm) {
          flex-direction: row;
          gap: 0;

          &-col-left {
            max-width: 416px;
          }
          &-col-right {
            justify-content: flex-end;
            max-width: calc(100% - 416px);
          }
        }
      }

      &-text {
        color: $black;
        font-family: $font-family-averta;
        font-size: 20px;
        font-weight: 400;
        line-height: 32px;
      }

      &-img {
        width: 100%;
        max-width: 372px;
        height: auto;
        object-fit: contain;

        @media (min-width: $responsive-sm) {
          margin-right: 100px;
        }
      }
    }

    &-green-block {
      display: flex;
      justify-content: center;
      padding-top: 84px;
      padding-bottom: 60px;
      background-color: #165416;
    }

    &-block-with-dots {
      position: relative;
      width: 100%;
      height: 127px;
      background-image: url("@/assets/images/auth-signup-white-background-with-dots.png");
      background-color: $white;
      background-repeat: repeat;
    }

    &-btn {
      width: 431px;
      max-width: 100%;
      height: 65px;
      color: rgba(223, 239, 220, 1);
      font-family: $font-family-averta;
      font-size: 21px;
      font-weight: 900;
      line-height: 30px;
      border-radius: 10px;
      background-color: rgba(77, 185, 53, 1);
    }


    // action-block component
    &-action {
      width: 431px;
      max-width: 100%;
      margin: 0 auto;

      &-mt-56 {
        margin-top: 56px;
      }

      &-hint {
        margin-top: 10px;
        color: $white;
        font-family: $font-family-averta;
        font-size: $font-size-12;
        font-weight: 700;
        text-align: center;
        line-height: 26px;

        &-black {
          color: $black;
          font-size: $font-size-14;
        }

        br {
          display: none;
        }

        @media (min-width: $responsive-xxs) {
          br {
            display: initial;
          }
        }

      }

      @media (min-width: $responsive-sm) {
        margin-left: 0;
        margin-right: 0;
      }
    }


    &-light-block {
      display: flex;
      justify-content: center;
      padding-top: 100px;
      padding-bottom: 25px;
      background-color: $grayish-green;
    }


    &-footer {
      text-align: center;

      &-image-section {
        &-image {
          > a > img {
            height: 70px;
          }
        }

        &-text {
          color: $green;
          margin-left: 7px;
          margin-top: 6px;

          > a {
            text-decoration: none;
          }
        }

      }

      &-menu-section {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
        width: 100%;

        &-content {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          list-style-type: none;
          gap: 20px;

          @media (min-width: $responsive-xs) {
            flex-direction: row;
          }

          > li {
            color: $forest-green;

            > a {
              text-decoration: none;
            }
          }
        }
      }
    }
  }
}
