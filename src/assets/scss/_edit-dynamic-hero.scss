.main {
  font-family: $font-family-averta;
  .quiz-form-modal {
    width: 470px;
    height: 180px;
    .quiz-form-container {
      display: flex;
      gap: 16px;
      padding: 25px 7px;
      .quiz-form-text {
        width: 64%;
        text-align: left;
        line-height: 1.4;
        font-weight: 700;
        font-size: 20px;
        margin-top: 5px;
      }
    }
    .quiz-form-btn {
      display: flex;
      justify-content: end;
      width: 97%;
      .quiz-form-cancel-btn {
        float: right;
        color: $green;
        background-color: $white;
        font-size: 14px;
        border-radius: 50px;
        border: none;
        padding: 10px 25px;
        margin-left: 12px;
        font-weight: 800;
        cursor: pointer;
        box-shadow: 0px 1px 5px 0px $box-shadow;
        width: 108px;
        height: 44px;
      }
      .quiz-form-save-btn {
        width: 121px;
        height: 44px;
        float: right;
        color: $white;
        background-color: $green;
        font-size: 14px;
        border-radius: 50px;
        border: none;
        padding: 10px 25px;
        margin-left: 12px;
        font-weight: 800;
        cursor: pointer;
        box-shadow: 0px 1px 5px 0px $box-shadow;
      }
    }
  }
  .news-preview-modal {
    width: 675px;
    min-height: 406px;

    .news-preview-main-container {
      margin: 22px 30px;
      text-align: initial;

      .news-preview-top-section {
        display: flex;
        justify-content: space-between;

        .news-preview-heading {
          font-weight: 700;
          color: $black;
          font-size: 20px;
        }

        .news-preview-close-icon {
          .close-icon {
            height: 24px;
            width: 24px;
            cursor: pointer;
          }
        }
      }

      .news-preview-main-section {
        position: relative;
        display: flex;
        width: 100%;

        .news-preview-left-section {
          width: 57%;

          .news-preview-content-section {
            display: flex;
            width: 100%;
          }

          .news-preview-device-selection-section {
            margin-top: 15px;

            .news-device-heading {
              font-weight: 700;
              color: $black;
              font-size: 20px;
            }

            .news-option-section {
              margin-top: 7px;

              .news-preview-device-option-section {
                display: flex;
                padding: 12px 0px;

                .round {
                  position: relative;
                }

                .round label {
                  background-color: $white;
                  border: 2px solid $grainsboro;
                  border-radius: 100%;
                  cursor: pointer;
                  height: 24px;
                  left: 0px;
                  position: absolute;
                  width: 24px;

                  &:hover {
                    border: 1px solid $green-light;
                  }
                }

                .round label:after {
                  border: 2px solid $white;
                  border-top: none;
                  border-right: none;
                  content: "";
                  height: 6px;
                  left: 5px;
                  opacity: 0;
                  position: absolute;
                  top: 7px;
                  -webkit-transform: rotate(-45deg);
                  transform: rotate(-45deg);
                  width: 12px;
                  cursor: pointer;
                }

                .round input[type="radio"] {
                  visibility: hidden;
                  display: none;
                }

                .round input[type="radio"] + label {
                  background-color: $white;
                  border: 2px solid $green-light;
                }

                .round input[type="radio"] + label:after {
                  opacity: 1;
                  top: 3px;
                  left: 2px;
                  width: 16px;
                  height: 16px;
                  border-radius: 70%;
                  background: $green-light;
                }

                .news-preview-device-info {
                  display: flex;
                  justify-content: space-between;
                  margin-left: 34px;
                  margin-top: 1px;

                  .news-preview-device-name {
                    font-size: 16px;
                    font-weight: 400;
                    color: $black;
                  }
                }
              }
            }
          }
        }

        .news-preview-right-section {
          width: 246px;
          display: flex;
          justify-content: center;
          position: relative;
          overflow: hidden;
          margin-top: 30px;

          .iframe-container {
            height: 155%;
            width: 138%;
            position: absolute;
            top: 7px;
            left: 41px;
            overflow: hidden;
          }

          .website-frame {
            height: 100%;
            width: 81%;
            border: none;
            background-color: $transparent;
            transform: scale(0.6);
            transform-origin: top left;
            position: relative;
          }

          .blank-area {
            background-color: $white;
            height: 92%;
            width: 20px;
            position: absolute;
            top: 7px;
            right: 39px;
          }

          .news-preview-mobile-size {
            border-radius: 12px;
            border: 4px solid $black;
            overflow: hidden;
          }

          .news-preview-small-mobile-view {
            height: 284px;
            width: 177px;
          }

          .news-preview-large-mobile-view {
            height: 355px;
            width: 178px;
          }
        }
      }
    }
  }
  .edit-dynamic-hero {
    .main-content {
      position: relative;
      .background-image-news-section {
        background: $sliver-whisper;
        height: 200px;
        position: relative;
        width: 100%;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        display: flex;

        .background-image {
          position: absolute;
          object-fit: cover;
          width: 100%;
          height: 100%;
          filter: brightness(60%) blur(12px);
        }

        .back-btn {
          top: 40px;
          cursor: pointer;
          position: absolute;
          left: 20px;

          .back-arrow-image {
            position: relative;
            top: -2px;
            width: 18px;
            height: 14px;
            cursor: pointer;
          }

          .back-to-hero-list {
            font-size: 16px;
            margin: 0px 4px;
            color: $green;
            cursor: pointer;
            font-weight: $font-weight-bold;
          }
        }

        .head-btn {
          position: absolute;
          right: 60px;
          display: flex;
          align-items: center;
          flex-direction: row-reverse;
          top: 32px;

          .cancel-btn {
            float: right;
            background-color: $white;
            color: $green;
            font-size: 14px;
            border-radius: 50px;
            border: 1px solid $subtle-whisper-grey;
            padding: 10px 25px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }

          .disable {
            opacity: 0.5;
            pointer-events: none;
          }

          .save-btn {
            float: right;
            background-color: $green;
            color: $white;
            font-size: 14px;
            border-radius: 50px;
            border: none;
            padding: 10px 25px;
            margin-left: 12px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }
        }

        .edit-organization-isin {
          font-weight: 400;
          font-size: 16px;
          color: $white;
          position: absolute;
          top: 82px;
          left: 26px;
        }
      }
      .news-intro-section {
        position: absolute;
        top: 75px;
        width: 92%;
        background: $white;
        height: auto;
        border-radius: 8px;
        margin: 13px 24px;
        padding: 12px 16px;
        .news-intro-container {
          display: flex;
          justify-content: space-between;
          .news-intro-inner-container {
            display: flex;
          }
          .news-icon {
            width: 19px;
            height: 20px;
            margin-top: 3px;
          }
          .news-heading {
            font-size: 20px;
            font-weight: 700;
            color: HSL(0, 0%, 0%);
            padding-bottom: 3px;
            margin-left: 5px;
          }
          .news-date-picker-container {
            display: flex;
          }
          .start-date-text {
            font-size: 16px;
            font-weight: 700;
            line-height: 10px;
            color: $jet-black;
            margin-right: 20px;
            align-self: center;
          }
          .compulsory-field {
            margin-left: 5px;
            color: $red !important;
          }
        }
        .image-input-container {
          display: flex;
          position: relative;
          .news-image-section {
            width: 120px;
            margin-top: 15px;
            margin-right: 10px;
            height: 120px;
            border-radius: 4px;
            .news-image-upload {
              width: 120px;
              height: 120px;
              border-radius: 4px;
            }
          }
          .news-input {
            width: 88%;
            margin-top: 25px;
            position: relative;
            .news-input-container {
              border-bottom: 1px dashed HSL(0, 0%, 76%);
              position: relative;
              .asterisk-input {
                color: $cinnabar;
                position: absolute;
                left: 223px;
                top: 8px;
              }
              .news-input-text {
                border: none;
                height: 40px;
                width: 80%;
                font-size: 24px;
                font-weight: 700;
                color: $black;
              }
              ::placeholder {
                font-size: 24px;
                color: $sliver;
                font-weight: 700;
              }
            }
            .news-image-format {
              font-weight: 400;
              font-size: 16px;
              position: absolute;
              bottom: 15px;
              color: HSL(0, 0%, 49%);
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              .warning-asterisk-input {
                color: $cinnabar;
              }
              .delete {
                cursor: pointer;
                align-items: center;
                font-weight: 700;
                font-size: 14px;
                width: 100%;
                text-align: right;
                position: relative;
                right: 14px;

                span {
                  color: $fiery-red-blaze;
                  position: relative;
                  top: 2px;
                  left: 6px;
                }
              }
            }
          }
          .publish-btn {
            position: absolute;
            right: 0px;
            width: 130px;
            top: 20px;
            .text {
              position: relative;
              left: 14px;
              top: 4px;
              font-weight: 700;
              font-size: 16px;
              color: $black;
            }

            .switch {
              position: relative;
              display: inline-block;
              width: 42px;
              height: 26px;
              margin-left: 20px;

              input {
                opacity: 0;
                width: 0;
                height: 0;
              }
            }

            .inactive-publish {
              opacity: 0.5;
              pointer-events: none;
            }

            .slider-round {
              position: absolute;
              cursor: pointer;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-color: $light-white;
              -webkit-transition: 0.4s;
              transition: 0.4s;
              border-radius: 30px;

              &:before {
                position: absolute;
                content: "";
                height: 23px;
                width: 23px;
                left: 2px;
                bottom: 2px;
                background-color: $white;
                -webkit-transition: 0.4s;
                transition: 0.4s;
                border-radius: 50%;
              }
            }

            input {
              &:checked {
                + {
                  .slider-round {
                    background-color: $green;

                    &:before {
                      -webkit-transform: translateX(15px);
                      -ms-transform: translateX(15px);
                      transform: translateX(15px);
                    }
                  }
                }
              }

              &:focus {
                + {
                  .slider-round {
                    box-shadow: 0 0 1px $green;
                  }
                }
              }
            }
          }
        }
      }
    }
    .edit-form-container-dynamic {
      background: white;
      border-radius: 8px;
      margin-left: 25px;
      margin-bottom: 20px;
      box-shadow: 0 0 13px $box-shadow;
      padding: 20px;
      width: 92%;
      margin-top: 100px;
      .recipe-variant-notes-text-container-dynamic {
        width: 100%;
        display: flex;
        .form-title-dynamic {
          width: 100%;
          .form-title-header-dynamic {
            margin-right: 10px;
            text-transform: capitalize;
            font-size: 16px;
            font-weight: 700;
            color: $black;
            .compulsory-field {
              margin-left: 5px;
              color: $red !important;
            }
          }
        }
        .preview-section-dynamic {
          width: 100%;
          display: flex;
          justify-content: end;
          .image-preview-dynamic {
            width: 18.4px;
            height: 19px;
            opacity: 0.5;
            margin-right: 2px;
          }
        }
      }
      .description-section-dynamic {
        position: relative;
        .description-length {
          position: absolute;
          right: 10px;
          font-size: 12px;
          bottom: 20px;
          font-weight: 400;
          line-height: 1.5;
        }
        .description-dynamic {
          width: 100%;
          resize: none;
          background: $pristine-white;
          border-radius: 4px;
          padding: 10px;
          margin: 5px 0;
          border: 1px solid $ethereal-whisper-gray;
          height: 131px;
          color: $charcoal-light;
        }

        .description-notes-dynamic {
          width: 100%;
          resize: none;
          background: $pristine-white;
          border-radius: 4px;
          padding: 10px;
          margin: 5px 0;
          border: 1px solid $ethereal-whisper-gray;
          height: 108px;
          color: $charcoal-light;
        }
      }
      .event-details {
        display: flex;
        gap: 23px;
        .sub-title-container {
          width: 58%;
          .sub-title-header {
            color: $black;
            font-size: 16px;
            font-weight: 700;
            font-family: $font-family-Helvetica;
            .asterisk-input {
              color: red;
            }
          }
          .sub-text {
            width: 100%;
            background-color: $feather-gray;
            height: 43px;
            color: $gunmetal-grey;
            font-size: 16px;
            border: 1px solid $ethereal-whisper-gray;
            border-radius: 4px;
          }
        }
        .event-container {
          width: 44%;
          .sub-title-header {
            color: $black;
            font-size: 16px;
            font-weight: 700;
            font-family: $font-family-Helvetica;
            .asterisk-input {
              color: red;
            }
          }
          .sub-text {
            width: 100%;
            background-color: $feather-gray;
            height: 43px;
            color: $gunmetal-grey;
            font-size: 16px;
            border: 1px solid $ethereal-whisper-gray;
            border-radius: 4px;
          }
        }
      }
      .cta-container {
        width: 100%;
        margin-top: 20px;
        .cta-text {
          margin-right: 10px;
          text-transform: capitalize;
          font-size: 16px;
          font-weight: 700;
          color: $black;
          .compulsory-field {
            margin-left: 2px;
            color: $red !important;
          }
        }
        .cta-section {
          width: 100%;
          display: flex;
          margin-bottom: 20px;
          .cta-input {
            position: relative;
            right: 4px;
            display: flex;
            width: 375px;
            height: 46px;
            margin-top: 12px;
            border: 2px solid $green-light;
            border-radius: 23px;
            .cta-input-length {
              position: absolute;
              right: 14px;
              font-size: 12px;
              bottom: 11px;
              font-weight: 400;
              line-height: 1.5;
            }
            .cta-input-text {
              height: 42px;
              width: 307px;
              border-radius: 23px;
              font-size: 16px;
              opacity: 0.7;
              position: relative;

              left: 3px;
              text-align: center;
              border: none;
            }
          }
          .cta-url {
            margin-top: 12px;
            margin-left: 36px;
            width: 526px;
            height: 46px;
            border-radius: 4px;
            background-color: $feather-gray;
            border: 1px solid $grainsboro;
            display: flex;
            justify-content: end;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            .correct-icon {
              position: absolute;
              z-index: 9;
              width: 16.8px;
              height: 14px;
              align-self: center;
            }
            .wrong-icon {
              position: absolute;
              z-index: 9;
              width: 18px;
              height: 18px;
              align-self: center;
            }
            .input-loading {
              position: absolute;
              z-index: 9;
              height: 50px;

              .loader-image {
                position: absolute;
                border: 3px solid $white;
                border-radius: 50%;
                border-top: 3px solid $green;
                border-right: 3px solid $green;
                border-bottom: 3px solid $green;
                width: 22px;
                height: 22px;
                -webkit-animation: spin 2s linear infinite;
                animation: spin 2s linear infinite;
                right: 8px;
                top: 11px;
              }
            }
            .cta-input-url {
              border: none;
              width: 522px;
              background-color: $feather-gray;
              height: 43px;
              color: $gunmetal-grey;
              font-size: 16px;
              padding-left: 18px;
            }
          }
          .error-validation {
            position: absolute;
            display: flex;
            width: 92%;
            right: 87px;
            margin-top: 64px;
            color: $red-orange;
            font-size: 14px;
            justify-content: center;
          }
        }
      }
      .info-section {
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 13px;
        color: $gunmetal-grey;
        .compulsory-field {
          margin-left: 2px;
          color: $red !important;
        }
      }
    }
  }
}
