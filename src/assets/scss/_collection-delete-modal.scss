.collection-delete-modal-container {
  text-align: left;
  display: flex;
  flex-direction: inherit;
  justify-content: space-between;
  align-items: normal;
  margin-top: 30px;
  padding: 0 77px 14px 29px;
  width: 532px;

  .collection-delete-image {
    img {
      width: 29px;
      height: 35px;
      object-fit: cover;
      margin-top: -4px;
    }
  }

  .collection-delete-content {
    font-family: $font-family-averta;
    font-weight: bold;
    .collection-delete-title {
      position: relative;
      left: -8px;
      color: $black;
      font-size: 20px;
    }
    .collection-text {
      opacity: 1;
      color: $charcoal-gray;
      font-size: 20px;
      font-weight: 700;
      font-style: bold;
      letter-spacing: 0px;
      font-family: $font-family-averta;
      text-align: left;
      line-height: 26px;
    }

    .collection-button-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 10px;
      position: relative;
      left: 30px;
      margin-top: 40px;
    }
  }
}
