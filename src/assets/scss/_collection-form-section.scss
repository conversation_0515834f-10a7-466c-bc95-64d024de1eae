.form-content-section {
  width: 100%;
  background-color: $white;
  border-radius: 8px;
  position: relative;
  height: auto;
  font-family: $font-family-averta;

  .form-content-inner-section {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 134px;
    padding: 22px 35px;

    .content {
      .collection-name {
        font-size: 14px;
        color: $graphite-gray;
        font-weight: 600;

        .compulsory-collection-name-field {
          color: $fiery-red-blaze;
          font-weight: 900;
        }
      }

      .publish-section {

        .disable {
          opacity: 0.5;
          cursor: default;
        }

        .publish-btn {
          width: 130px;
          top: 20px;
          right: 25px;
          position: absolute;

          .text {
            position: relative;
            left: 14px;
            top: 4px;
            font-weight: 700;
            font-size: 16px;
            color: $graphite-gray;
          }
          .toggle-section{
            background: none;
            border: none;
          }
        }
      }

      .input-delete-container {
        display: flex;
        justify-content: space-between;

        .input-box {
          width: 425px;
          height: 50px;
          border: 1px solid $grainsboro;
          border-radius: 4px;
          margin-top: 15px;
          display: flex;
          background-color: $pearl-mist;

          input {
            width: 100%;
            height: 100%;
            border: none;
            padding: 10px 10px;
            font-size: 16px;
            background-color: $pearl-mist;
            font-weight: 400;
            color: $graphite-gray;
          }

          ::placeholder {
            opacity: 0.5;
          }

          .collection-name-count {
            text-align: center;
            margin-top: 14px;
            margin-right: 15px;
            color: $jet-black;
            opacity: 0.5;
          }
        }

        .delete-section {
          position: relative;
          top: 50px;

          .disable-publish {
            cursor: default;
            opacity: 0.5;
            z-index: 0;
          }

          .delete {
            color: $fiery-red-blaze;
            cursor: pointer;

            .delete-collection-button {
              background: none;
              border: none;
              img {
                margin-right: 5px;
                width: 22px;
                height: 22px;
              }
              span {
                color: $fiery-red-blaze;
              }
            }
          }
        }
      }
    }
  }
}