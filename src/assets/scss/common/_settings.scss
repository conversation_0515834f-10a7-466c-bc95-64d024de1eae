$line-height-base: 1.3;

$shell-max-width: 1224px;
$shell-gutter: 10px;

$responsive-xxs: 374px;
$responsive-xs: 767px;
$responsive-sm: 1023px;
$responsive-md: 1200px;

$breakpoint-desktop: '(min-width: #{$responsive-md})';
$breakpoint-mobile-portrait: '(max-width: #{$responsive-xxs})';
$breakpoint-mobile: '(max-width: #{$responsive-xs})';
$breakpoint-tablet-portrait: '(max-width: #{$responsive-sm})';
$breakpoint-small-desktop: '(max-width: #{$responsive-md})';
$retina: '(min-resolution: 2dppx)';

$border-radius: 8px;

$content-left-shift: 240px;
$content-padding-left: 44px;

// header
$header-height: 70px;
$header-padding-right: 40px;
$header-padding-left: calc($content-left-shift + $content-padding-left);


// content-wrapper component
$content-wrapper-top-shift: $header-height;
$content-wrapper-left-shift: $content-left-shift;

$content-wrapper-padding-top: 30px;
$content-wrapper-padding-left: $content-padding-left;
$content-wrapper-padding-right: 30px;
$content-wrapper-padding-bottom: 45px;
