.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.px-5 {
  padding-left: 5px;
  padding-right: 5px;
}

.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.visibility-hidden {
  visibility: hidden;
}

.hide-block {
  opacity: 0;
  pointer-events: none;
  display: none;
}

.disable-content {
  opacity: 0.5;
  pointer-events: none;
}

.cursor-pointer {
  cursor: pointer;
}

.clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.rotate-90 {
  transform: rotate(90deg);
}

.border-bottom-dashed {
  border-bottom: 2px dashed $sliver;
}

.rotate-270 {
  transform: rotate(270deg);
}

// ZammadForm | src/plugins/zendesk.js
.zammad-form-modal {
  z-index: 9999999 !important;
}

.iq-scrollbar {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 12px;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: $whispering-white-smoke;
  }

  &::-webkit-scrollbar-thumb {
    background: $grainsboro;
    border: 3px solid $transparent;
    border-radius: 15px;
    background-clip: content-box;
  }
}

.iq-scrollbar-small {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: $whispering-white-smoke;
  }

  &::-webkit-scrollbar-thumb {
    background: $grainsboro;
    border: 1px solid $transparent;
    border-radius: 3px;
    background-clip: content-box;
  }
}
