.edit-organizations-loader {
  cursor: default;
  position: relative;
  padding-left: 20px;
  padding-right: 60px;
  width: 100%;
  height: 100%;
  margin: 30px 0;
}

.background-image-add-organizations {
  background: $sliver-whisper;
  height: 200px;
  position: relative;
  width: 100%;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  display: flex;

  .background-image {
    position: absolute;
    object-fit: cover;
    width: 100%;
    height: 100%;
    filter: brightness(60%) blur(12px);
  }

  .back-btn {
    top: 40px;
    cursor: pointer;
    position: absolute;
    left: 20px;

    .back-arrow-image {
      position: relative;
      top: -2px;
      width: 18px;
      height: 14px;
      cursor: pointer;
    }

    .back-to-organizations {
      margin: 0px 4px;
      color: $green;
      cursor: pointer;
    }
  }

  .head-btn {
    position: absolute;
    right: 60px;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    top: 32px;
    gap: 20px;
  }

  .edit-organization-isin {
    color: $white;
    position: absolute;
    top: 82px;
    left: 26px;
  }
}

.input-organizatons-section {
  height: 160px;
  width: 100%;
  position: relative;
  margin-top: -90px;
  padding-left: 20px;
  padding-right: 60px;
  margin-bottom: -60px;

  .organizatons-input-section {
    width: 100%;
    height: 100%;
    background-color: $white;
    border: 1px solid $grainsboro;
    border-radius: 8px;
    justify-content: space-between;
    display: flex;
    position: relative;

    .organizatons-left-section {
      width: 68%;
      display: flex;

      .image-section {
        float: left;
        width: 120px;
        height: 120px;
        margin: 20px;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        background-image: url("@/assets/images/upload-image-category.png");
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        .organizations-delete-icon {
          display: block;

          .delete-organizations-icon-image {
            position: absolute;
            top: 28px;
            left: 104px;
            cursor: pointer;
            z-index: 999;
            height: 28px;
            width: 28px;
            background: $white;
            border-radius: 4px;
          }
        }

        .hover-image {
          opacity: 0;
          width: 100%;
          height: 100%;

          &:hover {
            opacity: 1;
            background-image: url("@/assets/images/edit-image.png") !important;
            background-repeat: no-repeat;
            background-position: center;
            background-color: $translucent-black;
            border-radius: 4px;
            background-size: contain;
          }
        }
      }

      .image-main-div {
        width: 120px;
        height: 120px;
        position: relative;

        .display-image-section {
          width: 120px;
          height: 120px;
          object-fit: cover;
          position: absolute;
        }

        .image-inner-container {
          position: absolute;
          height: 120px;
          width: 100%;

          .progress-image {
            height: 120px;
            width: 100%;
            background-color: $jet-black;
            border-radius: 4px;
            position: relative;

            .progress-image-content {
              display: block !important;
              position: absolute;
              width: 100%;
              text-align: center;
              margin-top: 15px;

              .upload-text {
                display: flex;
                flex-direction: column;
                margin-top: 11px;

                .upload-heading {
                  height: 18px;
                  color: $white;
                  font-family: $font-family-averta;
                }

                .upload-media {
                  height: 13px;
                  color: $white;
                }
              }
            }
          }
        }

        .replace-image-tag {
          cursor: pointer;
          position: absolute;
          top: 0px;
          text-align: left;
          color: $white;

          .upload-input {
            width: 120px;
            height: 120px;
            opacity: 0;
          }
        }
      }

      .disable {
        opacity: 0.5;
        pointer-events: none;
      }

      .organizations-detail {
        width: 75%;

        .organizations-text-section {
          margin: 20px 1px 1px 1px;
          width: 100%;
          display: flex;
          float: none;
          position: relative;

          .organizatons-title {
            width: 100%;
            border: none;
            border-bottom: 4px dashed $sliver;
            background: $transparent;
            color: $black;
            padding: 0px 0 7px 0;
            cursor: text;
            text-overflow: ellipsis;
            line-height: 16px;
          }

          ::placeholder {
            color: $sliver;
            font-weight: 700;
          }

          .compulsory-field:after {
            content: " *";
            color: $cinnabar;
            position: absolute;
            left: 324px;
            font-weight: bolder;
            height: 10px;
            cursor: default;
          }
        }
      }

      .organizatons-image-details {
        position: relative;
        top: 50px;
        color: $grey;
        font-family: $font-family-averta;
      }
    }

    .delete-btn {
      width: 20%;
      display: flex;
      align-items: flex-end;
      margin-bottom: 17px;
      position: relative;

      .faded {
        .image {
          opacity: 0.5;
        }

        .text {
          opacity: 0.5;
        }
      }

      .delete-btn-content {
        cursor: pointer;

        .image {
          cursor: pointer;
          width: 14px;
          height: 16px;
        }

        .text {
          cursor: pointer;
          position: relative;
          top: 1px;
          margin-left: 6px;
          color: $fiery-red-blaze;
        }
      }
    }
  }
}


.organizations-confirmation-modal {
  font-family: $font-family-averta;
  padding: 10px 20px;
  text-align: center;
  width: 430px;

  .organizations-confirmation-modal-content {
    .subtitle-confirmation-description {
      margin-left: -6px !important;
      margin-top: -19px !important;
      margin-bottom: 38px !important;
      color: $ruby-red;
      font-size: $font-size-12;
    }
    .confirm-exit-top-section {
      display: flex;

      .confirm-exit-image {
        min-width: 80px;
      }
      .confirm-exit-image-container {
        -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
        transform: rotate(90deg);
        height: 80px;
        min-width: 80px;
      }
    }

    .confirmation-description {
      font-size: 18px;
      text-align: left;
      margin-bottom: 20px;
      margin-left: 20px;
      margin-top: 10px;
      font-weight: 700;
    }

    .edit-description-subtitle {
      font-family: $font-family-averta;
      font-weight: 400;
      font-size: 16px;
      color: $red;
      margin-top: 10px;
    }

    .organizations-confirmation-button-container {
      display: flex;
      align-items: center;
      justify-content: right;
      gap: 20px;
    }
  }
}


.edit-filter-publish-modal {
  font-family: $font-family-averta;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 559px;
  min-height: 293px;
  padding: 0 20px;

  .input-loading {
    height: 60px;
    display: flex;
    justify-content: center;

    .loader-image {
      border: 3px solid $white;
      border-radius: 50%;
      border-top: 3px solid $green;
      border-right: 3px solid $green;
      border-bottom: 3px solid $green;
      width: 20px;
      height: 20px;
      -webkit-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
    }
  }

  .publishing-text {
    background-color: $green-peppermint;
    border-radius: 4px;
    border: 1px solid $green-fringy-flower;
    width: 468px;
    height: 57px;

    p {
      font-family: $font-family-averta;
      font-weight: 400;
      font-size: 16px;
      color: $shadow-gray;
      padding: 18px 0px;
    }
  }
}

.disable-button {
  opacity: 0.5;
  pointer-events: none;
}

.padding-zero {
  padding: 0;
}