.ingredient-table {

  .simple-table-head-column-ingredient-name,
  .simple-table-head-column-products {
    width: 40%;
  }

  .simple-table-head-column-2 {
    width: 20%;
  }

  &-name,
  &-products {
    display: inline-flex;
    gap: 5px;
    align-items: center;
  }

  &-name {
    &-tooltip {
      width: 32px;
      height: 32px;
      margin-left: 5px;
    }
  }

  &-products {
    &-tooltip {
      width: 16px;
      height: 16px;
      margin: 0 2px;
    }
  }

  &-name-tooltip > img,
  &-products-tooltip > img {
    width: 100%;
    vertical-align: baseline;
  }
}
