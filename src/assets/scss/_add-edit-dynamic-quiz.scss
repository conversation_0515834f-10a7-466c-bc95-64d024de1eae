

  .dynamic-hero-quiz-sub-container {
    margin-top: 15px;
    padding-right: 50px;
    margin-bottom: 40px;
    font-family: $font-family-averta;

    .dynamic-hero-quiz-top-container {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .back-to-dynamic-hero-list-section {
        display: flex;
        cursor: pointer;

        .back-arrow-image {
          width: 18px;
          height: 14px;

          .back-arrow-image {
            height: 100%;
            width: 100%;
          }
        }

        .back-text {
          margin-left: 10px;
          font-weight: 700;
          color: $green;
          font-size: 16px;
        }
      }

      .dynamic-hero-quiz-continue-and-cancel-section {
        display: flex;

        .cancel-button {
          button {
            background-color: $white;
            color: $green;
            font-size: 14px;
            border-radius: 50px;
            border: 1px solid $subtle-whisper-grey;
            padding: 10px 24px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }
        }

        .continue-button {
          margin-left: 18px;

          button {
            background-color: $green;
            color: $white;
            font-size: 14px;
            border-radius: 50px;
            border: none;
            padding: 10px 31px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }
        }

        .disable-button {
          pointer-events: none;
          opacity: 0.5;
        }
      }
    }
    .dynamic-hero-form-edit-id {
      position: relative;
      top: 24px;
      left: 18px;
      font-weight: 400;
      font-size: 16px;
      color: $jet-black;
    }

    .dynamic-hero-quiz-middle-container {
      margin-top: 30px;
      background-color: $white;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      padding: 15px 20px;

      .dynamic-hero-quiz-middle-section {
        .dynamic-hero-quiz-name-and-calender-section {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .dynamic-hero-quiz-form-name-section {
            display: flex;

            .dynamic-hero-quiz-form-info-question-image-section {
              height: 24px;
              width: 24px;
            }

            .dynamic-hero-quiz-form-text {
              margin-left: 10px;
              font-weight: 700;
              color: $black;
              font-size: 20px;
            }
          }

          .dynamic-hero-quiz-form-calender-section {
            display: flex;

            .news-date-picker-container {
              display: flex;
              position: relative;

              .start-date-text {
                font-size: 16px;
                font-weight: 700;
                line-height: 10px;
                color: $jet-black;
                margin-right: 20px;
                align-self: center;

                .compulsory-field {
                  margin-left: 5px;
                  color: $red !important;
                }
              }
              .disable {
                opacity: 0.5;
                cursor: not-allowed;
                pointer-events: none;
              }
            }
          }
        }

        .dynamic-hero-quiz-name-and-schedule-section {
          margin-top: 15px;
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding-bottom: 7px;
          border-bottom: 2px dashed $sliver;

          .dynamic-hero-quiz-name-section {
            position: relative;
            left: -5px;
            display: flex;
            width: 100%;

            .compulsory-quiz-field {
              position: relative;
              left: 257px;
              color: $red;
              font-weight: 900;
              font-size: 24px;
              height: 15px;
            }

            .quiz-name-field {
              width: 100%;
              border: none;
              background: $transparent;
              text-overflow: ellipsis;
            }

            ::placeholder {
              color: $slate-gray;
              font-weight: 700;
            }
          }

          .dynamic-hero-quiz-schedule-section {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .schedule-text {
              font-weight: 700;
              color: $black;
              font-size: 16px;
            }
            .disabled-text {
              opacity: 0.3;
            }

            .schedule-toggle-button {
              .switch {
                position: relative;
                display: inline-block;
                width: 42px;
                height: 26px;
                margin-left: 20px;

                input {
                  opacity: 0;
                  width: 0;
                  height: 0;
                }
              }

              .slider-round {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: $light-white;
                -webkit-transition: 0.4s;
                transition: 0.4s;
                border-radius: 30px;

                &:before {
                  position: absolute;
                  content: "";
                  height: 23px;
                  width: 23px;
                  left: 2px;
                  bottom: 2px;
                  background-color: $white;
                  -webkit-transition: 0.4s;
                  transition: 0.4s;
                  border-radius: 50%;
                }
              }
              .disabled-slider {
                cursor: default;
              }

              input {
                &:checked {
                  + {
                    .slider-round {
                      background-color: $green;

                      &:before {
                        -webkit-transform: translateX(15px);
                        -ms-transform: translateX(15px);
                        transform: translateX(15px);
                      }
                    }
                  }
                }

                &:focus {
                  + {
                    .slider-round {
                      box-shadow: 0 0 1px $green;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .delete-quiz-section {
        display: flex;
        justify-content: flex-end;

        .delete {
          padding: 18px 0px 3px 0px;
          background: none;
          border: none;
          cursor: pointer;

          span {
            color: $fiery-red-blaze;

            font-weight: 700;
            font-size: 14px;
            margin-left: 3px;
            padding-top: 2px;
            position: relative;
            top: 1px;
          }
        }
      }
    }

    .dynamic-hero-quiz-bottom-container {
      margin-top: 25px;
      background-color: $white;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      padding: 22px 20px;

      .preview-section-dynamic {
        display: flex;
        justify-content: end;
      }
      .dynamic-hero-quiz-question-section {
        .dynamic-hero-quiz-question-container {
          display: flex;
          justify-content: space-between;
        }
        .switch {
          position: relative;
          display: inline-block;
          width: 42px;
          height: 26px;
          margin-left: 10px;
          bottom: 4px;

          input {
            opacity: 0;
            width: 0;
            height: 0;
          }
        }

        .slider-round {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: $light-white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
          border-radius: 30px;

          &:before {
            position: absolute;
            content: "";
            height: 23px;
            width: 23px;
            left: 2px;
            bottom: 2px;
            background-color: $white;
            -webkit-transition: 0.4s;
            transition: 0.4s;
            border-radius: 50%;
          }
        }
        .disabled-slider {
          cursor: default;
        }
        input {
          &:checked {
            + {
              .slider-round {
                background-color: $green;

                &:before {
                  -webkit-transform: translateX(15px);
                  -ms-transform: translateX(15px);
                  transform: translateX(15px);
                }
              }
            }
          }

          &:focus {
            + {
              .slider-round {
                box-shadow: 0 0 1px $green;
              }
            }
          }
        }
        .dynamic-hero-quiz-question-heading {
          .heading-text {
            font-weight: 700;
            color: $black;
            font-size: 16px;
          }

          .compulsory-field {
            color: $red;
            font-weight: 700;
            font-size: 16px;
            height: 15px;
          }
        }

        .dynamic-hero-quiz-question-input-section {
          position: relative;
          margin: 10px 0px;
          border-radius: 4px;
          background: $pearl-mist;
          border: 1px solid $grainsboro;

          .input-text-area {
            min-height: 108px;
            background: $transparent;
            border: none;
            width: 100%;
            padding: 10px;
            font-size: 16px;
            font-weight: 400;
            color: $black;
            line-height: 16px;
          }

          .question-section-word-count-section {
            display: flex;
            justify-content: flex-end;
            padding-right: 10px;
            padding-bottom: 5px;
            font-size: 12px;
            font-weight: 400;
            line-height: 1.5;
          }
        }
      }

      .dynamic-hero-quiz-result-section {
        margin: 18px 0px;

        .dynamic-hero-quiz-result-head-section {
          display: flex;

          .dynamic-hero-quiz-result-heading {
            .heading-text {
              font-weight: 700;
              color: $black;
              font-size: 16px;
            }

            .compulsory-field {
              color: $red;
              font-weight: 700;
              font-size: 16px;
              height: 15px;
            }
          }

          .dynamic-hero-quiz-result-radio-button-section {
            display: flex;
            justify-content: space-between;
            width: 242px;
            position: relative;
            bottom: 2px;
            left: 30px;

            .radio-button-section {
              .round {
                position: relative;
              }

              .round label {
                height: 24px;
                width: 24px;
                background-color: $white;
                border: 2px solid $grainsboro;
                border-radius: 100%;
                cursor: pointer;
                position: absolute;
                left: 0px;

                &:hover {
                  border: 1px solid $green-light;
                }
              }

              .round label:after {
                position: absolute;
                top: 7px;
                left: 5px;
                border: 2px solid $white;
                border-top: none;
                border-right: none;
                content: "";
                height: 6px;
                opacity: 0;
                -webkit-transform: rotate(-45deg);
                transform: rotate(-45deg);
                width: 12px;
                cursor: pointer;
              }

              .round input[type="radio"] {
                visibility: hidden;
                display: none;
              }

              .round input[type="radio"] + label {
                background-color: $white;
                border: 2px solid $green-light;
              }

              .round input[type="radio"] + label:after {
                opacity: 1;
                top: 3px;
                left: 2px;
                width: 16px;
                height: 16px;
                border-radius: 70%;
                background: $green-light;
              }

              .result-text {
                position: relative;
                cursor: pointer;
                top: 4px;
                left: 32px;
                font-size: 16px;
                font-weight: 400;
                color: $jet-black;
                line-height: 16px;
              }
            }
          }
        }

        .dynamic-hero-quiz-result-input-section {
          margin: 12px 0px;
          min-height: 80px;
          border-radius: 4px;
          background: $pearl-mist;
          border: 1px solid $grainsboro;

          .input-text-area {
            background: $transparent;
            min-height: 80px;
            border: none;
            width: 100%;
            padding: 10px;
            font-size: 16px;
            font-weight: 400;
            color: $black;
            line-height: 16px;
          }

          .result-section-word-count-section {
            display: flex;
            justify-content: flex-end;
            padding-right: 10px;
            padding-bottom: 5px;
            font-size: 12px;
            font-weight: 400;
            line-height: 1.5;
          }
        }
      }

      .dynamic-hero-quiz-cta-link-section {
        .dynamic-hero-quiz-cta-link-heading {
          .heading-text {
            font-weight: 700;
            color: $black;
            font-size: 16px;
          }
        }

        .dynamic-hero-quiz-cta-link-input-section {
          display: flex;
          width: 100%;
          margin: 10px 0px;
          border-radius: 4px;
          background: $pearl-mist;
          border: 1px solid $grainsboro;
          height: 39px;

          .input-section {
            display: flex;
            width: 95%;

            .input-text-area {
              background: $transparent;
              min-height: 30px;
              border: none;
              width: 100%;
              padding: 5px 0px 0px 10px;
              font-size: 16px;
              font-weight: 400;
              color: $black;
              line-height: 16px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            ::placeholder {
              color: $slate-gray;
              font-weight: 400;
              font-size: 16px;
            }
          }

          .cta-link-input-verify-section {
            display: flex;
            justify-content: flex-end;
            width: 4%;
            align-items: center;

            .cta-link-progress-check {
              .loader-image {
                border: 3px solid $white;
                border-radius: 50%;
                border-top: 3px solid $green;
                border-right: 3px solid $green;
                border-bottom: 3px solid $green;
                width: 20px;
                height: 20px;
                -webkit-animation: spin 2s linear infinite;
                animation: spin 2s linear infinite;
              }
            }

            .link-image-check {
              .correct-icon {
                width: 16.8px;
                height: 14px;
              }

              .wrong-icon {
                width: 18px;
                height: 18px;
              }
            }
          }
        }

        .cta-broken-link-validation-section {
          margin: 5px 0px;

          .cta-link-broken-message {
            font-size: 14px;
            font-weight: 400;
            font-family: $font-family-arial-serif;
            color: $red-orange;
          }
        }
      }
    }
  }

  .quiz-form-modal-container {
    width: 470px;
    height: 180px;

    .quiz-form-section {
      display: flex;
      gap: 30px;
      padding: 25px 15px;

      .quiz-form-text {
        width: 64%;
        text-align: left;
        line-height: 1.4;
        font-weight: 700;
        font-size: 20px;
        margin-top: 5px;
      }
    }

    .quiz-form-btn {
      display: flex;
      justify-content: end;
      width: 97%;

      .quiz-form-cancel-btn {
        float: right;
        color: $green;
        background-color: $white;
        font-size: 14px;
        border-radius: 50px;
        border: none;
        padding: 10px 25px;
        margin-left: 12px;
        font-weight: 800;
        cursor: pointer;
        box-shadow: 0px 1px 5px 0px $box-shadow;
        width: 108px;
        height: 44px;
      }

      .quiz-form-save-btn {
        width: 121px;
        height: 44px;
        float: right;
        color: $white;
        background-color: $green;
        font-size: 14px;
        border-radius: 50px;
        border: none;
        padding: 10px 25px;
        margin-left: 12px;
        font-weight: 800;
        cursor: pointer;
        box-shadow: 0px 1px 5px 0px $box-shadow;
      }
    }
  }

  .quiz-schedule-form-modal-container {
    width: 519px;
    max-height: 255px;
    height: 255px;
    overflow: visible;

    .quiz-schedule-sub-container {
      .quiz-schedule-top-container {
        display: flex;
        justify-content: space-between;
        padding: 0px 15px;

        .schedule-text {
          font-size: 20px;
          font-weight: 700;
          color: $black;
        }

        .close-icon {
          width: 24px;
          height: 24px;
          cursor: pointer;

          .close-icon-image {
            height: 100%;
            width: 100%;
          }
        }
      }

      .quiz-schedule-middle-container {
        padding: 20px;
        position: fixed;
      }

      .quiz-schedule-bottom-container {
        position: relative;
        top: 180px;

        .quiz-schedule-button-section {
          float: right;

          .quiz-form-cancel-button {
            color: $green;
            background-color: $white;
            font-size: 14px;
            border-radius: 50px;
            border: none;
            padding: 10px 25px;
            margin-left: 12px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
            width: 108.63px;
            height: 44px;
          }

          .quiz-form-schedule-button {
            width: 128px;
            height: 44px;
            color: $white;
            background-color: $green;
            font-size: 14px;
            border-radius: 50px;
            border: none;
            padding: 10px 25px;
            margin-left: 12px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }

          .disable-schedule-button {
            pointer-events: none;
            opacity: 0.5;
          }
        }
      }
    }
  }
