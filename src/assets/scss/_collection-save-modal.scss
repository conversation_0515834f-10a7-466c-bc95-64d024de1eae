.collection-save-modal {
    width: 532px;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;
    padding: 10px;
    font-family: $font-family-averta;

    .collection-save-info-popup-container {
        width: 25%;

        .collection-save-image {
            margin-top: 23px;

            .collection-save-image-container {
                img {
                    height: 28px;
                    width: 34px;
                    margin-right: 38px;
                    margin-top: 5px;
                }
            }
        }
    }

    .collection-publish-content {
        width: 72%;
        padding-top: 22px;

        .collection-publish-head {
            font-size: 20px;
            color: $charcoal-gray;
            font-weight: 700;
            text-align: left;
            line-height: 36px;
            margin-left: -64px;
        }

        .collection-publish-head-desc {
            font-size: $font-size-base;
            color: $gunmetal-grey;
            font-weight: 400;
            text-align: left;
            padding-right: 10px;
            margin-left: -64px;
        }

        .collection-button-container {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 15px;
            margin-right: 16px;
            padding-bottom: 4px;
            margin-top: 45px;

        }
    }
}