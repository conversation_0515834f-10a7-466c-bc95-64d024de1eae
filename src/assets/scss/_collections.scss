.collection-master-list-main-section {
  padding: 20px 35px 115px 50px;
  font-family: $font-family-averta;

  .loader {
    margin-bottom: 20px;
  }

  .collection-master-list-detail-main {
    background: $white;
    width: 100%;
    padding: 38px 38px 28px 38px;
    border-radius: 8px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;

    .collection-details-count-container {
      display: flex;

      .collection-star-image img {
        width: 34px;
        height: 34px;
      }

      .collection-count-main-section {
        margin-left: 14px;

        .collection-count-text {
          display: flex;
          color: $graphite-gray;
          margin-bottom: 6px;
        }

        .count {
          color: $green;
          margin-right: 10px;
        }

        .add-collection-detail {
          color: $gunmetal-grey;
          font-weight: 400;
          font-size: $font-size-base;
          width: 90%;
          line-height: 22px;
        }
      }
    }
  }

  .collection-count-recipe-main {
    display: grid;
    grid-template-columns: 50% 50%;
    gap: 3px;
    background: $white;
    width: 100%;
    border-radius: 8px;
    margin-top: 20px;

    .collection-count-section {
      display: flex;
      padding: 30px 40px;
      border-right: 4px solid $feather-gray;

      .collection-star-image img {
        width: 34px;
        height: 34px;
      }

      .collection-count-main-section {
        margin-left: 14px;

        .collection-count-text {
          display: flex;
          color: $graphite-gray;
          margin-bottom: 6px;
        }

        .count {
          color: $green-light;
          margin-right: 10px;
        }
      }
    }

    .collection-active-recipe {
      margin-left: 14px;
      font-weight: 400;
      padding: 30px 40px;

      .collection-active-recipe-count {
        display: flex;

        .count {
          font-size: 28px;
          color: $green;
        }

        .active-recipe-text {
          color: $graphite-gray;
          font-size: 14px;
          position: relative;
          top: 14px;
          left: 14px;
        }
      }
    }
  }

  .master-list-main-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 50px 0px 20px 0px;

    .master-list-text {
      color: $graphite-gray;
    }
  }

  .collection-list-master-data-main {
    .collection-list-table-content {
      cursor: default;

      .collection-list-content {
        width: 100%;
        height: 100%;
        background-color: $white;
        border-radius: 8px;

        .collection-table {
          position: relative;

          .collection-table-head {
            border-radius: 8px 8px 0px 0px;

            .collection-title {
              color: $grey;
              font-size: $font-size-14;
              line-height: 18px;
              text-align: left;
              margin: 0 4px;

              th {
                padding: 20px 0;
                font-weight: 700;
              }

              .collection-title-section {
                padding: 20px 0 20px 20px;

                span {
                  cursor: pointer;
                }

                img.hovered-image {
                  width: 12px;
                  height: 8px;
                  margin-left: 4px;
                  cursor: pointer;
                }

                img.green-image {
                  width: 25px;
                  height: 25px;
                  cursor: pointer;
                }

                img.green-image.collection-rotate-arrow {
                  transform: rotate(-180deg);
                }

                &.green-text {
                  color: $vivid-green;
                }

                &.hovered img {
                  filter: none;
                }
              }
              
              .collection-title-button {
                background: none;
                border: none;
                cursor: pointer;
              }

              .collection-tags {
                width: 100px;
              }

              .collection-recipe-count {
                width: 100px;
              }
            }
          }

          .collection-table-body {
            border-bottom: 1px solid $grainsboro;
            height: 79px;

            .collection-title {
              width: 20%;
            }

            .collection-tags {
              width: 40%;
            }

            .collection-recipe-count {
              width: 18%;
            }

            .collection-published-date {
              width: 18%;
            }

            .collection-state {
              .published-state {
                width: 95px;
                height: 25px;
                color: $green;
                background-color: $aqua-spring;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                border-radius: 4px;

                img {
                  position: relative;
                  right: 6px;
                  width: 16px;
                  height: 16px;
                }
              }

              .unpublished-state {
                font-size: 13px;
                width: 112px;
                height: 25px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-transform: capitalize;
                color: $silver;
                background-color: $pearl-mist;
                border-radius: 4px;

                img {
                  width: 16px;
                  height: 16px;
                  position: relative;
                  right: 6px;
                }
              }
            }

            td {
              padding: 0px 10px 0px 0px;
            }

            .collection-title-text {
              padding-left: 22px;
              color: $graphite-gray;
            }

            .collection-tags-text,
            .collection-recipe-number,
            .published-date {
              width: 100%;
              font-size: 14px;
              font-weight: 400;
              color: $stone-gray;
            }

            .buttons-section {
              width: 100px;
              display: flex;
              justify-content: flex-end;
              margin: auto;
              position: relative;
              top: 18px;

              .edit-btn {
                border-radius: 4px;
                box-shadow: 0px 1px 5px 0px $box-shadow;
                height: 32px;
                width: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                img {
                  width: 16px;
                  height: 16px;
                }
              }

              .menu {
                position: relative;
                margin-right: 18px;
                top: 10px;

                .menu-container {
                  background-color: $white;
                  border-radius: 10px;
                  width: 28px;
                  height: 20px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;

                  .table-edit-btn {
                    width: 17px;
                    height: 5px;
                    padding: 0;
                    margin: 0 auto;
                    object-fit: cover;
                    z-index: 1;
                  }

                  &:hover {
                    background-color: $pure-white;
                  }
                }

                .menu-selected {
                  background-color: $aqua-spring;

                  &:hover {
                    background-color: $aqua-spring;
                  }
                }

                .menu-box {
                  display: block;
                  position: absolute;
                  right: 10px;
                  width: 151px;
                  top: 24px;
                  z-index: 2;
                  box-shadow: 0 4px 10px 0 $shadow-black,
                    0 3px 5px 0 $faint-black,
                    0 0 0 1px $shadowy-black;
                  border-radius: 8px;
                  background: $white;

                  .menu-list {
                    list-style: none;
                    background: $white;
                    border-radius: 8px;
                    margin: 13px 5px;
                    .disabled-delete {
                      color: $neutral-gray;
                      cursor: default;

                      &:hover {
                        color: $neutral-gray;
                        background: none;
                        cursor: default;
                      }
                    }

                    li {
                      height: 36px;
                      width: 141px;
                      display: flex;
                      align-items: center;
                     button{
                      text-align: left;
                      width: inherit;
                      height: inherit;
                      font-size: $font-size-base;
                      color: $black;
                      font-weight: 700;
                      padding-left: 10px;
                      border-radius: 7px;
                     }
                     .disabled-button {
                      color: $neutral-gray;
                      cursor: default;

                      &:hover {
                        color: $neutral-gray;
                        background: none;
                        cursor: default;
                      }
                    }

                      &:hover {
                        color: $white;
                        background: $green;
                        cursor: pointer;
                      }
                    }

                    .hide-data {
                      display: none;
                    }
                  }
                }
              }
            }
          }

          .collection-table-body:first-child {
            border-top: 1px solid $bright-gray;
          }
        }
      }
    }
  }
}