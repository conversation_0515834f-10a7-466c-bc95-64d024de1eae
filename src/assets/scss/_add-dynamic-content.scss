.main-section {
  font-family: $font-family-averta;
  .content-preview-modal {
    width: 675px;
    min-height: 406px;

    .content-preview-main-container {
      margin: 22px 30px;
      text-align: initial;

      .content-preview-top-section {
        display: flex;
        justify-content: space-between;

        .content-preview-heading {
          font-weight: 700;
          color: $black;
          font-size: 20px;
        }

        .content-preview-close-icon {
          .close-icon {
            height: 24px;
            width: 24px;
            cursor: pointer;
          }
        }
      }

      .content-preview-main-section {
        position: relative;
        display: flex;
        width: 100%;

        .content-preview-left-section {
          width: 57%;

          .content-preview-content-section {
            display: flex;
            width: 100%;
          }

          .content-preview-device-selection-section {
            margin-top: 15px;

            .content-device-heading {
              font-weight: 700;
              color: $black;
              font-size: 20px;
            }

            .content-option-section {
              margin-top: 7px;

              .content-preview-device-option-section {
                display: flex;
                padding: 12px 0px;

                .round {
                  position: relative;
                }

                .round label {
                  background-color: $white;
                  border: 2px solid $grainsboro;
                  border-radius: 100%;
                  cursor: pointer;
                  height: 24px;
                  left: 0px;
                  position: absolute;
                  width: 24px;

                  &:hover {
                    border: 1px solid $green-light;
                  }
                }

                .round label:after {
                  border: 2px solid $white;
                  border-top: none;
                  border-right: none;
                  content: "";
                  height: 6px;
                  left: 5px;
                  opacity: 0;
                  position: absolute;
                  top: 7px;
                  -webkit-transform: rotate(-45deg);
                  transform: rotate(-45deg);
                  width: 12px;
                  cursor: pointer;
                }

                .round input[type="radio"] {
                  visibility: hidden;
                  display: none;
                }

                .round input[type="radio"] + label {
                  background-color: $white;
                  border: 2px solid $green-light;
                }

                .round input[type="radio"] + label:after {
                  opacity: 1;
                  top: 3px;
                  left: 2px;
                  width: 16px;
                  height: 16px;
                  border-radius: 70%;
                  background: $green-light;
                }

                .content-preview-device-info {
                  display: flex;
                  justify-content: space-between;
                  margin-left: 34px;
                  margin-top: 1px;

                  .content-preview-device-name {
                    font-size: 16px;
                    font-weight: 400;
                    color: $black;
                  }
                }
              }
            }
          }
        }

        .content-preview-right-section {
          width: 246px;
          display: flex;
          justify-content: center;
          position: relative;
          overflow: hidden;
          margin-top: 30px;

          .iframe-container {
            height: 155%;
            width: 138%;
            position: absolute;
            top: 7px;
            left: 41px;
            overflow: hidden;
          }

          .website-frame {
            height: 100%;
            width: 81%;
            border: none;
            background-color: $transparent;
            transform: scale(0.6);
            transform-origin: top left;
            position: relative;
          }

          .blank-area {
            background-color: $white;
            height: 92%;
            width: 20px;
            position: absolute;
            top: 7px;
            right: 39px;
          }

          .content-preview-mobile-size {
            border-radius: 12px;
            border: 4px solid $black;
            overflow: hidden;
          }

          .content-preview-small-mobile-view {
            height: 284px;
            width: 177px;
          }

          .content-preview-large-mobile-view {
            height: 355px;
            width: 178px;
          }
        }
      }
    }
  }

  .add-content-form-hero {
    .main-content {
      position: relative;

      .background-image-content-section {
        height: 200px;
        position: relative;
        width: 100%;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        display: flex;

        .back-btn {
          top: 40px;
          position: absolute;
          left: 20px;
          border: none;
          background: none;
          cursor: pointer;

          .back-arrow-image {
            position: relative;
            top: -2px;
            width: 18px;
            height: 14px;
            cursor: pointer;
          }

          .back-to-hero-list {
            font-size: 16px;
            margin: 0px 4px;
            color: $green;
            cursor: pointer;
            font-weight: $font-weight-bold;
          }
        }

        .head-btn {
          position: absolute;
          right: 70px;
          display: flex;
          align-items: center;
          flex-direction: row-reverse;
          top: 32px;

          .cancel-btn {
            float: right;
            background-color: $white;
            color: $green;
            font-size: 14px;
            border-radius: 50px;
            border: 1px solid $subtle-whisper-grey;
            padding: 10px 25px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }

          .disable {
            opacity: 0.5;
            pointer-events: none;
          }

          .save-btn {
            float: right;
            background-color: $green;
            color: $white;
            font-size: 14px;
            border-radius: 50px;
            border: none;
            padding: 10px 25px;
            margin-left: 12px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }
        }

        .edit-organization-isin {
          font-weight: 400;
          font-size: 16px;
          color: $white;
          position: absolute;
          top: 82px;
          left: 26px;
        }
      }

      .add-content-intro-section {
        position: absolute;
        top: 75px;
        width: 92%;
        background: $white;
        height: auto;
        border-radius: 8px;
        margin: 13px 24px;
        padding: 12px 16px;

        .add-content-intro-container {
          display: flex;
          justify-content: space-between;

          .main-section {
            display: flex;
          }

          .add-content-icon {
            width: 19px;
            height: 20px;
            margin-top: 14px;
          }

          .add-content-heading {
            font-size: 20px;
            font-weight: 700;
            color: HSL(0, 0%, 0%);
            padding-bottom: 3px;
            margin: 11px 10px;
          }

          .news-date-picker-container {
            display: flex;
          }

          .start-date-text {
            font-size: 16px;
            font-weight: 700;
            line-height: 10px;
            color: $jet-black;
            margin-right: 20px;
            align-self: center;
          }

          .compulsory-field {
            margin-left: 5px;
            color: $red !important;
          }

          .calendar-section {
            display: flex;

            .inner-section {
              display: flex;
              width: 264px;
              border: 1px solid $white;
              box-shadow: $black;
              box-shadow: 0 1px 2px 0 $shadow-black;
              border-radius: 4px;
              height: 44px;
              padding: 12px 0px;
            }

            .calendar-heading {
              padding-top: 12px;
              font-size: 16px;
              font-weight: 700;
              padding-right: 12px;
            }

            .sub-section-calender {
              margin-left: 17px;
              font-size: 16px;
              color: $sliver;
              font-weight: 500;
            }

            .image-calender {
              height: 15px;
              position: absolute;
              right: 30px;
              top: 13%;
            }
          }
        }

        .image-input-container {
          display: flex;
          position: relative;

          .add-content-image-section {
            width: 120px;
            margin-top: 15px;
            margin-right: 10px;
            height: 120px;
            border-radius: 4px;

            .add-content-image-upload {
              width: 120px;
              height: 120px;
              border-radius: 4px;
            }
          }

          .add-content-input {
            width: 100%;
            margin-top: 15px;
            position: relative;

            .add-content-input-container {
              border-bottom: 1px dashed HSL(0, 0%, 76%);
              position: relative;

              .asterisk-input {
                font-weight: 700;
                color: $red;
                position: absolute;
                left: 250px;
                top: 8px;
              }

              .add-content-input-text {
                border: none;
                height: 40px;
                width: 90%;
                text-overflow: ellipsis;
              }

              ::placeholder {
                font-size: 24px;
                color: $sliver;
                font-weight: 700;
              }
            }

            .dynamic-hero-quiz-result-head-section {
              display: flex;
              margin: 20px 0px 10px 0px;

              .dynamic-hero-content-main {
                display: flex;
              }

              .dynamic-hero-quiz-result-heading {
                .heading-text {
                  font-weight: 700;
                  color: $black;
                  font-size: 16px;
                }

                .compulsory-field {
                  color: $red;
                  font-weight: 700;
                  font-size: 16px;
                  height: 15px;
                }
              }

              .dynamic-hero-quiz-result-radio-button-section {
                display: flex;
                justify-content: space-between;
                width: 242px;
                margin-left: 42px;

                .radio-button-section {
                  .round {
                    position: relative;
                  }

                  .round label {
                    height: 24px;
                    width: 24px;
                    background-color: $white;
                    border: 2px solid $grainsboro;
                    border-radius: 100%;
                    cursor: pointer;
                    position: absolute;
                    left: 0px;

                    &:hover {
                      border: 1px solid $green-light;
                    }
                  }

                  .round label:after {
                    position: absolute;
                    top: 7px;
                    left: 5px;
                    border: 2px solid $white;
                    border-top: none;
                    border-right: none;
                    content: "";
                    height: 6px;
                    opacity: 0;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                    width: 12px;
                    cursor: pointer;
                  }

                  .round input[type="radio"] {
                    visibility: hidden;
                    display: none;
                  }

                  .round input[type="radio"] + label {
                    background-color: $white;
                    border: 2px solid $green-light;
                  }

                  .round input[type="radio"] + label:after {
                    opacity: 1;
                    top: 3px;
                    left: 2px;
                    width: 16px;
                    height: 16px;
                    border-radius: 70%;
                    background: $green-light;
                  }

                  .result-text {
                    cursor: pointer;
                    position: relative;
                    top: 4px;
                    left: 32px;
                    font-size: 16px;
                    font-weight: 400;
                    color: $black;
                    line-height: 16px;
                  }
                }
              }
            }
          }

          .schedule-btn {
            right: 0px;
            width: 140px;
            top: 20px;
            position: absolute;

            .text {
              position: relative;
              left: 14px;
              top: 4px;
              font-weight: 700;
              font-size: 16px;
              color: $black;
            }

            .disabled-text {
              opacity: 0.3;
            }

            .switch {
              position: relative;
              display: inline-block;
              width: 42px;
              height: 26px;
              margin-left: 20px;

              input {
                opacity: 0;
                width: 0;
                height: 0;
              }
            }

            .inactive-publish {
              opacity: 0.5;
              pointer-events: none;
            }

            .slider-round {
              position: absolute;
              cursor: pointer;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-color: $light-white;
              -webkit-transition: 0.4s;
              transition: 0.4s;
              border-radius: 30px;

              &:before {
                position: absolute;
                content: "";
                height: 23px;
                width: 23px;
                left: 2px;
                bottom: 2px;
                background-color: $white;
                -webkit-transition: 0.4s;
                transition: 0.4s;
                border-radius: 50%;
              }
            }

            .disabled-slider {
              cursor: default;
            }

            input {
              &:checked {
                + {
                  .slider-round {
                    background-color: $green;

                    &:before {
                      -webkit-transform: translateX(15px);
                      -ms-transform: translateX(15px);
                      transform: translateX(15px);
                    }
                  }
                }
              }

              &:focus {
                + {
                  .slider-round {
                    box-shadow: 0 0 1px $green;
                  }
                }
              }
            }
          }
        }
      }
    }

    .decrease-height {
      height: 290px !important;
    }

    .add-form-container-dynamic {
      background: white;
      border-radius: 8px;
      margin-left: 25px;
      margin-bottom: 20px;
      box-shadow: 0 0 13px $box-shadow;
      width: 92%;
      margin-top: 90px;
      height: 398px;

      .inner-container {
        padding: 20px;
        padding-bottom: 0px;

        .recipe-variant-notes-text-container-dynamic {
          width: 100%;
          display: flex;

          .preview-section-dynamic {
            display: flex;
            justify-content: end;
          }

          .dynamic-hero-text-main-container {
            display: flex;
            justify-content: space-between;
            width: 100%;
          }

          .switch {
            position: relative;
            display: inline-block;
            width: 42px;
            height: 26px;
            margin-left: 10px;
            bottom: 4px;

            input {
              opacity: 0;
              width: 0;
              height: 0;
            }
          }

          .slider-round {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: $light-white;
            -webkit-transition: 0.4s;
            transition: 0.4s;
            border-radius: 30px;

            &:before {
              position: absolute;
              content: "";
              height: 23px;
              width: 23px;
              left: 2px;
              bottom: 2px;
              background-color: $white;
              -webkit-transition: 0.4s;
              transition: 0.4s;
              border-radius: 50%;
            }
          }

          .disabled-slider {
            cursor: default;
          }

          input {
            &:checked {
              + {
                .slider-round {
                  background-color: $green;

                  &:before {
                    -webkit-transform: translateX(15px);
                    -ms-transform: translateX(15px);
                    transform: translateX(15px);
                  }
                }
              }
            }

            &:focus {
              + {
                .slider-round {
                  box-shadow: 0 0 1px $green;
                }
              }
            }
          }

          .form-title-dynamic {
            .form-title-header-dynamic {
              margin-right: 10px;
              text-transform: capitalize;
              font-size: 16px;
              font-weight: 700;
              color: $black;

              .compulsory-field {
                margin-left: 5px;
                color: $red !important;
              }
            }
          }
        }

        .description-section-dynamic {
          position: relative;

          .description-length {
            position: absolute;
            right: 10px;
            font-size: 12px;
            bottom: 20px;
            font-weight: 400;
            line-height: 1.5;
          }

          .description-dynamic {
            width: 100%;
            resize: none;
            background: $pristine-white;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid $ethereal-whisper-gray;
            height: 131px;
            color: $charcoal-light;
          }

          .description-notes-dynamic {
            width: 100%;
            resize: none;
            background: $pristine-white;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid $ethereal-whisper-gray;
            height: 108px;
            color: $charcoal-light;
          }
        }

        .cta-container {
          width: 100%;
          margin-top: 20px;

          .cta-text {
            margin-right: 10px;
            text-transform: capitalize;
            font-size: 16px;
            font-weight: 700;
            color: $black;

            .compulsory-field {
              margin-left: 2px;
              color: $red !important;
            }
          }

          .cta-section {
            width: 100%;
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;

            .select-or-replace-recipe {
              width: auto;
              display: flex;
              align-items: center;

              .select-text {
                width: 132px;
                height: 26px;
                font-weight: 700;
                font-size: 14px;
                text-transform: capitalize;
                color: $slate-gray;
                display: flex;
                align-items: center;
                justify-content: end;
                margin-right: 25px;
              }

              .or-text {
                font-weight: 700;
                font-size: $font-size-14;
                color: $slate-gray;
                width: 14px;
                height: 18px;
                margin: 0px 12px;
              }

              .select-btn-recipe {
                float: right;
                background-color: $green;
                color: $white;
                font-size: 16px;
                height: 44px;
                border-radius: 50px;
                border: none;
                padding: 10px 25px;
                margin: 10px 0px;
                font-weight: 900;
                letter-spacing: 1px;
                cursor: pointer;
                box-shadow: 0px 1px 5px 0px $box-shadow;
              }

              .select-btn-category {
                float: right;
                background-color: $white;
                color: $green-light;
                font-size: 14px;
                height: 44px;
                border-radius: 50px;
                border: none;
                padding: 10px 25px;
                margin: 10px 0px;
                font-weight: 900;
                letter-spacing: 0.5px;
                cursor: pointer;
                box-shadow: 0px 1px 5px 0px $box-shadow;
              }
            }

            .cta-input {
              margin-top: 6px;
              height: 46px;
              width: 375px;
              border-radius: 23px;
              position: relative;
              right: 4px;
              display: flex;
              border: 2px solid $green-light;

              .cta-input-text {
                height: 42px;
                width: 86%;
                border-radius: 23px;
                font-size: 16px;
                opacity: 0.7;
                position: relative;
                left: 3px;
                text-align: center;
                border: none;
                color: $jet-black;
                font-weight: 700;
              }

              ::placeholder {
                font-weight: 400;
                font-size: 16px;
              }

              .cta-input-length {
                position: absolute;
                top: 12px;
                right: 10px;
                font-size: 12px;
                font-weight: 400;
                line-height: 1.5;
              }
            }

            .select-btn {
              float: right;
              background-color: $green;
              color: $white;
              font-size: 16px;
              height: 44px;
              border-radius: 50px;
              border: none;
              padding: 10px 25px;
              margin: 10px 0px;
              font-weight: 800;
              cursor: pointer;
              box-shadow: 0px 1px 5px 0px $box-shadow;
            }

            .replace-btn {
              float: right;
              background-color: $white;
              color: red;
              font-size: 14px;
              height: 44px;
              border-radius: 50px;
              border: none;
              padding: 10px 25px;
              margin: 10px 0px;
              font-weight: 800;
              cursor: pointer;
              box-shadow: 0px 1px 5px 0px $box-shadow;
            }
            .replace-btn-article {
              float: right;
              background-color: $white;
              color: $green;
              font-size: 14px;
              height: 44px;
              border-radius: 50px;
              border: none;
              padding: 10px 25px;
              margin: 10px 0px;
              font-weight: 800;
              cursor: pointer;
              box-shadow: 0px 1px 5px 0px $box-shadow;
            }
            .red-replace-btn {
              float: right;
              background-color: $white;
              color: red;
              font-size: 14px;
              height: 44px;
              border-radius: 50px;
              border: none;
              padding: 10px 25px;
              margin: 10px 0px;
              font-weight: 800;
              cursor: pointer;
              box-shadow: 0px 1px 5px 0px $box-shadow;
            }

            .error-validation {
              position: absolute;
              display: flex;
              width: 92%;
              right: 87px;
              margin-top: 64px;
              color: $red-orange;
              font-size: 14px;
              justify-content: center;
            }
          }
        }
      }

      .recipe-section {
        display: flex;
        align-items: center;
        padding: 8px 20px;
        border-top: 1px solid $platinum;
        border-bottom: 1px solid $platinum;

        .recipe-image {
          width: 7%;
          border-radius: 4px;
          overflow: hidden;
          padding-right: 5px;

          img {
            height: 60px;
            width: 60px;
            -o-object-fit: cover;
            object-fit: cover;
          }
        }

        .recipe-isin {
          width: 20%;
          padding: 0px 5px;

          .isin-text {
            font-size: 12px;
            font-weight: 400;
            line-height: 1.5;
            color: $castle-rock;
          }
        }

        .recipe-name {
          width: 30%;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          padding: 0px 5px;

          .name-text {
            font-weight: 700;
            font-size: 14px;
            line-height: 1.4;
          }
        }

        .recipe-time {
          width: 10%;
          font-size: 14px;
          font-weight: 400;
          color: $castle-rock;
        }

        .recipe-ingredients {
          width: 10%;
          font-size: 14px;
          font-weight: 400;
          color: $castle-rock;
        }
      }

      .article-section {
        display: flex;
        align-items: center;
        padding: 8px 20px;
        border-top: 1px solid $platinum;
        border-bottom: 1px solid $platinum;

        .article-image {
          width: 7%;
          border-radius: 4px;
          overflow: hidden;
          padding-right: 5px;

          img {
            height: 60px;
            width: 60px;
            -o-object-fit: cover;
            object-fit: cover;
          }
        }

        .article-isin {
          width: 20%;
          padding: 0px 5px;

          .isin-text {
            font-size: 12px;
            font-weight: 400;
            line-height: 1.5;
            color: $castle-rock;
          }
        }

        .article-name {
          width: 40%;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          padding: 0px 5px;

          .name-text {
            font-weight: 700;
            font-size: 14px;
            line-height: 1.4;
          }
        }

        .article-time {
          width: 10%;
          font-size: 14px;
          font-weight: 400;
          color: $castle-rock;
        }

        .article-delete {
          width: 10%;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .delete-icon {
            cursor: pointer;
            height: 22px;
            width: 20px;
          }
        }
      }
    }
  }

  .add-recipe-matches-modal-hero {
    padding: 0;
    width: 838px;
    height: 100%;
    text-align: left;
    overflow-y: visible !important;
    scrollbar-color: $grainsboro $whispering-white-smoke;
    scrollbar-width: thin;


    ::-webkit-scrollbar {
      width: 12px;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
    }

    ::-webkit-scrollbar-thumb {
      background: $grainsboro;
      border: 3px solid $transparent;
      border-radius: 15px;
      background-clip: content-box;
    }

    .header-section {
      display: flex;
      width: 100%;
      align-items: center;
      margin-bottom: 20px;
      margin-top: 6px;
      padding: 0px 15px;
    }

    .title-section-hero {
      display: flex;
      justify-content: space-between;
      width: 90%;

      .title-hero {
        font-size: 24px;
        color: $black;
        font-weight: 700;
      }

      .search-box-pop-hero {
        position: relative;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 30px;
        padding: 0 12px 0 16px;
        height: 36px;
        width: 300px;

        .search-input-box-hero {
          width: 250px;
          height: 34px;
          margin: 0px 0px 0px 22px;
          padding: 0;
          background: none;
          color: $black;
          border: none;
          font-size: 16px;
          border-radius: 0;
          box-shadow: none;

          ::placeholder {
            font-size: 16px;
            color: $graphite-gray;
            font-weight: 400;
          }
        }

        .align-search-input-box-hero {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 230px;
          display: block;
        }
        .disable-icon {
          cursor: default !important;
          opacity: 0.4;
        }

        .search-icon-green-image-hero {
          position: relative;
          top: -28px;
          left: -5px;
          height: 18px;
          width: 18px;
          cursor: pointer;
        }

        .exit-search-icon-hero {
          width: 12px;
          height: 12px;
          position: relative;
          top: -24px;
          float: right;
          cursor: pointer;
        }
      }
    }

    .close-icon-section {
      width: 10%;
      display: flex;
      justify-content: flex-end;

      .close-icon {
        height: 24px;
        width: 24px;
        cursor: pointer;

        img {
          height: 100%;
          width: 100%;
        }
      }
    }

    .add-table-content-hero {
      position: relative;
      left: 10px;
      max-height: 323px;
      min-height: 243px;
      overflow-y: scroll;
      padding-left: 6px;
      padding-right: 22px;

      .table-image-loader-hero {
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9;
        background-color: $white;
        margin: 0 auto;
        height: 200px;
        width: 210px;

        .loader-hero {
          border: 3px solid $pristine-white;
          border-radius: 50%;
          border-top: 3px solid $spanish-gray;
          border-right: 3px solid $spanish-gray;
          border-bottom: 3px solid $spanish-gray;
          width: 24px;
          height: 24px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 2s linear infinite;
        }
        @-webkit-keyframes spin {
          0% {
            -webkit-transform: rotate(0deg);
          }

          100% {
            -webkit-transform: rotate(360deg);
          }
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }

      .add-table-hero {
        .no-recipe-result-hero {
          position: relative;
          top: 90px;
          display: flex;
          justify-content: center;
          font-weight: 700;
          font-size: 20px;
          color: $shadow-gray;
        }

        .background-color {
          background-color: $aqua-spring;
        }

        .add-recipe-body-hero {
          border-bottom: 1px solid $grainsboro;

          .image-recipe-hero {
            width: 60px;
            height: 60px;
            margin-top: 10px;
            margin-bottom: 10px;
            margin-right: 20px;
            overflow: hidden;

            .image {
              width: 100%;
              height: 60px;
              object-fit: cover;
            }
          }

          .table-image-recipe-hero {
            width: 70px;
          }

          .table-recipe-code-hero {
            width: 70px;
          }

          .recipe-code-hero {
            font-size: 12px;
            font-weight: 400;
            color: $stone-gray;
          }

          .recipe-name-tooltip-hero {
            position: relative;
            display: inline-block;
            display: -webkit-box;
            margin: 10px 0px;
            top: -1px;
            font-size: 14px;
            font-weight: 700;
            max-width: 328px;
            color: $black;
          }
          .recipe-subtitle-hero {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin-top: 3px;
            color: $shadow-gray;
            font-weight: 400;
            font-size: 14px;
          }

          .recipe-details-hero {
            .details-hero {
              padding-left: 4px;
              width: 150px;
              font-size: 14px;
              font-weight: 400;
              color: $stone-gray;
            }

            .image {
              position: relative;
              top: -1px;
              width: 12px;
              height: 12px;
            }
          }

          .add-recipe-btn-hero {
            text-align: center;

            .disabled {
              opacity: 0.5;
              justify-content: center;
              background-color: $white;
              color: $green;
              font-size: 14px;
              padding: 8px 22px;
              font-weight: 700;
              cursor: not-allowed;
              pointer-events: none;
              border-radius: 20px;
              border: 1px solid $isabelline;
              box-shadow: 0px 1px 5px 0px $shadow-black;
            }

            .add-btn-hero {
              justify-content: center;
              background-color: $white;
              color: $green;
              font-size: 14px;
              padding: 8px 22px;
              font-weight: 700;
              cursor: pointer;
              border-radius: 20px;
              border: 1px solid $isabelline;
              box-shadow: 0px 1px 5px 0px $shadow-black;
            }

            .added-btn-hero {
              background-color: $green;
              color: $white;
              font-size: 14px;
              padding: 8px 22px;
              font-weight: 700;
              cursor: pointer;
              border-radius: 20px;
              border: 1px solid $isabelline;
              box-shadow: 0px 1px 5px 0px $shadow-black;
            }
          }
        }

        .add-recipe-body-hero:first-child {
          border-top: 1px solid $grainsboro;
          .background-color {
            background-color: $aqua-spring;
          }
        }
      }

      .load-button-hero {
        padding: 15px 0px;
        width: 140px;
        margin: 0 auto;

        .load-more-hero {
          background: $apple-essence-green;
          border-radius: 40px;
          border: none;
          box-shadow: 0px 0px 4px 0px $box-shadow;
          width: 140px;
          height: 44px;
          color: $white;
          font-size: 14px;
          font-weight: 700;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 0px;
          line-height: 14px;
        }
      }
    }

    .done-section-hero {
      text-align: center;
      border-top: 1px solid $grainsboro;

      .done-button-hero {
        margin-top: 14px;
        padding: 12px 37px;
      }
    }
  }

  .content-quiz-schedule-hero-modal {
    .content-save-draft-modal-content {
      padding: 12px 30px;
      width: 471px;

      .content-cross-image-div {
        margin-right: 20px;
      }

      .content-cross-image {
        height: 80px;
        min-width: 80px;
      }

      .content-quiz-schedule-main-container {
        display: flex;

        .content-quiz-schedule-modal-heading {
          text-align: left;

          .content-draft-text {
            font-weight: 700;
            font-size: 20px;
            color: $black;
            margin-bottom: 12px;
          }
        }
      }

      .event-quiz-schedule-date-picker-container {
        display: flex;
        margin-top: 14px;

        .date-picker-container {
          position: relative;
        }
      }

      .content-draft-modal-btn-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;

        .content-draft-save-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $green;
          text-shadow: 0 -1px 0 $faint-black;
          color: $white;
          font-weight: 700;
          box-shadow: 0 2px 4px 0 $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 121px;
          height: 44px;
          font-size: 14px;
        }

        .content-draft-cancel-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $white;
          text-shadow: 0 -1px 0 $faint-black;
          color: $green;
          box-shadow: 0 1px 4px 1px $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 109px;
          height: 44px;
          font-size: 14px;
          font-weight: 800;
          font-weight: bolder;
        }
      }
    }

    .content-quiz-schedule-modal-content {
      padding: 12px 30px;
      width: 519px;
      height: 215px;

      .content-quiz-schedule-date-picker-container {
        display: flex;
        margin-top: 14px;
      }

      .content-quiz-schedule-main-container {
        display: flex;

        .content-quiz-schedule-modal-heading {
          font-weight: 700;
          font-size: 20px;
          color: $black;
          margin-bottom: 12px;
        }

        .content-quiz-date-close-icon {
          position: absolute;
          right: 30px;

          img {
            cursor: pointer;
          }
        }
      }

      .event-quiz-schedule-date-picker-container {
        display: flex;
        margin-top: 14px;

        .date-picker-container {
          position: relative;
        }
      }

      .content-draft-schedule-modal-btn-container {
        display: flex;
        justify-content: flex-end;
        position: absolute;
        right: 32px;
        bottom: 36px;

        .content-draft-schedule-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $green;
          text-shadow: 0 -1px 0 $faint-black;
          color: $white;
          font-weight: 700;
          box-shadow: 0 2px 4px 0 $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 121px;
          height: 44px;
          font-size: 14px;
        }

        .content-draft-cancel-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $white;
          text-shadow: 0 -1px 0 $faint-black;
          color: $green;
          box-shadow: 0 1px 4px 1px $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 109px;
          height: 44px;
          font-size: 14px;
          font-weight: 800;
          font-weight: bolder;
        }
      }
    }
  }
}

.select-articles-hero-modal {
  font-family: $font-family-averta;
  padding: 0;
  width: 838px;
  height: 100%;
  text-align: left;
  overflow-y: visible !important;
  scrollbar-color: $grainsboro $whispering-white-smoke;
  scrollbar-width: thin;

  ::-webkit-scrollbar {
    width: 12px;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-track {
    background: $whispering-white-smoke;
  }

  ::-webkit-scrollbar-thumb {
    background: $grainsboro;
    border: 3px solid $transparent;
    border-radius: 15px;
    background-clip: content-box;
  }

  .select-articles-title-section {
    justify-content: space-between;
    display: flex;
    padding-left: 38px;
    padding-right: 64px;
    margin-top: 27px;
    margin-bottom: 20px;

    .select-articles-title {
      font-size: 24px;
      color: $black;
      font-weight: 700;
    }

    .select-articles-search-box {
      position: relative;
      background-color: $pearl-mist;
      border: 1px solid $grainsboro;
      border-radius: 30px;
      padding: 0 12px 0 16px;
      height: 36px;
      width: 300px;

      .select-articles-search-input-box {
        width: 250px;
        height: 34px;
        margin: 0px 0px 0px 22px;
        padding: 0;
        background: none;
        color: $black;
        border: none;
        font-size: 16px;
        border-radius: 0;
        box-shadow: none;

        ::placeholder {
          font-size: 16px;
          color: $graphite-gray;
          font-weight: 400;
        }
      }

      .select-articles-align-search-input-box {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 230px;
        display: block;
      }

      .disable-icon {
        cursor: default !important;
        opacity: 0.4;
      }
      .select-articles-search-icon-green {
        position: relative;
        top: -28px;
        left: -5px;
        height: 18px;
        width: 18px;
        cursor: pointer;
      }

      .select-articles-exit-search-icon {
        width: 12px;
        height: 12px;
        position: relative;
        top: -24px;
        float: right;
        cursor: pointer;
      }
    }
  }

  .select-articles-add-table-content {
    max-height: 350px;
    min-height: 243px;
    overflow-y: scroll;
    padding-left: 12px;
    padding-right: 12px;
    background-color: $grainsboro;
    .no-articles-result {
      position: relative;
      top: 90px;
      display: flex;
      justify-content: center;
      font-weight: 700;
      font-size: 20px;
      color: $shadow-gray;
    }

    .table-image-loader-hero {
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;
      background-color: $white;
      margin: 0 auto;
      height: 220px;
      margin-top: 12px;

      .loader-hero {
        border: 3px solid $pristine-white;
        border-radius: 50%;
        border-top: 3px solid $spanish-gray;
        border-right: 3px solid $spanish-gray;
        border-bottom: 3px solid $spanish-gray;
        width: 24px;
        height: 24px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }
      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }

    .add-article-list-section {
      padding-top: 10px;

      .add-article-list-group {
        .add-article-group-box {
          width: 100%;
          background-color: $white;
          box-shadow: 0px 1px 5px 0px $box-shadow;
          margin-bottom: 17px;
          border-radius: 8px;
          position: relative;

          .add-article-group-container {
            padding: 7px 16px;
            min-height: 54px;
            display: flex;
            background-color: $white;
            border: 1px solid $white;
            border-radius: 8px;
            justify-content: space-between;
            align-items: center;

            .add-article-group-text {
              padding: 14px 0px;
              font-size: 16px;
              color: $jet-black;
              font-weight: 700;
            }
          }

          .add-article-list-container {
            .add-article-table-body {
              .add-article-list-group {
                .background-color {
                  background-color: $aqua-spring;
                }

                .add-article-table-tr {
                  display: flex;
                  text-align: left;
                  border-top: 1px solid $grainsboro;
                  border-bottom: 1px solid $white;
                  border-left: 1px solid $white;
                  border-right: 1px solid $white;
                  border-radius: 5px;
                  margin: 0px;
                  position: relative;

                  .add-article-body-sr-no {
                    padding: 6px;
                    min-width: 35px;
                    width: 35px;
                    max-width: 35px;
                    display: grid;
                    align-content: center;
                    font-size: 14px;
                    color: $jet-black;
                    margin-left: 10px;
                  }

                  .add-article-body-image {
                    padding: 6px;
                    min-width: 72px;
                    width: 10%;
                    max-width: 10%;
                    display: grid;
                    align-content: center;

                    .add-article-image-div {
                      width: 60px;
                      height: 60px;
                      border-radius: 4px;
                      overflow: hidden;

                      .add-article-image {
                        object-fit: cover;
                        width: 100%;
                        height: 60px;
                      }
                    }
                  }

                  .add-article-body-isin {
                    padding: 6px;
                    min-width: 13%;
                    width: 13%;
                    max-width: 13%;
                    display: grid;
                    align-content: center;
                    font-size: 12px;
                    color: $gunmetal-grey;
                  }

                  .add-article-body-title {
                    padding: 25px 6px;
                    min-width: 41%;
                    width: 41%;
                    max-width: 41%;
                    display: grid;
                    align-content: center;
                    font-size: 14px;
                    color: $jet-black;
                    font-weight: 700;
                  }

                  .add-article-body-modified {
                    padding: 6px;
                    min-width: 14%;
                    width: 14%;
                    max-width: 14%;
                    display: grid;
                    align-content: center;
                    font-size: 14px;
                    color: $stone-gray;
                  }

                  .add-article-body-status {
                    padding: 6px;
                    min-width: 120px;
                    width: 20%;
                    max-width: 20%;
                    display: grid;
                    align-content: center;

                    .add-article-status-container {
                      display: flex;

                      .add-articles-btn {
                        text-align: center;

                        .add-btn {
                          justify-content: center;
                          background-color: $white;
                          color: $green;
                          font-size: 14px;
                          padding: 8px 22px;
                          font-weight: 700;
                          cursor: pointer;
                          border-radius: 20px;
                          border: 1px solid $isabelline;
                          box-shadow: 0px 1px 5px 0px $shadow-black;
                        }

                        .added-btn {
                          opacity: 0.6;
                          background-color: $white;
                          color: $green;
                          font-size: 14px;
                          padding: 8px 22px;
                          font-weight: 700;
                          cursor: context-menu;
                          border-radius: 20px;
                          border: 1px solid $isabelline;
                          box-shadow: 0px 1px 5px 0px $shadow-black;
                        }
                      }
                    }
                  }

                  .add-articles-btn {
                    text-align: center;

                    .articles-add-btn {
                      justify-content: center;
                      background-color: $white;
                      color: $green;
                      font-size: 14px;
                      padding: 8px 22px;
                      font-weight: 700;
                      cursor: pointer;
                      border-radius: 20px;
                      border: 1px solid $isabelline;
                      box-shadow: 0px 1px 5px 0px $shadow-black;
                    }

                    .articles-added-btn {
                      background-color: $green;
                      color: $white;
                      font-size: 14px;
                      padding: 8px 22px;
                      font-weight: 700;
                      cursor: pointer;
                      border-radius: 20px;
                      border: 1px solid $isabelline;
                      box-shadow: 0px 1px 5px 0px $shadow-black;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .add-articles-load-button {
      padding: 15px 0px;
      padding-top: 0px;
      width: 140px;
      margin: 0 auto;

      .add-articles-load-more {
        background: $apple-essence-green;
        border-radius: 40px;
        border: none;
        box-shadow: 0px 0px 4px 0px $box-shadow;
        width: 140px;
        height: 44px;
        color: $white;
        font-size: 14px;
        font-weight: 700;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 0px;
        line-height: 14px;
      }
    }
  }

  .add-articles-done-section {
    text-align: center;
    border-top: 1px solid $grainsboro;
    padding-bottom: 20px;

    .add-articles-done-btn {
      margin-top: 14px;
      background-color: $green;
      color: $white;
      font-size: 16px;
      padding: 12px 37px;
      font-weight: bold;
      cursor: pointer;
      border-radius: 50px;
      border: none;
      box-shadow: 0px 1px 5px 0px $shadow-black;
    }
  }
}

.add-category-modal {
  width: 789px;
  font-family: $font-family-averta;

  .done-section-hero {
    text-align: center;
    border-top: 1px solid $grainsboro;

    .done-button-hero {
      margin-top: 14px;
      padding: 12px 37px;
      border: none;
      border-radius: 50px;
      font-size: $font-size-base;
      font-weight: $font-weight-bold;
      color: $white;
      background-color: $green;
      box-shadow: 0px 1px 5px 0px $shadow-black;
      cursor: pointer;
    }
  }

  .title-section {
    display: flex;
    justify-content: space-between;
    padding: 0px 10px;
    margin-bottom: 20px;
    align-items: center;

    .search-container-for-category {
      display: flex;
      justify-content: space-between;

      .search-title {
        font-size: 16px;
      }

      .search-box {
        position: relative;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 30px;
        padding: 0 12px 0 16px;
        height: 36px;
        width: 300px;

        .search-input-box {
          width: 250px;
          height: 34px;
          padding: 0;
          background: none;
          color: $black;
          border: none;
          font-size: 16px;
          border-radius: 0;
          box-shadow: none;

          ::placeholder {
            font-size: 16px;
            color: $graphite-gray;
            font-weight: 400;
          }
        }

        .align-search-input-box {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 230px;
          display: block;
        }

        .disabled-search-icon {
          cursor: default !important;
          opacity: 0.4;
        }

        .search-icon-green-image {
          position: relative;
          top: 7px;
          left: -5px;
          float: left;
          height: 18px;
          width: 18px;
          cursor: pointer;
        }

        .exit-search-icon {
          width: 12px;
          height: 12px;
          position: relative;
          top: -24px;
          float: right;
          cursor: pointer;
        }
      }
    }

    .disable {
      opacity: 0.2;
      pointer-events: none;
    }

    .exit {
      cursor: pointer;
      position: absolute;
      right: 18px;
      top: -4px;
      width: 24px;
      height: 24px;
    }

    .title {
      width: auto;
      cursor: default;
      padding: 0 10px;
      margin-top: 8px;
      font-size: 24px;
      font-weight: 700;
      color: $black;
      margin-bottom: 14px;
    }
  }

  .add-group-content {
    height: 350px;
    position: relative;
    left: -10px;
    width: 103%;
    overflow-y: scroll;
    background-color: $white-smoke;

    .table-image-loader {
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9;
      background-color: $white-smoke;
      margin: 12% auto;
      height: 100%;
      width: 210px;

      .loader {
        border: 3px solid $pristine-white;
        border-radius: 50%;
        border-top: 3px solid $spanish-gray;
        border-right: 3px solid $spanish-gray;
        border-bottom: 3px solid $spanish-gray;
        width: 24px;
        height: 24px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }
      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }
    .category-popup-container {
      overflow-y: visible !important;
      scrollbar-color: $grainsboro $whispering-white-smoke;
      scrollbar-width: thin;

      ::-webkit-scrollbar {
        width: 12px;
        border-radius: 3px;
      }

      ::-webkit-scrollbar-track {
        background: $whispering-white-smoke;
      }

      ::-webkit-scrollbar-thumb {
        background: $grainsboro;
        border: 3px solid $transparent;
        border-radius: 15px;
        background-clip: content-box;
      }
    }
    .container {
      margin: 0 auto;
      width: 630px;
      .no-recipe-result {
        position: relative;
        top: 40%;
        margin-top: 25%;
        font-weight: 700;
        font-size: 20px;
        color: $shadow-gray;
      }

      .card {
        cursor: pointer;
        position: relative;
        float: left;
        margin: 10px;
        width: 190px;
        height: 210px;
        border-radius: 8px;
        background-color: $white;
        border: 1px solid $grainsboro;

        .card-image {
          margin: 10px auto;
          width: 170px;
          height: 140px;
          border-radius: 4px;
          object-fit: contain;
          overflow: hidden;

          .image {
            width: inherit;
            height: inherit;
            object-fit: cover;
          }
        }

        .card-title {
          margin-left: 8px;
          text-align: left;
          font-size: 14px;
          font-weight: 700;
          color: $black;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .total-recipe {
          margin-left: 8px;
          text-align: left;
          font-size: 14px;
          color: $grey;
        }
      }

      .selected-categories {
        border: 1px solid $green;
        background-color: $snowy-mint;
        .checkmark {
          position: absolute;
          right: 16px;
          bottom: 16px;
          width: 8px;
          height: 14px;
          border: 1.5px solid $green;
          border-width: 0 2px 2px 0;
          -webkit-transform: rotate(45deg);
          -ms-transform: rotate(45deg);
          transform: rotate(45deg);
        }
      }

      .already-added-category {
        opacity: 0.4;
        pointer-events: none;
      }
    }

    .load-button {
      padding: 15px 0px;
      width: 140px;
      margin: 0 auto;

      .load-more {
        background: $apple-essence-green;
        border-radius: 40px;
        border: none;
        box-shadow: 0px 0px 4px 0px $box-shadow;
        width: 140px;
        height: 44px;
        color: $white;
        font-size: 14px;
        font-weight: 700;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 0px;
        line-height: 14px;
      }
    }
  }

  .create-section {
    display: flex;
    justify-content: center;
    padding: 10px 0px;
    background-color: $white;

    .count-selected {
      cursor: default;
      text-align: left;
      margin-left: 22px;
      width: 220px;
      margin-top: 30px;
      font-size: 16px;
    }
  }
}
