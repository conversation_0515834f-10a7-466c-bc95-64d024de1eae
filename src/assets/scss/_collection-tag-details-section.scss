.tag-content-section {
  width: 100%;
  height: 100%;
  background-color: $white;
  border-radius: 8px;
  
  .content-inner-section {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: auto;
    padding: 25px 35px;
    margin-bottom: 50px;
    .no-results-found{
      color: $graphite-gray-light;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: $font-family-averta;
      margin-top: 30px;
    }

    .content {
      margin-bottom: 10px;
      .tags-name {
        color: $graphite-gray;
      }

      .compulsory-select-tag-field {
        position: relative;
        left: 80px;
        bottom: 20px;
        color: $fiery-red-blaze;
        font-weight: 900;
      }

      .input-section-and-suggested-tag {
        position: relative;

        .input-box {
          width: 100%;
          min-height: 50px;
          height: auto;
          border: 1px solid $grainsboro;
          background: $pearl-mist;
          margin: -5px 0px;
          position: relative;
          border-radius: 4px;
          padding: 12px 6px;
          display: flex;
          flex-wrap: wrap;
          margin-bottom: 5px;
          gap: 10px 5px;

          .selected-tags-section {
            display: flex;
            align-items: center;
            cursor: default;

            .tag {
              font-family: $font-family-averta;
              display: flex;
              padding: 2px 15px;
              margin-right: 0px;
              margin-left: 10px;
              height: 32px;
              align-items: center;
              background-color: $white;
              box-shadow: 1px 1px 1px 0 $ink-wash-black;
              border-radius: 8px;
              cursor: default;

              img {
                width: 10px;
                height: 23px;
                margin-left: 12px;
                cursor: pointer;
              }
            }
          }

          input {
            border: none;
            outline: none;
            background: none;
            margin-left: 15px;
            height: 100%;
            font-size: 16px;
            line-height: 16px;
            font-weight: 400;
            font-family: $font-family-averta;
            align-self: center;
            color: $charcoal-gray;
          }
          .empty-input{
            width: 347px;
          }
          .tag-input{
            width: 170px;
          }
          ::placeholder {
            font-weight: 400;
            font-size: 16px;
            opacity: 0.5;
            font-family: $font-family-averta;
          }
        }
        .input-box:hover{
          cursor: pointer;
        }  

        .suggested-tags-section {
          position: absolute;
          width: 100%;
          height: auto;
          background-color: $white;
          padding: 20px 10px;
          box-shadow: 0px 1px 1px 0px $box-shadow;
          z-index: 99;
          border-radius: 7px;

          .intro {
            color: $graphite-gray;
            font-family: $font-family-averta;
            padding-left: 10px;
          }

          .tag-section {
            display: inline-block;
            align-content: center;
            justify-content: space-between;
            margin-top: 20px;

            .tags-box {
              margin: 9px;
              text-align: center;
              float: left;
              padding: 10px 33px;
              background-color: $pearl-mist;
              border-radius: 8px;
              cursor: pointer;

              .tag-name {
                font-family: $font-family-averta;
                color: $graphite-gray;
              }

              .tag-count {
                line-height: 18px;
                color: $gunmetal-grey;
              }
            }
            
            .tags-box:hover,
            .selected-tags {
              background-color: $green;

              .tag-name,
              .tag-count {
                color: $white;
              }
            }
          }

          .loading-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            padding: 50px 0px;

            .loading-text {
              padding: 15px 200px;
              background: $feather-gray;
              border-radius: 10px;
              font-family: $font-family-averta;
              color: $shadow-gray;
            }
          }

          .no-tag-result-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;

            .no-result-text {
              font-family: $font-family-averta;
              color: $graphite-gray-light;
            }
          }
          .disabled-button{
            pointer-events: none;
            opacity: 0.6;
          }

          .button-section {
            display: flex;
            justify-content: flex-end;
            margin-right: 30px;

            .pagination-button {
              display: flex;
              gap: 4px;
              width: 110px;
              background-color: $green;
              color: $white;
              border-radius: 50px;
              border: none;
              padding: 12px 25px;
              margin-left: 12px;
              cursor: pointer;
              box-shadow: 0px 1px 5px 0px $box-shadow;
            }

            .no-button {
              display: none;
            }

            .prev-image {
              img {
                transform: rotate(180deg);
              }
            }
          }
        }
      }

      .feature-recipe-text {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-top: 20px;

        .text {
          color: $graphite-gray;
          font-size: 14px;
          font-weight: 600;
          font-family: $font-family-averta;
        }

        .info-container {
          position: relative;

          img {
            width: 14px;
            height: 14px;
            margin-left: 5px;
          }
        }
      }
      .no-recipe-and-select-tag{
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20px 0px 10px 0px;
        .text{
          color: $graphite-gray-light;
          font-size: 16px;
          line-height: 16px;
          font-weight: 400;
          font-family: $font-family-averta;
        }
      }

      .edit-collection-table {
        margin-bottom: 22px;

        .edit-collection-table-head {
          background-color: $white;
          border-radius: 8px 8px 0px 0px;

          .title {
            color: $graphite-gray;
            font-size: 14px;
            line-height: 4;
            text-align: left;
            margin: 0 4px;
            border-bottom: 3px solid $grainsboro;

            th {
              padding: 8px 0px;
              font-family: $font-family-averta;
              font-weight: 600;
            }

            .edit-collection-isin {
              width: 160px;
            }

            .edit-collection-tags {
              span {
                margin-left: 110px;
              }
            }
          }
        }


      .collection-body {
        position: relative;
        border-bottom: 1px solid $grainsboro;

        .collection-image-recipe {
          width: 60px;
          height: 60px;
          overflow: hidden;
          border-radius: 2px;
          margin: 10px 18px;
          color: $misty-gray;

          .image {
            object-fit: cover;
            width: 100%;
            height: 60px;
          }
        }

        .collection-table-image-recipe {
          width: 70px;
        }

        .collection-table-recipe-code {
          width: 160px;
        }

        .collection-recipe-code {
          span {
            color: $gunmetal-grey;
            font-size: 12px;
            font-weight: 400;
          }
        }

        .collection-table-recipe-name{
          width: 250px;
        }

        .collection-recipe-name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 250px;
          font-family: $font-family-averta;
          color: $graphite-gray;
          line-height: 20px;
        }

        .collection-recipe-subtitle {
          margin-top: 3px;
          color: $shadow-gray;
          font-family: $font-family-averta;
        }

        .collection-recipe-tag {
          margin-left: 110px;
          text-align: left;
          display: inline-block;
          font-family: $font-family-averta;
          color: $stone-gray;
        }

        .collection-tag {
          width: 100px;
          display: flex;
          justify-content: flex-end;
          margin: auto;
          position: relative;
          top: 18px;

          .collection-menu {
            position: relative;
            margin-right: 18px;
            top: 10px;

            .collection-menu-container {
              background-color: $white;
              border-radius: 10px;
              width: 28px;
              height: 20px;
              cursor: pointer;
              display: flex;
              align-items: center;

              .collection-edit-btn {
                width: 17px;
                height: 5px;
                padding: 0;
                margin: 0 auto;
                object-fit: cover;
                z-index: 1;
              }

              &:hover {
                background-color: $pure-white;
              }
            }

            .menu-selected {
              background-color: $aqua-spring;
            }

            .menu-box {
              display: block;
              position: absolute;
              right: 10px;
              width: 151px;
              top: 24px;
              z-index: 2;
              box-shadow: 0 4px 10px 0 $shadow-black,
                0 3px 5px 0 $faint-black,
                0 0 0 1px $shadowy-black;
              border-radius: 4px;
              background: $white;
              border: none;

              .menu-list {
                list-style: none;
                background: $white;
                border-radius: 8px;
                margin: 11px 5px;

                li {
                  display: flex;
                  align-items: center;
                  height: 30px;
                  width: 141px;
                  color: $black;
                  padding-left: 10px;

                  &:hover {
                    color: $white;
                    background: $green;
                    cursor: pointer;
                  }
                }
              }
            }
          }
        }
      }
      }
    }
  }
 
}


