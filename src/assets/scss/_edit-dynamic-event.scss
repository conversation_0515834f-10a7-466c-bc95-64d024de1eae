  .event-preview-modal {
    width: 675px;
    min-height: 406px;

    .event-preview-main-container {
      margin: 22px 30px;
      text-align: initial;

      .event-preview-top-section {
        display: flex;
        justify-content: space-between;

        .event-preview-heading {
          font-weight: 700;
          color: $black;
          font-size: 20px;
        }

        .event-preview-close-icon {
          .close-icon {
            height: 24px;
            width: 24px;
            cursor: pointer;
          }
        }
      }

      .event-preview-main-section {
        position: relative;
        display: flex;
        width: 100%;

        .event-preview-left-section {
          width: 57%;

          .event-preview-content-section {
            display: flex;
            width: 100%;
          }

          .event-preview-device-selection-section {
            margin-top: 15px;

            .event-device-heading {
              font-weight: 700;
              color: $black;
              font-size: 20px;
            }

            .event-option-section {
              margin-top: 7px;

              .event-preview-device-option-section {
                display: flex;
                padding: 12px 0px;

                .round {
                  position: relative;
                }

                .round label {
                  background-color: $white;
                  border: 2px solid $grainsboro;
                  border-radius: 100%;
                  cursor: pointer;
                  height: 24px;
                  left: 0px;
                  position: absolute;
                  width: 24px;

                  &:hover {
                    border: 1px solid $green-light;
                  }
                }

                .round label:after {
                  border: 2px solid $white;
                  border-top: none;
                  border-right: none;
                  content: "";
                  height: 6px;
                  left: 5px;
                  opacity: 0;
                  position: absolute;
                  top: 7px;
                  -webkit-transform: rotate(-45deg);
                  transform: rotate(-45deg);
                  width: 12px;
                  cursor: pointer;
                }

                .round input[type="radio"] {
                  visibility: hidden;
                  display: none;
                }

                .round input[type="radio"] + label {
                  background-color: $white;
                  border: 2px solid $green-light;
                }

                .round input[type="radio"] + label:after {
                  opacity: 1;
                  top: 3px;
                  left: 2px;
                  width: 16px;
                  height: 16px;
                  border-radius: 70%;
                  background: $green-light;
                }

                .event-preview-device-info {
                  display: flex;
                  justify-content: space-between;
                  margin-left: 34px;
                  margin-top: 1px;

                  .event-preview-device-name {
                    font-size: 16px;
                    font-weight: 400;
                    color: $black;
                  }
                }
              }
            }
          }
        }

        .event-preview-right-section {
          width: 246px;
          display: flex;
          justify-content: center;
          position: relative;
          overflow: hidden;
          margin-top: 30px;

          .iframe-container {
            height: 155%;
            width: 138%;
            position: absolute;
            top: 7px;
            left: 41px;
            overflow: hidden;
          }

          .website-frame {
            height: 100%;
            width: 81%;
            border: none;
            background-color: $transparent;
            transform: scale(0.6);
            transform-origin: top left;
            position: relative;
          }

          .blank-area {
            background-color: $white;
            height: 92%;
            width: 20px;
            position: absolute;
            top: 7px;
            right: 39px;
          }

          .event-preview-mobile-size {
            border-radius: 12px;
            border: 4px solid $black;
            overflow: hidden;
          }

          .event-preview-small-mobile-view {
            height: 284px;
            width: 177px;
          }

          .event-preview-large-mobile-view {
            height: 355px;
            width: 178px;
          }
        }
      }
    }
  }

  .edit-event-form-hero {
    .main-content {
      position: relative;
      font-family: $font-family-averta;

      .background-image-event-section {
        background: $sliver-whisper;
        height: 200px;
        position: relative;
        width: 100%;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        display: flex;

        .background-image {
          position: absolute;
          object-fit: cover;
          width: 100%;
          height: 100%;
          filter: brightness(60%) blur(12px);
        }

        .back-btn {
          top: 40px;
          cursor: pointer;
          position: absolute;
          left: 20px;

          .back-arrow-image {
            position: relative;
            top: -2px;
            width: 18px;
            height: 14px;
            cursor: pointer;
          }

          .back-to-hero-list {
            font-size: 16px;
            margin: 0px 4px;
            color: $green;
            cursor: pointer;
            font-weight: $font-weight-bold;
          }
        }

        .head-btn {
          position: absolute;
          right: 70px;
          display: flex;
          align-items: center;
          flex-direction: row-reverse;
          top: 32px;

          .cancel-btn {
            float: right;
            background-color: $white;
            color: $green;
            font-size: 14px;
            border-radius: 50px;
            border: 1px solid $subtle-whisper-grey;
            padding: 10px 25px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }

          .disable {
            opacity: 0.5;
            pointer-events: none;
          }

          .save-btn {
            float: right;
            background-color: $green;
            color: $white;
            font-size: 14px;
            border-radius: 50px;
            border: none;
            padding: 10px 25px;
            margin-left: 12px;
            font-weight: 800;
            cursor: pointer;
            box-shadow: 0px 1px 5px 0px $box-shadow;
          }
        }

        .edit-organization-isin {
          font-weight: 400;
          font-size: 16px;
          color: $white;
          position: absolute;
          top: 82px;
          left: 26px;
        }
      }
      .dynamic-hero-form-edit-id {
        position: absolute;
        top: 82px;
        left: 42px;
        font-weight: 400;
        font-size: 16px;
        color: $white;
        z-index: 2;
      }

      .edit-event-intro-section {
        position: absolute;
        top: 95px;
        width: 92%;
        background: white;
        height: auto;
        border-radius: 8px;
        margin: 13px 24px;
        padding: 12px 16px;

        .edit-event-intro-container {
          display: flex;
          justify-content: space-between;

          .main-section {
            display: flex;
          }

          .edit-event-icon {
            width: 19px;
            height: 20px;
            margin-top: 14px;
          }

          .edit-event-heading {
            font-size: 20px;
            font-weight: 700;
            color: HSL(0, 0%, 0%);
            padding-bottom: 3px;
            margin: 11px 10px;
          }

          .news-date-picker-container {
            display: flex;

            .start-date-text {
              font-size: 16px;
              font-weight: 700;
              line-height: 10px;
              color: $jet-black;
              margin-right: 20px;
              align-self: center;

              .compulsory-field {
                margin-left: 5px;
                color: $red !important;
              }
            }
            .disable {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none;
            }
          }
        }

        .image-input-container {
          display: flex;
          position: relative;
          .disable {
            background: none !important;
          }
          .image-section {
            cursor: pointer;
            width: 120px;
            min-width: 120px;
            height: 120px;
            float: left;
            margin-top: 15px;
            margin-right: 10px;
            border-radius: 4px;
            overflow: hidden;
            background-image: url("@/assets/images/upload-image-category.png");
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;

            .hover-image {
              opacity: 0;
              width: 100%;
              height: 100%;

              &:hover {
                opacity: 1;
                background-image: url("@/assets/images/edit-image.png") !important;
                background-repeat: no-repeat;
                background-position: center;
                background-color: $translucent-black;
                border-radius: 4px;
                background-size: contain;
              }
            }
          }

          .image-main-div {
            width: 120px;
            height: 120px;
            position: relative;

            .display-image-section {
              width: 120px;
              height: 120px;
              object-fit: cover;
              position: absolute;
            }

            .image-inner-container {
              position: absolute;
              height: 120px;
              width: 100%;

              .progress-image {
                height: 120px;
                width: 100%;
                background-color: $jet-black;
                border-radius: 4px;
                position: relative;

                .progress-image-content {
                  display: block !important;
                  position: absolute;
                  width: 100%;
                  text-align: center;
                  margin-top: 15px;

                  .upload-text {
                    display: flex;
                    flex-direction: column;
                    margin-top: 11px;

                    .upload-heading {
                      font-size: 12px;
                      font-weight: 400;
                      height: 18px;
                      color: $white;
                    }

                    .upload-media {
                      font-size: 10px;
                      font-weight: 400;
                      height: 13px;
                      color: $white;
                    }
                  }
                }
              }
            }

            .replace-image-tag {
              cursor: pointer;
              position: absolute;
              top: 0px;
              text-align: left;
              font-weight: 400;
              font-size: 14px;
              color: $white;

              .upload-input {
                width: 120px;
                height: 120px;
                opacity: 0;
              }
            }
          }

          .edit-event-input {
            width: 88%;
            margin-top: 25px;
            position: relative;

            .edit-event-input-container {
              border-bottom: 1px dashed HSL(0, 0%, 76%);
              position: relative;

              .asterisk-input {
                color: $cinnabar;
                position: absolute;
                left: 223px;
                top: 8px;
              }
              .edit-event-input-text {
                border: none;
                height: 40px;
                width: 90%;
                text-overflow: ellipsis;
              }

              ::placeholder {
                font-size: 24px;
                color: $sliver;
                font-weight: 700;
              }
            }

            .edit-event-image-format {
              font-weight: 400;
              font-size: 16px;
              position: absolute;
              bottom: 15px;
              color: HSL(0, 0%, 49%);

              .warning-asterisk-input {
                color: $cinnabar;
              }
            }

            .delete {
              cursor: pointer;
              align-items: center;
              font-weight: 700;
              font-size: 14px;
              padding: 35px 0px;
              width: max-content;
              position: absolute;
              right: 0;

              span {
                color: $ruby-red ;
                position: relative;
                top: 2px;
                left: 3px;
              }
            }
          }

          .schedule-btn {
            right: 0px;
            width: 140px;
            top: 0px;
            position: absolute;

            .text {
              position: relative;
              left: 14px;
              top: 4px;
              font-weight: 700;
              font-size: 16px;
              color: $black;
            }
            .disabled-text {
              opacity: 0.3;
            }

            .switch {
              position: relative;
              display: inline-block;
              width: 42px;
              height: 26px;
              margin-left: 20px;

              input {
                opacity: 0;
                width: 0;
                height: 0;
              }
            }

            .inactive-publish {
              opacity: 0.5;
              pointer-events: none;
            }

            .slider-round {
              position: absolute;
              cursor: pointer;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-color: $light-white;
              -webkit-transition: 0.4s;
              transition: 0.4s;
              border-radius: 30px;

              &:before {
                position: absolute;
                content: "";
                height: 23px;
                width: 23px;
                left: 2px;
                bottom: 2px;
                background-color: $white;
                -webkit-transition: 0.4s;
                transition: 0.4s;
                border-radius: 50%;
              }
            }
            .disabled-slider {
              cursor: default;
            }

            input {
              &:checked {
                + {
                  .slider-round {
                    background-color: $green;

                    &:before {
                      -webkit-transform: translateX(15px);
                      -ms-transform: translateX(15px);
                      transform: translateX(15px);
                    }
                  }
                }
              }

              &:focus {
                + {
                  .slider-round {
                    box-shadow: 0 0 1px $green;
                  }
                }
              }
            }
          }
        }
      }
    }

    .edit-form-container-dynamic {
      background: $white;
      border-radius: 8px;
      margin-left: 25px;
      margin-bottom: 20px;
      box-shadow: 0 0 13px $box-shadow;
      padding: 20px;
      width: 92%;
      margin-top: 130px;

      .recipe-variant-notes-text-container-dynamic {
        width: 100%;
        display: flex;

        .form-title-dynamic {
          width: 100%;

          .form-title-header-dynamic {
            margin-right: 10px;
            text-transform: capitalize;
            font-size: 16px;
            font-weight: 700;
            color: $black;

            .compulsory-field {
              margin-left: 5px;
              color: $red !important;
            }
          }
        }

        .preview-section-dynamic {
          width: 100%;
          display: flex;
          justify-content: end;
          cursor: pointer;

          .image-preview-dynamic {
            width: 18.4px;
            height: 19px;
            margin-right: 2px;
          }

          .switch {
            position: relative;
            display: inline-block;
            width: 42px;
            height: 26px;
            margin-left: 10px;
            bottom: 4px;

            input {
              opacity: 0;
              width: 0;
              height: 0;
            }
          }

          .slider-round {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: $light-white;
            -webkit-transition: 0.4s;
            transition: 0.4s;
            border-radius: 30px;

            &:before {
              position: absolute;
              content: "";
              height: 23px;
              width: 23px;
              left: 2px;
              bottom: 2px;
              background-color: $white;
              -webkit-transition: 0.4s;
              transition: 0.4s;
              border-radius: 50%;
            }
          }
          .disabled-slider {
            cursor: default;
          }
          input {
            &:checked {
              + {
                .slider-round {
                  background-color: $green;

                  &:before {
                    -webkit-transform: translateX(15px);
                    -ms-transform: translateX(15px);
                    transform: translateX(15px);
                  }
                }
              }
            }

            &:focus {
              + {
                .slider-round {
                  box-shadow: 0 0 1px $green;
                }
              }
            }
          }
        }
      }

      .description-section-dynamic {
        position: relative;

        .description-length {
          position: absolute;
          right: 10px;
          font-size: 12px;
          bottom: 20px;
          font-weight: 400;
          line-height: 1.5;
        }

        .description-dynamic {
          width: 100%;
          resize: none;
          background: $pristine-white;
          border-radius: 4px;
          padding: 10px;
          margin: 5px 0;
          border: 1px solid $ethereal-whisper-gray;
          height: 131px;
          color: $charcoal-light;
        }

        .description-notes-dynamic {
          width: 100%;
          resize: none;
          background: $pristine-white;
          border-radius: 4px;
          padding: 10px;
          margin: 5px 0;
          border: 1px solid $ethereal-whisper-gray;
          height: 108px;
          color: $charcoal-light;
        }
      }

      .event-details {
        display: flex;
        gap: 23px;

        .sub-title-container {
          width: 58%;
          position: relative;

          .sub-text-count {
            position: absolute;
            right: 10px;
            font-size: 12px;
            bottom: 20px;
            font-weight: 400;
            line-height: 1.5;
          }

          .sub-title-header {
            color: $black;
            font-size: 16px;
            font-weight: 700;
            font-family: $font-family-Helvetica;

            .asterisk-input {
              color: red;
            }
          }

          .sub-text {
            width: 100%;
            background-color: $feather-gray;
            height: 43px;
            color: $jet-black;
            font-weight: 700;
            font-size: 16px;
            border: 1px solid $ethereal-whisper-gray;
            border-radius: 4px;
            padding: 8px;
          }
        }

        .event-container {
          width: 44%;
          position: relative;

          .sub-text-count {
            position: absolute;
            right: 10px;
            font-size: 12px;
            bottom: 20px;
            font-weight: 400;
            line-height: 1.5;
          }

          .sub-title-header {
            color: $black;
            font-size: 16px;
            font-weight: 700;
            font-family: $font-family-Helvetica;

            .asterisk-input {
              color: red;
            }
          }

          .sub-text {
            width: 100%;
            background-color: $feather-gray;
            height: 43px;
            color: $gunmetal-grey;
            font-size: 16px;
            border: 1px solid $ethereal-whisper-gray;
            border-radius: 4px;
            padding: 8px;

            .input {
              height: 30px;
              background-color: $feather-gray;
              border: none;
              color: $jet-black;
              font-weight: 700;
              width: 90%;
            }
          }
        }
      }

      .cta-main-container {
        width: 100%;
        margin-top: 20px;

        .cta-heading-text {
          margin-right: 10px;
          text-transform: capitalize;
          font-size: 16px;
          font-weight: 700;
          color: $black;

          .compulsory-field {
            margin-left: 2px;
            color: $red !important;
          }
        }

        .cta-input-and-url-section {
          margin-top: 10px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .cta-input-section {
            width: 42%;
            position: relative;

            .cta-input-area {
              display: flex;
              border: 2px solid $green-light;
              border-radius: 23px;
              padding: 10px 1px;

              .cta-input-text {
                border: none;
                border-radius: 23px;
                width: 88%;
                font-size: 16px;
                opacity: 0.7;
                text-align: center;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                font-weight: 700;
                color: $jet-black;
              }
              ::placeholder {
                font-weight: 400 !important;
                font-size: 16px;
              }
            }

            .cta-input-length {
              position: absolute;
              right: 10px;
              font-size: 12px;
              font-weight: 400;
              line-height: 1.5;
            }
          }

          .cta-url-section {
            display: flex;
            width: 54%;
            margin: 10px 0px;
            border-radius: 4px;
            background: $pearl-mist;
            border: 1px solid $grainsboro;
            height: 39px;

            .input-section {
              display: flex;
              width: 93%;
              position: relative;
              .cta-broken-link-validation-section {
                margin: 5px 0px;
                position: absolute;
                left: 9px;
                top: 40px;
                .cta-link-broken-message {
                  font-size: 14px;
                  font-weight: 400;
                  font-family: $font-family-arial-serif;
                  color: $red-orange;
                }
              }

              .input-text-area {
                background: $transparent;
                min-height: 30px;
                border: none;
                width: 98%;
                padding: 5px 0px 0px 10px;
                font-size: 16px;
                font-weight: 400;
                color: $black;
                line-height: 16px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
              }

              ::placeholder {
                color: $slate-gray;
                font-weight: 400;
                font-size: 16px;
              }
            }

            .cta-link-input-verify-section {
              display: flex;
              justify-content: flex-end;
              width: 4%;
              align-items: center;

              .cta-link-progress-check {
                .loader-image {
                  border: 3px solid $white;
                  border-radius: 50%;
                  border-top: 3px solid $green;
                  border-right: 3px solid $green;
                  border-bottom: 3px solid $green;
                  width: 20px;
                  height: 20px;
                  -webkit-animation: spin 2s linear infinite;
                  animation: spin 2s linear infinite;
                }
              }

              .link-image-check {
                .correct-icon {
                  width: 16.8px;
                  height: 14px;
                }

                .wrong-icon {
                  width: 18px;
                  height: 18px;
                }
              }
            }
          }
        }
      }

      .info-section {
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 13px;
        color: $gunmetal-grey;

        .compulsory-field {
          margin-left: 2px;
          color: $red !important;
        }
      }
    }
  }

  .event-quiz-schedule-hero-modal {
    .event-save-draft-modal-content {
      padding: 12px 30px;
      width: 471px;

      .event-cross-image {
        height: 80px;
        min-width: 80px;
      }

      .event-quiz-schedule-main-container {
        display: flex;

        .event-quiz-schedule-modal-heading {
          text-align: left;

          .event-draft-text {
            font-weight: 700;
            font-size: 20px;
            color: $black;
            margin-bottom: 12px;
          }
        }
      }

      .event-draft-modal-btn-container {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;

        .event-draft-save-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $green;
          text-shadow: 0 -1px 0 $faint-black;
          color: $white;
          font-weight: 700;
          box-shadow: 0 2px 4px 0 $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 121px;
          height: 44px;
          font-size: 14px;
        }

        .event-draft-cancel-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $white;
          text-shadow: 0 -1px 0 $faint-black;
          color: $green;
          box-shadow: 0 1px 4px 1px $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 109px;
          height: 44px;
          font-size: 14px;
          font-weight: 800;
          font-weight: bolder;
        }
      }
    }

    .event-quiz-schedule-modal-content {
      padding: 12px 30px;
      width: 519px;
      height: 215px;

      .event-quiz-schedule-date-picker-container {
        display: flex;
        margin-top: 14px;
    
        .date-picker-container {
          position: relative;
        }
      }

      .event-quiz-schedule-main-container {
        display: flex;

        .event-quiz-schedule-modal-heading {
          font-weight: 700;
          font-size: 20px;
          color: $black;
          margin-bottom: 12px;
        }

        .event-quiz-date-close-icon {
          position: absolute;
          right: 30px;

          img {
            cursor: pointer;
          }
        }
      }

      .event-draft-schedule-modal-btn-container {
        display: flex;
        justify-content: flex-end;
        position: absolute;
        right: 32px;
        bottom: 36px;

        .event-draft-schedule-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $green;
          text-shadow: 0 -1px 0 $faint-black;
          color: $white;
          font-weight: 700;
          box-shadow: 0 2px 4px 0 $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 121px;
          height: 44px;
          font-size: 14px;
        }

        .event-draft-cancel-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 21px;
          border-radius: 50px;
          border: 0;
          background-color: $white;
          text-shadow: 0 -1px 0 $faint-black;
          color: $green;
          box-shadow: 0 1px 4px 1px $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 109px;
          height: 44px;
          font-size: 14px;
          font-weight: 800;
          font-weight: bolder;
        }
      }
    }
  }

.quiz-schedule-form-modal-container {
  padding: 12px 30px;
  width: 519px;
  height: 215px;
  overflow: visible;
  font-family: $font-family-averta;

  .quiz-schedule-sub-container {
    .quiz-schedule-top-container {
      display: flex;
      justify-content: space-between;
      padding: 0;

      .schedule-text {
        font-size: 20px;
        font-weight: 700;
        color: $black;
      }

      .close-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;

        .close-icon-image {
          height: 100%;
          width: 100%;
        }
      }
    }

    .quiz-schedule-middle-container {
      padding: 25px 0 0 0;
      position: relative;

      .event-quiz-schedule-date-picker-container {
        display: flex;
        margin-top: 14px;
      }

      .date-picker-container {
        position: relative;
      }
    }

    .quiz-schedule-bottom-container {
      position: relative;
      top: 45px;

      .quiz-schedule-button-section {
        float: right;

        .quiz-form-cancel-button {
          color: $green;
          background-color: $white;
          font-size: 14px;
          border-radius: 50px;
          border: none;
          padding: 10px 25px;
          margin-left: 12px;
          font-weight: 800;
          cursor: pointer;
          box-shadow: 0px 1px 5px 0px $box-shadow;
          width: 108.63px;
          height: 44px;
        }

        .quiz-form-schedule-button {
          width: 128px;
          height: 44px;
          color: $white;
          background-color: $green;
          font-size: 14px;
          border-radius: 50px;
          border: none;
          padding: 10px 25px;
          margin-left: 12px;
          font-weight: 800;
          cursor: pointer;
          box-shadow: 0px 1px 5px 0px $box-shadow;
        }

        .disable-schedule-button {
          pointer-events: none;
          opacity: 0.5;
        }
      }
    }
  }
}
