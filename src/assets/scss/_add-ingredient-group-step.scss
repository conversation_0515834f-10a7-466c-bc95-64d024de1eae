.add-recipe-variant-modal {
  font-family: $font-family-averta;
  text-align: left;
  display: flex;
  flex-direction: inherit;
  justify-content: space-between;
  align-items: normal;
  margin-top: 30px;
  max-height: 160px;
  padding: 0 14px;
  width: 450px;

  .recipe-variant-image {
    img {
      width: 80px;
      margin-bottom: 10px;
    }
  }

  .recipe-variant-content {
    width: 310px;

    .recipe-variant-title {
      position: relative;
      left: -8px;
      color: $black;
      font-weight: 700;
      font-size: 20px;
    }

    .recipe-variant-desc {
      position: relative;
      left: -8px;
      color: $fiery-red-blaze;
      font-weight: 400;
      font-size: 12px;
      line-height: 24px;
      margin-top: 5px;
    }

    .recipe-variant-button-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 32px;
      margin-bottom: 12px;
      gap: 20px;
    }
  }
}
