.switch {
    position: relative;
    display: inline-block;
    width: 42px;
    height: 26px;
    margin-left: 20px;

    input {
        opacity: 0;
        width: 0;
        height: 0;
    }
}

.slider-round {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $light-white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    border-radius: 30px;

    &:before {
        position: absolute;
        content: "";
        height: 23px;
        width: 23px;
        left: 2px;
        bottom: 2px;
        background-color: $white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
        border-radius: 50%;
    }
}

input {
    &:checked {
        + {
            .slider-round {
                background-color: $green;

                &:before {
                    -webkit-transform: translateX(15px);
                    -ms-transform: translateX(15px);
                    transform: translateX(15px);
                }
            }
        }
    }

    &:focus {
        + {
            .slider-round {
                box-shadow: 0 0 1px $green;
            }
        }
    }
}