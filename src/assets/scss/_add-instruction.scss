.main-instruction-section {
  font-family: $font-family-averta;
  .add-instruction-container {
    padding: 0 0 50px;
    margin-top: 20px;
    .title-data-section {
      height: 90px;
      width: 100%;
      display: flex;
      justify-content: center;
      padding: 20px 0;
      .recipe-title-section {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0 30px;
        height: 30%;
        align-items: center;
        .text-section {
          width: 62%;
          display: flex;
          .title {
            width: 100%;
            border: none;
            border-bottom: 2px solid $white;
            background: $transparent;
            font-size: 26px;
            color: $white;
            padding: 5px 0 5px 10px;
          }
          ::placeholder {
            color: $pearl-mist;
          }
        }
        .button-section {
          display: flex;
          gap: 20px;
        }
      }
    }
    .add-instruction-form-container {
      box-shadow: 0 0 13px $box-shadow;
      background: $pure-white;
      border-radius: 8px;
      padding: 20px;
      width: 95%;
      margin-left: 25px;
      margin-bottom: 75px;
      float: left;
      .step-number {
        color: $spanish-gray;
        font-weight: 700;
        font-size: 16px;
      }
      .step-title {
        width: 100%;
        height: 50px;
        border-bottom: 2px solid $sliver;
        position: relative;
        .input-title-area {
          width: 100%;
          font-size: 28px;
          transition: box-shadow 180ms, border 180ms;
          border: none;
          position: relative;
          top: 12px;
          font-weight: 700;
        }
        ::placeholder {
          color: $spanish-gray;
        }
        .asterisk-input {
          color: $cinnabar;
          position: absolute;
          left: 206px;
          top: 10px;
        }
      }
      .form-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .form-title-header {
          display: inline-block;
          font-size: 18px;
          font-weight: 700;
          color: $black;
          .compulsory-field {
            position: relative;
            left: 5px;
            color: $dark-red;
          }
        }
      }
      .description-section {
        margin-bottom: 0;
        padding-top: 10px;
        position: relative;
        .description {
          width: 92%;
          resize: none;
          background: $pristine-white;
          border-radius: 4px;
          padding: 10px;
          margin: 5px 0;
          border: 1px solid $ethereal-whisper-gray;
          min-height: 44px;
          color: $charcoal-light;
        }
        .asterisk-input {
          margin-top: 10px;
          color: $cinnabar;
          position: absolute;
          left: 136px;
        }
        .buttons-section {
          width: 8%;
          float: right;
          height: 50px;
          display: flex;
          margin: 5px 0;
          align-items: center;
          justify-content: center;
          .delete {
            img {
              cursor: pointer;
            }
          }
        }
        .add-instruction-button-main-container {
          .add-instruction-button-container {
            display: flex;
          }
          .add-instruction-text {
            position: relative;
            top: 8px;
            margin-left: 12px;
            font-weight: 400;
            font-size: 12px;
            color: $gunmetal-grey;
          }
        }
        .disable-add-button {
          display: none;
        }
      }
    }
    .recipe-steps-container {
      box-shadow: 0 0 13px $box-shadow;
      background: white;
      border-radius: 8px;
      padding: 20px;
      width: 95%;
      margin-left: 25px;
      margin-bottom: 20px;
      .recipe-title {
        font-weight: 700;
        color: $black;
        text-align: left;
        font-size: 20px;
        border-bottom: 1px solid $grainsboro;
      }
      .recipe-flag-box {
        margin-right: 10px;
        .recipe-flag {
          max-width: 32px;
          max-height: 30px;
        }
      }
      .recipe-title-container {
        display: flex;
        border-bottom: 1px solid $grainsboro;
        .recipe-variant-flag-section {
          margin-right: 10px;
          .recipe-flag {
            max-width: 32px;
            max-height: 30px;
          }
        }
        .recipe-title {
          border-bottom: none;
        }
        .language-symbol {
          font-weight: 700;
          font-size: 20px;
          color: $copper-rust;
        }
      }
      .recipe-steps-main-content {
        display: flex;
        justify-content: flex-start;
      }
      .video-container {
        position: relative;
        margin-top: 17px;
        .disable-delete-button {
          display: none;
        }
        .delete-box {
          position: absolute;
          background-color: $white;
          height: 25px;
          width: 25px;
          top: 8px;
          right: 8px;
          border-radius: 3px;
        }
        .delete-video-icon-image {
          border-radius: 4px;
          background-color: $white;
          width: 32px;
          height: 32px;
          position: absolute;
          top: 11px;
          right: 12px;
          z-index: 1;
          cursor: pointer;
        }
        .video-main-div {
          position: relative;
          height: 200px;
          width: 100%;
          .image-and-video {
            position: absolute;
            height: 200px;
            width: 100%;
            object-fit: cover;
          }
          .image-inner-container {
            position: absolute;
            height: 200px;
            width: 100%;
            .progress-image {
              height: 200px;
              width: 100%;
              background-color: $jet-black;
              border-radius: 4px;
              position: relative;
              .progress-image-content {
                display: block !important;
                position: absolute;
                top: 68px;
                width: 100%;
                text-align: center;
                .upload-text {
                  display: flex;
                  flex-direction: column;
                  margin-top: 11px;
                  .upload-heading {
                    font-size: 14px;
                    font-weight: 400;
                    height: 18px;
                    color: $white;
                  }
                  .upload-media {
                    margin-top: 4px;
                    font-size: 10px;
                    font-weight: 400;
                    height: 13px;
                    color: $white;
                  }
                }
              }
            }
          }
          .progress-video {
            height: 200px;
            width: 100%;
            position: absolute;
            background-repeat: no-repeat;
            background-position: center;
            background-color: $jet-black;
            border-radius: 4px;
            background-size: auto;
            .progress-video-content {
              display: block !important;
              position: absolute;
              top: 68px;
              width: 100%;
              text-align: center;
              .upload-text {
                display: flex;
                flex-direction: column;
                margin-top: 11px;
                .upload-heading {
                  font-size: 14px;
                  font-weight: 400;
                  height: 18px;
                  color: $white;
                }
                .upload-media {
                  margin-top: 4px;
                  font-size: 10px;
                  font-weight: 400;
                  height: 13px;
                  color: $white;
                }
              }
            }
          }
          .video-loader {
            mix-blend-mode: hard-light;
            margin-left: 35px;
          }
          .replace-video-tag {
            cursor: pointer;
            position: absolute;
            top: 0px;
            text-align: left;
            font-weight: 400;
            font-size: 14px;
            color: $white;
            width: 100%;
            .hover-image {
              &:hover {
                position: relative;
                opacity: 1;
                background-repeat: no-repeat;
                background-position: center;
                background-color: $translucent-black;
                border-radius: 4px;
                background-size: auto;
                .replace-logo {
                  display: block !important;
                }
                .replace-video-icon {
                  position: absolute;
                  top: 83px;
                  left: 69px;
                  filter: contrast(3.5);
                }
                .replace-button-title {
                  position: absolute;
                  top: 85px;
                  left: 113px;
                  font-size: 14px;
                  font-weight: 400;
                  color: $white;
                  text-transform: uppercase;
                }
              }
            }
            .upload-input {
              height: 200px;
              width: 100%;
              opacity: 0;
            }
          }
        }
        .image-section {
          height: 200px;
          width: 100%;
          border-radius: 4px;
          cursor: pointer;
          background-image: url("@/assets/images/recipe-detail-upload.png");
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
          .hover-image {
            &:hover {
              position: relative;
              opacity: 1;
              background-repeat: no-repeat;
              background-position: center;
              background-color: $translucent-black;
              border-radius: 4px;
              background-size: auto;
              .replace-logo {
                display: block !important;
              }
              .replace-video-icon {
                position: absolute;
                top: 74px;
                left: 69px;
                filter: contrast(3.5);
              }
              .replace-button-title {
                position: absolute;
                top: 78px;
                left: 113px;
                font-size: 14px;
                font-weight: 400;
                color: $white;
                text-transform: uppercase;
              }
            }
          }
          .upload-input {
            opacity: 0;
            height: 200px;
            width: 100%;
          }
        }
        .optional-video {
          color: $steel-gray;
          font-style: italic;
          font-size: 14px;
        }
        .play-video-icon-image {
          float: right;
          pointer-events: all;
          left: 12px;
          position: absolute;
          bottom: 12px;
          cursor: pointer;
          z-index: 1;
          height: 40px;
          width: 40px;
        }
      }
      .disable-media {
        pointer-events: none;
      }
      .recipe-steps-title {
        width: 100%;
        margin: 33px 0px 0 20px;
        .steps-count {
          font-weight: 700;
          font-size: 16px;
          line-height: 16px;
          color: $spanish-gray;
        }
      }
      .step-title {
        border-bottom: 2px solid $sliver;
        position: relative;
        margin-top: 15px;
        .input-title-area {
          width: 100%;
          font-size: 16px;
          border: none;
          position: relative;
          height: 44px;
          font-weight: 400;
          color: $spanish-gray;
          background-color: $white;
          cursor: text;
        }
        ::placeholder {
          color: $spanish-gray;
        }
        .asterisk-input {
          color: $cinnabar;
          position: absolute;
          left: 75px;
          top: 8px;
          height: 9px;
          cursor: default;
        }
      }
    }
  }
  .edit-product-publish-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 334px;
    min-height: 118px;
    padding: 0 20px;
    .publish-content {
      font-size: 16px;
      color: $charcoal-light;
      .publish-head {
        font-size: 16px;
        margin-bottom: 10px;
        color: $dim-gray;
        font-weight: bolder;
        .unable-to-save {
          color: $dark-red;
          font-weight: 700;
        }
        .unableToSaveError {
          text-align: left;
        }
        .unable-to-save-title {
          color: $charcoal-light;
          font-weight: 300;
        }
        .error-list {
          margin-top: 6px;
          li {
            display: list-item;
          }
          .unable-to-save-list {
            font-weight: 300;
            text-align: left;
            margin-left: 22px;
          }
          .no-instruction {
            margin-bottom: 20px;
          }
        }
      }
    }
  }
  .add-ingredients-from-recipe {
    padding: 16px 0 0px 26px !important;
    height: 100%;
    overflow-y: visible !important;
    scrollbar-color: $grainsboro $whispering-white-smoke;
    scrollbar-width: thin;
    ::-webkit-scrollbar {
      width: 12px;
      border-radius: 3px;
    }
    ::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
    }
    ::-webkit-scrollbar-thumb {
      background: $grainsboro;
      border: 3px solid $transparent;
      border-radius: 15px;
      background-clip: content-box;
    }
    .close-image {
      cursor: pointer;
      position: absolute;
      right: 19px;
      top: 13px;
      background: none;
      border: none;
      img {
        height: 12px;
        width: 12px;
      }
    }
    .add-ingredients-title {
      text-align: left;
      color: $black;
      padding-bottom: 16px;
      padding-left: 8px;
    }
    .add-table {
      padding-right: 30px !important;
      position: relative;
      right: -10px;
      max-height: 243px;
      overflow-y: scroll;
      overflow-x: hidden;
      .table-data {
        max-height: 500px;
        overflow-y: scroll;
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 8px;
        .add-ingredients-col-name {
          th {
            padding: 8px 6px;
            text-align: left;
            color: $steel-gray;
          }
        }
        .add-ingredients-data {
          border: 1px solid $grainsboro;
          cursor: pointer;
          td {
            padding: 12px 6px;
            text-align: left;
            color: $black;
            .ingredient-checkbox {
              border: 1px solid $spanish-gray;
              height: 22px;
              width: 22px;
              border-radius: 2px;
              position: relative;
              bottom: 0px;
              .round {
                position: relative;
                label {
                  background-color: $white;
                  border: 1px solid $transparent;
                  border-radius: 2px;
                  cursor: pointer;
                  height: 20px;
                  left: 0px;
                  position: absolute;
                  top: 0px;
                  width: 20px;
                  &:after {
                    border: 2px solid $white;
                    border-top: none;
                    border-right: none;
                    content: "";
                    height: 5px;
                    left: 3px;
                    opacity: 0;
                    position: absolute;
                    top: 5px;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                    width: 12px;
                  }
                }
                input[type="checkbox"] {
                  visibility: hidden;
                  & + label {
                    background-color: $green-light;
                    &:after {
                      opacity: 1;
                    }
                  }
                }
              }
            }
          }
        }
        .add-ingredients-background {
          background-color: $light-mint;
        }
      }
    }
    .add-ingredients-save-button {
      display: flex;
      justify-content: center;
      cursor: pointer;
      margin-top: 20px;
    }
  }
  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.main-instruction-section
  .add-ingredients-from-recipe
  .add-table
  .table-data
  .ingredien-name,
.main-instruction-section
  .add-ingredients-from-recipe
  .add-table
  .table-data
  .ingredien-match {
  min-width: 381px;
}
.main-instruction-section
  .add-ingredients-from-recipe
  .add-table
  .table-data
  .quantity,
.main-instruction-section
  .add-ingredients-from-recipe
  .add-table
  .table-data
  .uom {
  min-width: 148px;
}
.main-instruction-section input::-webkit-outer-spin-button,
.main-instruction-section input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.add-instruction-form-container {
  box-shadow: 0 0 13px $box-shadow;
  background: white;
  border-radius: 8px;
  padding: 20px;
  width: 65%;
  margin-left: 25px;
  margin-bottom: 75px;
  float: left;
}
.instruction-data {
  margin-bottom: 20px !important;
}
.ingredients-section {
  width: 95%;
  margin-top: 0;
  .compulsory-field {
    color: $dark-red;
  }
}
.recipe-variant-instruction-text {
  display: flex;
  .recipe-variant-flag-section {
    margin-right: 10px;
    .recipe-flag {
      max-width: 32px;
      max-height: 30px;
    }
  }
  .instruction-text {
    font-size: 20px;
  }
}
.recipe-variant-ingredient-text {
  display: flex;
  .recipe-variant-flag-section {
    margin-right: 10px;
    .recipe-flag {
      max-width: 32px;
      max-height: 30px;
    }
  }
}
.form-title {
  font-family: $font-family-averta;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  .form-title-header {
    display: inline-block;
    font-size: 20px;
    font-weight: 700;
    color: $black;
  }
}
.description-section {
  padding-top: 0px;
  border-radius: 4px;
  cursor: default;
}
.ingredients-table {
  font-family: $font-family-averta;
  overflow: visible;
  thead {
    border: none;
    display: block;
    th {
      margin-left: 15px;
      padding: 12px 0px;
      text-align: left;
      border: none;
      font-size: 14px;
      color: $black;
      font-weight: 400;
      &:first-child {
        border-left: none;
        border-top-left-radius: 4px;
      }
      &:last-child {
        border-top-right-radius: 4px;
      }
      .compulsory-field {
        position: relative;
        left: 5px;
        color: $dark-red;
      }
    }
  }
}
.hidden-list {
  opacity: 0.5;
  background: $light-mint;
}
.table-row-data {
  font-family: $font-family-averta;
  tr {
    display: block;
    border: 1px solid $white;
    &:hover {
      background-color: $light-mint;
      border: 1px solid $green;
      .draggable-icon {
        visibility: visible;
        padding: 0px;
        width: 36px;
        height: 36px;
        img {
          cursor: all-scroll;
        }
      }
    }
    &:last-child {
      border-bottom: 1px solid $white;
      &:hover {
        border-bottom: 1px solid $green;
      }
    }
    .draggable-icon {
      visibility: hidden;
      padding: 0 7px;
      width: 4%;
      img {
        cursor: all-scroll;
      }
    }
    .ingredient-name-box {
      width: 30%;
    }
    .filter-icon {
      width: 20%;
    }
    .filter-icon-ing {
      width: 50%;
    }
    td {
      padding: 5px 0px;
      position: relative;
      .ing-name-tool {
        position: relative;
      }
      .filter-select-input-popup {
        border: 1px solid $grainsboro;
        border-radius: 6px;
        background-color: $transparent;
        color: $black;
        padding: 10px;
        width: 90% !important;
        height: 37px;
        white-space: nowrap;
        overflow-x: hidden;
        overflow-y: visible;
        text-overflow: ellipsis;
        position: relative;
      }
      .quantity-number {
        text-align: center;
      }
      .no-ingredients-data {
        pointer-events: none;
      }
      .uom-input {
        width: 82% !important;
      }
      .drop-icon-box {
        position: absolute;
        top: 2px;
        right: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        .dropdown-icon {
          transform: rotate(90deg);
          width: 6px;
          height: 10px;
          cursor: pointer;
        }
      }
      .autocomplete-results-nouom {
        position: absolute;
        list-style: none;
        top: 40px;
        left: 0px;
        right: 0px;
        box-shadow: 0 1px 10px 0 $box-shadow;
        background: $white;
        z-index: 1;
        color: $charcoal-light;
        max-height: 145px;
        overflow-y: scroll;
        width: 96%;
        scrollbar-width: none;
        padding: 8px 10px;
        cursor: pointer;
        margin: 2px;
        border-radius: 4px;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .autocomplete-results {
        position: absolute;
        list-style: none;
        top: 40px;
        left: 0px;
        right: 0px;
        box-shadow: 0 1px 10px 0 $box-shadow;
        background: $white;
        z-index: 1;
        color: $charcoal-light;
        max-height: 145px;
        overflow-y: scroll;
        width: 96%;
        border-radius: 4px;
        &::-webkit-scrollbar {
          display: block;
          width: 6px;
        }
        &::-webkit-scrollbar-track {
          background: $whispering-white-smoke;
        }
        &::-webkit-scrollbar-thumb {
          background: $grainsboro;
          background-clip: content-box;
        }
      }
      .autocomplete-result {
        padding: 8px 10px;
        cursor: pointer;
        margin: 2px;
        border-radius: 4px;
      }
    }
  }
  .menu {
    min-width: 40px;
    .menu-box {
      img {
        cursor: pointer;
        max-width: unset;
      }
      .parse-button {
        background-color: $white;
        color: $green;
        font-size: 14px;
        padding: 3px 16px;
        font-weight: bold;
        cursor: pointer;
        position: relative;
        top: 1px;
        margin-right: 20px;
        border-radius: 20px;
        border: 1px solid $green;
        box-shadow: 0px 1px 5px 0px $shadow-black;
      }
    }
  }
}
.table-row-data tr td .autocomplete-result.is-active,
.table-row-data tr td .autocomplete-result:hover {
  background: $green;
  color: $white;
}
.add-ingredient-main-container {
  font-family: $font-family-averta;
  .add-ingredient-container {
    margin-top: 20px;
  }
  .add-ingredient-text {
    position: relative;
    top: 8px;
    margin-left: 12px;
    color: $gunmetal-grey;
  }
}
