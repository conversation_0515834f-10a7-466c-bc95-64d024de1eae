.main {
  font-family: $font-family-averta;
  .add-banner-form-hero {
    width: 100%;

    .banner-main-content {
      position: relative;
    }
    .banner-image-input-container {
      display: flex;
      position: relative;
      .banner-item-details-top {
        width: 100%;
        display: inline-flex;
        border-bottom: 2px dashed $sliver;
      }

      .add-banner-input {
        width: 100%;
        margin-top: 15px;
        position: relative;

        .banner-head-section {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 30px 0px 10px 0px;

          .type-of-banner-section {
            display: flex;
          }

          .delete-banner-section {
            display: flex;
            justify-content: flex-end;

            .delete-banner-button {
              cursor: pointer;
              align-items: center;

              span {
                color: $fiery-red-blaze;
                font-weight: 700;
                font-size: 14px;
                margin-left: 3px;
                padding-top: 2px;
                position: relative;
                top: 1px;
              }
            }
          }

          .banner-heading {
            .banner-heading-text {
              font-weight: 700;
              color: $black;
              font-size: 16px;
            }

            .compulsory-field {
              color: $red;
              font-weight: 700;
              font-size: 16px;
              height: 15px;
            }
          }

          .banner-radio-button-container {
            display: flex;
            justify-content: space-between;
            width: 242px;
            margin-left: 42px;

            .banner-radio-button-section {
              display: flex;

              .banner-result-text {
                position: relative;
                top: 2px;
                line-height: $line-height-16;
                font-weight: $font-weight-normal;
                font-size: $font-size-base;
                color: $black;
                cursor: pointer;
              }
            }
          }
        }
      }

      .schedule-btn {
        margin-top: 8px;
      }
    }
  }
  .banner-inner-container {
    .cta-banner-main-container {
      width: 100%;

      .cta-banner-heading-text {
        text-transform: capitalize;
        font-size: 16px;
        font-weight: 700;
        color: $black;

        .compulsory-field {
          margin-left: 2px;
          color: $red !important;
        }
      }

      .banner-cta-input-and-url-section {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .banner-cta-input-section {
          width: 42%;
          position: relative;
        }
      }
    }
  }
}
