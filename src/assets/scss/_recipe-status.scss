.recipe-to-publish-popup-main {
  width: 100%;
  overflow-y: scroll;
  max-height: 50vh;
}

.recipe-to-publish-popup-main::-webkit-scrollbar {
  width: 10px;
  border-radius: 3px;
}

.recipe-to-publish-popup-main::-webkit-scrollbar-track {
  background: $whispering-white-smoke;
}

.recipe-to-publish-popup-main::-webkit-scrollbar-thumb {
  background: $grainsboro;
  background-clip: content-box;
}

.recipe-publish-for-recipe-list-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  margin-bottom: 2px;
  margin-right: 2%;
  gap: 20px;
}

.recipe-publish-table {
  width: 100%;
  display: flex;
  justify-content: center;
  background: $white;
  border-radius: 8px;
  cursor: default;
  font-family: $font-family-averta;

  .main-table {
    border-collapse: collapse;
    width: 100%;
    font-size: 0.9em;
    overflow: visible;
    th {
      text-transform: uppercase;
      padding: 8px 15px;
      background: $white-smoke;
      font-weight: 700;
    }

    td {
      padding: 12px 15px;
      border-bottom: 1px solid $grainsboro-cloud;
    }

    .title {
      color: $spanish-gray;
      font-weight: bolder;
      font-size: 12px;
      line-height: 1;
      text-align: left;
    }

    .table-head {
      border-radius: 8px 8px 0px 0px;
    }

    th:first-child {
      border-top-left-radius: 8px;
    }

    th:last-child {
      border-top-right-radius: 8px;
    }

    .table-body {
      font-size: 14px;
      font-weight: lighter;
      text-align: left;
      padding: 20px;
      line-height: 1;

      tr:last-child {
        td {
          border-bottom: unset;
        }

        td:first-child {
          border-bottom-left-radius: 8px;
        }

        td:last-child {
          border-bottom-right-radius: 8px;
        }
      }

      .langs-data {
        padding: 2px;
      }

      .table-image {
        object-fit: cover;
        width: 50px;
        height: 50px;
      }

      .recipe-name {
        font-weight: 700;
        width: 360px;
        min-width: 350px;
        position: relative;

        .recipe-name-text {
          max-width: 350px;
        }

        .recipe-subtitle-name {
          color: $shadow-gray;
          font-weight: 400;
          width: 350px;
          min-width: 350px;
          margin-top: 8px;
        }
      }
    }
  }
}
