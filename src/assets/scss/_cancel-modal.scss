.save-recipe-variant-modal {
  font-family: $font-family-averta;
  width: 454px;
  display: flex;
  flex-direction: inherit;
  justify-content: space-between;

  .save-info-popup-container {
    width: 25%;

    .save-image {
      margin-top: 23px;

      .save-image-container {
        img {
          height: 80px;
          width: 80px;
        }
      }
    }
  }

  .publish-content {
    width: 72%;
    padding-top: 22px;

    .publish-head {
      font-size: 20px;
      color: $black;
      font-weight: 700;
      text-align: left;
      padding-right: 10px;
    }

    .note-message {
      color: $ruby-red;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      padding-top: 15px;
      padding-right: 30px;
    }

    .slug-warnings {
      color: $ruby-red;
      font-size: 12px;
      text-align: left;
      font-weight: 400;
      margin-top: 10px;
      padding-right: 30px;
    }

    .button-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-right: 16px;
      padding-bottom: 4px;
      margin-top: 30px;

      .save-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 24px;
        border-radius: 50px;
        border: 0;
        background-color: $green;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;
        font-weight: bold !important;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 121px;
        height: 45px;
        font-size: 16px;
      }

      .cancel-btn {
        min-width: 109px;
        height: 45px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $green;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;
        cursor: pointer;
        padding: 0 24px;
        font-weight: 400;
        border-radius: 50px;
        border: 0;
        background-color: $white;
        float: left;
      }
    }
  }
}
