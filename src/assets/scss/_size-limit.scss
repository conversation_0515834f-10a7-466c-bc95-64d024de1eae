.uploading-image-confirmation-modal {
  padding: 10px 20px;
  text-align: center;
  width: 489px;
  font-family: $font-family-averta;

  .uploading-image-confirmation-modal-content {
    .upload-image-top-section {
      display: flex;

      .upload-image {
        min-width: 80px;
      }
    }

    .uploading-image-description {
      font-size: 20px;
      text-align: left;
      margin-bottom: 30px;
      margin-left: 20px;
      font-weight: 700;

      .uploading-image-description-subtitle {
        font-weight: 400;
        font-size: 12px;
        color: $red;
        margin-top: 10px;
      }
    }

    .uploading-image-confirmation-button-container {
      display: flex;
      align-items: center;
      justify-content: right;
      gap: 20px;
    }
  }
}
.max-image-upload-modal {
  padding: 10px 20px;
  text-align: center;
  width: 432px;
  font-family: $font-family-averta;

  .max-image-upload-modal-content {
    .max-image-description {
      font-size: 18px;
      margin-bottom: 30px;
      font-weight: 700;
      text-align: center;
      color: red;

      .max-image-description-subtitle {
        font-weight: 400;
        font-size: 12px;
        color: $charcoal-light;
        margin-top: 10px;
      }
    }

    .max-image-confirmation-button-container {
      display: flex;
      align-items: center;
      justify-content: center;

      .max-image-confirmation-cancel-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        background-color: $green;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 90px;
        height: 41px;
        font-size: 16px;
        font-weight: 900;
      }
    }
  }
}
