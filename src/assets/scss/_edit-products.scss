.main {
  font-family: $font-family-averta;
  .main-inner-section {
    .about-first-edit-products {
      cursor: default;

      .back-to-food-items-btn {
        margin: 20px 0px;
        position: relative;
        left: -30px;

        .back-arrow-image-edit-products {
          position: relative;
          top: -2px;
          left: 30px;
          width: 18px;
          height: 14px;
          cursor: pointer;
        }

        .back-to-food-items {
          font-size: 16px;
          margin: 0px 4px;
          color: $green;
          cursor: pointer;
          padding: 2px 2px 2px 30px;
          font-weight: 700;
        }
      }

      .back-to-food-items:hover {
        background-color: $gentle-whisper-gray;
      }

      .main-btn-text-edit-products {
        .head-text-edit-products {
          float: left;
          color: $deep-charcoal-gray;
          position: relative;
          font-weight: 700;
          font-size: 24px;

          .recipe-name-edit-products {
            color: $shade-of-orange;
          }
        }

        .head-btn-edit-products {
          position: absolute;
          right: 0;
          margin-right: 20px;
          display: flex;
          align-items: center;
          flex-direction: row-reverse;

          .cancel-btn-edit-products {
            float: right;
            background-color: $white;
            color: $green;
            font-size: 16px;
            border-radius: 50px;
            border: 1px solid $subtle-whisper-grey;
            padding: 12px 22px;
            margin: 0px 8px;
            font-weight: 700;
            cursor: pointer;
            box-shadow: 0px 1px 2px 0px $box-shadow;
          }

          .disable {
            opacity: 0.5;
            pointer-events: none;
          }

          .save-btn-edit-products {
            float: right;
            background-color: $green;
            color: $white;
            font-size: 16px;
            border-radius: 50px;
            border: none;
            padding: 12px 22px;
            margin: 0px 8px;
            font-weight: 700;
            cursor: pointer;
            box-shadow: 0px 1px 2px 0px $box-shadow;

            &:hover {
              background-color: $ink-wash-green;
              color: $green;
            }
          }
        }
      }
    }

    .about-second-edit-products {
      font-weight: 300;
      cursor: default;

      .container-edit-products {
        float: left;
        width: 100%;
        margin: 30px auto;
        color: $white;
      }

      .content-edit-products {
        color: $black;
        background-color: $white;
        border-radius: 8px;
      }

      .tabcontent-edit-products {
        padding: 20px;
        border-radius: 8px;
        box-shadow: 3px 3px 6px 3px $gentle-gainsboro-gray;

        .promoted-matches-head {
          width: 100%;
          font-size: 24px;
          color: $black;
          font-weight: bolder;
        }

        .promoted-matches-text {
          width: 100%;
          color: $spanish-gray;
          font-size: 16px;
        }

        .zero-promoted-matches-table {
          width: 100%;
          color: $spanish-gray;
          font-size: 15px;
          background-color: $ethereal-whisper-gray;
          border: 2px solid $grainsboro-mist;
          border-radius: 8px;
          padding-top: 25px;
          padding-bottom: 25px;
          margin-top: 16px;
          text-align: center;
          font-weight: 700;
        }

        .table-head-edit-products {
          position: relative;
          padding-top: 15px;
          font-size: 24px;
          font-weight: bolder;
          color: $black;
          margin-bottom: 10px;
        }

        .search-box-edit-product {
          position: relative;
          top: -55px;
          float: right;
          background-color: $pristine-white;
          border: 1px solid $grainsboro-gray;
          border-radius: 30px;
          padding: 0 12px 0 16px;
          height: 46px;
          width: 345px;
          margin-bottom: -40px;

          .search-input-box {
            width: 280px;
            height: 40px;
            margin: 2px 10px 2px 2px;
            padding: 2px 8px 2px 8px;
            background: none;
            color: $spanish-gray;
            caret-color: $green;
            border: none;
            border-radius: 0;
            box-shadow: none;
          }

          .align-search-input-box {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 255px;
            display: block;
          }

          .exit-search-icon {
            width: 12px;
            height: 12px;
            position: relative;
            top: -28px;
            float: right;
            right: 45px;
            cursor: pointer;
          }

          .search-icon-green-edit-product-image {
            position: relative;
            top: -33px;
            float: right;
            height: 24px;
            cursor: pointer;
            text-align: right;
            vertical-align: middle;
          }
        }

        .crud-table-edit-product {
          .main-table-edit-product {
            font-size: $font-size-14;
            border-collapse: collapse;
            td {
              padding: 10px 12px;
              border-top: 1px solid $grainsboro-cloud;
              border-bottom: 1px solid $grainsboro-cloud;
              overflow: hidden;
            }
            .table-body-edit-product {
              font-size: 14px;
              font-weight: bolder;
              text-align: left;
              line-height: 1.5;

              .first-number-edit-product {
                position: relative;
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .second-image-edit-product {
                width: 60px;

                .product-image-edit-product {
                  max-height: 60px;
                  max-width: 60px;
                }
              }

              .userAdded {
                font-style: italic;
              }

              .first-checkbox-recipe-matches-edit-product {
                width: 14px;

                .container-recipe-matches-edit-product {
                  top: -15px;
                  display: block;
                  position: relative;
                  cursor: pointer;
                  font-size: 22px;
                  -webkit-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;

                  input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;
                    height: 0;
                    width: 0;

                    &:checked {
                      ~ {
                        .checkmark-recipe-matches-edit-product {
                          background-color: $green-light;

                          &:after {
                            display: block;
                          }
                        }
                      }
                    }
                  }

                  .checkmark-recipe-matches-edit-product {
                    &:after {
                      left: 9px;
                      top: 3px;
                      width: 8px;
                      height: 16px;
                      border: solid $white;
                      border-width: 0 3px 3px 0;
                      -webkit-transform: rotate(45deg);
                      -ms-transform: rotate(45deg);
                      transform: rotate(45deg);
                    }
                  }
                }

                .checkmark-recipe-matches-edit-product {
                  border-radius: 4px;
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 25px;
                  width: 25px;
                  background-color: $gentle-whisper-gray;

                  &:after {
                    content: "";
                    position: absolute;
                    display: none;
                  }
                }
              }

              .second-image-recipe-matches-edit-product {
                width: 60px;

                .product-recipe-matches-image-edit-product {
                  height: 60px;
                  width: 60px;
                  object-fit: cover;
                }
              }

              .menu {
                position: relative;
                padding: 10px 6px;

                .edit-btn-recipe-matches-edit-product {
                  background-color: $white;
                  border-radius: 5px;
                  border: none;
                  padding: 8px 8px;
                  font-weight: bold;
                  cursor: pointer;
                  box-shadow: 0px 1px 2px 0px $box-shadow;

                  .edit-image-recipe-matches-edit-product {
                    height: 16px;
                    width: 16px;
                  }
                }

                .hide-data {
                  display: none;
                }
              }
            }
          }

          .center {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            text-align: center;

            .load-more-table-btn {
              background-color: $white;
              color: $green;
              font-size: 15px;
              border-radius: 50px;
              border: none;
              padding: 12px 22px;
              margin-bottom: 20px;
              font-weight: normal;
              cursor: pointer;
              box-shadow: 0px 1px 2px 0px $box-shadow;

              &:hover {
                background-color: $translucent-gray;
              }
            }
          }
        }

        .crud-table-edit-product-promoted-products {
          width: 100%;
          margin-bottom: 40px;
          margin-top: 15px;
          background-color: $white;

          .main-table-edit-product-promoted-products {
            font-size: $font-size-14;
            border-collapse: collapse;
            
            td {
              padding: 10px 12px;
              border-top: 1px solid $grainsboro-cloud;
              border-bottom: 1px solid $grainsboro-cloud;
            }

            .table-body-edit-product-promoted-products {
              font-weight: bolder;
              text-align: left;
              line-height: 1.5;

              .first-number-edit-product-promoted-products {
                position: relative;
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .second-image-edit-product-promoted-products {
                width: 60px;

                .product-image-edit-product-promoted-products {
                  max-height: 60px;
                  max-width: 60px;
                }
              }

              .userAdded {
                font-style: italic;
              }

              .sixth-promote-btn-edit-product-promoted-products {
                .promote-btn {
                  width: 114px;
                  margin: auto 0;

                  .promote-btn-edit-product-promoted-products {
                    margin: auto 0;
                    background-color: $pure-white;
                    font-size: 14px;
                    border: none;
                    font-weight: bold;
                    cursor: pointer;
                  }
                }
              }

              .disable-buttons {
                .promote-btn-edit-product-promoted-products {
                  opacity: 0.5;
                }
              }
              .seventh-delete-icon-edit-product-promoted-products {
                .menu-container {
                  background-color: $white;
                  border-radius: 10px;
                  width: 28px;
                  height: 20px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;

                  .table-edit-btn {
                    width: 17px;
                    height: 5px;
                    padding: 0;
                    margin: 0 auto;
                    object-fit: cover;
                    z-index: 1;
                  }

                  &:hover {
                    background-color: $pure-white;
                  }
                }

                .menu-selected {
                  background-color: $aqua-spring;

                  &:hover {
                    background-color: $aqua-spring;
                  }
                }

                  .menu-box {
                    display: block;
                    position: absolute;
                    right: 4px;
                    width: 151px;
                    top: 66%;
                    z-index: 2;
                    box-shadow: 0 4px 10px 0 $shadow-black,
                      0 3px 5px 0 $faint-black,
                      0 0 0 1px $shadowy-black;
                    border-radius: 4px;
                    background: $white;

                    .menu-list {
                      list-style: none;
                      background: $white;
                      border-radius: 8px;
                      margin: 11px 5px;

                      li {
                        display: flex;
                        align-items: center;
                        height: 30px;
                        width: 141px;
                        font-size: 16px;
                        color: $black;
                        font-weight: 700;
                        padding-left: 10px;

                        &:hover {
                          color: $white;
                          background: $green;
                          cursor: pointer;
                        }
                      }
                    }
                  }
                }
              

              .first-checkbox-recipe-matches-edit-product-promoted-products {
                width: 14px;

                .container-recipe-matches-edit-product-promoted-products {
                  top: -15px;
                  display: block;
                  position: relative;
                  cursor: pointer;
                  font-size: 22px;
                  -webkit-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;

                  input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;
                    height: 0;
                    width: 0;

                    &:checked {
                      ~ {
                        .checkmark-recipe-matches-edit-product-promoted-products {
                          background-color: $green-light;

                          &:after {
                            display: block;
                          }
                        }
                      }
                    }
                  }

                  .checkmark-recipe-matches-edit-product-promoted-products {
                    &:after {
                      left: 9px;
                      top: 3px;
                      width: 8px;
                      height: 16px;
                      border: solid $white;
                      border-width: 0 3px 3px 0;
                      -webkit-transform: rotate(45deg);
                      -ms-transform: rotate(45deg);
                      transform: rotate(45deg);
                    }
                  }
                }

                .checkmark-recipe-matches-edit-product-promoted-products {
                  border-radius: 4px;
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 25px;
                  width: 25px;
                  background-color:$gentle-whisper-gray;

                  &:after {
                    content: "";
                    position: absolute;
                    display: none;
                  }
                }
              }

              .second-image-recipe-matches-edit-product-promoted-products {
                width: 60px;

                .product-recipe-matches-image-edit-product-promoted-products {
                  height: 60px;
                  width: 60px;
                  object-fit: cover;
                }
              }

              .third-product-name-recipe-matches-edit-product-promoted-products {
                width: 259px;
                position: relative;
                left: -2px;

                .product-code-recipe-matches-edit-product-promoted-products {
                  font-size: 12px;
                  color: $spanish-gray;
                  margin-bottom: 8px;
                  font-family: $font-family-averta-regular;
                }

                .product-name-recipe-matches-edit-product-promoted-products {
                  color: $black;
                  display: inline-block;
                  line-height: 1;
                  font-weight: 700;
                }

                .product-name-recipe-matches-edit-product-promoted-products-subtitle {
                  color: $spanish-gray;
                  font-size: 12px;
                  display: inline-block;
                  line-height: 1;
                }
              }

              .fourth-none-recipe-matches-edit-product-promoted-products {
                color: $black;
                width: 78px;
                position: relative;
                left: -7px;
              }

              .fifth-cal-recipe-matches-edit-product-promoted-products {
                color: $black;
                width: 78px;
                position: relative;
                left: -7px;
              }

              .sixth-ingredients-recipe-matches-edit-product-promoted-products {
                color: $black;
                width: 100px;
                position: relative;
                left: -7px;
              }

              .seventh-btn-recipe-matches-edit-product-promoted-products {
                width: 111px;

                .promote-btn-recipe-matches-edit-product-promoted-products {
                  background-color: $pure-white;
                  font-size: 14px;
                  border: none;
                  cursor: pointer;
                }
              }

              .menu {
                position: relative;
                padding: 10px 6px;

                .edit-btn-recipe-matches-edit-product-promoted-products {
                  .menu-container {
                    background-color: $white;
                    border-radius: 10px;
                    width: 28px;
                    height: 20px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;

                    .table-edit-btn {
                      width: 17px;
                      height: 5px;
                      padding: 0;
                      margin: 0 auto;
                      object-fit: cover;
                      z-index: 1;
                    }

                    &:hover {
                      background-color: $pure-white;
                    }
                  }

                  .menu-selected {
                    background-color: $aqua-spring;

                    &:hover {
                      background-color: $aqua-spring;
                    }
                  }

                  .menu-box {
                    display: block;
                    position: absolute;
                    right: 18px;
                    width: 151px;
                    top: 66%;
                    z-index: 2;
                    box-shadow: 0 4px 10px 0 $shadow-black,
                      0 3px 5px 0 $faint-black,
                      0 0 0 1px $shadowy-black;
                    border-radius: 4px;
                    background: $white;

                    .menu-list {
                      list-style: none;
                      background: $white;
                      border-radius: 8px;
                      margin: 11px 5px;

                      li {
                        display: flex;
                        align-items: center;
                        height: 30px;
                        width: 141px;
                        font-size: 16px;
                        color: $black;
                        font-weight: 700;
                        padding-left: 10px;

                        &:hover {
                          color: $white;
                          background: $green;
                          cursor: pointer;
                        }
                      }

                      .hide-data {
                        display: none;
                      }
                    }
                  }
                }
              }

              .body-edit-product-promoted-products:first-child {
                .disable-for-first-row {
                  opacity: 0.5;
                  pointer-events: none;
                }
              }

              .body-edit-product-promoted-products:last-child {
                .disable-for-last-row {
                  opacity: 0.5;
                  pointer-events: none;
                }
              }
            }
          }
        }
      }
    }
  }

  .edit-product-delete-modal {
    text-align: left;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;
    align-items: normal;
    margin-top: 30px;
    max-height: 160px;
    padding: 0 14px;
    width: 450px;

    .delete-image {
      img {
        width: 80px;
        margin-bottom: 10px;
      }
    }

    .delete-content {
      width: 310px;

      .delete-title {
        position: relative;
        left: -8px;
        color: $black;
        font-weight: bold;
        font-size: 20px;
      }

      .delete-description {
        position: relative;
        left: -8px;
        color: $grey;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 32px;
        margin-bottom: 8px;
        gap: 20px;
      }
    }
  }

  .edit-product-publish-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 334px;
    min-height: 118px;
    padding: 0 20px;

    .publish-content {
      font-size: 16px;
      color: $charcoal-light;

      .unpublish-note-message {
        color: $ruby-red;
        font-size: 12px;
        font-weight: 400;
        text-align: left;
      }

      .publish-head {
        font-size: 16px;
        margin-bottom: 10px;
        color: $dim-gray;
        font-weight: bolder;

        .unable-to-save {
          color: $red;
          font-weight: 700;
        }

        .unable-to-save-title {
          color: $red;
          font-weight: 300;
        }

        .unable-to-save-list {
          display: flex;
          font-weight: 300;
        }
      }

      .button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;

        .publish-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 24px;
          border-radius: 20px;
          border: 0;
          background-color: $green;
          text-shadow: 0 -1px 0 $faint-black;
          color: $white;
          font-weight: bold !important;
          box-shadow: 0 2px 4px 0 $box-shadow;
          margin: 5px;
          text-align: center;
          cursor: pointer;
          min-width: 100px;
          height: 40px;
          font-size: 16px;
        }

        .cancel-btn {
          min-width: 100px;
          height: 40px;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $green;
          box-shadow: 0 2px 4px 0 $box-shadow;
          margin: 5px;
          cursor: pointer;
          padding: 0 24px;
          font-weight: 700;
          border-radius: 20px;
          border: 1px solid $subtle-whisper-grey;
          background-color: $white;
          float: left;
        }
      }
    }
  }

  .edit-product-save-modal {
    width: 400px;
    min-height: 160px;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;

    .nutrition-info-popup-container {
      width: 25%;

      .nutrition-image {
        margin-bottom: 0;
        margin-top: 0;
      }
    }

    .publish-content {
      width: 70%;

      .publish-head {
        text-align: left;
        color: $black;
      }

      .button-container {
        justify-content: flex-end;
        margin-top: 30px;
        font-weight: 700;
      }
    }
  }
}
