.dynamic-hero-actions {
  display: inline-flex;
  gap: 15px;
}

.dynamic-hero-table {

  .simple-table-head-column {
    width: initial;
  }

  .simple-table-column-publishdate,
  .simple-table-column-lastupdate {
    width: 135px;
  }

  .simple-table-column-uuid {
    width: 250px;
    min-width: 80px;
  }

  .simple-table-column-title {
    min-width: 250px;
  }

  .simple-table-column-image {
    width: 95px;
    min-width: 80px;
  }

  .simple-table-column-status,
  .simple-table-column-select {
    width: 180px;
    min-width: 146px;
  }

  .simple-table-column-actions {
    width: 60px;
  }

  .dynamic-hero-image {
    height: 60px;
    width: 60px;
    object-fit: cover;
    border-radius: 4px;
  }

  .dynamic-hero-uuid {
    width: 100px;
    word-break: break-all;
  }

  .dynamic-hero-title-wrapper {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
}

.dynamic-hero-select-type-modal {
  width: 100%;
  max-width: 390px;

  @media (min-width: 600px) {
    width: 390px;
  }

  &-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 18px;
    padding: 30px 0 10px;
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
  }
}

.dynamic-hero-schedule-template-modal {
  width: 100%;
  max-width: 480px;

  @media (min-width: 600px) {
    width: 480px;
  }

  &-body {
    display: flex;
    padding: 30px 0;
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
  }
}

// src/components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue
.quiz-schedule-form-modal-containerr {
  width: 519px;
  height: 215px;
  overflow: visible;
  font-family: $font-family-averta;

  .quiz-schedule-sub-container {
    .quiz-schedule-top-container {
      display: flex;
      justify-content: space-between;
      padding: 10px 30px;


      .close-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;

        .close-icon-image {
          height: 100%;
          width: 100%;
        }
      }
    }

    .event-quiz-schedule-date-picker-container {
      display: flex;
      margin-top: 15px;
      margin-left: 30px;
    }

    .date-picker-container {
      position: relative;
    }

    .quiz-schedule-bottom-container {
      position: relative;
      top: 50px;
      right: 20px;

      .quiz-schedule-button-section {
        float: right;

        .quiz-form-cancel-button {
          color: $green;
          background-color: $white;
          font-size: 14px;
          border-radius: 50px;
          border: none;
          padding: 10px 25px;
          margin-left: 12px;
          font-weight: 800;
          cursor: pointer;
          box-shadow: 0px 1px 5px 0px $box-shadow;
          width: 108.63px;
          height: 44px;
        }

        .quiz-form-schedule-button {
          width: 128px;
          height: 44px;
          color: $white;
          background-color: $green;
          font-size: 14px;
          border-radius: 50px;
          border: none;
          padding: 10px 25px;
          margin-left: 12px;
          font-weight: 800;
          cursor: pointer;
          box-shadow: 0px 1px 5px 0px $box-shadow;
        }

        .disable-schedule-button {
          pointer-events: none;
          opacity: 0.5;
        }
      }
    }
  }
}
