.notification-popup-container {
    position: fixed;
    left: 51%;
    z-index: 99999;
    transform: translate(-50%, 0%);
    margin-top: 10px;
    display: flex;
    align-items: center;
    padding: 10px;
    min-width: 366px;
    min-height: 46px;
    border-radius: $border-radius;
    font-family: $font-family-averta;

    .notification-icon-section {
        height: 22px;
        width: 22px;

        img {
            height: 100%;
            width: 100%;
        }
    }

    .notification-content-section {
        display: flex;
        flex-direction: column;
        margin: 0 22px 0 12px;

        span {
            color: $charcoal-gray;
        }
    }

    .close-button {
        position: absolute;
        right: 10px;
        bottom: 22px;

        img {
            cursor: pointer;
            height: 12px;
            width: 12px;
        }
    }
}

.success {
    background: $light-mint;
    border: 1px solid $green;
}

.labels {
    background: $baby-blue;
    border: 1px solid $dark-turquoise;
}

.error,
.deleted {
    background: $pastel-pink;
    border: 1px solid $fiery-red-blaze;
}