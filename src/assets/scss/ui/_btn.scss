.btn-reset,
.btn,
.btn-icon,
.btn-green,
.btn-green-outline,
.btn-green-text,
.btn-red,
.btn-red-outline,
.btn-red-text,
.btn-white {

  &:disabled,
  &[disabled] {
    opacity: 0.6;
    cursor: default;
    user-select: none;

    &:not(.simple-data-tooltip) {
      pointer-events: none;
    }
  }
}

.btn-reset {
  margin: 0;
  padding: 0;
  background-color: $transparent;
  border: none;
  box-shadow: none;
  outline: none;
  transition: none;
}

.btn-icon {
  appearance: none;
  user-select: none;
  opacity: 1;
  cursor: pointer;
  display: inline-flex;
  width: 40px;
  height: 40px;
  margin: 0;
  padding: 0;
  border-radius: 4px;
  background-color: $white;
  border: 1px solid $transparent;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  outline: none;

  img, svg {
    opacity: 1;
    width: 20px;
    height: auto;
    margin: auto;
    vertical-align: baseline;
    transition: opacity 0.3s linear;
  }

  img {
    object-fit: contain;
  }
}

.btn,
.btn-green,
.btn-green-outline,
.btn-green-text,
.btn-red,
.btn-red-outline,
.btn-red-text,
.btn-white {
  appearance: none;
  opacity: 1;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  width: auto;
  min-width: 108px;
  height: 42px;
  margin: 0;
  padding-inline: 22px;
  border-radius: 50px;
  border: 1px solid $transparent;
  background-color: $transparent;
  box-shadow: none;
  outline: none;
  vertical-align: baseline;
  font-family: $font-family-averta;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0;
  text-align: center;
  text-decoration: none;
  color: $black;
  transition: all 0.3s linear;

  img, svg {
    width: 16px;
    height: 16px;
    color: currentColor;
    vertical-align: baseline;
  }

  img {
    object-fit: contain;
  }
}

.btn-small {
  height: 36px;
  font-size: 14px;

  img, svg {
    width: 14px;
    height: 14px;
  }
}

.btn-green {
  background-color: $green;
  color: $white;

  &:hover {
    background-color: $islamic-green;
  }
  &:active {
    background-color: $emerald-green;
  }
}

.btn-green-outline {
  border-color: $green;
  color: $green;

  &:hover {
    border-color: $islamic-green;
    color: $islamic-green;
  }
  &:active {
    border-color: $emerald-green;
    color: $emerald-green;
  }
}

.btn-green-text {
  color: $green;

  &:hover {
    color: $islamic-green;
  }
  &:active {
    color: $emerald-green;
  }
}

.btn-red {
  background-color: $fiery-red-blaze;
  color: $white;

  &:hover {
    background-color: $red;
  }
  &:active {
    background-color: $ruby-red;
  }
}

.btn-red-outline {
  border-color: $fiery-red-blaze;
  color: $fiery-red-blaze;

  &:hover {
    border-color: $red;
    color: $red;
  }
  &:active {
    border-color: $ruby-red;
    color: $ruby-red;
  }
}

.btn-red-text {
  color: $fiery-red-blaze;

  &:hover {
    color: $red;
  }
  &:active {
    color: $ruby-red;
  }
}

.btn-white {
  background-color: $white;
  box-shadow: 0 2px 4px 0 $box-shadow;
  color: $green;

  &:hover {
    color: $islamic-green;
  }
  &:active {
    color: $emerald-green;
  }
}

.btn-text-14 {
  font-size: $font-size-14;
}

.disabled-button,
.disable-confirm-button {
  opacity: 0.6;
  cursor: default;
  pointer-events: none;
  user-select: none;
}

.inactive-button {
  opacity: 0.6;
  cursor: default;
}

.hide-button {
  opacity: 0;
  pointer-events: none;
  cursor: default;
}
