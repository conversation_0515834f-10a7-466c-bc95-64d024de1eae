.wrapper {
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: $pristine-white;
}

.wrapper .footer {
  margin-top: auto;
}

.footer {
  position: fixed;
  bottom: 4px;
  background-color: $pristine-white;
  height: 55px;
  z-index: 4;
}

.disable-script-error-popup {
  position: fixed;
  top: 80px;
  left: 20%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70%;
  height: 68px; 
  box-sizing: border-box;
  border: 1px solid $peachy-pink;
  border-radius: 7px;
  background: $light-rose;
  font-family: $font-family-averta;
  font-size: $line-height-18;
  font-weight: $font-weight-medium;
  color:$jet-black;
}