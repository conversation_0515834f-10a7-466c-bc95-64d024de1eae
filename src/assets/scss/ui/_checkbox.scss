$checkbox-input-margin-right: 8px;
$checkbox-size-map: (
  "20": ("size": 20px, markTop: 1px, markLeft: 5px),
  "18": ("size": 18px, markTop: 0px, markLeft: 4px),
);
$checkbox-palette: (
  "default": (
    "text-color": $graphite-gray,
    "bg-color": $white,
    "border-color": $grainsboro,
    "hover-border-color": $green-light,
    "checked-bg-color": $green-light,
    "checked-border-color": $green-light,
    "disabled-bg-color": $light-gray,
    "disabled-border-color": $light-gray,
  ),
  "silver": (
    "text-color": $graphite-gray,
    "bg-color": $white,
    "border-color": $silver,
    "hover-border-color": $green-light,
    "checked-bg-color": $green-light,
    "checked-border-color": $green-light,
    "disabled-bg-color": $silver,
    "disabled-border-color": $silver,
  ),
);

@function get-checkbox-default-palette($key) {
 $default-palette: map-get($checkbox-palette, "default");
 @return map-get($default-palette, $key);
}

/*
  @example
  <label class="checkbox">
    Some text
    <input type="checkbox">
    <span class="checkmark"></span>
  </label>
*/
.checkbox {
  user-select: none;
  cursor: pointer;
  display: inline-block;
  position: relative;
  height: auto;
  min-height: 24px;
  padding-left: calc(24px + $checkbox-input-margin-right);

  color: get-checkbox-default-palette("text-color");
  font-family: $font-family-averta-regular;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;

  input {
    cursor: pointer;
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
  }

  .checkmark {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    height: 24px;
    width: 24px;
    background-color: get-checkbox-default-palette("bg-color");
    border: 2px solid get-checkbox-default-palette("border-color");
    border-radius: 4px;

    &:after {
      content: "";
      display: none;
      position: absolute;
      top: 45%;
      left: 47%;
      width: calc(24px / 4);
      height: calc(24px / 2);
      border: 2px solid $white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg) translateY(-20%) translateX(-100%);
    }

    &.test:after {
      top: 50%;
      left: 50%;
      transform: rotate(45deg) translateY(-20%) translateX(-100%);
    }
  }

  // hover
  &:hover .checkmark,
  .checkmark:hover {
    border-color: get-checkbox-default-palette("hover-border-color");
  }

  // checked
  input:checked ~ .checkmark {
    background-color: get-checkbox-default-palette("checked-bg-color");
    border-color: get-checkbox-default-palette("checked-border-color");
  }
  input:checked ~ .checkmark:after {
    display: block;
  }

  // disabled
  &:has(input:disabled),
  input:disabled,
  input:disabled ~ .checkmark,
  input:disabled ~ .checkmark:after {
    pointer-events: none;
    cursor: default;
  }
  input:disabled ~ .checkmark {
    background-color: get-checkbox-default-palette("disabled-bg-color");
    border-color: get-checkbox-default-palette("disabled-border-color");
  }

  // utils
  &.checkbox-without-text {
    padding-left: 24px;
  }
}

/*
  Sizes
  classes: checkbox-20, checkbox-18
 */
@each $size, $config in $checkbox-size-map {
  .checkbox-#{$size} {
    min-height: map-get($config, "size");
    padding-left: calc(map-get($config, "size") + $checkbox-input-margin-right);
    line-height: map-get($config, "size");

    .checkmark {
      width: map-get($config, "size");
      height: map-get($config, "size");

      &:after {
        width: calc(#{$size}px / 4);
        height: calc(#{$size}px / 2);
      }
    }

    // utils
    &.checkbox-without-text {
      padding-left: map-get($config, "size");
    }
  }
}

/*
  Palette
  classes: checkbox-silver
 */
@each $name, $palette in $checkbox-palette {
  @if $name != "default" {
    .checkbox-#{$name} {
      color: map-get($palette, "text-color");

      .checkmark {
        background-color: map-get($palette, "bg-color");
        border-color: map-get($palette, "border-color");
      }

      &:hover .checkmark,
      .checkmark:hover {
        border-color: map-get($palette, "hover-border-color");
      }

      input:checked ~ .checkmark {
        background-color: map-get($palette, "checked-bg-color");
        border-color: map-get($palette, "checked-border-color");
      }

      input:disabled ~ .checkmark {
        background-color: map-get($palette, "disabled-bg-color");
        border-color: map-get($palette, "disabled-border-color");
      }
    }
  }
}
