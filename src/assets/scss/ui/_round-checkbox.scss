.round {
    position: relative;
  }

  .round label {
    background-color: $white;
    border: 2px solid $grainsboro;
    border-radius: 100%;
    cursor: pointer;
    height: 24px;
    left: 0px;
    position: absolute;
    width: 24px;

    &:hover {
      border: 1px solid $green-light;
    }
  }

  .round label:after {
    border: 2px solid $white;
    border-top: none;
    border-right: none;
    content: "";
    height: 6px;
    left: 5px;
    opacity: 0;
    position: absolute;
    top: 7px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    width: 12px;
    cursor: pointer;
  }

  .round input[type="radio"] {
    visibility: hidden;
    display: none;
  }

  .round input[type="radio"] + label {
    background-color: $white;
    border: 2px solid $green-light;
  }

  .round input[type="radio"]+label:after {
    opacity: 1;
    top: 3px;
    left: 2px;
    width: 16px;
    height: 16px;
    border-radius: 70%;
    background: $green-light;
  }