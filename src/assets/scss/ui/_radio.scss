$control-radio-input-margin-right: 8px;
$control-radio-size-map: (
  "20": ("size": 20px, "innerSize": 12px),
  "18": ("size": 18px, "innerSize": 10px),
);
$control-radio-palette: (
  "default": (
    "text-color": $graphite-gray,
    "bg-color": $white,
    "border-color": $grainsboro,
    "hover-border-color": $green-light,
    "checked-bg-color": $green-light,
    "checked-border-color": $green-light,
    "disabled-bg-color": $light-gray,
    "disabled-border-color": $light-gray,
  ),
  "silver": (
    "text-color": $graphite-gray,
    "bg-color": $white,
    "border-color": $silver,
    "hover-border-color": $green-light,
    "checked-bg-color": $green-light,
    "checked-border-color": $green-light,
    "disabled-bg-color": $silver,
    "disabled-border-color": $silver,
  ),
);

@function get-control-radio-default-palette($key) {
  $default-palette: map-get($control-radio-palette, "default");
  @return map-get($default-palette, $key);
}

/*
  @example
  <label class="control-radio">
    Some text
    <input type="radio" name="field-name">
    <span class="checkmark"></span>
  </label>
*/
.control-radio {
  user-select: none;
  cursor: pointer;
  display: inline-block;
  position: relative;
  height: auto;
  min-height: 24px;
  padding-left: calc(24px + $control-radio-input-margin-right);

  color: get-control-radio-default-palette("text-color");
  font-family: $font-family-averta;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;

  input {
    cursor: pointer;
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    width: 24px;
    height: 24px;
    background-color: get-control-radio-default-palette("bg-color");
    border-radius: 50%;
    border: 2px solid get-control-radio-default-palette("border-color");

    &:after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background-color: transparent;
      transform: translateX(-50%) translateY(-50%);
    }
  }

  // hover
  &:hover .checkmark,
  .checkmark:hover {
    border-color: get-control-radio-default-palette("hover-border-color");
  }

  // checked
  input:checked ~ .checkmark {
    border-color: get-control-radio-default-palette("checked-border-color");
  }
  input:checked ~ .checkmark:after {
    background-color: get-control-radio-default-palette("checked-bg-color");
  }

  // disabled
  &:has(input:disabled),
  input:disabled,
  input:disabled ~ .checkmark,
  input:disabled ~ .checkmark:after {
    pointer-events: none;
    cursor: default;
  }
  input:disabled ~ .checkmark {
    border-color: get-control-radio-default-palette("disabled-border-color");
  }
  input:disabled ~ .checkmark:after {
    background-color: get-control-radio-default-palette("disabled-bg-color");
  }

  // utils
  &.control-radio-without-text {
    padding-left: 24px;
  }
}

/*
  Sizes
  classes: control-radio-20, control-radio-18
 */
@each $size, $config in $control-radio-size-map {
  .control-radio-#{$size} {
    min-height: map-get($config, "size");
    padding-left: calc(map-get($config, "size") + $control-radio-input-margin-right);
    line-height: map-get($config, "size");

    .checkmark {
      width: map-get($config, "size");
      height: map-get($config, "size");

      &:after {
        width: map-get($config, "innerSize");
        height: map-get($config, "innerSize");
      }
    }

    // utils
    &.control-radio-without-text {
      padding-left: map-get($config, "size");
    }
  }
}

/*
  @example
  <div class="control-radio-group">
    <label class="control-radio">
      Some text
      <input type="radio" name="field-name">
      <span class="checkmark"></span>
    </label>
    <label class="control-radio">
      Some text
      <input type="radio" name="field-name">
      <span class="checkmark"></span>
    </label>
  </div>
*/
.control-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  width: 100%;
}

/*
  Palette
  classes: control-radio-silver
 */
@each $name, $palette in $control-radio-palette {
  @if $name != "default" {
    .control-radio-#{$name} {
      color: map-get($palette, "text-color");

      .checkmark {
        background-color: map-get($palette, "bg-color");
        border-color: map-get($palette, "border-color");
      }

      &:hover .checkmark,
      .checkmark:hover {
        border-color: map-get($palette, "hover-border-color");
      }

      input:checked ~ .checkmark {
        border-color: map-get($palette, "checked-border-color");
      }
      input:checked ~ .checkmark:after {
        background-color: map-get($palette, "checked-bg-color");
      }

      input:disabled ~ .checkmark {
        border-color: map-get($palette, "disabled-border-color");
      }
      input:disabled ~ .checkmark:after {
        background-color: map-get($palette, "disabled-bg-color");
      }
    }
  }
}
