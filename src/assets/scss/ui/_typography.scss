.display-1,
.display-2,
.display-3,
.display-4,
.display-5,
.display-6 {
  padding-inline: 1px;
  font-weight: bold;
  font-family: $font-family-averta;
  letter-spacing: 0;
}

.display-1 {
  margin-bottom: 20px;
  font-size: 28px;
  line-height: 1.3;
}

.display-2 {
  margin-bottom: 20px;
  font-size: 24px;
  line-height: 1.3;
}

.display-3 {
  margin-bottom: 20px;
  font-size: 20px;
  line-height: 22px;
}

.display-4 {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 18px;
}

.display-5 {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 16px;
}

.display-6 {
  margin-bottom: 14px;
  font-size: 12px;
  line-height: 14px;
}

.display-text {
  @extend .display-4;
  color: $black;
}

.display-title {
  margin-bottom: 25px;
  font-size: $font-size-base;
  line-height: $line-height-40;
}

.text-h1,
.text-h2,
.text-h3,
.text-h4,
.text-h5,
.text-light-h4,
.text-title-1,
.text-title-2,
.text-title-3,
.text-title-4 {
  font-family: $font-family-averta;
  margin-bottom: 0;
}

.text-h1 {
  @extend .display-1;
  color: $green-dark;
}

.text-title-1 {
  @extend .display-1;
}

.text-h2 {
  @extend .display-2;
}

.text-h3 {
  @extend .display-5;
}

.text-light-h3 {
  @extend .display-5;
  @extend .font-normal;
}

.text-h4 {
  @extend .display-6;
}

.text-light-h4 {
  @extend .display-6;
  @extend .font-normal;
}

.text-h5 {
  @extend .display-3;
}

.text-light-h6 {
  @extend .font-normal;
  font-size: $font-size-10;
}

.text-title-2 {
  @extend .display-4;
}

.text-title-3 {
  font-size: $font-size-18;
  @extend .font-bold;
}

.text-title-4 {
  font-size: $font-size-18;
  @extend .font-normal;
}

.text-title-5 {
  font-size: $font-size-base;
  @extend .font-normal;
}

.text-head-title-1 {
  font-size: $font-size-14;
  @extend .font-bold;
}

.text-14 {
  font-size: $font-size-14;
}

$font-weights: (
  'font-bold': 700,
  'font-normal': 400,
  'font-light': 300,
  'font-medium': 500,
  'font-weight-semi-bold': 600,
  'font-weight-extra-bold': 800,
  'font-weight-black': 900,
);

// Function to get font-weight
@function get-font-weight($weight-name) {
  @return map-get($font-weights, $weight-name);
}

.font-bold {
  font-weight: get-font-weight('font-bold');
}

.font-normal {
  font-weight: get-font-weight('font-normal');
}

.font-light {
  font-weight: get-font-weight('font-light');
}

.font-medium {
  font-weight: get-font-weight('font-medium');
}

.font-weight-semi-bold {
  font-weight: get-font-weight('font-weight-semi-bold');
}

.font-weight-extra-bold {
  font-weight: get-font-weight('font-weight-extra-bold');
}

.font-weight-black {
  font-weight: get-font-weight('font-weight-black');
}

// Font size variables
$font-sizes-list: (
  10: $font-size-10,
  12: $font-size-12,
  14: $font-size-14,
  15: $font-size-15,
  base: $font-size-base,
  18: $font-size-18,
  19: $font-size-19,
  20: $font-size-20,
  22: $font-size-22,
  24: $font-size-24,
  26: $font-size-26,
  28: $font-size-28,
  30: $font-size-30,
  32: $font-size-32,
  34: $font-size-34,
  36: $font-size-36,
);

// Generate utility classes
@each $key, $value in $font-sizes-list {
  .font-size-#{$key} {
    font-size: $value;
  }
}

// Line height variables
$line-height-list: (
  14: $line-height-14,
  16: $line-height-16,
  18: $line-height-18,
  19: $line-height-19,
  20: $line-height-20,
  21: $line-height-21,
  23: $line-height-23,
  40: $line-height-40,
  42: $line-height-42,
  45: $line-height-45,
);

// Generate utility classes
@each $key, $value in $line-height-list {
  .line-height-#{$key} {
    line-height: $value;
  }
}

.font-family-averta {
  font-family: $font-family-averta;
}
