.shoppable-review-component {
  margin-right: 48px;
  margin-left: 21px;
  font-family: $font-family-averta;

  .main-inner-section {
    .add-ingredient-weights-popup {
      padding: 10px 20px;
      width: 515px;
      height: 240px;
      overflow-y: auto;

      .add-ingredient-headin-text-and-cross-logo {
        display: flex;
        align-items: center;
        padding: 10px 0px;

        .add-ingredient-heading-text {
          color: $obsidian-black;
          font-size: 20px;
          font-weight: 700;
          font-style: normal;
          letter-spacing: 0px;
          text-align: left;
          width: 424px;
          display: inline-block;
        }

        .add-ingredient-cross-image {
          width: 24px;
          height: 24px;
          cursor: pointer;
          margin-left: 30px;
        }
      }

      .sub-text-container {
        .sub-text {
          color: $graphite;
          font-style: normal;
          letter-spacing: 0px;
          margin: 10px 0px;
          text-align: left;

          .sub-text-bold-text {
            color: $graphite;
            font-weight: 700;
            font-style: normal;
            letter-spacing: 0px;
            text-align: left;
          }
        }
      }

      .input-container {
        display: flex;
        align-items: center;

        .input-box-container {
          .input-box {
            width: 192px;
            border-radius: 4px;
            border: 1px solid $moon-stone;
            background-color: $frost-white;
            padding: 10px;
            color: $gunmetal-grey;
          }
        }

        .radio-button-container-one {
          margin: 0px 0px 0px 65px;

          .round {
            position: relative;
          }

          .round label {
            background-color: $white;
            border: 2px solid $grainsboro;
            border-radius: 100%;
            cursor: pointer;
            height: 24px;
            left: 0px;
            position: absolute;
            width: 24px;
          }

          .round label:after {
            border: 2px solid $white;
            border-top: none;
            border-right: none;
            content: "";
            height: 6px;
            left: 5px;
            opacity: 0;
            position: absolute;
            top: 7px;
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
            width: 12px;
            cursor: pointer;
          }

          .round input[type="radio"] {
            visibility: hidden;
            display: none;
          }

          .round input[type="radio"] + label {
            background-color: $white;
            border: 3px solid $green-light;
          }

          .round input[type="radio"] + label:after {
            opacity: 1;
            top: 3px;
            left: 2px;
            width: 14px;
            height: 14px;
            border-radius: 70%;
            background: $green-light;
          }

          .radio-btn-text-container {
            position: relative;
            top: 2px;
            left: 34px;
            cursor: pointer;

            .radio-btn-text {
              cursor: pointer;
              color: $black;
            }
          }
        }
      }
    }

    .shoppable-top-container {
      padding-top: 12px;

      .shoppable-text {
        font-weight: 700;
        font-size: 24px;
        color: $black;
        margin-left: 20px;
      }

      .main-btn-text-shoppable-review {
        display: flex;
        justify-content: space-between;

        .head-btn-shoppable-review {
          display: flex;
          justify-content: space-between;

          .cancel-btn-shoppable-review {
            width: 108px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: $white;
            color: $green;
            font-size: 16px;
            border-radius: 50px;
            border: 1px solid $subtle-whisper-grey;
            padding: 12px 22px;
            margin: 0px 8px;
            font-weight: 700;
            cursor: pointer;
            box-shadow: 0px 1px 2px 0px $box-shadow;
          }

          .disable {
            opacity: 0.5;
            pointer-events: none;
          }

          .save-btn-shoppable-review {
            width: 83px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: $green;
            color: $white;
            font-size: 16px;
            border-radius: 50px;
            border: none;
            padding: 12px 22px;
            margin: 0px 8px;
            font-weight: 700;
            cursor: pointer;
            box-shadow: 0px 1px 2px 0px $box-shadow;

            &:hover {
              background-color: $ink-wash-green;
              color: $green;
            }
          }
        }
      }
    }

    .container-shoppable-review {
      width: 100%;
      margin: 30px auto;
      margin-bottom: 0px;
      color: $white;
    }

    .content-shoppable-review {
      color: $black;
      background-color: $white;
      border-radius: 8px;
    }

    .tabcontent-shoppable-review {
      display: flex;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 3px 3px 6px 3px $gentle-gainsboro-gray;
    }

    .shoppable-recipe-data {
      padding: 5px 20px;
    }

    .shoppable-recipe-number {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: $charcoal-gray;
    }

    .shoppable-recipe-name {
      position: relative;
      top: 13px;
      font-size: 16px;
      font-weight: 700;
      line-height: 16px;
      color: $jet-black;
    }

    .shoppable-recipe-subtitle {
      position: relative;
      top: 23px;
      font-size: 16px;
      font-weight: 400;
      line-height: 16px;
      color: $jet-black;
    }
  }

  .ingredient-product-section {
    display: flex;
    justify-content: space-between;
    margin: 50px 0;
    margin-bottom: 0px;

    .ingredient-product-head {
      font-size: 20px;
      font-weight: 700;
      color: $black;
      margin-left: 20px;
    }

    .show-hide-section {
      display: flex;
      color: $green;
      margin-left: 20px;
      cursor: pointer;

      .expand-collapse-image {
        height: 19px;
        width: 18px;
        margin-left: 6px;
        rotate: 90deg;
      }

      .collapse-image {
        rotate: 270deg;
      }
    }

    .quantity-product-section {
      display: flex;

      .quantity-product-text {
        position: relative;
        right: 20px;
        font-size: 16px;
        font-weight: 700;
        line-height: 20px;
        color: $jet-black;
      }

      .quantity-product-number-section {
        position: relative;
        bottom: 15px;
        display: flex;
        border: 1px solid $grainsboro;
        background: $white;
        border-radius: 4px;
        width: 65px;
        height: 50px;
        justify-content: space-around;
        align-items: center;

        .autocomplete-results {
          position: absolute;
          list-style: none;
          top: 45px;
          left: 0px;
          right: 5px;
          box-shadow: 0 1px 10px 0 $box-shadow;
          background: $white;
          z-index: 1;
          color: $charcoal-light;
          max-height: 360px;
          overflow-y: scroll;
          width: 100%;
          border-radius: 4px;
          scrollbar-width: none;

          &::-webkit-scrollbar {
            display: none;
          }
        }

        .autocomplete-result {
          padding: 8px 20px;
          cursor: pointer;
          margin: 2px;
          border-radius: 4px;

          &.is-active,
          &:hover {
            background: $green;
            color: $white;
          }
        }

        .quantity-product-number {
          font-size: 16px;
          font-weight: 400;
          color: $black;
        }

        .quantity-product-dropdown {
          transform: rotate(90deg);
          width: 6px;
          height: 10px;
          cursor: pointer;

          &.dropdown-disabled {
            cursor: default;
            pointer-events: none;
          }
        }
      }
    }
  }

  .shoppable-publish-modal {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 334px;
    min-height: 118px;
    padding: 0 20px;

    .shoppable-publish-content {
      font-size: 16px;
      color: $charcoal-light;

      .unpublish-note-message {
        color: $ruby-red;
        font-size: 12px;
        font-weight: 400;
        text-align: left;
      }

      .shoppable-publish-head {
        font-size: 16px;
        margin-bottom: 10px;
        color: $dim-gray;
        font-weight: bolder;

        .unable-to-save {
          color: $red;
          font-weight: 700;
        }

        .unable-to-save-title {
          color: $red;
          font-weight: 300;
        }

        .unable-to-save-list {
          display: flex;
          font-weight: 300;
        }
      }

      .shoppable-button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
        gap: 20px;
      }
    }
  }

  .shoppable-save-modal {
    width: 400px;
    min-height: 160px;
    display: flex;
    flex-direction: inherit;
    justify-content: space-between;

    .nutrition-info-popup-container {
      width: 25%;

      .nutrition-image {
        margin-bottom: 0;
        margin-top: 0;
      }
    }

    .shoppable-publish-content {
      width: 70%;

      .shoppabl-head-content {
        display: flex;
        justify-content: space-between;

        .shoppable-publish-head {
          text-align: left;
          color: $black;
          font-size: 18px;
          font-weight: 700;
        }

        .shoppable-exit-btn {
          height: 24px;
          width: 24px;
          position: relative;
          bottom: 15px;
          left: 12px;
          cursor: pointer;
        }
      }

      .shoppable-publish-subtitle {
        line-height: 19px;
        color: $jet-black;
        display: flex;
        text-align: left;
      }

      .button-container {
        justify-content: flex-end;
        margin-top: 30px;
        font-weight: 700;
        gap: 20px;
      }
    }
  }
}

.shoppable-preview-reference-ingredient-container {
  height: auto;
  width: 800px;
  padding: 15px 20px;

  .shoppable-preview-reference-ingredient-top-section {
    display: flex;
    justify-content: space-between;

    .reference-product-section-name {
      color: $black;
    }

    .reference-product-section-close-button {
      height: 24px;
      width: 24px;
      cursor: pointer;

      img {
        height: 100%;
        width: 100%;
      }
    }
  }

  .shoppable-preview-reference-ingredient-middle-section {
    margin-top: 10px;
    display: flex;

    .shoppable-preview-reference-ingredient-name {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: $grey;
    }
  }

  .shoppable-preview-reference-ingredient-bottom-section {
    margin-top: 15px;
    position: relative;

    .reference-preview-ingredient-table-data {
      position: relative;
      width: 100%;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      text-align: left !important;
      margin-bottom: 24px;

      .reference-preview-table {
        .reference-ingredient-table-head {
          .reference-ingredient-table-head-row {
            .ingredient-image-heading {
              width: 15%;
            }

            .ingredient-product-id-heading {
              width: 15%;
            }

            .ingredient-product-title-heading {
              width: 30%;
            }

            .ingredient-product-weight-heading {
              width: 10%;
              text-align: center;
            }

            .ingredient-product-quantity-heading {
              width: 10%;
              text-align: center;
            }

            .ingredient-product-store-count-heading {
              width: 16%;
              text-align: center;
            }

            th {
              text-align: left;
              text-transform: uppercase;
              padding: 8px 0px;
              background: $white-smoke;
              font-size: 12px;
              line-height: 1.3333;
              font-weight: 700;
              color: $spanish-gray;
            }

            th:first-child {
              border-top-left-radius: 8px;
            }

            th:last-child {
              border-top-right-radius: 8px;
            }
          }
        }

        .reference-ingredient-table-body {
          .reference-ingredient-table-body-row {
            td {
              padding: 18px 0px;
              text-align: left;
            }

            .ingredient-image {
              text-align: center;

              img {
                object-fit: contain;
                height: 60px;
                width: 60px;
              }
            }

            .ingredient-product-id {
              color: $gunmetal-grey;
              line-height: 1.5;
            }

            .ingredient-product-title {
              color: $jet-black;
              line-height: 1.5;

              .product-title {
                width: 90%;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }

            .ingredient-product-weight {
              color: $gunmetal-grey;
              line-height: 1.5;
              text-align: center;
            }

            .ingredient-product-quantity {
              color: $gunmetal-grey;
              line-height: 1.5;
              text-align: center;
            }

            .ingredient-product-store-count {
              color: $gunmetal-grey;
              line-height: 1.5;
              text-align: center;
            }

            &:last-child {
              td {
                border-bottom: unset;
              }

              td:first-child {
                border-bottom-left-radius: 8px;
              }

              td:last-child {
                border-bottom-right-radius: 8px;
              }
            }
          }
        }
      }
      .reference-tags-main-container {
        background-color: $pearl-mist;
        padding: 15px 10px;
        margin-top: 7px;

        .reference-ingredient-tags-heading {
          color: $jet-black;
        }

        .reference-tags-list-container {
          display: flex;
          flex-wrap: wrap;

          .ingredient-reference-tags-card {
            display: flex;
            color: $jet-black;
            border: 1px solid $grainsboro;
            background-color: $white;
            padding: 6px;
            border-radius: 4px;
            margin-top: 5px;
            margin-right: 8px;

            .ingredient-reference-tags-image {
              height: 18px;
              width: 18px;
            }

            .ingredient-reference-tags-name {
              font-family: $font-family-arial-serif;
              line-height: 1;
            }
          }
        }
      }
    }

    .shoppable-preview-reference-ingredient-cancel-button {
      display: flex;
      flex-direction: row-reverse;
      padding-bottom: 20px;

      .cancel-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140px;
        height: 44px;
        font-size: 16px;
        font-weight: 600;
        color: $green;
        box-shadow: 0 1px 2px 0 $box-shadow;
        cursor: pointer;
        border-radius: 50px;
        border: 1px solid $subtle-whisper-grey;
        background-color: $white;
      }
    }
  }
}
.shoppable-review-margin {
  margin-bottom: 270px !important;
}
.shoppable-main-container {
  margin-bottom: 28px;

  .shoppable-review {
    display: flex;
    justify-content: space-between;
    font-weight: 700;

    .shoppable-review-title-section {
      display: flex;

      .shoppable-review-title {
        width: 100%;
        font-size: 20px;
        color: $black;
      }

      .info-image {
        margin-top: 1px;

        .info-icon-image {
          margin-left: 11px;
          height: 16px;
          width: 16px;

          img {
            height: 16px;
            width: 16px;
          }
        }
      }
    }

    .shoppable-review-view {
      font-size: $font-size-14;
      color: $green;
      display: flex;
      cursor: pointer;
      height: fit-content;

      .view-dropdown-icon {
        transform: rotate(90deg);
        width: 15px;
        height: 15px;
        cursor: pointer;
        margin-left: 5px;
      }
    }
  }

  .shoppable-reference-product-container {
    margin-top: 8px;

    .shoppable-reference-product-text-clipboard {
      display: flex;
      align-items: center;

      .shoppable-reference-product-section {
        position: relative;

        .shoppable-underline-reference-product-text {
          display: flex;
          color: $green-light;
          cursor: pointer;
        }

        .shoppable-underline-reference-no-product-text {
          display: flex;
          color: $green-light;
        }
        .shoppable-reference-product-preview-section {
          visibility: hidden;
          position: absolute;
          top: 30px;
          z-index: 999;
          height: 210px;
          width: 200px;
          background: $black;
          opacity: 0.8;
          border-radius: 10px;

          &:before {
            position: absolute;
            transform: rotate(180deg);
            content: "";
            border-width: 10px;
            border-style: solid;
            border-color: $black $transparent $transparent $transparent;
            top: -19px;
            left: 28px;
          }

          .shoppable-reference-product-data-section {
            .shoppable-reference-top-section {
              display: flex;
              justify-content: center;
              margin: 15px 8px 0px 8px;

              .shoppable-reference-product-image {
                display: flex;
                height: 90px;
                width: 90px;
                margin-left: 8px;
                justify-content: center;

                img {
                  border-radius: 8px;
                  object-fit: contain;
                }
              }
            }

            .shoppable-reference-bottom-section {
              margin-top: 10px;
              padding: 8px;

              .shoppable-reference-product-name {
                line-height: 1.5;
                color: $white;
                text-align: center;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              .shoppable-reference-product-info {
                margin-top: 2px;
                line-height: 1.5;
                color: $white;
                text-align: center;
              }
            }
          }
        }

        &:hover .shoppable-reference-product-preview-section {
          visibility: visible;
        }
      }

      .shoppable-reference-product-copy-clipboard {
        position: relative;
        margin-left: 12px;

        .clipboard-image {
          position: relative;
          bottom: 3px;
          cursor: pointer;
          height: 20px;
          width: 18px;

          img {
            height: 100%;
            width: 100%;
          }
        }
      }
    }
  }

  .Shoppable-review-table {
    padding-top: 10px;
    display: flex;
    flex-direction: column;

    .shoppable-review-table-head {
      .th {
        padding: 8px 20px;
      }

      .shoppable-review-table-head-row {
        display: flex;
        color: $steel-gray;
        text-align: left;
        text-transform: uppercase;
      }
    }

    .shoppable-review-table-body {
      border: 1px solid $grainsboro;
      display: flex;
      flex-direction: column;
      border-radius: 4px;

      &:hover {
        background: $light-mint;
        border: 1px solid $green;

        .show-more-detail {
          .line {
            background-color: $green;
          }
        }
      }

      .td {
        padding: 4px 24px 4px 20px;
        position: relative;

        .filter-select-input {
          border: none;
          background-color: $transparent;
          color: $black;
          width: 90% !important;
          height: 37px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
        }

        .autocomplete-results-nouom {
          position: absolute;
          list-style: none;
          top: 40px;
          left: 16px;
          right: 5px;
          box-shadow: 0 1px 10px 0 $box-shadow;
          background: $white;
          z-index: 2;
          color: $black;
          max-height: 360px;
          overflow-y: scroll;
          width: 210px;
          scrollbar-width: none;
          padding: 8px 10px;
          cursor: pointer;
          margin: 2px;
          border-radius: 4px;

          &::-webkit-scrollbar {
            display: none;
          }
        }

        .autocomplete-results {
          position: absolute;
          list-style: none;
          top: 40px;
          left: 16px;
          right: 5px;
          box-shadow: 0 1px 10px 0 $box-shadow;
          background: $white;
          z-index: 2;
          color: $black;
          max-height: 360px;
          overflow-y: scroll;
          width: 210px;
          border-radius: 4px;
          scrollbar-width: none;

          &::-webkit-scrollbar {
            display: block;
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: $whispering-white-smoke;
          }

          &::-webkit-scrollbar-thumb {
            background: $grainsboro;
            background-clip: content-box;
          }
        }

        .autocomplete-result {
          padding: 8px 10px;
          cursor: pointer;
          margin: 2px;
          border-radius: 4px;

          &.is-active,
          &:hover {
            background: $green;
            color: $white;
          }
        }

        .filter-icon-box {
          position: absolute;
          right: 10px;
          width: 40px;
          left: 4px;
          top: 1px;
          height: 45px;
          display: flex;
          align-items: center;
          justify-content: center;

          .dropdown-icon {
            transform: rotate(90deg);
            width: 8px;
            cursor: pointer;
          }

          &.dropdown-disabled {
            cursor: default;
            pointer-events: none;
            background-color: inherit;
          }
        }
      }

      .shoppable-container {
        display: flex;
        justify-content: space-between;

        .shoppable-sub-container {
          cursor: pointer;
          .dropdown-shoppable-data {
            transform: rotate(90deg);
            width: 8px;
            cursor: pointer;
            position: absolute;
            left: 0px;
            top: 18px;

            &.dropdown-disabled {
              cursor: default;
              pointer-events: none;
            }
          }

          .autocomplete-results-shoppable {
            position: absolute;
            list-style: none;
            top: 45px;
            left: 0px;
            right: 5px;
            box-shadow: 0 1px 10px 0 $box-shadow;
            background: $white;
            z-index: 1;
            color: $charcoal-light;
            max-height: 360px;
            overflow-y: scroll;
            width: 80%;
            border-radius: 4px;
            scrollbar-width: none;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          .autocomplete-result-shoppable {
            padding: 8px 20px;
            cursor: pointer;
            margin: 2px;
            border-radius: 4px;

            &.is-active,
            &:hover {
              background: $green;
              color: $white;
            }
          }

          .shoppable-text-area {
            margin: 10px 0px 0px 4px;
            display: flex;

            .shoppable-name-text {
              color: $jet-black;
            }

            .shoppable-sub-name-text {
              color: $gunmetal-grey;
            }
          }
        }
      }

      .shoppable-review-table-body-row {
        display: flex;
        color: $black;
        text-align: left;

        .ing-tool {
          position: relative;
        }

        .ing-note {
          position: relative;
        }

        .shoppable-ingredient-data {
          color: $black;
        }

        .shoppable-notes-data {
          color: $black;
        }

        .shoppable-quantity-data {
          color: $black;
          position: relative;
        }

        .quantity-uom-data {
          color: $black;
          position: relative;

          .dropdown-quantity-icon {
            transform: rotate(90deg);
            width: 6px;
            cursor: pointer;
            position: absolute;
            left: 0px;
            top: 18px;
          }

          .autocomplete-results-uom {
            position: absolute;
            list-style: none;
            top: 33px;
            left: 0px;
            right: 5px;
            box-shadow: 0 1px 10px 0 $box-shadow;
            background: $white;
            z-index: 1;
            color: $charcoal-light;
            max-height: 360px;
            overflow-y: scroll;
            width: 100%;
            padding: 4px;
            border-radius: 4px;
            scrollbar-width: none;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          .autocomplete-result-uom {
            padding: 8px 7px;
            cursor: pointer;
            margin: 2px;
            border-radius: 4px;

            &.is-active,
            &:hover {
              background: $green;
              color: $white;
            }
          }
        }

        .left-ing {
          left: 18px;
        }

        .shoppable-data {
          color: $jet-black;
          position: relative;

          span {
            color: $gunmetal-grey;
          }

          .dropdown-shoppable-data {
            transform: rotate(90deg);
            width: 8px;
            cursor: pointer;
            position: absolute;
            left: 0px;
            top: 18px;

            &.dropdown-disabled {
              cursor: default;
              pointer-events: none;
            }
          }

          .autocomplete-results-shoppable {
            position: absolute;
            list-style: none;
            top: 33px;
            left: 0px;
            right: 5px;
            box-shadow: 0 1px 10px 0 $box-shadow;
            background: $white;
            z-index: 1;
            color: $charcoal-light;
            max-height: 360px;
            overflow-y: scroll;
            width: 80%;
            padding: 4px;
            border-radius: 4px;
            scrollbar-width: none;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          .autocomplete-result-shoppable {
            padding: 8px 7px;
            cursor: pointer;
            margin: 2px;
            border-radius: 4px;

            &.is-active,
            &:hover {
              background: $green;
              color: $white;
            }
          }
        }
      }

      .text-light-h3{
        margin-bottom: 0px;
      }

      .show-more-detail {
        padding-right: 24px;
        padding-left: 20px;
        font-size: 14px;

        .line {
          height: 1px;
          width: 100%;
          background-color: $grainsboro;
          margin-top: 6px;
        }
        .computed-size {
          position: relative;
          top: 9px;
          margin-left: 2px;
          .computed-size-text {
            color: $jet-black;
            font-family: $font-family-averta;
          }

          .computed-size-no-data {
            margin-top: -18px;
            color: $gunmetal-grey;
            line-height: 18px;
            margin-left: 108px;
            font-style: normal;
          }

          .computed-size-with-data {
            margin-top: -18px;
            color: $jet-black;
            line-height: 18px;
            margin-left: 108px;
            font-style: normal;
            display: flex;
          }

          .computed-unit {
            margin-left: 1px;
          }
        }
      }

      .dropdown-icon-show-more-detail {
        width: 19px;
        cursor: pointer;
        transform: rotate(90deg);
        margin: 0 auto;
        margin-bottom: 4px;
      }
    }
  }

  .not-shoppable-products {
    text-align: left;
    margin: 20px 0px;

    .not-shoppable-text {
      color: $red-orange;
    }
  }

  .non-products {
    display: flex;
    justify-content: center;
    padding-top: 25px;

    .text {
      color: $gunmetal-grey;
    }
  }

  .main-product-container {
    padding: 0px 20px;
  }

  .shoppable-promoted-products {
    padding-top: 25px;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;

    .filter {
      color: $slate-gray;
      text-transform: uppercase;
      display: flex;
      gap: 12px;
      position: relative;
      align-items: center;

      .filter-text {
        text-align: center;
        color: $copper-rust !important;
      }

      .filter-search-tag-popup {
        display: flex;
        border: 1px solid $grainsboro;
        border-radius: 22px;
        width: auto;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        padding: 7px 20px;

        .brand-details {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          cursor: pointer;
          gap: 10px;

          .brand-selected {
            color: $copper-rust !important;
            font-size: 16px;
            text-transform: capitalize;
          }

          .arrow {
            width: 20px;
            height: 20px;
            cursor: pointer;

            .brand-dropdown-icon {
              transform: rotate(90deg);
              height: 100%;
              width: 100%;
            }
          }
        }

        .line {
          height: 30px;
          width: 1px;
          background-color: $bright-gray;
          margin-left: 10px;
        }

        .exit-box-section {
          height: 100%;

          .exit-brand-icon {
            height: 15px;
            width: 15px;
            position: relative;
            cursor: pointer;
            top: -2px;
            left: 8px;
          }
        }
      }

      .tag-filter-search-brand-main {
        overflow-y: visible !important;
        scrollbar-color: $grainsboro $whispering-white-smoke;
        scrollbar-width: thin;
        background-color: $white;
        width: 414px;
        height: auto;
        z-index: 9;
        position: absolute;
        top: 58px;
        left: -30px;
        border: 2px solid $grainsboro;
        border-bottom: 16px solid $grainsboro;
        box-shadow: 0 1px 10px 0 $shadow-black;
        border-radius: 4px;

        ::-webkit-scrollbar {
          width: 12px;
          border-radius: 3px;
        }

        ::-webkit-scrollbar-track {
          background: $whispering-white-smoke;
        }

        ::-webkit-scrollbar-thumb {
          background: $grainsboro;
          border: 3px solid $transparent;
          border-radius: 15px;
          background-clip: content-box;
        }

        .ingredient-brand-search-bar {
          background-color: $white;
          position: static;
          height: 84px;

          .search-bar-content {
            position: relative;
            top: 30px;
            left: 30px;
            background-color: $pristine-white;
            border: 1px solid $grainsboro-gray;
            border-radius: 30px;
            padding: 0 0px 0 16px;
            height: 36px;
            width: 342px;
            margin-bottom: -40px;

            .search-icon-grey-image {
              position: relative;
              top: 7px;
              height: 18px;
              width: 20px;
              cursor: pointer;
            }

            .search-bar-text {
              position: relative;
              left: 3px;
              width: 274px;
              top: 7px;
              background: none;
              color: $graphite-gray;
              caret-color: $green;
              border: none;
              border-radius: 0;
              box-shadow: none;
            }

            .align-search-input-box {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 272px;
            }

            .exit-search-icon {
              width: 14px;
              height: 14px;
              position: absolute;
              top: 11px;
              right: 8px;
              float: right;
              cursor: pointer;
            }
          }
        }

        .ingredient-search-brand-data-main {
          overflow-y: auto;
          max-height: 314px;

          .table-image-loader {
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9;
            background-color: $white;
            margin: 0 auto;
            height: 200px;
            width: 210px;

            .loader {
              border: 3px solid $pristine-white;
              border-radius: 50%;
              border-top: 3px solid $spanish-gray;
              border-right: 3px solid $spanish-gray;
              border-bottom: 3px solid $spanish-gray;
              width: 24px;
              height: 24px;
              -webkit-animation: spin 2s linear infinite;
              animation: spin 2s linear infinite;
            }

            @-webkit-keyframes spin {
              0% {
                -webkit-transform: rotate(0deg);
              }

              100% {
                -webkit-transform: rotate(360deg);
              }
            }

            @keyframes spin {
              0% {
                transform: rotate(0deg);
              }

              100% {
                transform: rotate(360deg);
              }
            }
          }

          .brand-details-checkbox {
            width: 100%;
            cursor: pointer;

            .ingredient-brand-data {
              display: flex;
              width: 100%;
              padding: 12px;

              &:hover {
                background: $aqua-spring !important;
              }

              .rounded {
                position: relative;
                top: 3px;

                .container-promote {
                  background-color: $white;
                  width: 24px;
                  height: 24px;
                  position: relative;
                  cursor: pointer;
                  font-size: 3px;
                  -webkit-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;
                  left: 34px;

                  input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;
                    height: 0;
                    width: 0;

                    &:checked {
                      ~ {
                        .checkmark {
                          background-color: $copper-rust;
                          border: 3px solid $copper-rust;

                          &:after {
                            display: block;
                          }
                        }
                      }
                    }
                  }

                  .checkmark {
                    position: absolute;
                    left: 0;
                    height: 24px;
                    width: 24px;
                    color: $grainsboro;
                    background-color: $white;
                    border: 2px solid $grainsboro;
                    border-radius: 4px;

                    &:hover {
                      border: 3px solid $copper-rust;
                    }

                    &:after {
                      content: "";
                      position: absolute;
                      display: none;
                      left: 6px;
                      top: 2px;
                      width: 6px;
                      height: 12px;
                      border: solid $white;
                      border-width: 0 2px 2px 0;
                      -webkit-transform: rotate(45deg);
                      -ms-transform: rotate(45deg);
                      transform: rotate(45deg);
                    }
                  }
                }
              }

              .brand-search-list-data {
                display: flex;
                position: relative;
                top: 4px;
                left: 70px;

                .search-brand-sort-name {
                  color: $black;
                  text-transform: capitalize;

                  .search-brand-quantity {
                    color: $black;
                  }
                }
              }
            }

            .shoppable-brand-data-add-background {
              background-color: $aqua-spring;
            }

            .is-active {
              background-color: $aqua-spring;
            }

            .load-button {
              width: 140px;
              margin: 0 auto;
              margin-top: 10px;
            }
          }

          .add-ingredients-background {
            background-color: RGB(231, 246, 228);
          }
        }

        .no-result-for-brand {
          display: flex;
          justify-content: space-around;
          margin-top: 14px;
          color: $shadow-gray;
        }

        .apply-btn-container {
          position: relative;
          top: 14px;

          .filter-save-btn {
            display: flex;
            justify-content: center;
            position: sticky;
            background-color: $white;
            width: 410px;
            height: 83px;

            .filter-btn {
              background: $copper-rust;
              border-radius: 20px;
              border: none;
              width: 175px;
              height: 35px;
              text-transform: uppercase;
              color: $white;
              cursor: pointer;
              margin-top: 24px;
            }
          }
        }
      }
    }

    .promoted-products {
      font-size: 16px;
      font-weight: 600;
      color: $jet-black;
    }

    .show-only-promoted-products {
      color: $copper-rust;
      position: relative;
      display: flex;
      align-items: end;

      .toggle-swtich-promoted-section {
        display: flex;
        align-items: center;
      }

      .toggle-top {
        vertical-align: middle;
      }

      .switch {
        position: relative;
        display: inline-block;
        width: 42px;
        height: 26px;
        margin-left: 20px;

        input {
          opacity: 0;
          width: 0;
          height: 0;
        }
      }

      .slider-round {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: $light-white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
        border-radius: 30px;

        &:before {
          position: absolute;
          content: "";
          height: 23px;
          width: 23px;
          left: 2px;
          bottom: 2px;
          background-color: $white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
          border-radius: 50%;
        }
      }

      input {
        &:checked {
          + {
            .slider-round {
              background-color: $copper-rust;

              &:before {
                -webkit-transform: translateX(15px);
                -ms-transform: translateX(15px);
                transform: translateX(15px);
              }
            }
          }
        }

        &:focus {
          + {
            .slider-round {
              box-shadow: 0 0 1px $green;
            }
          }
        }
      }
    }

    .disable {
      pointer-events: none;
      opacity: 0.5;
    }
  }

  .end {
    display: flex !important;
    justify-content: end !important;
  }
}

.conformation-popup-for-shoppable-page {
  padding: 10px 20px;
  text-align: center;
  width: 430px;

  .content-for-shoppable-page {
    .conformation-description {
      font-size: 18px;
      text-align: center;
      margin-bottom: 20px;
      font-weight: 700;
    }

    .shoppable-page-button-container {
      display: flex;
      align-items: center;
      justify-content: center;

      .shoppable-page-confirm-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 20px;
        border: 0;
        background-color: $green;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;
        font-weight: 700;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 90px;
        height: 41px;
        font-size: 16px;
      }

      .shoppable-page-cancel-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 20px;
        border: 1px solid $subtle-whisper-grey;
        background-color: $white;
        text-shadow: 0 -1px 0 $faint-black;
        color: $green;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 90px;
        height: 41px;
        font-size: 16px;
      }
    }
  }
}

.delete-shoppable-review-modal {
  text-align: left;
  display: flex;
  flex-direction: inherit;
  justify-content: space-between;
  align-items: normal;
  margin-top: 30px;
  max-height: 160px;
  padding: 0 14px;
  width: 450px;

  .delete-shoppable-review-image {
    img {
      width: 80px;
      margin-bottom: 10px;
    }
  }

  .delete-shoppable-review-content {
    width: 310px;

    .delete-shoppable-review-title {
      position: relative;
      left: -8px;
      color: $black;
      font-weight: bold;
      font-size: 20px;
    }

    .delete-shoppable-review-description {
      position: relative;
      left: -8px;
      color: $grey;
      line-height: 24px;
    }

    .delete-shoppable-review-button-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 32px;
      margin-bottom: 8px;
      gap: 20px;
    }
  }
}

.shoppable-review-first-section {
  padding: 17px;
  box-shadow: 3px 3px 6px 3px $gentle-gainsboro-gray;
  background-color: $white;
  border-radius: 8px;

  .shoppable-review-first-section-content {
    display: flex;

    .shoppable-image-container {
      min-width: 247px;
      width: 247px;
      height: 173px;
      min-height: 173px;
      margin-right: 20px;

      .image {
        width: 247px;
        height: 173px;
        object-fit: cover;
      }

      .text-container-shoppable-review {
        visibility: visible;
        position: absolute;
        cursor: pointer;
        top: 278px;
        height: 54px;
        width: 247px;
        border-radius: 4px;
        background-image: linear-gradient(
          $veiled-gray,
          $veiled-gray
        );

        .upper-text-container-shoppable-review {
          padding: 8px;
          color: $white;
          font-size: 10px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .lower-text-container-shoppable-review {
          padding: 0px 8px;
          color: $white;
          font-size: 14px;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .shoppable-review-first-section-details {
      .shoppable-review-first-section-id {
        font-weight: 400;
        font-size: 14px;
        color: $charcoal-gray;
        padding-bottom: 13px;
      }

      .shoppable-review-first-section-name {
        color: $jet-black;
        padding-bottom: 10px;
      }

      .shoppable-review-first-section-desc {
        color: $jet-black;
      }
    }
  }
}

.shoppable-product-copy-to-clipboard-information {
  display: flex;
  justify-content: center;

  .copy-to-clipboard-successfully {
    position: fixed;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: $charcoal-gray;
    background-color: $green-peppermint;
    border-radius: 7px;
    border: 1px solid $green-light;
    padding: 10px 15px;
    top: 75px;
    z-index: 999;

    .green-correct {
      height: 22px;
      width: 22px;
      position: relative;
      top: -2px;
      margin-right: 8px;
    }

    .close-icon {
      margin-left: 15px;
      height: 16px;
      width: 16px;
      position: relative;
      bottom: 1px;
      cursor: pointer;
    }
  }
}

.top-section-shoppable-review {
  .shoppable-review-top-container {
    display: flex;
    justify-content: space-between;

    .saved-recipe-popup {
      display: flex;
      background: $light-mint;
      border: 1px solid $green;
      width: 128px;
      height: 47px;
      border-radius: 7px;
      margin-top: 8px;

      .saved-recipe-popup-container {
        display: flex;
        padding: 10px 24px;
      }

      .saved-recipe-popup-image {
        width: 22px;
        height: 22px;
      }

      .saved-recipe-popup-text {
        color: $charcoal-gray;
        margin-left: 8px;
      }
    }

    .shoppable-review-top-button {
      display: flex;
      justify-content: space-between;
      position: relative;
      top: 5px;
      margin: 20px;

      .back-button-text {
        color: $black;
      }
    }

    .shoppable-review-section {
      margin: 20px;
      display: flex;
      gap: 20px;
      
      .add-tag-button {
        background-color: $green;
        color: $white;
        border-radius: 50px;
        border: none;
        padding: 10px 30px;
        margin: 0px 8px;
        cursor: pointer;
        box-shadow: 0px 1px 2px 0px $box-shadow;
      }

      .disable {
        opacity: 0.5;
        pointer-events: none;
      }

      .tag-cancel-btn {
        float: left;
        border: 1px solid $subtle-whisper-grey;
        background-color: $white;
        color: $green;
      }
    }
  }
}

.promoted-product-message {
  display: flex;
  justify-content: flex-end;

  .promoted-product-text {
    font-size: 12px;
    font-weight: 400;
    color: $grey;
  }
}
.product-matches-section {
  .filter-section-no-sort {
    display: flex;
    justify-content: flex-end !important;
  }

  .filter-section {
    display: flex;
    margin-bottom: 10px;
    justify-content: space-between;

    .product-matches-search {
      margin-bottom: 30px;
      display: flex;

      .search-box-shoppable-review {
        float: right;
        background-color: $pristine-white;
        border: 1px solid $grainsboro-gray;
        border-radius: 30px;
        padding: 0 12px 0 16px;
        height: 46px;
        width: 345px;

        .shoppable-review-input-box {
          width: 320px;
          height: 40px;
          margin: 2px 0px 2px 2px;
          padding: 2px 0px 2px 30px;
          background: none;
          color: $spanish-gray;
          caret-color: $green;
          border: none;
          border-radius: 0;
          box-shadow: none;
        }

        .align-search-input-box {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 293px;
          display: block;
        }

        .exit-shoppable-review-ingredient {
          width: 18px;
          height: 18px;
          position: relative;
          bottom: 32px;
          right: 4px;
          float: right;
          cursor: pointer;
        }

        .shoppable-review-icon-green-edit-product-image {
          position: relative;
          top: -33px;
          float: left;
          height: 24px;
          cursor: pointer;
          text-align: right;
          vertical-align: middle;
        }
      }
    }

    .sort-by-section-main-container {
      text-align: right;
      margin-bottom: 15px;

      .sort-by-section-main {
        display: inline-flex;

        .sort-by-heading {
          line-height: 20px;
          text-transform: uppercase;
          color: $slate-gray;
          align-self: center;
          margin-right: 6px;
        }

        .sort-by-result-main {
          border: 1px solid $grainsboro;
          border-radius: 4px;
          padding: 12px;
          width: 213px;
          height: 44px;
          cursor: pointer;
          position: relative;

          .sort-by-result-text {
            display: flex;
            justify-content: space-between;
            line-height: 16px;
            color: $jet-black;

            .sort-by-arrow-icon {
              .sort-by-dropdown-icon-open {
                height: 15px;
                width: auto;
                transform: rotate(270deg);
              }

              .sort-by-dropdown-icon-close {
                height: 15px;
                width: auto;
                transform: rotate(90deg);
              }
            }
          }

          .sort-by-dropdown-result {
            position: absolute;
            right: 0;
            width: 210px;
            margin-top: 16px;
            box-shadow: 0 1px 10px 0 $box-shadow;
            background: $white;
            z-index: 1;
            color: $charcoal-light;
            overflow-y: scroll;
            border-radius: 4px;
            scrollbar-width: none;

            &::-webkit-scrollbar {
              display: none;
            }

            .sort-by-result-main-container {
              padding: 8px 10px;
              color: $jet-black;
              cursor: pointer;
              margin: 2px;
              border-radius: 4px;

              &.is-active {
                background: $green;
                color: $white;
                pointer-events: none;
              }

              &:hover {
                background: $green;
                color: $white;
              }

              .sort-by-result-content {
                .sort-by-result-content-text {
                  margin-top: 3px;
                  margin-left: 3px;
                  display: flex;
                }
              }
            }
          }
        }
      }
    }

    .search-brand-popup {
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  // css dropdown//
  .product-matches-title-search {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 25px;

    .product-matches-title {
      font-weight: 700;
      font-size: 16px;
      color: $jet-black;
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 0;

      .not-applicable {
        color: $fiery-red-blaze;
        font-size: 14px;
        font-weight: 400;
      }

      .tag-filter-zero-product-tooltip-section {
        position: relative;
        cursor: pointer;
        bottom: 4px;

        .tooltip-icon {
          height: 16px;
          width: 16px;
          cursor: pointer;

          img {
            height: 100%;
            width: 100%;
          }
        }
      }
    }

    .search-results-from-all-product-matches {
      font-weight: 400;
      font-size: 14px;
      color: $grey;
      padding-top: 4px;
    }
  }
}

.loading-product-matches-shoppable,
.no-product-matches-shoppable {
  color: $shadow-gray;
  font-family: $font-family-averta;
  font-weight: 700;
  font-size: 16px;
  text-align: center;
  margin-top: 33px;
}

.bottomborder {
  margin-left: 26px;
  height: 1px;
  border-color: $platinum;
  border: 1px solid $platinum;
}
.product-disable {
  opacity: 0.5;
  pointer-events: none;
  padding-top: 20px;
}

.zero-promoted-product {
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: $gunmetal-grey;
  margin-top: 30px;
}

.extra-top-margin {
  margin-bottom: 30px !important;
}

.shop-preview-popup {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .list-group {
    display: contents;
  }

  .card-container-promoted {
    position: relative;
    background-color: $baby-blue !important;

    .promoted-product-counting {
      background-color: $white;
    }

    &:hover {
      border: 1px solid $dark-turquoise !important;
      background-color: $baby-blue !important;

      .promoted-product-counting {
        background-color: $white;
        border: 1px solid $dark-turquoise !important;
      }
    }
  }

  .empty-card-for-shoppable {
    width: 172px;
    height: 240px;
  }

  .decrease-height-shop-preview-card {
    height: 240px !important;
  }

  .disable-card {
    pointer-events: none;
    opacity: 0.5;
  }

  #disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  .shop-preview-card {
    position: relative;
    width: 172px;
    height: 260px;
    padding: 1px 4px;
    border-radius: 8px;
    border: 1px solid $whisper;
    margin-left: 5px;

    &:hover {
      background-color: $light-mint;
      border: 1px solid $green-light;
    }

    .shop-preview-card-navigation {
      display: flex;
      justify-content: space-between;
      padding: 6px 3px;

      .qty {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background: $alabaster none repeat scroll 0% 0%;
        height: 19px;
        width: 40px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 400;
        cursor: pointer;

        &:hover {
          color: $copper-rust;
        }
      }

      .recipe-name-tooltip-qty {
        height: 20px;
        position: relative;
        color: $black;
        .recipe-title-text {
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-word;
        }
      }

      .menu {
        width: 20px;
        height: 20px;
        cursor: pointer;

        .edit-btn {
          width: 20px;
          height: 20px;
          padding: 0;
          z-index: 1;
        }
      }

      .promoted-product-counting-container {
        position: relative;
        left: 42px;

        .promoted-product-counting {
          cursor: pointer;
          font-weight: 400;
          font-size: 12px;
          color: $jet-black;
          height: 22px;
          width: 22px;
          border: 1px solid $whisper;
          display: flex;
          background-color: $white;
          justify-content: space-around;
          align-items: center;
          border-radius: 4px;
        }

        .autocomplete-results-promoted {
          font-weight: 400;
          font-size: 14px;
          color: $jet-black;
          height: auto;
          max-height: 178px;
          width: 40px;
          position: absolute;
          top: 23px;
          left: -10px;
          border: 1px solid $grainsboro;
          border-radius: 4px;
          scrollbar-width: none;
          overflow: scroll;
          z-index: 999;
          background-color: $white;
          box-shadow: 0 1px 10px 0 $box-shadow;

          &::-webkit-scrollbar {
            display: none;
          }
        }

        .autocomplete-result-promoted {
          cursor: pointer;
          margin: 4px;
          border-radius: 4px;
          height: 30px;
          width: 30px;
          display: flex;
          justify-content: space-around;
          align-items: center;

          &.is-active,
          &:hover {
            background: $dark-turquoise;
            color: $white;
          }
        }

        .disable-current-position {
          margin: 4px;
          border-radius: 4px;
          height: 30px;
          pointer-events: none;
          color: $steel-gray;
          width: 30px;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
      }
    }

    .shop-preview-card-span-image {
      height: 74px;
      position: relative;

      .shop-preview-card-image {
        max-width: 74px;
        max-height: 74px;
        position: absolute;
        top: 50%;
        left: 30%;
        margin-left: 6px;
        transform: translate(-30%, -50%);
      }
    }

    .shop-preview-card-title {
      height: 37px;
      font-weight: 400;
      color: $black;
      text-align: center;
      font-size: 12px;
      padding: 6px 3px;
      word-wrap: wrap;
      overflow: hidden;
    }

    .shop-preview-ingredient-price-info-container {
      display: flex;
      justify-content: center;
      position: relative;
      margin: 5px 0px;
      padding-top: 2px;
      height: 13px;

      .shop-preview-card-price {
        height: 18px;
        font-weight: bold;
        font-weight: 700;
        font-size: 14px;
        text-align: center;
        padding: 0px 5px;
      }

      .shop-preview-info-icon-section {
        position: relative;
        width: 14px;
        height: 14px;
      }
      .info-icon-image {
        .info-tooltip-right {
          background-color: $jet-black;
          transform: translate(-50%, 0px);
          color: $white;
          word-break: keep-all;
          height: auto;
          border-radius: 8px;
          padding: 12px 5px;
          position: absolute;
          z-index: 9999;
          top: 24px;
          left: -26px;
          font-size: 12px;
          font-weight: 300;
          text-align: center;
          width: 227px;

          .info-tooltip-text-intro {
            display: flex;
            margin-bottom: 6px;

            .info-text-heading {
              width: 20%;
              font-weight: 400;
              font-size: 12px;
              font-family: $font-family-Helvetica;
              color: RGB(168, 171, 176);
            }

            .info-text-content {
              width: 80%;
              text-align: initial;
              font-weight: 400;
              color: $white;
              font-size: 12px;
              line-height: 1.333;

              .info-tag-content {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                position: relative;
                bottom: 1px;
              }
            }
          }

          .info-availability-text {
            font-weight: 400;
            font-size: 12px;
            color: RGB(255, 255, 255);
            position: relative;
            right: 10px;

            .info-count {
              color: RGB(77, 185, 53);
              font-weight: 700;
            }
          }

          &::after {
            content: "";
            position: absolute;
            top: -16px;
            left: 65%;
            margin-left: -11px;
            border-width: 10px;
            border-style: solid;
            border-color: $transparent $transparent $jet-black $transparent;
          }
        }

        &:hover .info-tooltip-right {
          visibility: visible;
        }

        .info-tooltip {
          background-color: $jet-black;
          transform: translate(-50%, 0px);
          word-break: keep-all;
          height: auto;
          border-radius: 8px;
          padding: 12px 5px;
          position: absolute;
          z-index: 9999;
          top: 24px;
          left: 37px;
          font-size: 12px;
          text-align: center;
          width: 227px;

          .info-tooltip-text-intro {
            display: flex;
            margin-bottom: 6px;

            .info-text-heading {
              width: 20%;
              font-weight: 400;
              font-size: 12px;
              font-family: $font-family-Helvetica;
              color: RGB(168, 171, 176);
            }

            .info-text-content {
              width: 80%;
              text-align: initial;
              font-weight: 400;
              color: $white;
              font-size: 12px;
              line-height: 1.333;

              .info-tag-content {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                position: relative;
                bottom: 1px;
              }
            }
          }

          .info-availability-text {
            font-weight: 400;
            font-size: 12px;
            color: RGB(255, 255, 255);
            position: relative;
            right: 10px;

            .info-count {
              color: RGB(77, 185, 53);
              font-weight: 700;
            }
          }

          &::after {
            content: "";
            position: absolute;
            top: -16px;
            left: 37%;
            margin-left: -11px;
            border-width: 10px;
            border-style: solid;
            border-color: $transparent $transparent $jet-black $transparent;
          }
        }

        &:hover .info-tooltip {
          visibility: visible;
        }
      }

      .info-icon-image:hover {
        cursor: pointer;
      }
    }
    .shop-preview-card-quantity {
      width: auto;
      height: 15px;
      font-weight: 400;
      font-size: 10px;
      color: $slate-gray;
      text-align: center;
      padding: 6px 3px;
    }

    .shop-preview-card-size {
      height: 12px;
      font-weight: 400;
      font-size: 8px;
      color: $stone-gray;
      text-align: center;
      padding-top: 3px;
      margin-top: 7px;

      .parallel-icon {
        padding: 0 6px;
      }
    }

    .shop-preview-card-number {
      height: 12px;

      font-weight: 400;
      font-size: 8px;
      color: $stone-gray;
      text-align: center;
      padding-top: 3px;
      margin-top: 7px;
    }

    .shop-preview-card-btn-container {
      margin-top: 10px;
      display: flex;
      justify-content: center;
    }

    .recipe-name-tooltip {
      padding: 6px 3px;
      height: 37px;
      position: relative;
      top: 5px;
      font-size: 12px;
      font-weight: 400;
      max-width: 328px;

      color: $black;
      text-align: center;
      .recipe-title-text {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-word;
      }

      .tool-tip-text {
        visibility: hidden;
        max-width: 324px;
        text-align: center;
        width: 152px;
        background-color: $jet-black;
        color: $white;
        height: fit-content;
        border-radius: 8px;
        padding: 5px 10px;
        position: absolute;
        z-index: 1;
        bottom: 94%;
        word-break: break-word;
        left: 65px;
        margin-left: -60px;
        font-size: 12px;
        font-weight: 400;

        &::after {
          content: "";
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -4px;
          border-width: 6px;
          border-style: solid;
          border-color: $black $transparent $transparent $transparent;
        }
      }

      &:hover .tool-tip-text {
        visibility: visible;
      }
    }
  }
}

.shoppable-main-card-section-details {
  background-color: $white;
  padding: 28px;
  box-shadow: 0px 1px 5px 0px $box-shadow;
  border-radius: 8px;

  .promoted-products-count-section {
    display: flex;
    gap: 10px;
    margin: 10px 0px;

    .count {
      font-weight: 700;
      font-size: 16px;
      color: $jet-black;
      border-radius: 12px;
      align-items: center;
      justify-content: center;
      display: flex;
    }
  }

  .tooltip-zero-promoted-message {
    position: relative;
    cursor: pointer;

    .tooltip-icon {
      height: 16px;
      width: 16px;
      cursor: pointer;

      img {
        height: 100%;
        width: 100%;
      }
    }
  }
}

.no-product-line {
  height: 1px;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 10px;
  background: $grainsboro;
}

.shoppable-group-name {
  background-color: $whisper;
  padding: 18px 28px;
  font-family: $font-family-averta;
  margin-top: 28px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
}

.margin-if-group-not-present {
  margin-bottom: 28px;
}

.main-ingredient-keywords-popup-modal {
  width: 727px;

  .main-edit-keyword-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .edit-keyword-section {
      font-size: 20px;
      font-weight: 700;
      color: $black;
      margin-left: 28px;
    }

    .close-image-icon {
      height: fit-content;
      width: fit-content;
      cursor: pointer;
      margin-right: 28px;
    }
  }

  .ingredients-quantity-section {
    display: block;
    color: $black;
    margin-left: 28px;
    margin-bottom: 5px;
    text-align: left;
    margin-right: 31px;
  }

  .ingredients-text-section {
    color: $black;
    margin-left: 4px;
  }

  .input-container {
    display: flex;
    align-items: center;

    .ingredient-keyword-container {
      width: 92%;
      border-radius: 4px;
      border: 1px solid $moon-stone;
      background-color: $frost-white;
      padding: 23px;
      color: $gunmetal-grey;

      margin-left: 28px;
      margin-bottom: 5px;

      .cross-icon-off {
        display: none;
      }

      .keywords-full-version {
        width: 667px;
        position: absolute;
        right: 41px;
        padding: 13px;
        margin-top: -24px;
        color: $black;
        background: $transparent;
        border: none;
      }
    }

    ::placeholder {
      font-style: italic;
      font-weight: 400;
      font-size: 14px;
      color: $gunmetal-grey;
    }

    .ingredient-keyword-after-edit {
      width: 92%;
      border-radius: 4px;
      border: 1px solid $moon-stone;
      background-color: $frost-white;
      padding: 10px;
      color: $gunmetal-grey;
      margin-left: 28px;
      margin-bottom: 5px;
      align-items: flex-start;
      display: flex;
      flex-wrap: wrap;
      position: relative;

      .keywords {
        min-width: 330px;
        border: none;
        background: $transparent;
        font-size: 14px;
        position: relative;
        height: 26px;
        font-weight: 400;
        color: $black;
        padding: 0px 0px 0px 9px;
      }

      .cross-icon-edit {
        position: absolute;
        right: 15px;
        width: 16px;
        height: 16px;
        align-self: center;
      }

      .selected-ingredient-keyword-main {
        float: left;
        flex-wrap: inherit;
        width: auto;
        display: flex;
        align-items: center;

        .selected-ingredient-keyword-popup {
          background-color: $whisper;
          border: 1px solid $grainsboro;
          border-radius: 4px;
          max-width: -moz-fit-content;
          max-width: 96%;
          height: fit-content;
          padding-left: 12px;
          padding-top: 3px;
          padding-bottom: 3px;
          padding-right: 12px;
          display: flex;
          align-items: center;
          position: relative;
          top: -4px;
          margin-left: 8px;
          margin-top: 8px;

          .ingredient-data-keyword {
            display: flex;
            align-items: center;
            width: -moz-max-content;
            width: 96%;
            text-align: initial;

            .ingredient-data-keyword-section {
              width: 100%;

              .data-truncated {
                max-width: 100%;
                word-wrap: break-word;
                line-height: 1.6;
                text-align: initial;
              }
            }

            .remove-ingredient-keyword-image {
              margin-left: 2px;
              cursor: pointer;
              position: relative;
              right: -6px;
              width: 8px;
              height: 8px;
            }
          }
        }
      }
    }
  }

  .keyword-article {
    display: flex;
    font-size: 14px;
    color: $gunmetal-grey;
    margin-bottom: 12px;
    margin-left: 28px;
  }

  .global-keywords {
    display: flex;
    font-size: 14px;
    font-weight: 700;
    color: $jet-black;
    margin-left: 28px;
    margin-bottom: 10px;
  }

  .main-section-breadcrumb-global-keywords {
    display: flex;
    margin-left: 19px;
    align-items: center;
    flex-wrap: wrap;

    .breadcrumb-global-keywords {
      background-color: $white;
      border: 1px solid $grainsboro;
      border-radius: 4px;
      max-width: -moz-fit-content;
      max-width: fit-content;
      padding: 12px 12px;
      height: 19px;
      display: flex;
      align-items: center;
      position: relative;
      top: -11px;
      margin-left: 8px;
      margin-top: 8px;
      font-size: 14px;
      color: $gunmetal-grey;
      font-weight: 400 !important;
    }
  }

  .global-keywords-empty {
    display: flex;
    font-size: 14px;
    color: $gunmetal-grey;
    margin-left: 28px;
  }
}

.button-div-container {
  display: flex;
  justify-content: flex-end;
  gap: 27px;
}

.search-slider {
  margin: 0 1px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .swiper-slide {
    display: flex;
    width: 194px !important;
    padding: 10px;
  }
}

.swiper-container {
  overflow: hidden;
}

.search-slider .slider-button-prev,
.search-slider .slider-button-next {
  font-size: 0;
  text-indent: -4000px;
  border: 0;
  margin-top: 0;
  position: absolute;
  z-index: 1;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: $white;
  box-shadow: 0 0 10px $box-shadow;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.search-slider .slider-button-next:before,
.search-slider .slider-button-prev:before {
  content: "";
  display: block;
  background: url(../images/green-right-arrow.png) no-repeat center center;
  width: 16px;
  height: 17px;
  z-index: 20;
  margin: 0 auto;
}

.hide-arrows {
  display: none !important;
}

.slider-container {
  max-width: 100%;
}

.search-slider .slider-button-prev {
  left: -44px;
}

.search-slider .slider-button-next {
  right: -44px;
}

.search-slider .slider-button-prev:before {
  transform: rotate(180deg);
}

.promoted-recipe-logo {
  background-color: $summer-sky;
  padding: 3px;
  display: flex;
  width: 92px;
  height: 20px;
  border-radius: 4px;
  justify-content: center;
  position: absolute;
  top: 76px;
  left: 50%;
  transform: translate(-50%, 0%);

  .promoted-recipe-text {
    font-weight: 700;
    font-size: 12px;
    text-transform: uppercase;
    color: $white;
  }
}

.loader-main-container-shoppable-review {
  height: 240px;
  background: $white;
  display: flex;
  justify-content: center;
  margin: 0px 21px 0px 4px;
  border-radius: 8px;

  .loading-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 559px;
    min-height: 293px;
    padding: 0 20px;

    .input-loading {
      height: 60px;
      display: flex;
      justify-content: center;

      .loader-image {
        border: 3px solid $white;
        border-radius: 50%;
        border-top: 3px solid $green;
        border-right: 3px solid $green;
        border-bottom: 3px solid $green;
        width: 20px;
        height: 20px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
      }
    }

    .loading-text {
      background-color: $green-peppermint;
      border-radius: 4px;
      border: 1px solid $green-fringy-flower;
      width: 468px;
      height: 57px;
      text-align: center;

      p {
        font-weight: 400;
        font-size: 16px;
        color: $shadow-gray;
        padding: 18px 0px;
      }
    }
  }
}

.shoppable-ingredient-product-tag-container {
  max-height: 84vh;
  width: 800px;
  padding: 15px 20px;
  padding-bottom: 0;

  .shoppable-ingredient-reference-top-section {
    display: flex;
    justify-content: space-between;

    .product-tag-section-heading {
      font-weight: 700;
      font-size: 24px;
      color: $black;
    }

    .product-tag-section-close-button {
      height: 24px;
      width: 24px;
      cursor: pointer;

      img {
        height: 100%;
        width: 100%;
      }
    }
  }

  .product-reference-ingredient-middle-section {
    margin-top: 10px;
    display: flex;

    .product-reference-ingredient-name {
      font-size: 16px;
      font-weight: 400;
      color: $grey;
    }
  }

  .product-reference-ingredient-bottom-section {
    margin-top: 15px;
    position: relative;

    .product-reference-ingredient-table-data::-webkit-scrollbar {
      width: 12px;
      border-radius: 3px;
    }

    .product-reference-ingredient-table-data::-webkit-scrollbar-track {
      background: $whispering-white-smoke;
    }

    .product-reference-ingredient-table-data::-webkit-scrollbar-thumb {
      background: $grainsboro;
      border: 3px solid $transparent;
      border-radius: 15px;
      background-clip: content-box;
    }

    .product-reference-ingredient-table-data {
      max-height: 49vh;
      margin-bottom: 24px;
      position: relative;
      width: 100%;
      border: 1px solid $grainsboro;
      border-radius: 8px;
      text-align: left !important;
      overflow-y: auto;
      scrollbar-color: $grainsboro $whispering-white-smoke;
      scrollbar-width: thin;

      .reference-product-table {
        background: $white-smoke;

        .reference-product-ingredient-table-head {
          .reference-product-ingredient-table-head-row {
            .reference-ingredient-product-image-heading {
              width: 12%;
            }

            .reference-ingredient-product-id-heading {
              width: 12%;
            }

            .reference-ingredient-product-title-heading {
              width: 30%;
            }

            .reference-ingredient-product-weight-heading {
              width: 10%;
              text-align: center;
            }

            .reference-ingredient-product-quantity-heading {
              width: 10%;
              text-align: center;
            }

            .reference-ingredient-product-store-count-heading {
              width: 16%;
              text-align: center;
            }

            th {
              text-align: left;
              text-transform: uppercase;
              padding: 8px 0px;
              background: $white-smoke;
              font-size: 12px;
              line-height: 1.3333;
              font-weight: 700;
              color: $spanish-gray;
            }
          }
        }

        .reference-product-ingredient-table-body {
          .reference-product-ingredient-table-body-row {
            background-color: $white;
            td {
              padding: 18px 0px;
              text-align: left;
            }

            .ingredient-product-image {
              width: 12%;
              text-align: center;
              position: relative;
              img {
                object-fit: contain;
                height: 60px;
                width: 60px;
              }
              .ingredient-promoted-recipe-logo {
                background-color: $summer-sky;
                padding: 3px;
                display: flex;
                width: 92px;
                height: 20px;
                border-radius: 4px;
                justify-content: center;
                position: absolute;
                top: 76px;
                left: 55%;
                transform: translate(-50%, 0%);
                .promoted-recipe-text {
                  font-weight: 700;
                  font-size: 12px;
                  text-transform: uppercase;
                  color: $white;
                }
              }
            }

            .ingredient-product-id {
              width: 12%;
              color: $gunmetal-grey;
              line-height: 1.5;
            }

            .ingredient-product-title {
              width: 30%;
              color: $jet-black;
              line-height: 1.5;

              .product-title {
                width: 90%;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }

            .ingredient-product-weight {
              width: 10%;
              color: $gunmetal-grey;
              line-height: 1.5;

              text-align: center;
            }

            .ingredient-product-quantity {
              width: 10%;
              color: $gunmetal-grey;
              line-height: 1.5;

              text-align: center;
            }

            .ingredient-product-store-count {
              width: 16%;
              color: $gunmetal-grey;
              line-height: 1.5;

              text-align: center;
            }

            &:last-child {
              td {
                border-bottom: unset;
              }

              td:first-child {
                border-bottom-left-radius: 8px;
              }

              td:last-child {
                border-bottom-right-radius: 8px;
              }
            }
          }
        }
      }
    }

    .shoppable-tag-and-nutrition-main-container {
      background-color: $pearl-mist;
      padding: 15px 10px;

      .shoppable-ingredient-reference-tags-main-container {
        margin-top: 7px;

        .shoppable-ingredient-tags-heading {
          color: $jet-black;
        }

        .shoppable-ingredient-reference-tags-list-container {
          display: flex;
          flex-wrap: wrap;

          .shoppable-ingredient-reference-tags-card {
            display: flex;
            color: $jet-black;
            border: 1px solid $grainsboro;
            background-color: $white;
            padding: 6px;
            border-radius: 4px;
            margin-top: 5px;
            margin-right: 8px;

            .shoppable-ingredient-reference-tags-image {
              height: 18px;
              width: 18px;
            }

            .shoppable-ingredient-reference-tags-name {
              font-family: $font-family-arial-serif;
              line-height: 1;
            }
          }
        }
      }

      .shoppable-ingredient-product-details-nutrition-container {
        padding-top: 10px;

        .nutrition-section-heading {
          color: $jet-black;
        }
        .nutrition-table-container {
          display: flex;
          width: 100%;
          margin-top: 7px;
          .nutrition-table-section {
            border-radius: 5px;
            background-color: $white;
            padding: 10px;
            width: 50%;

            .nutrition-section-area {
              .nutrition-content-row {
                display: flex;
                width: 100%;
                justify-content: space-between;
                padding-bottom: 3px;
                font-family: $font-family-averta;
                color: $jet-black;
                white-space: nowrap;
                overflow: hidden;
                position: relative;
                text-overflow: ellipsis;
                .line-break {
                  position: absolute;
                  width: 100%;
                  border: none;
                  border-bottom: 1px solid $grainsboro;
                  bottom: 0px;
                }

                .line-break-sub {
                  position: absolute;
                  bottom: 0px;
                  width: 97%;
                  margin-left: 17px;
                  border: none;
                  border-bottom: 1px solid $grainsboro;
                }

                #bold-text {
                  font-weight: 600 !important;
                }

                .nutrition-name-heading {
                  font-weight: 400;
                  width: 50%;
                }
                .nutrition-sub-name-heading {
                  width: 50%;
                  font-weight: 400;
                  padding-left: 15px;
                }
                .nutrition-unit-heading {
                  font-weight: 400;
                  width: 30%;
                }
                .nutrition-precentage-unit-heading {
                  font-weight: 600;
                  line-height: 18px;
                  width: 15%;
                  display: flex;
                  justify-content: end;
                }
              }
            }
            .nutrition-notes-section {
              margin-top: 4px;
              display: flex;
              justify-content: flex-end;
              .nutrition-notes-text {
                padding: 5px;
                border-radius: 5px;
                background-color: $pearl-mist;
                line-height: 16px;
              }
            }
          }
        }
      }
    }

    .shoppable-product-reference-ingredient-cancel-button {
      display: flex;
      flex-direction: row-reverse;
    }
  }
}

.error-save-recipe-modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 334px;
  min-height: 118px;
  padding: 0 20px;

  .publish-content {
    font-size: 16px;
    color: $charcoal-light;

    .publish-head {
      font-size: 16px;
      margin-bottom: 10px;
      color: $dim-gray;
      font-weight: bolder;

      .unable-to-save {
        color: $red;
        font-weight: 700;
      }

      .unable-to-save-title {
        color: $charcoal-light;
        font-weight: 300;
      }

      .error-list {
        margin-top: 6px;

        .unable-to-save-list {
          font-weight: 300;
          text-align: left;
          margin-left: 22px;
        }
      }

      .unable-to-save-and-publish {
        color: $red;

        .error-resolution-text {
          color: $charcoal-light;
        }
      }
    }

    .button-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      gap: 20px;
    }
  }
}

.not-shoppable-main-box-container {
  margin: 10px 0px;
  text-align: left;

  .not-shoppable-main-box-content {
    span {
      font-family: $font-family-averta;
      color: $red-orange;
    }
  }
}
