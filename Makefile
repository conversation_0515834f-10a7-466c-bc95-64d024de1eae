HOST=127.0.0.1
SOCKET=9008

all: setup

setup:
	rm -rf node_modules; npm install

run:
	npm run serve -- --host=${HOST} --port=${SOCKET}

clean:
	@echo "clean up package files"
	@rm -rf .nuxt/*

ci: clean
	@if [ -z "${VERSION}" ]; then (echo "Please export VERSION" && exit 1); fi
	@if [ -z "${DOCKER_IMAGE_NAME}" ]; then (echo "Please export DOCKER_IMAGE_NAME" && exit 1); fi
	@if [ ! -d build/resources ] ;then mkdir -p build/resources; fi
	@cp -R resources build/
	@echo "Create eb distribution..."
	@if [ ! -d build/infra-resources ] ;then mkdir -p build/infra-resources; fi
	@jq '.Image.Name = "nexus.inh0.net:18079/${DOCKER_IMAGE_NAME}:${VERSION}"' infrastructure/aws/eb/docker/Dockerrun.aws.json > build/infra-resources/Dockerrun.aws.json
	@if [ ! -d build/distributions ] ;then mkdir -p build/distributions; fi
	@(cd resources && zip -r ../build/distributions/eb-fims-lite-${VERSION}.zip .ebextensions)
	@zip -uj build/distributions/eb-fims-lite-${VERSION}.zip build/infra-resources/Dockerrun.aws.json
	@rm build/infra-resources/Dockerrun.aws.json
	@rm -rf build/infra-resources
	@echo "Create package archive..."
	@zip -rq build/distributions/fims-lite.zip . --exclude '*build*' '*.git*'
