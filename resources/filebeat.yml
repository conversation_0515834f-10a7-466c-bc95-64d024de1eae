name: "fims-lite"
fields:
  env: ${ENVIRONMENT:unknown}
cloud.id: "Monitoring:dXMtd2VzdC0yLmF3cy5mb3VuZC5pbyQ4MDYwMjgxNjcwMGM0MDdkOTU5Nzc0OTlkZjUwODNjZCQwZmI2ODZkYWZjNmY0NWUzYjU2YjI0YTM3OWQwMDczZQ=="
cloud.auth: "elastic:oZMUTunEmYMswHmDvMpKjjxc"
filebeat.inputs:
  - type: log
    enabled: true
    json.keys_under_root: true
    json.overwrite_keys: true
    json.add_error_key: true
    json.expand_keys: true
    paths:
      - "/app/fims-lite.log"