name: PR Notification on PR Opened or Updated

on:
  pull_request:
    branches:
      - main
      - develop
    types: [opened, synchronize, closed]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Slack notification action
        uses: thunkable/slack-release-notifications@v1.0.6
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          slack-channel-id: 'C078W24F2DC'
          github-to-slack-map: |
            {
              "leiweng21": "lei.weng",
              "gangrish": "greg",
              "Verbalman": "Oleg.Kasianets",
              "adititare-tudip": "aditi.tare",
              "Anirudh-singh1108": "anirudh.singh",
              "aryangiri-tudip": "aryan.giri",
              "ravi-verma-tudip": "ravi.verma",
              "riteshkumar-tudip": "ritesh.kumar",
              "shubhamthakur-tudip": "shubham.thakur",
              "prachiwalekar-tudip": "prachi.walekar",
              "akhandptp": "akhandpratap.singh",
              "johngifford": "john.gifford",
              "varcelo": "varcelo",
              "kslats9": "slats",
              "IvanBesarab": "ivan.besarab",
              "louckousse": "lucas.dupuy",
              "BlueBirdStudio": "serhii.zhdan",
              "OzgrCn": "ozgur.can661",
              "stathism": "stathis",
              "printimo": "nick",
              "paraggajbhiye-tudip": "parag.gajbhiye",
              "ozgetascet": "ozge",
              "mustafavolkank": "mustafa.kaymak",
              "kushalgupta-tudip": "kushal.gupta",
              "gurkankaymak": "gurkan",
              "kadiryildirim0": "kadir.yildirim",
              "krizhanovskii": "kryzhanovskyi.kostian",
              "mehmet-suer": "mehmet.suer",
              "rishabh14augshuklaTudip": "rishabh.shukla",
              "sidharthadas-tudip": "sidhartha.das",
              "vkrm79": "vikram.natarajan"
            }