name: Sonarqube

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop
    types: [opened, synchronize, reopened]

jobs:
  build:
    name: Sonarqube
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - uses: sonarsource/sonarqube-scan-action@v5.1.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
        with:
          projectBaseDir: src
          args: >
              -Dsonar.projectKey=fims-lite
