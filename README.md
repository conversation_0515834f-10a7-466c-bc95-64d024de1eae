# fims-lite


## Project scripts

`npm run dev` - serve project in developer mode, compiles and hot-reloads for development. <br>
`npm run build` - compiles and minifies for production. <br>
`npm run serve` - compiles and minifies for production, serve Node.js on server side <br>
`npm run icon` - generate svg sprite. <br>


## Project structure and concepts

Nuxt.js [directory structure](https://v2.nuxt.com/docs/directory-structure/nuxt).

First you start working with project you should read the [Web Development Standards](https://chicken.atlassian.net/l/cp/9drEcH2H)

Then go to the [Innit IQ Project - code and structure concept](https://chicken.atlassian.net/l/cp/LzXRZ6Bu) and read it.


## Pay attention

- Issue with v-calendar package. Stable version is: 3.0.0. Issue in GitHub repository: [Vue 3.5 - Uncaught TypeError: Cannot read properties of undefined (reading 'dayIndex')](https://github.com/nathanreyes/v-calendar/issues/1498)


## Useful links

- [Web Development Standards](https://chicken.atlassian.net/l/cp/iywfCDr6)
- [Vue.js v2](https://v2.vuejs.org/)
- Customize configuration, see Vue CLI [Configuration Reference](https://cli.vuejs.org/config/)
- Nuxt.js [Get started](https://v2.nuxt.com/docs/get-started/installation)
- Nuxt.js [directory structure](https://v2.nuxt.com/docs/directory-structure/nuxt).
- [Vuex v3](https://v3.vuex.vuejs.org/)
- [@nuxt/auth](https://auth.nuxtjs.org/)
- [@nuxt/axios](https://axios.nuxtjs.org/)
- [Watch for Vuex State changes!](https://dev.to/viniciuskneves/watch-for-vuex-state-changes-2mgj)
- [Vue custom input](https://dev.to/viniciuskneves/vue-custom-input-bk8)



