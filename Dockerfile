# to create innitdocker/fims-lite
FROM --platform=linux/arm64 node:18

WORKDIR /app
COPY . .

RUN : \
    && apt-get update \
    && npm install -g pm2 \
    && npm install \
    && npm run build \
    && :

ADD https://innit-elastic.s3-us-west-2.amazonaws.com/filebeat-arm64.deb /tmp/filebeat-arm64.deb
RUN dpkg -i /tmp/filebeat-arm64.deb && rm /tmp/filebeat-arm64.deb
COPY resources/filebeat.yml /etc/filebeat/filebeat.yml
RUN chmod 0640 /etc/filebeat/filebeat.yml

EXPOSE 8080 

ENTRYPOINT pm2-runtime /app/ecosystem.config.cjs